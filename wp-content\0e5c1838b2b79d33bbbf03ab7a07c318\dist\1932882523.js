var realCookieBanner_admin;(()=>{"use strict";var e,t,o,n,s,i={39555:(e,t,o)=>{o.d(t,{OF:()=>s,XR:()=>a,iy:()=>n});const n="Google Tag Manager",s="Matomo Tag Manager",i="gtm",r="mtm";function a(e,t){let o,a,c,{presetId:l,isGcm:d}=t,p=!1,u="";const h={events:!0,executeCodeWhenNoTagManagerConsentIsGiven:!0};let g=e||"none";switch("googleTagManagerWithGcm"!==g||d||(g="googleTagManager"),g){case"googleTagManager":case"googleTagManagerWithGcm":c=i,o="dataLayer",u=n,h.events="googleTagManagerWithGcm"!==g;break;case"matomoTagManager":c=r,o="_mtm",u=s;break;default:h.events=!1,h.executeCodeWhenNoTagManagerConsentIsGiven=!1}return o&&(a=()=>(window[o]=window[o]||[],window[o])),c&&l===c&&(p=!0,h.events=!1,h.executeCodeWhenNoTagManagerConsentIsGiven=!1),{getDataLayer:a,useManager:g,serviceIsManager:p,managerLabel:u,expectedManagerPresetId:c,features:h}}},67993:(e,t,o)=>{o.d(t,{Y:()=>a});var n=o(3713),s=o(25330),i=o(73491),r=o(59726);const a=e=>{let{url:t,style:o,label:a}=e;const{__:c}=(0,r.s)(),l={cursor:"pointer",...o};return(0,n.jsxs)(i.A,{style:l,onClick:()=>window.open(t,"_blank"),children:[(0,n.jsx)(s.A,{})," ",a||c("Learn more")]})}},39795:(e,t,o)=>{o.d(t,{q:()=>s});var n=o(3713);const s=e=>{let{notices:t}=e;return(0,n.jsx)(n.Fragment,{children:null==t?void 0:t.filter(Boolean).map((e=>{let{message:t,severity:o,nop:s,key:i}=e;return t?o?(0,n.jsx)("div",{className:`notice notice-${o} below-h2 notice-alt`,style:{margin:"10px 0px 0px"},children:"string"==typeof t?t.startsWith("<p>")||s?(0,n.jsx)("span",{dangerouslySetInnerHTML:{__html:t}}):(0,n.jsx)("p",{dangerouslySetInnerHTML:{__html:t}}):s?t:(0,n.jsx)("p",{children:t})},i||t):t:null}))})}},45277:(e,t,o)=>{o.d(t,{n:()=>u,x:()=>p});var n=o(3713),s=o(19117),i=o(8116),r=o(73491),a=o(41594),c=o(97745),l=o(59726),d=o(24325);const p="promo=in-app",u=e=>{let{style:t}=e;const{message:o}=s.A.useApp(),{__:p,_i:u}=(0,l.s)(),{fomoCoupon:h}=(0,d.J)(),g=null==h?void 0:h.coupon,y=null==h?void 0:h.valueInPercent,b=null==h?void 0:h.validUntil,v=(0,a.useCallback)((()=>{if(b){const e=new Date(b).getTime()-(new Date).getTime();if(e<=0)return;const t=e/1e3;return[Math.floor(t/3600),Math.floor(t/60)%60,Math.floor(t%60)].map((e=>e<10?`0${e}`:e)).filter(((e,t)=>"00"!==e||t>0)).join(":")}}),[b]),[,m]=(0,a.useState)();(0,a.useEffect)((()=>{const e=setInterval((()=>{m((new Date).getTime())}),1e3);return()=>{clearInterval(e)}}),[]);const f=v();return f?(0,n.jsx)(i.A,{style:t,message:u(p("Use coupon {{tag}}%s{{/tag}} in the next {{strongHours}}%s hours{{/strongHours}} and save {{strongPercent}}%d %%{{/strongPercent}} in the first year!",g,f,y),{tag:(0,n.jsx)(r.A,{color:"success",style:{marginRight:0,cursor:"pointer"},onClick:()=>{(0,c.l)(g),o.success(p("Successfully copied coupon to clipboard!"))}}),strongHours:(0,n.jsx)("strong",{style:{color:"#d33131"}}),strongPercent:(0,n.jsx)("strong",{})})}):null}},23291:(e,t,o)=>{o.d(t,{X:()=>u});var n=o(3713),s=o(15582),i=o(78915),r=o(41594),a=o(55924),c=o(45277),l=o(48104),d=o(59726),p=o(24325);const u=e=>{let{mode:t="pro",visible:o=!1,showHints:u=!0,showFomoCouponCounter:h=!0,title:g,testDrive:y=!1,assetName:b,assetMaxHeight:v,description:m,feature:f,onClose:S,inContainer:C,inContainerElement:k}=e;const{__:x,_i:j}=(0,d.s)(),{proUrl:w,hint:_,isPro:O}=(0,p.J)(),[P,R]=(0,r.useState)(),A=(0,r.useCallback)((()=>{"pro"===t?window.open(`${w}&feature=${f}&${c.x}`,"_blank").focus():window.location.href=`#/licensing?navigateAfterActivation=${encodeURIComponent(window.location.href)}`,null==S||S()}),[S,t]);if((0,r.useEffect)((()=>{C&&P&&P.parentElement.parentElement.parentElement.removeAttribute("tabIndex")}),[P]),C&&!k)return null;const T=b?`https://assets.devowl.io/in-app/wp-real-cookie-banner/${b}`:void 0;return(0,n.jsxs)(i.A,{transitionName:C?null:void 0,open:!!C||o,title:(0,n.jsxs)("span",{children:[(0,n.jsx)(s.A,{})," ",g," ","pro"===t&&x("Get PRO!")]}),onOk:A,onCancel:S,cancelText:x("No, not interested..."),okText:x("pro"===t?"I want to learn more!":O?"Activate license":"Activate free license"),className:"rcb-antd-hero-modal",width:b?800:700,getContainer:C?k:void 0,children:[!!b&&(b.endsWith(".webm")?(0,n.jsx)("video",{autoPlay:!0,muted:!0,loop:!0,style:{marginTop:10,maxHeight:v},children:(0,n.jsx)("source",{src:T,type:"video/webm"})}):(0,n.jsx)("img",{style:{marginTop:10,maxHeight:v},src:T})),(0,n.jsxs)("div",{style:{maxWidth:600,margin:"auto"},ref:R,children:[(0,n.jsx)("p",{children:"string"==typeof m?(0,a.g)(m):m}),y&&"pro"===t&&(0,n.jsx)("p",{children:j(x("Check out this feature with a {{a}}free sandbox{{/a}} before buying!"),{a:(0,n.jsx)("a",{href:x("https://try.devowl.io/?product=RCB"),target:"_blank",rel:"noreferrer"})})})]}),!!_&&u&&"pro"===t&&(0,n.jsx)("div",{style:{maxWidth:600,margin:"auto",textAlign:"left"},children:(0,n.jsx)(l.c,{..._})}),h&&"pro"===t&&(0,n.jsx)(c.n,{style:{marginBottom:15}})]})}},48104:(e,t,o)=>{o.d(t,{c:()=>a});var n=o(3713),s=o(19991),i=o(6099),r=o(92453);const a=e=>{let{title:t,description:o,link:a,linkText:c,logo:l}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.A,{children:t}),(0,n.jsxs)(i.A,{wrap:!1,style:{marginBottom:10},children:[(0,n.jsx)(r.A,{flex:"auto",children:!!o&&(0,n.jsxs)("p",{style:{margin:0},children:[(0,n.jsx)("span",{dangerouslySetInnerHTML:{__html:o}})," ",!!a&&!!c&&(0,n.jsx)("a",{href:a,target:"_blank",rel:"noreferrer",className:"button-link",children:c})]})}),(0,n.jsx)(r.A,{flex:"150px",style:{alignSelf:"center"},children:!!l&&(0,n.jsx)("img",{src:l,style:{maxWidth:"calc(100% - 20px)",height:"auto",marginLeft:20}})})]})]})}},59726:(e,t,o)=>{o.d(t,{s:()=>i,v:()=>r});var n=o(52113);const s=Symbol(),i=()=>(0,n.NV)(s),r=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,n.gm)(s,...t)}},24325:(e,t,o)=>{o.d(t,{J:()=>i,Y:()=>r});var n=o(52113);const s=Symbol(),i=()=>(0,n.NV)(s),r=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,n.gm)(s,...t)}},32041:(e,t,o)=>{o.d(t,{$:()=>d,g:()=>u});var n=o(3713),s=o(19117),i=o(19991),r=o(36086),a=o(41594),c=o(12719),l=o(59726);const d=`${c.sK}cookie-experts.svg`,p=["niklas.moselewski","mario.guenter","matthias.guenter","jan.karres"];function u(){const{modal:e}=s.A.useApp(),{__:t}=(0,l.s)();return{openDialog:(0,a.useCallback)((()=>{const o=e.info({icon:null,width:500,closable:!0,okButtonProps:{style:{display:"none"}},content:(0,n.jsxs)("div",{style:{textAlign:"center"},children:[(0,n.jsx)("img",{src:d,style:{display:"block",paddingTop:15,margin:"auto",height:176}}),(0,n.jsx)("h3",{style:{margin:"10px 0 0"},children:"Cookie Experts"}),(0,n.jsx)("p",{style:{marginTop:0},children:t("Let our team help you with the setup")}),(0,n.jsx)(i.A,{children:(0,n.jsx)(r.A.Group,{size:"large",children:p.map((e=>(0,n.jsx)(r.A,{src:`${c.sK}cookie-experts-faces/${e}.jpeg?v=3`},e)))})}),(0,n.jsx)("a",{href:t("https://devowl.io/wordpress-real-cookie-banner/cookie-experts/"),target:"_blank",rel:"noreferrer",className:"button button-large button-primary",children:t("Get help from Cookie Experts")}),(0,n.jsx)("p",{children:t("We admit, it is not easy to find all the services, cookies, etc. The legal requirements in the EU are quite complex for many website operators. We can understand if you feel overwhelmed - if this goes far beyond what you can technically do. After you know what all has to be considered, the question of how to make your website privacy compliant does not let you sleep peacefully either.")}),(0,n.jsx)("p",{children:t("Don't worry, we have a solution for you! Our Cookie Experts have already set up many cookie banners and know exactly what they are doing. They can also set up your cookie banner quickly and easily. So, we can simply take this worry away from you.")}),(0,n.jsx)("a",{style:{marginTop:10,display:"inline-block"},onClick:()=>o.destroy(),children:t("Close")})]})})}),[])}}},12719:(e,t,o)=>{o.d(t,{QB:()=>d,WH:()=>u,sK:()=>p});var n=o(3713),s=o(29766),i=o(73491),r=o(41594),a=o(23291),c=o(59726),l=o(24325);const d="#2db7f5",p="https://assets.devowl.io/in-app/wp-real-cookie-banner/";function u(e,t){const{__:o}=(0,c.s)(),{isPro:p,isLicensed:u}=(0,l.J)(),[h,g]=(0,r.useState)(!1),y="boolean"==typeof t?t:p,b=(0,r.useCallback)((e=>{g(!0),null==e||e.preventDefault()}),[g]),v=(0,r.useMemo)((()=>y?null:(0,n.jsx)(i.A,{icon:(0,n.jsx)(s.A,{}),color:d,style:{cursor:"pointer"},onClick:b,children:e.tagText||o("Unlock feature")})),[b,e]),m=(0,r.useMemo)((()=>y&&"license-activation"!==e.mode?null:(0,n.jsx)(a.X,{visible:h,onClose:()=>g(!1),...e})),[h,g,e]);return{isPro:y,isLicensed:u,tag:v,modal:m,open:b}}},52113:(e,t,o)=>{o.d(t,{NV:()=>c,gm:()=>l});var n=o(41594),s=o(75933),i=o(56702);const r={};function a(e){let t=r[e];if(!t){const o=(0,n.createContext)({});t=[o,()=>(0,n.useContext)(o)],r[e]=t}return t}const c=e=>a(e)[1]();function l(e,t,o,r){void 0===o&&(o={}),void 0===r&&(r={});const{refActions:c,observe:l,inherit:d,deps:p}=r,u=a(e),[h,g]=(0,n.useState)((()=>{const e=Object.keys(o),n=Object.keys(c||{}),s=function(t){for(var s=arguments.length,r=new Array(s>1?s-1:0),a=1;a<s;a++)r[a-1]=arguments[a];return new Promise((s=>g((a=>{const l={...a},d=[];let p=!0;const u=new Proxy(l,{get:function(){for(var t=arguments.length,s=new Array(t),r=0;r<t;r++)s[r]=arguments[r];const[a,l]=s;let h=Reflect.get(...s);if(!p)return h;if(-1===d.indexOf(l)&&(h=(0,i.G)(h),Reflect.set(a,l,h),d.push(l)),"string"==typeof l){let t;if(e.indexOf(l)>-1?t=o[l]:n.indexOf(l)>-1&&(t=c[l]),t)return function(){for(var e=arguments.length,o=new Array(e),n=0;n<e;n++)o[n]=arguments[n];return t(u,...o)}}return h}}),h=t(u,...r),g=e=>{p=!1,s(e)};return h instanceof Promise?h.then(g):g(void 0),l}))))},r={set:e=>s("function"==typeof e?e:t=>Object.assign(t,e)),...t,...e.reduce(((e,t)=>(e[t]=function(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];return s(o[t],...n)},e)),{}),...n.reduce(((e,t)=>(e[t]=function(){for(var e=arguments.length,o=new Array(e),n=0;n<e;n++)o[n]=arguments[n];return c[t](h,...o)},e)),{})};return r.suspense||(r.suspense={}),r}));(null==l?void 0:l.length)&&(0,s.C)((()=>{l.filter((e=>t[e]!==h[e])).length&&h.set(l.reduce(((e,o)=>(e[o]=t[o],e)),{}))}),[l.map((e=>t[e]))]),Array.isArray(p)&&(0,s.C)((()=>{h.set(t)}),p);const[{Provider:y}]=u;let b=h;(null==d?void 0:d.length)&&(b={...h,...d.reduce(((e,o)=>(e[o]=t[o],e)),{})});const v=(0,n.useMemo)((()=>({})),[]);return(0,n.useEffect)((()=>{const{suspense:e}=h;if(e)for(const t in e){const o=e[t],n=v[t];o instanceof Promise&&n!==o&&(v[t]=o,o.then((e=>h.set({[t]:e}))))}}),[h]),[y,b]}},75933:(e,t,o)=>{o.d(t,{C:()=>s});var n=o(41594);const s=(e,t)=>{const o=(0,n.useRef)(0);(0,n.useEffect)((()=>{if(o.current++,1!==o.current)return e()}),t)}},97745:(e,t,o)=>{function n(e){const t=document.createElement("textarea");t.innerHTML=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),t.remove()}o.d(t,{l:()=>n})},56702:(e,t,o)=>{function n(e,t){if(void 0===t&&(t=new Map),t.has(e))return t.get(e);let o;if("structuredClone"in window&&(e instanceof Date||e instanceof RegExp||e instanceof Map||e instanceof Set))o=structuredClone(e),t.set(e,o);else if(Array.isArray(e)){o=new Array(e.length),t.set(e,o);for(let s=0;s<e.length;s++)o[s]=n(e[s],t)}else if(e instanceof Map){o=new Map,t.set(e,o);for(const[s,i]of e.entries())o.set(s,n(i,t))}else if(e instanceof Set){o=new Set,t.set(e,o);for(const s of e)o.add(n(s,t))}else{if(!function(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(e))return e;o={},t.set(e,o);for(const[s,i]of Object.entries(e))o[s]=n(i,t)}return o}o.d(t,{G:()=>n})},55924:(e,t,o)=>{o.d(t,{g:()=>i});var n=o(41594);const s=/(\r\n|\r|\n|<br[ ]?\/>)/g,i=e=>"string"==typeof e?e.split(s).map(((e,t)=>e.match(s)?(0,n.createElement)("br",{key:t}):e)):e},60971:(e,t,o)=>{function n(e){void 0===e&&(e=0),document.body.scrollTop=e,document.documentElement.scrollTop=e}o.d(t,{V:()=>n})},94314:(e,t,o)=>{function n(e,t,o){if(void 0===t&&(t=50),void 0===o&&(o="..."),!e||e.length<=t)return e;const n=t-o.length,s=Math.ceil(n/2),i=Math.floor(n/2);return e.substr(0,s)+o+e.substr(e.length-i)}o.d(t,{k:()=>n})},21014:(e,t,o)=>{o.r(t),o.d(t,{ChecklistStore:()=>g.WK,ConsentStore:()=>g.s4,CookieStore:()=>g.kv,CustomizeBannerStore:()=>g.pz,OptionStore:()=>g.t6,RootStore:()=>g.yd,ScannerStore:()=>g.gU,StatsStore:()=>g.PL,TcfStore:()=>g.XS,locationRestBannerLinksOrderPut:()=>h.il,locationRestChecklistGet:()=>h.uI,locationRestChecklistPut:()=>h.LB,locationRestConsentAllDelete:()=>h.MG,locationRestConsentAllGet:()=>h.DW,locationRestConsentClearDelete:()=>h.Gg,locationRestConsentDelete:()=>h.LP,locationRestConsentDynamicPredecisionGet:()=>h.UU,locationRestConsentGet:()=>h.kU,locationRestConsentPost:()=>h.uk,locationRestConsentRefererGet:()=>h.fJ,locationRestCookieGroupOrderPut:()=>h.qg,locationRestCookieUnassignedGet:()=>h.dT,locationRestCookiesOrderPut:()=>h.aF,locationRestCountryBypassDatabasePut:()=>h.qk,locationRestCreateCookiePolicyPost:()=>h.Ac,locationRestExportConsentsGet:()=>h.Rh,locationRestExportGet:()=>h.Ai,locationRestForwardCookieGet:()=>h.HD,locationRestForwardEndpointsGet:()=>h.zS,locationRestImportPost:()=>h.hh,locationRestMigrationDelete:()=>h.Zj,locationRestMigrationPost:()=>h.aH,locationRestNavMenuAddLinksPost:()=>h.oJ,locationRestPresetsBannerGet:()=>h.Kl,locationRestRevisionCurrentGet:()=>h.qz,locationRestRevisionCurrentPut:()=>h.Dk,locationRestRevisionGet:()=>h.Ft,locationRestRevisionIndependentGet:()=>h.$c,locationRestRevisionSecondView:()=>h.ZU,locationRestScannerQueuePost:()=>h.xz,locationRestScannerResultAllExternalUrlsByHostGet:()=>h.Sk,locationRestScannerResultExternalsGet:()=>h.rO,locationRestScannerResultIgnorePost:()=>h.hN,locationRestScannerResultMarkupGet:()=>h.F4,locationRestScannerResultTemplatesGet:()=>h.mp,locationRestScannerScanWithoutLoginGet:()=>h.ko,locationRestSettings:()=>h.ZC,locationRestSettingsPatch:()=>h.Nq,locationRestStatsButtonsClickedGet:()=>h.BB,locationRestStatsCustomBypassGet:()=>h.AD,locationRestStatsMainGet:()=>h.St,locationRestTcfDeclarationsGet:()=>h.OE,locationRestTcfGvlPut:()=>h.D7,locationRestTcfVendorsGet:()=>h._0,locationRestTemplatesBlockerUseGet:()=>h.K7,locationRestTemplatesBlockersGet:()=>h.aq,locationRestTemplatesServiceUseGet:()=>h.lt,locationRestTemplatesServicesGet:()=>h.rL,useStores:()=>g.gy});var n=o(3713),s=(o(2077),o(56719),o(38123)),i=o.n(s),r=o(68789),a=o(636),c=o(12559),l=o(71951),d=o(30617),p=o(42090),u={};for(const e in r)"default"!==e&&(u[e]=()=>r[e]);o.d(t,u);var h=o(72955),g=o(34577);(0,r.handleCorruptRestApi)({[l.y.get.optionStore.restNamespace]:async()=>{await(0,p.E)({location:{path:"/plugin"}})},"wp/v2":async()=>{await(0,p.E)({location:{path:"/posts",namespace:"wp/v2"}})}});const y=document.getElementById(`${l.y.get.optionStore.slug}-component`);y&&(0,r.createRoot)(y).render((0,n.jsx)(c.K,{configProvider:{locale:{locale:i().locale(),Pagination:{items_per_page:(0,d.__)("/ page")}}},children:(0,n.jsx)(a.oC,{})}))},70697:(e,t,o)=>{o.d(t,{k:()=>a});var n=o(3713),s=o(57922),i=o(71951),r=o(30617);const a=(0,s.PA)((e=>{let{style:t}=e;const{optionStore:{isBlockerActive:o,allBlockerCount:s}}=(0,i.g)();return!o&&s>0&&(0,n.jsx)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:t,children:(0,n.jsxs)("p",{children:[(0,r.__)("Content Blockers are globally deactivated in the settings and are therefore not displayed on your website.")," ","• ",(0,n.jsx)("a",{href:"#/settings",children:(0,r.__)("Enable now")})]})})}))},45377:(e,t,o)=>{o.d(t,{A:()=>p,m:()=>u});var n=o(3713),s=o(61787),i=o(64715),r=o(33146),a=o(14383),c=o(71951),l=o(33464),d=o(30617);function p(e){let{minId:t,maxId:o,process:n}=e,s=0,i=0;const r=o-t;if(r<=0)return"n/a";for(const e of Object.values(n))for(const n of Object.values(e))s+=r,i+=(n>o?o:n)-t;return(i/s*100).toLocaleString(document.documentElement.lang,{minimumFractionDigits:2,maximumFractionDigits:2})}const u=e=>{let{children:t}=e;const{optionStore:{others:{colorScheme:[,,o]}}}=(0,c.g)(),{percent:u,currentJob:h,remaining:g}=(0,a.useProgress)({type:l.G7});return g>0&&u>0&&h?(0,n.jsx)(i.A,{title:(0,d.__)("Real Cookie Banner v5.0 introduces an optimized database schema that allows consent documents to be stored in less storage space. Previously stored consent data is currently being automatically migrated in the background (%s %%). Please have a little patience!",p(h.data)),children:(0,n.jsx)(r.A,{count:(0,n.jsx)(s.A,{style:{color:o}}),style:{top:-11,height:16,lineHeight:"16px"},children:t})}):t}},42837:(e,t,o)=>{o.d(t,{y:()=>S});var n=o(3713),s=o(95964),i=o(19162),r=o(18197),a=o(65824),c=o(73491),l=o(57922),d=o(41594),p=o(80537),u=o(14322),h=o(64715),g=o(45277),y=o(12719),b=o(98348),v=o(71951),m=o(30617);const f=(0,l.PA)((e=>{let{id:t,checked:o,title:s,description:r,link:a,linkText:l,linkTarget:d,needsPro:f,onLinkClick:S}=e;const{optionStore:{others:{isPro:C},fomoCoupon:k}}=(0,v.g)(),x=!C&&f;let j=null,w=null,_=a;switch(t){case"scanner":{const{percent:e,remaining:t,currentJob:o}=(0,b.X)();t>0&&e>0&&(null==o?void 0:o.group_position)>0&&(j=(0,n.jsx)(c.A,{children:(0,m.__)("Currently scanning (%d %%)",e)}));break}case"get-pro":w=(0,n.jsx)(g.n,{}),k&&(_+=`&${g.x}`)}return(0,n.jsxs)("div",{style:{margin:"5px 0"},children:[(0,n.jsx)(h.A,{title:o&&r,children:(0,n.jsxs)("span",{children:[x?(0,n.jsx)(p.A,{style:{color:"#7ec8ec"}}):o?(0,n.jsx)(u.A,{style:{color:"#52c41a"}}):(0,n.jsx)(i.A,{style:{color:"#e2e2e2"}})," ",(0,n.jsx)("span",{style:{textDecoration:o?"line-through":void 0,fontWeight:"bold"},children:s}),!!_&&!!l&&(0,n.jsxs)(n.Fragment,{children:["  •  ",(0,n.jsx)("a",{href:_,target:d,rel:"noreferrer",onClick:S,children:l})]})]})}),x&&"get-pro"!==t&&(0,n.jsxs)(n.Fragment,{children:[" ",(0,n.jsx)(c.A,{color:y.QB,children:(0,m.__)("Needs PRO")})]}),!!j&&(0,n.jsxs)(n.Fragment,{children:[" ",j]}),!!r&&!o&&(0,n.jsx)("p",{className:"description",style:{paddingLeft:17},children:r}),!!w&&(0,n.jsx)("div",{style:{paddingLeft:17},children:w})]})})),S=(0,l.PA)((e=>{let{onLinkClick:t,...o}=e;const{checklistStore:l}=(0,v.g)(),{busyChecklist:p,items:u,completed:h,checkable:g,done:y,checklist:{overdue:b}}=l,[S,C]=(0,d.useState)("boolean"==typeof o.showHidden?o.showHidden:!y);return(0,n.jsx)(r.A,{spinning:p,children:(0,n.jsxs)(a.A,{direction:"vertical",size:"large",children:[b&&(0,n.jsx)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:{margin:0},children:(0,n.jsx)("p",{children:(0,m.__)("How time flies! You have installed Real Cookie Banner some time ago, but you still haven't finished the configuration yet.")})}),(0,n.jsx)("p",{className:"description",children:(0,m.__)("We have collected all relevant steps for the legally compliant use of Real Cookie Banner after the first installation. Set up the cookie banner step by step to add a cookie banner to your website in compliance with the ePrivacy Directive and GDPR.")}),(0,n.jsxs)("div",{children:[y&&(S?(0,n.jsx)(c.A,{icon:(0,n.jsx)(s.A,{}),color:"default",style:{cursor:"pointer"},onClick:()=>C(!1),children:(0,m.__)("Hide completed steps")}):(0,n.jsx)(c.A,{icon:(0,n.jsx)(i.A,{}),color:"success",style:{cursor:"pointer"},onClick:()=>C(!0),children:(0,m.__)("%d / %d steps completed",h.length>g.length?g.length:h.length,g.length)})),u.map((e=>{let{id:o,...s}=e;return!S&&s.checked?null:(0,n.jsx)(f,{id:o,...s,onLinkClick:e=>{["get-pro"].indexOf(o)>-1&&l.toggleChecklistItem(o,!0),null==t||t(e)}},o)}))]})]})})}))},89657:(e,t,o)=>{o.d(t,{b:()=>p});var n=o(3713),s=o(80537),i=o(78915),r=o(57922),a=o(41594),c=o(53603),l=o(71951),d=o(30617);const p=(0,r.PA)((e=>{let{identifier:t,width:o,title:r,sprintfArgs:p=[],always:u}=e;const{optionStore:h}=(0,l.g)(),{others:{modalHints:g}}=h,[y,b]=(0,a.useState)(!1);(0,a.useEffect)((()=>{if(u){const e=g.indexOf(t);e>-1&&g.splice(e,1)}}),[u,g]),(0,a.useEffect)((()=>{b(-1===g.indexOf(t))}),[g.length,t]);const v=(0,c.m)(t,...p),m="string"==typeof v&&v.indexOf("?")>-1?v.split("?")[0]:"",f=r||(m?`${m}?`:(0,d.__)("What you should definitely know!")),S="string"==typeof v&&"string"==typeof f&&m?v.replace(f,"").trim():v,C=(0,a.useCallback)((()=>{h.setModalHintSeen(t)}),[t,h]);return(0,n.jsx)(i.A,{open:y,title:(0,n.jsxs)("span",{children:[(0,n.jsx)(s.A,{style:{color:"#1890ff"}})," ",f]}),closable:!1,onOk:C,width:o,okText:(0,d.__)("Okay, I got it"),cancelButtonProps:{style:{display:"none"}},children:S})}))},636:(e,t,o)=>{o.d(t,{oO:()=>V,oC:()=>ce});var n=o(3713),s=o(18197),i=o(33146),r=o(57922),a=o(41594),c=o(34133),l=o(27667),d=o(59726),p=o(24325),u=o(39795),h=o(14383),g=o(68789),y=o(70697),b=o(89657);const v=(0,r.PA)((()=>(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(y.k,{style:{margin:"10px 0 0 0"}}),(0,n.jsx)(l.sv,{}),(0,n.jsx)(b.b,{identifier:"blocker"})]})));var m=o(87354),f=o(71951),S=o(30617);const C=(0,r.PA)((()=>{const{optionStore:{publicUrl:e}}=(0,f.g)();return(0,n.jsxs)("div",{className:"rcb-config-footer",children:[(0,S._i)((0,S.__)("Real Cookie Banner is brought to you with {{icon/}} by"),{icon:(0,n.jsx)(m.A,{style:{color:"#dca7a7"}})}),(0,n.jsx)("a",{href:(0,S.__)("https://devowl.io/"),target:"_blank",rel:"noreferrer",children:(0,n.jsx)("img",{src:`${e}images/logos/devowl-with-text.svg`})})]})}));var k=o(19162),x=o(78915),j=o(42837),w=o(19488),_=o(24262);const O=(0,r.PA)((()=>{const{optionStore:e}=(0,f.g)(),{others:{proUrl:t,isPro:o,isConfigProNoticeVisible:s}}=e,i=`${t}&feature=main-button`,r=(0,a.useCallback)((()=>{window.open(i,"_blank"),e.dismissConfigProNotice()}),[e]),c=(0,a.useCallback)((()=>{e.dismissConfigProNotice()}),[e]);return!o&&(0,n.jsx)(_.A,{open:!!s,title:(0,S.__)("Thank you for using the free version of Real Cookie Banner. You might also be interested in the PRO version, which offers you more features, 20+ design templates, 160+ service templates, 130+ content blocker templates and much more."),icon:(0,n.jsx)(w.A,{style:{color:"#1890ff"}}),placement:"bottom",onConfirm:r,onCancel:c,cancelText:(0,S.__)("No, not interested..."),okText:(0,S.__)("I want to learn more!"),overlayStyle:{maxWidth:450},children:(0,n.jsx)("a",{className:"page-title-action",rel:"noreferrer",onClick:r,children:(0,S.__)("Get PRO version")})})})),P=(0,r.PA)((()=>{const[e,t]=(0,a.useState)(!1),{optionStore:o,checklistStore:s}=(0,f.g)(),{pathname:i}=(0,l.zy)(),{others:{customizeBannerUrl:r},publicUrl:c}=o,{completed:d,checkable:p,done:u}=s;(0,a.useEffect)((()=>{s.fetchChecklist()}),[]);const h=(0,a.useCallback)((e=>{window.location.href=`${r}&return=${encodeURIComponent(window.location.href)}`,e.preventDefault()}),[]),g=(0,a.useCallback)((()=>{t(!0),s.fetchChecklist()}),[t]),y=(0,a.useCallback)((()=>{t(!1)}),[t]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("h1",{className:"wp-heading-inline",children:(0,S.__)("Real Cookie Banner")}),(0,n.jsx)("img",{className:"rcb-nav-logo",src:`${c}images/logos/real-cookie-banner.svg`}),(0,n.jsx)("a",{className:"page-title-action",href:"#",onClick:h,children:(0,S.__)("Customize banner")}),(0,n.jsx)(O,{}),!u&&"/"!==i&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("a",{className:"page-title-action",style:{color:"#52c41a",background:"#f6ffed",borderColor:"#85af65"},onClick:g,children:[(0,n.jsx)(k.A,{})," ",(0,S.__)("%d / %d steps completed",d.length>p.length?p.length:d.length,p.length)]}),(0,n.jsx)(x.A,{open:e,title:(0,S.__)("Set up the cookie banner"),onCancel:y,okButtonProps:{style:{display:"none"}},cancelText:(0,S.__)("Close"),width:800,children:(0,n.jsx)(j.y,{showHidden:!0,onLinkClick:y})})]}),(0,n.jsx)("hr",{className:"wp-header-end"})]})}));var R=o(79521);const A=(0,r.PA)((()=>{const{showNoticeAnonymousScriptNotWritable:e}=(0,R.j)();return!!e&&(0,n.jsx)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:{margin:"10px 0 0 0"},children:(0,n.jsx)("p",{children:(0,S._i)((0,S.__)("Your {{code}}wp-content{{/code}} folder is not writable. Please check your permissions!"),{code:(0,n.jsx)("code",{})})})})})),T=(0,r.PA)((()=>{const{optionStore:{publicCookieCount:e,busySettings:t,isBannerActive:o}}=(0,f.g)();return o&&!e&&!t&&(0,n.jsx)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:{margin:"10px 0 0 0"},children:(0,n.jsx)("p",{children:(0,S.__)("The cookie banner is activated, but unfortunately you have not yet created or enabled services.")})})})),F=(0,r.PA)((()=>{const{optionStore:e,cookieStore:t}=(0,f.g)(),{bannerlessConsentChecks:{legalBasisLegitimateInterest:o,legalBasisConsentWithoutVisualContentBlocker:s},isBannerLessConsent:i}=e,{pathname:r}=(0,l.zy)();return(0,n.jsx)(u.q,{notices:[{nop:!0,key:"bannerless-consent-legitimate-interest",message:i&&-1===r.indexOf("/edit/")&&-1===r.indexOf("/new")&&o.length>0&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{children:(0,S.__)("You have decided to obtain consent without cookie banners (banner-less consent) on your website. This means that when your website visitors visit your website for the first time, they will not know that you use this service on the basis of a legitimate interest and can object to it. They will need to read your cookie policy or privacy policy to find out.")}),(0,n.jsx)("p",{children:(0,S.__)("At the same time, you use the following non-essential services on the legal basis of a legitimate interest:")}),(0,n.jsx)("ul",{children:o.map((e=>{let{name:t,groupId:o,id:s}=e;return(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:t})," • ",(0,n.jsx)("a",{href:`#/cookies/${o}/edit/${s}`,children:(0,S.__)("Edit")})]},s)}))}),(0,n.jsx)("p",{children:(0,S._i)((0,S.__)("Check whether there really is a legitimate interest in the legal sense according to {{a}}Art. 6 (1) (f) GDPR{{/a}}, which is only given in rare cases!"),{a:(0,n.jsx)("a",{href:"https://gdpr-text.com/read/article-6/",target:"_blank",rel:"noreferrer"})})}),(0,n.jsx)("p",{children:(0,n.jsx)("a",{onClick:()=>e.dismissBannerlessConsentLegitimateServicesNotice(),children:(0,S.__)("Dismiss notice")})})]}),severity:"warning"},{nop:!0,key:"bannerless-consent-services-without-visual-content-blocker",message:i&&-1===r.indexOf("/edit/")&&-1===r.indexOf("/new")&&s.length>0&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{children:(0,S.__)("You have decided to obtain consent without cookie banners (banner-less consent) on your website. At the same time, some non-essential services, which requires a consent, doese not have visual content blockers, which is why website visitors without cookie banners cannot consent to them (except via the legal link to change the privacy settings).")}),(0,n.jsx)("p",{children:(0,S.__)("You should create visual content blockers for these services, if the services display visual elements, or disable them for your website! The following services are affected:")}),(0,n.jsx)("ul",{children:s.map((e=>{let{name:o,groupId:s,id:i}=e;return(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:o})," • ",(0,n.jsx)("a",{href:"#/blocker/new",children:(0,S.__)("Create Content Blocker")})," • ",(0,n.jsx)(_.A,{title:(0,S.__)("Are you sure you want to disable this service?"),onConfirm:async()=>{const e=t.groups.entries.get(s);await e.fetchCookies();const o=e.cookies.entries.get(i);return o.setStatus("private"),o.patch()},okText:(0,S.__)("Deactivate"),cancelText:(0,S.__)("Cancel"),children:(0,n.jsx)("a",{children:(0,S.__)("Disable")})})]},i)}))}),(0,n.jsx)("p",{children:(0,n.jsx)("a",{onClick:()=>e.dismissBannerlessConsentServicesWithoutVisualContentBlockerNotice(),children:(0,S.__)("Dismiss notice")})})]}),severity:"warning"}]})}));var E=o(39555);const B=(0,r.PA)((()=>{const{optionStore:{setCookiesViaManager:e,createdTagManagers:t,isGcm:o}}=(0,f.g)(),{managerLabel:s,expectedManagerPresetId:i}=(0,E.XR)(e,{isGcm:o,presetId:""}),{pathname:r}=(0,l.zy)();return s&&!t[i].length&&/^\/cookies\/\d+$/.test(r)&&(0,n.jsx)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:{margin:"10px 0 0 0"},children:(0,n.jsxs)("p",{children:[(0,S._i)((0,S.__)("You have not yet defined a %s service. To use {{strong}}%s{{/strong}} with Real Cookie Banner, you must create the appropriate service.",s,s),{strong:(0,n.jsx)("strong",{})})," ","• ",(0,n.jsx)("a",{href:`#${r}/new?force=${i}`,children:(0,S.__)("Create now")})]})})})),D=(0,r.PA)((()=>{const{optionStore:e}=(0,f.g)(),{pathname:t}=(0,l.zy)();return e.needsRevisionRetrigger&&-1===t.indexOf("/edit/")&&-1===t.indexOf("/new")&&(0,n.jsx)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:{margin:"10px 0 0 0"},children:(0,n.jsxs)("p",{children:[(0,S.__)("You have changed settings that affect the content or behavior of the cookie banner. You should ask all visitors for their consent again.")," ","•"," ",(0,n.jsx)("a",{onClick:()=>e.updateCurrentRevision({needs_retrigger:!0}),children:(0,S.__)("Request new consent")})," ","•"," ",(0,n.jsx)(_.A,{title:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("strong",{children:(0,S.__)("Are you sure you don't want to collect new consents?")}),(0,n.jsx)("br",{}),(0,n.jsx)("br",{}),(0,S._i)((0,S.__)("Consent must be obtained in accordance with {{a}}Art. 7 GDPR{{/a}}. Your visitor must be informed about all circumstances of the consent. However, you have made changes to your cookie banner, which lead to the fact that previously visitors have consented differently or to something different than is now in the cookie banner. Therefore, previously collected consents may have become partially invalid."),{a:(0,n.jsx)("a",{href:(0,S.__)("https://gdpr-text.com/read/article-7/"),rel:"noreferrer",target:"_blank"})}),(0,n.jsx)("br",{}),(0,n.jsx)("br",{}),(0,S.__)("If you have created a new service like Google Analytics (without visual content blocker), previous visitors can never consent without collecting new consents and you cannot track them, for example.")]}),placement:"bottomRight",onConfirm:()=>e.updateCurrentRevision({needs_retrigger:!1}),okText:(0,S.__)("Dismiss notice"),cancelText:(0,S.__)("Cancel"),overlayStyle:{maxWidth:450},children:(0,n.jsx)("a",{children:(0,S.__)("Dismiss notice")})})]})})})),I=(0,r.PA)((()=>{const{optionStore:e}=(0,f.g)(),{pathname:t}=(0,l.zy)(),{servicesDataProcessingInUnsafeCountriesNoticeHtml:o}=e;return o&&-1===t.indexOf("/edit/")&&-1===t.indexOf("/new")&&(0,n.jsxs)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:{margin:"10px 0 0 0"},children:[(0,n.jsx)("span",{dangerouslySetInnerHTML:{__html:o}})," ",(0,n.jsxs)("p",{children:[(0,n.jsx)("a",{href:"#/settings/consent",children:(0,S.__)("Enable consent to data processing in unsafe third countries")})," ","•"," ",(0,n.jsx)("a",{onClick:()=>e.dismissServiceDataProcessingInUnsafeCountriesNotice(),children:(0,S.__)("Dismiss notice")})]})]})}));var M=o(60971);const G=()=>{const e=(0,l.zy)();return(0,a.useEffect)((()=>{(0,M.V)(0)}),[e.pathname]),null};var N=o(98348),U=o(33464),L=o(45377);const V={display:"block",marginTop:10},H=(e,t)=>({Component:()=>(0,n.jsx)(g.SuspenseChunkTranslation,{chunkFile:o.u(`chunk-config-tab-${e}`),options:()=>(0,f.g)().optionStore,fallback:(0,n.jsx)(s.A,{spinning:!0,style:V}),children:t})}),q=()=>Promise.all([o.e(731),o.e(645),o.e(799),o.e(147),o.e(650),o.e(924),o.e(985),o.e(668)]).then(o.bind(o,63581)).then((e=>{let{DashboardCards:t}=e;return H("dashboard",(0,n.jsx)(t,{}))})),z=()=>Promise.all([o.e(731),o.e(645),o.e(799),o.e(230),o.e(386),o.e(493),o.e(221),o.e(716),o.e(171),o.e(79)]).then(o.bind(o,83753)).then((e=>{let{SettingsForm:t}=e;return H("settings",(0,n.jsx)(t,{}))})),W=()=>Promise.all([o.e(731),o.e(645),o.e(799),o.e(230),o.e(147),o.e(493),o.e(97),o.e(221),o.e(650),o.e(502),o.e(276),o.e(362)]).then(o.bind(o,73481)).then((e=>{let{ScannerList:t}=e;return H("scanner",(0,n.jsx)(t,{}))})),$=()=>Promise.all([o.e(731),o.e(645),o.e(799),o.e(230),o.e(386),o.e(147),o.e(493),o.e(97),o.e(221),o.e(650),o.e(716),o.e(171),o.e(55),o.e(134),o.e(241),o.e(108),o.e(349)]).then(o.bind(o,82706)).then((e=>{let{CookieGroupsTabLayout:t}=e;return H("cookies",(0,n.jsx)(t,{}))})),Y=()=>Promise.all([o.e(731),o.e(645),o.e(799),o.e(230),o.e(386),o.e(147),o.e(493),o.e(97),o.e(221),o.e(650),o.e(716),o.e(171),o.e(55),o.e(134),o.e(241),o.e(108),o.e(349)]).then(o.bind(o,2493)).then((e=>{let{CookiesList:t}=e;return H("cookies",(0,n.jsx)(t,{}))})),J=()=>Promise.all([o.e(731),o.e(645),o.e(799),o.e(230),o.e(386),o.e(147),o.e(493),o.e(97),o.e(221),o.e(650),o.e(716),o.e(171),o.e(55),o.e(134),o.e(241),o.e(108),o.e(349)]).then(o.bind(o,41122)).then((e=>{let{CookieTemplateCenter:t}=e;return H("cookies",(0,n.jsx)(t,{}))})),K=()=>Promise.all([o.e(731),o.e(645),o.e(799),o.e(230),o.e(386),o.e(147),o.e(493),o.e(97),o.e(221),o.e(650),o.e(716),o.e(171),o.e(55),o.e(134),o.e(241),o.e(108),o.e(349)]).then(o.bind(o,67120)).then((e=>{let{CookieEditForm:t}=e;return H("cookies",(0,n.jsx)(t,{}))})),Z=()=>Promise.all([o.e(731),o.e(645),o.e(230),o.e(386),o.e(147),o.e(493),o.e(97),o.e(221),o.e(502),o.e(716),o.e(276),o.e(241),o.e(189),o.e(763)]).then(o.bind(o,57759)).then((e=>{let{TcfLayout:t}=e;return H("tcf",(0,n.jsx)(t,{}))})),X=()=>Promise.all([o.e(731),o.e(645),o.e(230),o.e(386),o.e(147),o.e(493),o.e(97),o.e(221),o.e(502),o.e(716),o.e(276),o.e(241),o.e(189),o.e(763)]).then(o.bind(o,55117)).then((e=>{let{TcfVendorConfigurationList:t}=e;return H("tcf",(0,n.jsx)(t,{}))})),Q=()=>Promise.all([o.e(731),o.e(645),o.e(230),o.e(386),o.e(147),o.e(493),o.e(97),o.e(221),o.e(502),o.e(716),o.e(276),o.e(241),o.e(189),o.e(763)]).then(o.bind(o,70884)).then((e=>{let{TcfVendorSelector:t}=e;return H("tcf",(0,n.jsx)(t,{}))})),ee=()=>Promise.all([o.e(731),o.e(645),o.e(230),o.e(386),o.e(147),o.e(493),o.e(97),o.e(221),o.e(502),o.e(716),o.e(276),o.e(241),o.e(189),o.e(763)]).then(o.bind(o,36962)).then((e=>{let{TcfVendorConfigurationForm:t}=e;return H("tcf",(0,n.jsx)(t,{}))})),te=()=>Promise.all([o.e(731),o.e(645),o.e(799),o.e(230),o.e(386),o.e(147),o.e(493),o.e(97),o.e(650),o.e(502),o.e(924),o.e(227),o.e(173),o.e(38)]).then(o.bind(o,51781)).then((e=>{let{ConsentTabRouter:t}=e;return H("consent",(0,n.jsx)(t,{}))})),oe=()=>Promise.all([o.e(731),o.e(645),o.e(799),o.e(230),o.e(386),o.e(147),o.e(493),o.e(97),o.e(221),o.e(650),o.e(502),o.e(716),o.e(171),o.e(55),o.e(985),o.e(287),o.e(241),o.e(108),o.e(189),o.e(462)]).then(o.bind(o,22923)).then((e=>{let{BlockerList:t}=e;return H("blocker",(0,n.jsx)(t,{}))})),ne=()=>Promise.all([o.e(731),o.e(645),o.e(799),o.e(230),o.e(386),o.e(147),o.e(493),o.e(97),o.e(221),o.e(650),o.e(502),o.e(716),o.e(171),o.e(55),o.e(985),o.e(287),o.e(241),o.e(108),o.e(189),o.e(462)]).then(o.bind(o,88663)).then((e=>{let{BlockerTemplateCenter:t}=e;return H("blocker",(0,n.jsx)(t,{}))})),se=()=>Promise.all([o.e(731),o.e(645),o.e(799),o.e(230),o.e(386),o.e(147),o.e(493),o.e(97),o.e(221),o.e(650),o.e(502),o.e(716),o.e(171),o.e(55),o.e(985),o.e(287),o.e(241),o.e(108),o.e(189),o.e(462)]).then(o.bind(o,67748)).then((e=>{let{BlockerEditForm:t}=e;return H("blocker",(0,n.jsx)(t,{}))})),ie=()=>Promise.all([o.e(731),o.e(645),o.e(799),o.e(230),o.e(386),o.e(924),o.e(276),o.e(859),o.e(655)]).then(o.bind(o,23708)).then((e=>{let{ImportExportCards:t}=e;return H("import",(0,n.jsx)(t,{}))})),re=()=>Promise.all([o.e(731),o.e(799),o.e(386),o.e(508)]).then(o.bind(o,44591)).then((e=>{let{ConfigLicensing:t}=e;return H("licensing",(0,n.jsx)(t,{}))})),ae=(0,a.lazy)((()=>re().then((e=>{let{Component:t}=e;return{default:t}})))),ce=()=>{const e=(0,a.useMemo)((()=>(0,c.Ge)((0,l.Eu)((0,n.jsxs)(l.qh,{element:(0,n.jsx)(le,{}),children:[(0,n.jsx)(l.qh,{index:!0,lazy:q}),(0,n.jsx)(l.qh,{path:"settings/:tab?",lazy:z}),(0,n.jsx)(l.qh,{path:"scanner",lazy:W}),(0,n.jsxs)(l.qh,{path:"cookies",lazy:$,children:[(0,n.jsxs)(l.qh,{path:"tcf-vendors",lazy:Z,children:[(0,n.jsx)(l.qh,{index:!0,lazy:X}),(0,n.jsx)(l.qh,{path:"new",lazy:Q}),(0,n.jsx)(l.qh,{path:"edit/:vendorConfiguration",lazy:ee})]}),(0,n.jsx)(l.qh,{path:":cookieGroup?",lazy:Y}),(0,n.jsx)(l.qh,{path:":cookieGroup/new",lazy:J}),(0,n.jsx)(l.qh,{path:":cookieGroup/edit/:cookie",lazy:K})]}),(0,n.jsx)(l.qh,{path:"consent/:tab?",lazy:te}),(0,n.jsxs)(l.qh,{path:"blocker",element:(0,n.jsx)(v,{}),children:[(0,n.jsx)(l.qh,{index:!0,lazy:oe}),(0,n.jsx)(l.qh,{path:"new",lazy:ne}),(0,n.jsx)(l.qh,{path:"edit/:blocker",lazy:se})]}),(0,n.jsx)(l.qh,{path:"import",lazy:ie}),(0,n.jsx)(l.qh,{path:"licensing",lazy:re})]})))),[]);return(0,n.jsx)(c.pg,{router:e})},le=(0,r.PA)((()=>{const{percent:e,remaining:t,currentJob:o}=(0,N.X)(),{optionStore:r,checklistStore:g}=(0,f.g)(),{others:{isPro:y,isLicensed:b,proUrl:v,hints:m,showLicenseFormImmediate:k,colorScheme:[,,x],isDemoEnv:j,capabilities:{activate_plugins:w}},fomoCoupon:_,checkSavingConsentViaRestApiEndpointWorkingHtml:O,templateUpdateNoticeHtml:R,templateSuccessorsNoticeHtml:E,googleConsentModeNoticesHtml:M,servicesWithEmptyPrivacyPolicyNoticeHtml:H}=r,[q,z]=(0,a.useState)(!1);(0,a.useLayoutEffect)((()=>{r.fetchSettings().then((()=>{z(!0)}));const e=()=>{g.fetchChecklist()},t=`${h.JOB_DONE_EVENT_PREFIX}${U.oP}`;return document.addEventListener(t,e),()=>{document.removeEventListener(t,e)}}),[]);const[W,$]=(0,d.v)({__:S.__,_i:S._i,_n:S._n,_x:S._x}),[Y,J]=(0,p.Y)({fomoCoupon:_,isPro:y,isLicensed:b,isDemoEnv:j,proUrl:v,hint:null==m?void 0:m.proDialog},{},{inherit:["isLicensed","fomoCoupon"]}),K=(0,a.useCallback)((e=>{let{isActive:t}=e;return t?"nav-tab nav-tab-active":"nav-tab"}),[]);return(0,n.jsx)(W,{value:$,children:(0,n.jsx)(Y,{value:J,children:k?(0,n.jsx)(a.Suspense,{fallback:(0,n.jsx)(s.A,{spinning:!0,style:V}),children:(0,n.jsx)(ae,{})}):(0,n.jsxs)(s.A,{spinning:!q,children:[(0,n.jsx)(P,{}),(0,n.jsxs)("nav",{className:"nav-tab-wrapper wp-clearfix",children:[(0,n.jsx)(c.k2,{to:"/",className:K,end:!0,children:(0,S.__)("Dashboard")}),(0,n.jsx)(c.k2,{to:"/settings",className:K,children:(0,S.__)("Settings")}),(0,n.jsx)(c.k2,{to:"/scanner",className:K,children:t>0&&e>0&&(null==o?void 0:o.group_position)>0?(0,n.jsx)(i.A,{count:`${e} %`,style:{top:-11,fontSize:10,height:16,lineHeight:"16px",background:x},children:(0,S.__)("Scanner")}):(0,S.__)("Scanner")}),(0,n.jsx)(c.k2,{to:"/cookies",className:K,children:(0,S.__)("Services (Cookies)")}),(0,n.jsx)(c.k2,{to:"/blocker",className:K,children:(0,S.__)("Content Blocker")}),(0,n.jsx)(c.k2,{to:"/consent",className:K,children:(0,n.jsx)(L.m,{children:(0,S.__)("Consent")})}),(0,n.jsx)(c.k2,{to:"/import",className:K,children:(0,S.__)("Import / Export")}),"try.devowl.io"!==window.location.host&&w&&(0,n.jsx)(c.k2,{to:"/licensing",className:K,children:(0,S.__)("Licensing")}),(0,n.jsx)("a",{href:(0,S.__)("https://devowl.io/support/"),className:"nav-tab",target:"_blank",rel:"noreferrer",children:(0,S.__)("Support")})]}),(0,n.jsx)(D,{}),(0,n.jsx)(I,{}),(0,n.jsx)(u.q,{notices:[{severity:"error",message:O},{severity:"warning",message:H},{severity:"warning",message:R},{severity:"warning",message:E},...M.map((e=>({severity:"warning",message:e})))]}),(0,n.jsx)(T,{}),(0,n.jsx)(A,{}),(0,n.jsx)(B,{}),(0,n.jsx)(F,{}),(0,n.jsx)(G,{}),(0,n.jsx)(a.Suspense,{fallback:(0,n.jsx)(s.A,{spinning:!0,style:V}),children:q&&(0,n.jsx)(l.sv,{})}),(0,n.jsx)(C,{})]})})})}))},12559:(e,t,o)=>{o.d(t,{K:()=>c});var n=o(3713),s=o(19327),i=o(65666),r=o(19117),a=o(71951);const c=e=>{let{children:t,configProvider:o={},app:c={}}=e;return(0,n.jsx)(i.Ay,{prefixCls:"rcb-antd",iconPrefixCls:"rcb-antd-anticon",theme:{token:{colorPrimary:"#2271b1",borderRadius:3}},...o,children:(0,n.jsx)(s.Z_3,{value:{prefixCls:"rcb-antd-anticon"},children:(0,n.jsx)(r.A,{message:{top:50},...c,children:(0,n.jsx)(a.y.StoreProvider,{children:t})})})})}},53603:(e,t,o)=>{o.d(t,{m:()=>y});var n=o(3713),s=o(35207),i=o(33631),r=o(6099),a=o(92453),c=o(19991),l=o(38123),d=o.n(l),p=o(32041),u=o(67993),h=o(71951),g=o(30617);function y(e){for(var t=arguments.length,o=new Array(t>1?t-1:0),l=1;l<t;l++)o[l-1]=arguments[l];const{optionStore:{isTcf:y,consentsDeletedAt:b,consentDuration:v}}=(0,h.g)();switch(e){case"scanner":{const{openDialog:e}=(0,p.g)();return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{className:"description",children:(0,g.__)("The scanner finds services that you use on your website that might set/read cookies or process personal data. This is e.g. Google Analytics, YouTube or Elementor. If there is no template for a service, you will see from which external URLs content, scripts etc. are embedded. This allows you to set up your cookie banner quickly and easily.")}),(0,n.jsx)("p",{className:"description",children:(0,g._i)((0,g.__)("We explicitly do not find cookies because that would not work reliably. {{a}}We explained why in our knowledge base.{{/a}}"),{a:(0,n.jsx)("a",{rel:"noreferrer",href:(0,g.__)("https://devowl.io/knowledge-base/real-cookie-banner-cookie-scanner-finds-cookies-automatically/"),target:"_blank"})})}),(0,n.jsxs)(r.A,{style:{margin:"10px 0"},children:[(0,n.jsx)(a.A,{span:11,children:(0,n.jsxs)("div",{style:{paddingRight:10},children:[(0,n.jsx)(c.A,{children:(0,g.__)("What the scanner finds ...")}),[(0,g.__)("External services (with and without template)"),(0,g.__)("WordPress plugins with templates that require consent"),(0,g.__)("Automatic check of all subpages of your website")].map(((e,t)=>(0,n.jsxs)("div",{style:{marginBottom:10},children:[(0,n.jsx)(s.A,{twoToneColor:"#52c41a"}),"  ",e]},t)))]})}),(0,n.jsx)(a.A,{span:2,style:{textAlign:"center"},children:(0,n.jsx)(c.A,{type:"vertical",style:{height:"100%"}})}),(0,n.jsx)(a.A,{span:11,children:(0,n.jsxs)("div",{children:[(0,n.jsx)(c.A,{children:(0,g.__)("... and what it does not")}),[(0,g.__)("Cookies from unknown WordPress plugins"),(0,g.__)("Services embedded after the page load via JavaScript"),(0,g.__)("Complete coverage of your individual use case")].map(((e,t)=>(0,n.jsxs)("div",{style:{marginBottom:10},children:[(0,n.jsx)(i.A,{twoToneColor:"#eb2f96"}),"  ",e]},t)))]})})]}),(0,n.jsx)("p",{className:"description",children:(0,g._i)((0,g.__)("Just by using the scanner, you will not set up your cookie banner one hundred percent correctly. If it is too complex or time-consuming for you to set up the cookie banner yourself, just let one of our {{a}}cookie experts{{/a}} set it up for you!"),{a:(0,n.jsx)("a",{onClick:e})})})]})}case"cookie":return(0,n.jsxs)(n.Fragment,{children:[(0,g.__)("What are services? Services can be external applications such as Google Analytics or WordPress plugins or themes that process personal data (e.g. IP address) and/or set cookies. Cookies (and similar technologies) are small text files that are stored on the device of visitors to your website. You can store information about the visitor in cookies, such as the website's language, or unique advertising IDs to display personalized advertising. You, as the site owner, must ensure that cookies are only placed on your visitors' devices and personal data are only processed if they have given their explicit consent. Unless you have a legitimate interest in the legal sense to do so even without consent. You can define here all the services you use and their cookies with their legal and technical information.")," ",(0,n.jsx)(u.Y,{url:(0,g.__)("https://devowl.io/cookies/definition-cookies/")})]});case"blocker":return(0,g.__)("What is a content blocker? Imagine that a user of your website does not accept all services. At the same time, you have integrated e.g. a YouTube video that sets cookies that the visitor has not agreed to. According to the ePrivacy Directive, this is prohibited. Content blockers automatically replace iframes, script and link tags like YouTube videos for such users and offer them to watch the video as soon as they agree to load it.");case"list-of-consents":return(0,g._i)((0,g.__)("Consents are automatically documented in order to be able to prove compliance with the legal requirements according to {{a}}Art. 5 GDPR{{/a}} and, in case of dispute, to prove how the consent was obtained."),{a:(0,n.jsx)("a",{href:(0,g.__)("https://gdpr-text.com/read/article-5/"),target:"_blank",rel:"noreferrer"})});case"consents-deleted":return(0,n.jsx)(n.Fragment,{children:b?(0,g.__)("Consents before %s has been automatically deleted according to the settings you have made.",d()(b).subtract(v,"months").toDate().toLocaleString(document.documentElement.lang)):null});case"shortcodes":return(0,n.jsxs)(n.Fragment,{children:[(0,g._i)((0,g.__)("Your website visitors must be able to view their consent history, change their consent, or withdraw their consent at any time. This must be as easy as giving consent. Therefore, the legal links must be included on every subpage of the website (e.g. in the footer or as a floating icon called sticky legal links widget)."),{strong:(0,n.jsx)("strong",{})}),(0,n.jsx)("br",{}),(0,n.jsx)("br",{}),(0,g._i)((0,g.__)("Hiding these options, e.g. in the privacy policy, is in the opinion of multiple data protection authorities in the EU a violation of the GDPR."),{a:(0,n.jsx)("a",{href:(0,g.__)("https://devowl.io/ger-dsk-orientierungshilfe-cookie-banner"),target:"_blank",rel:"noreferrer"})}),y?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("br",{}),(0,n.jsx)("br",{}),(0,g.__)("To meet the requirements of the TCF standard, the shortcodes should be placed near the link to the privacy policy.")]}):null]});case"tcf-vendor":return(0,g._i)((0,g.__)("What is a TCF vendor? According to the IAB Europe Transparency & Consent Framework (TCF), any service (e.g. Google for Google Ads) that wants to use consents according to the TCF standard must register as a vendor in the {{a}}Global Vendor List (GVL){{/a}}. All TCF vendors specify for which purposes they need consent to process data and set cookies and which features they can offer with these consents. They also provide a link to their privacy policy for further information. You, as a website operator, must obtain consent in your cookie banner for all vendors you work with. You can limit the requested purposes of vendors to keep consents as privacy-friendly as possible."),{a:(0,n.jsx)("a",{href:(0,g.__)("https://iabeurope.eu/vendor-list-tcf/"),target:"_blank",rel:"noreferrer"})});case"tcf-integration":return(0,n.jsx)(n.Fragment,{children:(0,g.__)("You are trying to obtain consent for the advertising network %s. In order to be able to display advertising, you must obtain consent in accordance with the TCF standard. Therefore, please first activate TCF compatibility for your cookie banner so that you can then make all the necessary configurations!",...o)});case"import":return(0,n.jsxs)(n.Fragment,{children:[(0,g.__)("You can export and import all or only some of the settings you made in Real Cookie Banner. If you have several websites, you can save a lot of time by transferring the settings comfortably."),(0,n.jsx)("br",{}),(0,n.jsx)("br",{}),(0,g.__)("Also, you can export documented consents to save them in a local backup.")]});default:return""}}},98348:(e,t,o)=>{o.d(t,{X:()=>a});var n=o(41594),s=o(14383),i=o(71951),r=o(33464);function a(e,t,o){void 0===t&&(t=!1);const{scannerStore:a}=(0,i.g)();return(0,s.useProgress)({type:r.Mv,fetchStatusInterval:e,fetchAdditionalData:t,onAdditionalData:(0,n.useCallback)((e=>{let{"rcb-scan-list":t}=e;const{templates:o,externalUrls:n}=t;a.resultTemplatesFromResponse(o),a.resultExternalUrlsFromResponse(n)}),[a]),onCancel:o})}},62884:(e,t,o)=>{o.d(t,{b:()=>l});var n=o(59670),s=o(44497),i=o(68789),r=o(49949),a=o(42090),c=o(46504);class l extends i.AbstractPostCollection{get sortedBannerLinks(){const e=Array.from(this.entries.values());return e.sort(((e,t)=>e.data.menu_order<t.data.menu_order?-1:e.data.menu_order>t.data.menu_order||e.key<t.key?1:e.key>t.key?-1:0)),e}constructor(e){super(),this.orderCookies=(0,s.flow)((function*(e){this.busy=!0;try{yield(0,a.E)({location:c.i,request:{ids:e}});let t=0;for(const o of e)this.entries.get(o).setOrder(t),t++}catch(e){throw console.log(e),e}finally{this.busy=!1}})),this.store=e}instance(e){return new r.A(this).fromResponse(e)}}(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"sortedBannerLinks",null),l=(0,n.Cg)([i.ClientCollection.annotate({path:"/rcb-banner-link",singlePath:"/rcb-banner-link/:id",namespace:"wp/v2",methods:[i.RouteHttpVerb.GET],request:a.E}),(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof OptionStore?Object:OptionStore])],l)},49949:(e,t,o)=>{o.d(t,{A:()=>a});var n=o(59670),s=o(44497),i=o(68789),r=o(42090);class a extends i.AbstractPost{get rootStore(){return this.collection.store.rootStore}constructor(e,t={}){super(e,t)}setOrder(e){this.data.menu_order=e}setLabel(e){this.data.title.raw=e}setStatus(e){this.data.status=e}setMeta(e){this.data.meta=e}transformDataForPatch(){const e=super.transformDataForPatch();return{title:e.title,content:"",status:e.status,meta:e.meta,menu_order:e.menu_order,slug:e.title}}}(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],a.prototype,"rootStore",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Number]),(0,n.Sn)("design:returntype",void 0)],a.prototype,"setOrder",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[String]),(0,n.Sn)("design:returntype",void 0)],a.prototype,"setLabel",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof PostStatus?Object:PostStatus]),(0,n.Sn)("design:returntype",void 0)],a.prototype,"setStatus",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object]),(0,n.Sn)("design:returntype",void 0)],a.prototype,"setMeta",null),a=(0,n.Cg)([i.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:r.E,create:{path:"/rcb-banner-link"},patch:{path:"/rcb-banner-link/:id"},delete:{path:"/rcb-banner-link/:id"}}),(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object,"undefined"==typeof Partial?Object:Partial])],a)},14909:(e,t,o)=>{o.d(t,{M:()=>r});var n=o(59670),s=o(44497),i=o(59627);class r{constructor(e,t){(0,s.runInAction)((()=>(0,s.set)(this,e))),this.store=t}static getIframeStore(){try{return document.querySelector("#customize-preview > iframe").contentWindow.realCookieBanner_customize_banner.RootStore.get}catch(e){return}}applyInUi(){if(!this.store.rootStore.optionStore.others.isPro&&this.needsPro)return!1;const e=(0,i.getSidebarCustomize)();return this.previewInUi(),setTimeout((()=>{this.store.presetDefaults.forEach(((t,o)=>{e(o).set(void 0===this.settings[o]?t:this.settings[o])}))}),100),!0}previewInUi(){const{presetDefaults:e}=this.store,{settings:t}=this.store.rootStore.optionStore.others.customizeIdsBanner,o=[];this.resetPreviewInUiSettings={};for(const n of Object.keys(t)){const s=t[n];for(const t of Object.keys(s)){const r=s[t];if(!e.has(r))continue;const a=(0,i.getSanitizedControlValue)(r,e.get(r));if(null!==a){this.resetPreviewInUiSettings[r]=[n,t,a];const s=Object.prototype.hasOwnProperty.call(this.settings,r)?this.settings[r]:e.get(r);o.push([n,t,s])}}}r.getIframeStore().customizeBannerStore.setBannerFromPreset(o)}resetPreviewInUi(){this.resetPreviewInUiSettings&&(r.getIframeStore().customizeBannerStore.setBannerFromPreset(Object.values(this.resetPreviewInUiSettings)),this.resetPreviewInUiSettings={})}}(0,n.Cg)([s.observable,(0,n.Sn)("design:type",String)],r.prototype,"id",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",String)],r.prototype,"name",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Boolean)],r.prototype,"needsPro",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",String)],r.prototype,"description",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type","undefined"==typeof Array?Object:Array)],r.prototype,"tags",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],r.prototype,"settings",void 0)},26934:(e,t,o)=>{o.d(t,{h:()=>c});var n=o(59670),s=o(44497),i=o(68789),r=o(44227),a=o(42090);class c extends i.AbstractPostCollection{constructor(e){super(),this.store=e}get sortedBlockers(){const e=Array.from(this.entries.values());return e.sort(((e,t)=>e.data.menu_order<t.data.menu_order?-1:e.data.menu_order>t.data.menu_order||e.key<t.key?1:e.key>t.key?-1:0)),e}instance(e){return new r.g(this).fromResponse(e)}}(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"sortedBlockers",null),c=(0,n.Cg)([i.ClientCollection.annotate({path:"/rcb-blocker",singlePath:"/rcb-blocker/:id",namespace:"wp/v2",methods:[i.RouteHttpVerb.GET],request:a.E}),(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof CookieStore?Object:CookieStore])],c)},44227:(e,t,o)=>{o.d(t,{g:()=>c});var n=o(59670),s=o(44497),i=o(68789),r=o(71951),a=o(42090);class c extends i.AbstractPost{get templateModel(){var e;return r.y.get.cookieStore.templatesBlocker.get(null==(e=this.data.meta)?void 0:e.presetId)}get rules(){var e;return null==(e=this.data)?void 0:e.meta.rules.split("\n")}get tcfVendors(){var e;return(null==(e=this.data)?void 0:e.meta.tcfVendors)?this.data.meta.tcfVendors.split(",").filter(Boolean).map(Number):[]}get tcfPurposes(){var e;return(null==(e=this.data)?void 0:e.meta.tcfPurposes)?this.data.meta.tcfPurposes.split(",").filter(Boolean).map(Number):[]}get services(){var e;return null==(e=this.data)?void 0:e.meta.services.split(",").filter(Boolean).map(Number)}get rootStore(){return this.collection.store.rootStore}get isUpdateAvailable(){for(const{post_id:e}of this.rootStore.optionStore.templateNeedsUpdate)if(e===this.data.id)return!0;return!1}constructor(e,t={}){super(e,t),(0,s.reaction)((()=>{var e;return null==(e=this.data)?void 0:e.usedTemplate}),(e=>(0,s.runInAction)((()=>{e&&r.y.get.cookieStore.addBlockerTemplates([e])}))),{fireImmediately:!0})}setName(e){this.data.title.raw=e}setStatus(e){this.data.status=e}setDescription(e){this.data.content.raw=e}setMeta(e){this.data.meta=e}transformDataForPatch(){const e=super.transformDataForPatch();return{title:e.title,content:e.content,status:e.status,meta:e.meta,slug:e.title}}afterPatch(){this.collection.store.blockers.store.rootStore.optionStore.fetchCurrentRevision()}afterDelete(){this.collection.store.blockers.store.rootStore.optionStore.fetchCurrentRevision()}afterPersist(){this.collection.store.blockers.store.rootStore.optionStore.fetchCurrentRevision()}}(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"templateModel",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"rules",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"tcfVendors",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"tcfPurposes",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"services",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"rootStore",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"isUpdateAvailable",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[String]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setName",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof PostStatus?Object:PostStatus]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setStatus",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[String]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setDescription",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setMeta",null),c=(0,n.Cg)([i.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:a.E,create:{path:"/rcb-blocker"},patch:{path:"/rcb-blocker/:id"},delete:{path:"/rcb-blocker/:id"}}),(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object,"undefined"==typeof Partial?Object:Partial])],c)},87608:(e,t,o)=>{o.d(t,{x:()=>a});var n=o(59670),s=o(44497),i=o(42090),r=o(81472);class a{constructor(e,t){this.busy=!1,this.fetchUse=(0,s.flow)((function*(){try{this.busy=!0;const e=yield(0,i.E)({location:r.K,params:{identifier:this.data.identifier}});return this.use=e,this.store.addServiceTemplates(e.consumerData.serviceTemplates),this.use}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,s.runInAction)((()=>{this.data=e})),this.store=t}}(0,n.Cg)([s.observable],a.prototype,"busy",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"data",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type","undefined"==typeof ResponseRouteTemplatesBlockerUseGet?Object:ResponseRouteTemplatesBlockerUseGet)],a.prototype,"use",void 0)},55294:(e,t,o)=>{o.d(t,{N:()=>a});var n=o(59670),s=o(44497),i=o(42090),r=o(44175);class a{get revision(){return this.store.revisions.get(this.revision_hash)}get revision_independent(){return this.store.revisionsIndependent.get(this.revision_independent_hash)}get custom_bypass_readable(){const{custom_bypass:e}=this;return e?e.charAt(0).toUpperCase()+e.slice(1):""}get export(){return JSON.parse(JSON.stringify({...this.plain,revision:this.revision.data,revision_independent:this.revision_independent.data}))}constructor(e,t){this.busy=!1,this.delete=(0,s.flow)((function*(){this.busy=!0;try{yield(0,i.E)({location:r.L,params:{id:this.id}}),yield this.store.fetchAll()}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,s.runInAction)((()=>(0,s.set)(this,e))),this.store=t,this.plain=e}fetchRevisions(){return Promise.all([this.store.fetchRevision({hash:this.revision_hash}),this.store.fetchRevisionIndependent({hash:this.revision_independent_hash})])}}(0,n.Cg)([s.observable],a.prototype,"busy",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Number)],a.prototype,"id",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"plugin_version",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"design_version",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"ipv4",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"ipv6",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"ipv4_hash",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"ipv6_hash",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"uuid",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"previous_decision",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"decision",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"decision_labels",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"created",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"blocker",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"blocker_thumbnail",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"dnt",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"custom_bypass",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"user_country",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"revision_hash",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"revision_independent_hash",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"button_clicked",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"context",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"viewport_width",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"viewport_height",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"referer",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"url_imprint",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"url_privacy_policy",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"forwarded",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"forwarded_blocker",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"previous_tcf_string",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"tcf_string",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"previous_gcm_consent",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"gcm_consent",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"recorder",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"ui_view",void 0),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],a.prototype,"revision",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],a.prototype,"revision_independent",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],a.prototype,"custom_bypass_readable",null)},47656:(e,t,o)=>{o.d(t,{L:()=>l});var n=o(59670),s=o(44497),i=o(68789),r=o(27449),a=o(42090),c=o(61988);class l extends i.AbstractPostCollection{get sortedCookies(){const e=Array.from(this.entries.values());return e.sort(((e,t)=>e.data.menu_order<t.data.menu_order?-1:e.data.menu_order>t.data.menu_order||e.key<t.key?1:e.key>t.key?-1:0)),e}constructor(e){super(),this.orderCookies=(0,s.flow)((function*(e){this.busy=!0;try{yield(0,a.E)({location:c.a,request:{ids:e}});let t=0;for(const o of e)this.entries.get(o).setOrder(t),t++}catch(e){throw console.log(e),e}finally{this.busy=!1}})),this.store=e}instance(e){return new r.G(this).fromResponse(e)}}(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"sortedCookies",null),l=(0,n.Cg)([i.ClientCollection.annotate({path:"/rcb-cookie",singlePath:"/rcb-cookie/:id",namespace:"wp/v2",methods:[i.RouteHttpVerb.GET],request:a.E}),(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof CookieGroupModel?Object:CookieGroupModel])],l)},80963:(e,t,o)=>{o.d(t,{e:()=>l});var n=o(59670),s=o(44497),i=o(68789),r=o(65216),a=o(42090),c=o(75782);class l extends i.AbstractCategoryCollection{get sortedGroups(){const e=Array.from(this.entries.values());return e.sort(((e,t)=>e.data.meta.order<t.data.meta.order?-1:e.data.meta.order>t.data.meta.order?1:0)),e}constructor(e){super(),this.orderCookieGroups=(0,s.flow)((function*(e){this.busy=!0;try{yield(0,a.E)({location:c.q,request:{ids:e}});let t=0;for(const o of e)this.entries.get(o).setOrder(t),t++}catch(e){throw console.log(e),e}finally{this.busy=!1}})),this.store=e}instance(e){return new r.r(this).fromResponse(e)}}(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"sortedGroups",null),l=(0,n.Cg)([i.ClientCollection.annotate({path:"/rcb-cookie-group",singlePath:"/rcb-cookie-group/:id",namespace:"wp/v2",methods:[i.RouteHttpVerb.GET],request:a.E}),(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof CookieStore?Object:CookieStore])],l)},65216:(e,t,o)=>{o.d(t,{r:()=>c});var n=o(59670),s=o(44497),i=o(68789),r=o(47656),a=o(42090);class c extends i.AbstractCategory{get cookiesCount(){return this.fetchedAllCookies?this.cookies.entries.size:this.data.count}constructor(e,t={}){super(e,t),this.fetchedAllCookies=!1,this.fetchCookies=(0,s.flow)((function*(){yield this.cookies.get({request:{status:["draft","publish","private"]},params:{per_page:100,"rcb-cookie-group":this.key,context:"edit"}}),this.fetchedAllCookies=!0})),(0,s.runInAction)((()=>{this.cookies=new r.L(this)}))}setName(e){this.data.name=e}setDescription(e){this.data.description=e}setOrder(e){this.data.meta.order=e}afterDelete(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterPatch(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterPersist(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}}(0,n.Cg)([s.observable,(0,n.Sn)("design:type",void 0===r.L?Object:r.L)],c.prototype,"cookies",void 0),(0,n.Cg)([s.observable],c.prototype,"fetchedAllCookies",void 0),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"cookiesCount",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[String]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setName",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[String]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setDescription",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Number]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setOrder",null),c=(0,n.Cg)([i.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:a.E,create:{path:"/rcb-cookie-group"},patch:{path:"/rcb-cookie-group/:id"},delete:{path:"/rcb-cookie-group/:id"}}),(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof CookieGroupCollection?Object:CookieGroupCollection,"undefined"==typeof Partial?Object:Partial])],c)},27449:(e,t,o)=>{o.d(t,{G:()=>c});var n=o(59670),s=o(44497),i=o(68789),r=o(71951),a=o(42090);class c extends i.AbstractPost{get templateModel(){var e;return r.y.get.cookieStore.templatesServices.get(null==(e=this.data.meta)?void 0:e.presetId)}get rootStore(){return this.collection.store.collection.store.rootStore}get technicalDefinitions(){return JSON.parse(this.data.meta.technicalDefinitions||"[]")}get googleConsentModeConsentTypes(){return JSON.parse(this.data.meta.googleConsentModeConsentTypes||"[]")}get dataProcessingInCountries(){return JSON.parse(this.data.meta.dataProcessingInCountries||"[]")}get dataProcessingInCountriesSpecialTreatments(){return JSON.parse(this.data.meta.dataProcessingInCountriesSpecialTreatments||"[]")}get isUpdateAvailable(){for(const{post_id:e}of this.rootStore.optionStore.templateNeedsUpdate)if(e===this.data.id)return!0;return!1}get codeDynamics(){return JSON.parse(this.data.meta.codeDynamics||"{}")}constructor(e,t={}){super(e,t),(0,s.reaction)((()=>{var e;return null==(e=this.data)?void 0:e.usedTemplate}),(e=>(0,s.runInAction)((()=>{e&&r.y.get.cookieStore.addServiceTemplates([e])}))),{fireImmediately:!0})}afterPatch(){const e=this.collection.store.collection,[t]=this.data["rcb-cookie-group"];e.entries.forEach((e=>{t!==e.key?e.cookies.entries.delete(this.key):e.cookies.entries.set(this.key,this)})),this.rootStore.optionStore.fetchCurrentRevision(),this.rootStore.cookieStore.unassignedCookies.delete(this.key)}setOrder(e){this.data.menu_order=e}setName(e){this.data.title.raw=e}setStatus(e){this.data.status=e}setPurpose(e){this.data.content.raw=e}setGroup(e){this.data["rcb-cookie-group"]=[e]}setMeta(e){this.data.meta=e}transformDataForPersist(){return{...super.transformDataForPersist(),"rcb-cookie-group":[this.collection.store.key]}}transformDataForPatch(){const e=super.transformDataForPatch();return{title:e.title,content:e.content,status:e.status,meta:e.meta,menu_order:e.menu_order,"rcb-cookie-group":this.data["rcb-cookie-group"],slug:e.title}}afterDelete(){this.collection.store.cookies.store.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterPersist(){const{optionStore:e,checklistStore:t}=this.collection.store.cookies.store.collection.store.rootStore;e.fetchCurrentRevision(),t.probablyFetchByChangedItem("add-cookie")}}(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"templateModel",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"rootStore",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",Array)],c.prototype,"technicalDefinitions",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",Array)],c.prototype,"googleConsentModeConsentTypes",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",Object)],c.prototype,"dataProcessingInCountries",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",Object)],c.prototype,"dataProcessingInCountriesSpecialTreatments",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"isUpdateAvailable",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype","undefined"==typeof Record?Object:Record)],c.prototype,"codeDynamics",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"afterPatch",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Number]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setOrder",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[String]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setName",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof PostStatus?Object:PostStatus]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setStatus",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[String]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setPurpose",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Number]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setGroup",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"setMeta",null),c=(0,n.Cg)([i.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:a.E,create:{path:"/rcb-cookie"},patch:{path:"/rcb-cookie/:id"},delete:{path:"/rcb-cookie/:id"}}),(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object,"undefined"==typeof Partial?Object:Partial])],c)},56953:(e,t,o)=>{o.d(t,{A:()=>n});class n{constructor(e,t){this.data=e,this.store=t}}},947:(e,t,o)=>{o.d(t,{Q:()=>n});class n{constructor(e,t){this.data=e,this.store=t}}},81193:(e,t,o)=>{o.d(t,{e:()=>c});var n=o(59670),s=o(44497),i=o(30617),r=o(42090),a=o(88291);class c{get identifier(){return this.data.host}get inactive(){return"full"===this.blockedStatus||this.data.ignored}get blockedStatus(){const{foundCount:e,blockedCount:t}=this.data;return 0===t?"none":e===t?"full":"partial"}get blockedStatusText(){switch(this.blockedStatus){case"full":return(0,i.__)("Fully blocked");case"partial":return(0,i.__)("Partially blocked");default:return(0,i.__)("Not blocked")}}constructor(e,t){this.busy=!1,this.ignore=(0,s.flow)((function*(e){this.busy=!0;try{yield(0,r.E)({location:a.h,request:{type:"host",value:this.data.host,ignored:e}}),this.data.ignored=e}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,s.runInAction)((()=>{this.data=e})),this.store=t}}(0,n.Cg)([s.observable],c.prototype,"busy",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],c.prototype,"data",void 0),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"identifier",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"inactive",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"blockedStatus",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"blockedStatusText",null)},21169:(e,t,o)=>{o.d(t,{W:()=>r});var n=o(59670),s=o(44497),i=o(94314);class r{get markup(){return this.store.resultMarkup.get(this.data.id)}get blockedUrlTruncate(){return(0,i.k)(this.data.blockedUrl,50,"[...]")}get sourceUrlTruncate(){return(0,i.k)(this.data.sourceUrl,50,"[...]")}constructor(e,t){this.busy=!1,this.fetchMarkup=(0,s.flow)((function*(){yield this.store.fetchMarkup(this.data.id)})),(0,s.runInAction)((()=>{this.data=e})),this.store=t}}(0,n.Cg)([s.observable],r.prototype,"busy",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],r.prototype,"data",void 0),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],r.prototype,"markup",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],r.prototype,"blockedUrlTruncate",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],r.prototype,"sourceUrlTruncate",null)},56975:(e,t,o)=>{o.d(t,{C:()=>c});var n=o(59670),s=o(44497),i=o(14675),r=o(42090),a=o(88291);class c{get identifier(){return this.data.identifier}get type(){return this.templateModel instanceof i.s?"service":"blocker"}get inactive(){return this.data.consumerData.isCreated||this.data.consumerData.isIgnored}constructor(e,t){this.busy=!1,this.ignore=(0,s.flow)((function*(e){this.busy=!0;try{yield(0,r.E)({location:a.h,request:{type:"template",value:this.identifier,ignored:e}}),this.data.consumerData.isIgnored=e}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,s.runInAction)((()=>{this.data=e})),this.store=t;const{cookieStore:o}=t.rootStore;Object.hasOwn(e,"rules")?(o.addBlockerTemplates([e]),this.templateModel=o.templatesBlocker.get(e.identifier)):(o.addServiceTemplates([e]),this.templateModel=o.templatesServices.get(e.identifier))}}(0,n.Cg)([s.observable],c.prototype,"busy",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],c.prototype,"data",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],c.prototype,"templateModel",void 0),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"identifier",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"type",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"inactive",null)},14675:(e,t,o)=>{o.d(t,{s:()=>a});var n=o(59670),s=o(44497),i=o(42090),r=o(64841);class a{constructor(e,t){this.busy=!1,this.fetchUse=(0,s.flow)((function*(){try{this.busy=!0;const e=yield(0,i.E)({location:r.l,params:{identifier:this.data.identifier}});return this.use=e,this.use}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,s.runInAction)((()=>{this.data=e})),this.store=t}}(0,n.Cg)([s.observable],a.prototype,"busy",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],a.prototype,"data",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type","undefined"==typeof ResponseRouteTemplatesServiceUseGet?Object:ResponseRouteTemplatesServiceUseGet)],a.prototype,"use",void 0)},49055:(e,t,o)=>{o.d(t,{e:()=>i});var n=o(59670),s=o(44497);class i{constructor(e,t){(0,s.runInAction)((()=>{this.data=e})),this.store=t}}(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],i.prototype,"data",void 0)},3153:(e,t,o)=>{o.d(t,{u:()=>i});var n=o(59670),s=o(44497);class i{constructor(e,t,o){(0,s.runInAction)((()=>{this.special=t,this.data=e})),this.store=o}}(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],i.prototype,"data",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Boolean)],i.prototype,"special",void 0)},67705:(e,t,o)=>{o.d(t,{M:()=>i});var n=o(59670),s=o(44497);class i{constructor(e,t,o){(0,s.runInAction)((()=>{this.special=t,this.data=e})),this.store=o}}(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],i.prototype,"data",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Boolean)],i.prototype,"special",void 0)},80945:(e,t,o)=>{o.d(t,{K:()=>i});var n=o(59670),s=o(44497);class i{get vendorConfiguration(){for(const t of this.store.vendorConfigurations.entries.values()){var e;if((null==(e=t.vendorModel)?void 0:e.data.id)===this.data.id)return t}}get restrictivePurposes(){const e={normal:{}};for(const t of[...this.legIntPurposes,...this.purposes])e.normal[t.data.id.toString()]={enabled:!0,legInt:this.legIntPurposes.indexOf(t)>-1&&!t.special?"yes":"no"};return e}get purposes(){var e;return null==(e=this.data)?void 0:e.purposes.map((e=>this.store.purposes.get(`${e}`)))}get legIntPurposes(){var e;return null==(e=this.data)?void 0:e.legIntPurposes.map((e=>this.store.purposes.get(`${e}`)))}get specialPurposes(){var e;return null==(e=this.data)?void 0:e.specialPurposes.map((e=>this.store.specialPurposes.get(`${e}`)))}get features(){var e;return null==(e=this.data)?void 0:e.features.map((e=>this.store.features.get(`${e}`)))}get specialFeatures(){var e;return null==(e=this.data)?void 0:e.specialFeatures.map((e=>this.store.specialFeatures.get(`${e}`)))}get dataCategories(){var e;return null==(e=this.data)?void 0:e.dataDeclaration.map((e=>this.store.dataCategories.get(`${e}`)))}constructor(e,t){(0,s.runInAction)((()=>{this.data=e})),this.store=t}}(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],i.prototype,"data",void 0),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],i.prototype,"vendorConfiguration",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],i.prototype,"restrictivePurposes",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],i.prototype,"purposes",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],i.prototype,"legIntPurposes",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],i.prototype,"specialPurposes",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],i.prototype,"features",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],i.prototype,"specialFeatures",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],i.prototype,"dataCategories",null)},2183:(e,t,o)=>{o.d(t,{u:()=>a});var n=o(59670),s=o(68789),i=o(8140),r=o(42090);class a extends s.AbstractPostCollection{constructor(e){super(),this.store=e}instance(e){return new i.p(this).fromResponse(e)}}a=(0,n.Cg)([s.ClientCollection.annotate({path:"/rcb-tcf-vendor-conf",singlePath:"/rcb-tcf-vendor-conf/:id",namespace:"wp/v2",methods:[s.RouteHttpVerb.GET],request:r.E}),(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof TcfStore?Object:TcfStore])],a)},8140:(e,t,o)=>{o.d(t,{p:()=>d});var n=o(59670),s=o(41669),i=o.n(s),r=o(44497),a=o(68789),c=o(80945),l=o(42090);class d extends a.AbstractPost{get restrictivePurposes(){var e;const t=JSON.parse(this.data.meta.restrictivePurposes);return i().extend(!0,{},(null==(e=this.vendorModel)?void 0:e.restrictivePurposes)||{},t)}get dataProcessingInCountries(){return JSON.parse(this.data.meta.dataProcessingInCountries||"[]")}get dataProcessingInCountriesSpecialTreatments(){return JSON.parse(this.data.meta.dataProcessingInCountriesSpecialTreatments||"[]")}constructor(e,t={}){super(e,t),(0,r.reaction)((()=>this.data.vendor),(e=>(0,r.runInAction)((()=>{if(e){const{vendors:t}=this.collection.store,o=e.id.toString();let n=t.get(o);n||(n=new c.K(e,this.collection.store),t.set(o,n)),this.vendorModel=n}}))),{fireImmediately:!0}),(0,r.reaction)((()=>{var e;return null==(e=this.data.meta)?void 0:e.vendorId}),(e=>{e&&(this.vendorModel=this.collection.store.vendors.get(e.toString()))}),{fireImmediately:!0})}setStatus(e){this.data.status=e}setMeta(e){this.data.meta=e}transformDataForPatch(){const e=super.transformDataForPatch();return{status:e.status,meta:e.meta}}afterPatch(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterDelete(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterPersist(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}}(0,n.Cg)([r.observable,(0,n.Sn)("design:type",void 0===c.K?Object:c.K)],d.prototype,"vendorModel",void 0),(0,n.Cg)([r.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype","undefined"==typeof TcfVendorConfigurationRestrictivePurposes?Object:TcfVendorConfigurationRestrictivePurposes)],d.prototype,"restrictivePurposes",null),(0,n.Cg)([r.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",Object)],d.prototype,"dataProcessingInCountries",null),(0,n.Cg)([r.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",Object)],d.prototype,"dataProcessingInCountriesSpecialTreatments",null),(0,n.Cg)([r.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof PostStatus?Object:PostStatus]),(0,n.Sn)("design:returntype",void 0)],d.prototype,"setStatus",null),(0,n.Cg)([r.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object]),(0,n.Sn)("design:returntype",void 0)],d.prototype,"setMeta",null),d=(0,n.Cg)([a.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:l.E,create:{path:"/rcb-tcf-vendor-conf"},patch:{path:"/rcb-tcf-vendor-conf/:id"},delete:{path:"/rcb-tcf-vendor-conf/:id"}}),(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object,"undefined"==typeof Partial?Object:Partial])],d)},9097:(e,t,o)=>{o.d(t,{W:()=>l});var n=o(59670),s=o(44497),i=o(68789),r=o(42090),a=o(80106),c=o(53109);class l extends i.BaseOptions{constructor(e){super(),this.busyChecklist=!1,this.probablyFetchByChangedItem=(0,s.flow)((function*(e,t){if(t)return void(yield this.fetchChecklist());const o=Array.isArray(e)?e:[e];this.items.filter((e=>{let{id:t,checked:n}=e;return o.indexOf(t)>-1&&!n})).length>0&&(yield this.fetchChecklist())})),this.fetchChecklist=(0,s.flow)((function*(){this.busyChecklist=!0;try{this.checklist=yield(0,r.E)({location:a.u,sendReferer:!0})}catch(e){throw console.log(e),e}finally{this.busyChecklist=!1}})),this.toggleChecklistItem=(0,s.flow)((function*(e,t){this.busyChecklist=!0;try{this.checklist=yield(0,r.E)({location:c.L,request:{state:t},sendReferer:!0,params:{id:e}})}catch(e){throw console.log(e),e}finally{this.busyChecklist=!1}})),this.rootStore=e}get items(){return this.checklist&&Object.keys(this.checklist.items).map((e=>({id:e,...this.checklist.items[e]})))||[]}get completed(){return this.items.filter((e=>{let{checked:t}=e;return t}))}get checkable(){const{isPro:e}=this.rootStore.optionStore.others;return this.items.filter((t=>{let{needsPro:o}=t;return!o||e&&o}))}get done(){var e;return this.completed.length>=this.checkable.length||!!(null==(e=this.checklist)?void 0:e.dismissed)}}(0,n.Cg)([s.observable],l.prototype,"busyChecklist",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type","undefined"==typeof ResponseRouteChecklistGet?Object:ResponseRouteChecklistGet)],l.prototype,"checklist",void 0),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"items",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"completed",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"checkable",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"done",null)},69555:(e,t,o)=>{o.d(t,{s:()=>y});var n=o(59670),s=o(44497),i=o(68789),r=o(55294),a=o(56953),c=o(947),l=o(42090),d=o(35070),p=o(64047),u=o(24821),h=o(16299),g=o(84573);class y extends i.BaseOptions{constructor(e){super(),this.busyConsent=!1,this.busyReferer=!1,this.count=0,this.truncatedIpsCount=0,this.perPage=50,this.offset=0,this.pageCollection=new Map,this.revisions=new Map,this.revisionsIndependent=new Map,this.referer=[],this.filters=s.observable.object({page:1,dates:[void 0,void 0],context:void 0,referer:void 0,ip:void 0,uuid:void 0},{},{deep:!1}),this.fetchAll=(0,s.flow)((function*(e){this.busyConsent=!0;try{const{page:t,referer:o,ip:n,uuid:s,context:i}=this.filters,a=this.filters.dates.map((e=>e?e.format("YYYY-MM-DD"):"")),{count:c,truncatedIpsCount:d,items:u}=yield(0,l.E)({location:p.D,params:{per_page:this.perPage,offset:(t-1)*this.perPage,from:a[0],to:a[1],ip:n,uuid:s,referer:o,context:i,...e||{}}});this.count=c,d&&(this.truncatedIpsCount=d),this.pageCollection.clear();for(const e of u)this.pageCollection.set(e.id,new r.N(e,this))}catch(e){throw this.count=0,this.truncatedIpsCount=0,this.pageCollection.clear(),console.log(e),e}finally{this.busyConsent=!1}})),this.fetchRevision=(0,s.flow)((function*(e){try{const t=yield(0,l.E)({location:h.F,params:e});this.revisions.set(e.hash,new a.A(t,this))}catch(e){throw console.log(e),e}})),this.fetchRevisionIndependent=(0,s.flow)((function*(e){try{const t=yield(0,l.E)({location:g.$,params:e});this.revisionsIndependent.set(e.hash,new c.Q(t,this))}catch(e){throw console.log(e),e}})),this.fetchReferer=(0,s.flow)((function*(e){this.busyReferer=!0;try{const t=yield(0,l.E)({location:u.f,params:e});this.referer=t.items}catch(e){throw console.log(e),e}finally{this.busyReferer=!1}})),this.deleteAll=(0,s.flow)((function*(){this.busyConsent=!0;try{yield(0,l.E)({location:d.M}),this.applyPage(0),yield this.fetchAll()}catch(e){throw console.log(e),e}finally{this.busyConsent=!1}})),this.rootStore=e,(0,s.runInAction)((()=>{this.filters.context=this.rootStore.optionStore.others.context}))}applyPage(e){this.filters.page=e}applyDates(e){this.filters.dates=e}applyContext(e){this.filters.context=e}applyReferer(e){this.filters.referer=e}applyIp(e){this.filters.ip=e}applyUuid(e){this.filters.uuid=e}}(0,n.Cg)([s.observable],y.prototype,"busyConsent",void 0),(0,n.Cg)([s.observable],y.prototype,"busyReferer",void 0),(0,n.Cg)([s.observable],y.prototype,"count",void 0),(0,n.Cg)([s.observable],y.prototype,"truncatedIpsCount",void 0),(0,n.Cg)([s.observable],y.prototype,"perPage",void 0),(0,n.Cg)([s.observable],y.prototype,"offset",void 0),(0,n.Cg)([s.observable],y.prototype,"pageCollection",void 0),(0,n.Cg)([s.observable],y.prototype,"revisions",void 0),(0,n.Cg)([s.observable],y.prototype,"revisionsIndependent",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Array)],y.prototype,"referer",void 0),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object]),(0,n.Sn)("design:returntype",void 0)],y.prototype,"applyPage",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object]),(0,n.Sn)("design:returntype",void 0)],y.prototype,"applyDates",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object]),(0,n.Sn)("design:returntype",void 0)],y.prototype,"applyContext",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object]),(0,n.Sn)("design:returntype",void 0)],y.prototype,"applyReferer",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object]),(0,n.Sn)("design:returntype",void 0)],y.prototype,"applyIp",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object]),(0,n.Sn)("design:returntype",void 0)],y.prototype,"applyUuid",null)},72975:(e,t,o)=>{o.d(t,{k:()=>h});var n=o(59670),s=o(44497),i=o(26934),r=o(87608),a=o(80963),c=o(14675),l=o(42090),d=o(14819),p=o(72229),u=o(6659);class h{get blockersCount(){return this.fetchedAllBlockers?this.blockers.entries.size:this.rootStore.optionStore.allBlockerCount}get cookiesCount(){return Array.from(this.groups.entries.values()).map((e=>{let{cookiesCount:t}=e;return t})).reduce(((e,t)=>e+t),0)}constructor(e){this.busy=!1,this.unassignedCookies=new Map,this.templatesBlocker=new Map,this.busyTemplatesBlocker=!1,this.templatesServices=new Map,this.busyTemplatesServices=!1,this.fetchedAllBlockers=!1,this.fetchGroups=(0,s.flow)((function*(){yield this.groups.get({params:{per_page:100}}),yield this.fetchUnassignedCookies()})),this.fetchUnassignedCookies=(0,s.flow)((function*(){try{const e=yield(0,l.E)({location:d.d});for(const t of Object.values(e))this.unassignedCookies.set(t.id,t)}catch(e){throw console.log(e),e}})),this.fetchBlockers=(0,s.flow)((function*(){yield this.blockers.get({request:{status:["draft","publish","private"]},params:{per_page:100,context:"edit"}}),this.fetchedAllBlockers=!0})),this.fetchTemplatesBlocker=(0,s.flow)((function*(e){this.busyTemplatesBlocker=!0;try{const{items:t}=yield(0,l.E)({location:p.a,params:e});this.templatesBlocker.clear(),this.addBlockerTemplates(t)}catch(e){throw console.log(e),e}finally{this.busyTemplatesBlocker=!1}})),this.fetchTemplatesServices=(0,s.flow)((function*(e){this.busyTemplatesServices=!0;try{const{items:t}=yield(0,l.E)({location:u.r,params:e});if(["redownload","invalidate"].indexOf(null==e?void 0:e.storage)>-1){const{activeLanguages:t,currentLanguage:o}=this.rootStore.optionStore.others;for(const n of t)n!==o&&(yield(0,l.E)({location:u.r,params:{...e,_dataLocale:n}}))}this.templatesServices.clear(),this.addServiceTemplates(t)}catch(e){throw console.log(e),e}finally{this.busyTemplatesServices=!1}})),this.rootStore=e,(0,s.runInAction)((()=>{this.groups=new a.e(this),this.blockers=new i.h(this)}))}get essentialGroup(){if(0===this.groups.entries.size)return;const e=this.groups.entries.values();let t;for(;(t=e.next().value)&&!t.data.meta.isEssential;);return t}addBlockerTemplates(e){for(const t of e)this.templatesBlocker.set(t.identifier,new r.x(t,this))}addServiceTemplates(e){for(const t of e)this.templatesServices.set(t.identifier,new c.s(t,this))}}(0,n.Cg)([s.observable],h.prototype,"busy",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",void 0===a.e?Object:a.e)],h.prototype,"groups",void 0),(0,n.Cg)([s.observable],h.prototype,"unassignedCookies",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",void 0===i.h?Object:i.h)],h.prototype,"blockers",void 0),(0,n.Cg)([s.observable],h.prototype,"templatesBlocker",void 0),(0,n.Cg)([s.observable],h.prototype,"busyTemplatesBlocker",void 0),(0,n.Cg)([s.observable],h.prototype,"templatesServices",void 0),(0,n.Cg)([s.observable],h.prototype,"busyTemplatesServices",void 0),(0,n.Cg)([s.observable],h.prototype,"fetchedAllBlockers",void 0),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],h.prototype,"blockersCount",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],h.prototype,"cookiesCount",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],h.prototype,"essentialGroup",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Array]),(0,n.Sn)("design:returntype",void 0)],h.prototype,"addBlockerTemplates",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Array]),(0,n.Sn)("design:returntype",void 0)],h.prototype,"addServiceTemplates",null)},54826:(e,t,o)=>{o.d(t,{p:()=>l});var n=o(59670),s=o(44497),i=o(59627),r=o(14909),a=o(42090),c=o(81284);class l{constructor(e){this.visible=!1,this.individualPrivacyOpen=!1,this.previewCheckboxActiveState=!1,this.previewStickyMenuOpenState=!1,this.busyPresets=!1,this.presets=new Map,this.presetConstants=new Map,this.presetDefaults=new Map,this.debounceFromCustomize={},this.fetchPresets=(0,s.flow)((function*(){this.busyPresets=!0;try{const{defaults:e,constants:t,items:o}=yield(0,a.E)({location:c.K});for(const t of Object.keys(e))this.presetDefaults.set(t,e[t]);for(const e of Object.keys(t))this.presetConstants.set(e,t[e]);for(const e of Object.keys(o))this.presets.set(e,new r.M({id:e,...o[e]},this))}catch(e){throw console.log(e),e}finally{this.busyPresets=!1}})),this.rootStore=e}setBannerFromCustomize(e,t,o,n){void 0===n&&(n=!0);const{customizeValuesBanner:s}=this.rootStore.optionStore.others,i=t.toString();if(n&&["css","animationInDuration","animationOutDuration"].indexOf(i)>-1)clearTimeout(this.debounceFromCustomize[i]),this.debounceFromCustomize[i]=setTimeout((()=>this.setBannerFromCustomize(e,t,o,!1)),500);else{const n=s[e][t];s[e][t]=o,i.startsWith("animationOut")&&n!==o&&this.forceAnimationOutSimulation()}}setBannerFromPreset(e){for(const t of e){const[e,o,n]=t;this.rootStore.optionStore.others.customizeValuesBanner[e][o]=n}}forceAnimationOutSimulation(){const{customizeValuesBanner:e}=this.rootStore.optionStore.others;"none"!==e.layout.animationOut&&(this.visible=!1,setTimeout((()=>(0,s.runInAction)((()=>{this.visible=!0}))),+e.layout.animationOutDuration+1e3))}setVisible(e){this.visible=e}setIndividualPrivacyOpen(e){this.individualPrivacyOpen=e}setPreviewCheckboxActiveState(e){this.previewCheckboxActiveState=e}setPreviewStickyMenuOpenState(e){this.previewStickyMenuOpenState=e}exportPhp(){const e={},t=(0,i.getSidebarCustomize)();return this.presetDefaults.forEach(((o,n)=>{let s=t(n).get();"boolean"==typeof o?s=!!+s:isNaN(s)||""===s||(s=+s),JSON.stringify(o)!==JSON.stringify(s)&&(e[this.presetConstants.get(n)]=s)})),this.jsonToPHPArray(e)}jsonToPHPArray(e){const t=JSON.stringify(e,null,4).split("\n");return t.shift(),t.pop(),t.join("\n").replace(/^(\s+)"([A-Za-z\\]+::[A-Z_]+)"(:)/gm,"$1$2 =>").replace(/^(\s+)([A-Za-z\\]+)::/gm,((e,t,o)=>`${t}${o.replace(/\\\\/gm,"\\")}::`))}}(0,n.Cg)([s.observable],l.prototype,"visible",void 0),(0,n.Cg)([s.observable],l.prototype,"individualPrivacyOpen",void 0),(0,n.Cg)([s.observable],l.prototype,"previewCheckboxActiveState",void 0),(0,n.Cg)([s.observable],l.prototype,"previewStickyMenuOpenState",void 0),(0,n.Cg)([s.observable],l.prototype,"busyPresets",void 0),(0,n.Cg)([s.observable],l.prototype,"presets",void 0),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof T?Object:T,"undefined"==typeof P?Object:P,Object,void 0]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"setBannerFromCustomize",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof Array?Object:Array]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"setBannerFromPreset",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"forceAnimationOutSimulation",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Boolean]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"setVisible",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Boolean]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"setIndividualPrivacyOpen",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Boolean]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"setPreviewCheckboxActiveState",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Boolean]),(0,n.Sn)("design:returntype",void 0)],l.prototype,"setPreviewStickyMenuOpenState",null)},34577:(e,t,o)=>{o.d(t,{PL:()=>c.P,WK:()=>l.W,XS:()=>d.X,gU:()=>p.g,gy:()=>n.g,kv:()=>r.k,pz:()=>i.p,s4:()=>a.s,t6:()=>s.t,yd:()=>n.y});var n=o(71951),s=o(96254),i=o(54826),r=o(72975),a=o(69555),c=o(45652),l=o(9097),d=o(38494),p=o(55631)},96254:(e,t,o)=>{o.d(t,{t:()=>b});var n=o(59670),s=o(44497),i=o(68789),r=o(62884),a=o(49949),c=o(42090),l=o(56372),d=o(93445),p=o(33492),u=o(89552),h=o(38879),g=o(64799),y=o(11341);class b extends i.BaseOptions{get isOnlyRcbCookieCreated(){return!(1!==this.allCookieCount||this.isTcf&&this.allTcfVendorConfigurationCount)}get areSettingsFetched(){return void 0!==this.isRespectDoNotTrack}constructor(e){super(),this.busySettings=!1,this.busyCountryBypassUpdate=!1,this.busyAddLinksToNavigationMenu=!1,this.needsRevisionRetrigger=!1,this.fetchedBannerLinks=!1,this.publicCookieCount=0,this.allCookieCount=0,this.allBlockerCount=0,this.allTcfVendorConfigurationCount=0,this.allScannerResultTemplatesCount=0,this.allScannerResultExternalUrlsCount=0,this.cookieCounts={draft:0,private:0,publish:0},this.cloudReleaseInfo={blocker:null,service:null},this.navMenus=[],this.templateNeedsUpdate=[],this.googleConsentModeNoticesHtml=[],this.servicesDataProcessingInUnsafeCountriesNoticeHtml="",this.servicesWithEmptyPrivacyPolicyNoticeHtml="",this.createdTagManagers={gtm:[],mtm:[]},this.contexts={"":""},this.isBannerActive=!1,this.isBlockerActive=!1,this.hidePageIds=[],this.forwardTo=[],this.countryBypassCountries=[],this.isTcf=!1,this.isGcm=!1,this.isGcmShowRecommandationsWithoutConsent=!1,this.isGcmCollectAdditionalDataViaUrlParameters=!1,this.isGcmRedactAdsDataWithoutConsent=!0,this.isGcmListPurposes=!0,this.bannerlessConsentChecks={essential:[],legalBasisConsentWithoutVisualContentBlocker:[],legalBasisLegitimateInterest:[]},this.isBannerStickyLinksEnabled=!1,this.fetchSettings=(0,s.flow)((function*(e){this.busySettings=!0;try{const t=e||(yield(0,c.E)({location:g.Z}));this.isBannerActive=t["rcb-banner-active"],this.isBlockerActive=t["rcb-blocker-active"],this.hidePageIds=(t["rcb-hide-page-ids"]||"").split(",").map(Number).filter(Boolean),this.setCookiesViaManager=t["rcb-set-cookies-via-manager"]||"none",this.operatorCountry=t["rcb-operator-country"],this.operatorContactAddress=t["rcb-operator-contact-address"],this.operatorContactPhone=t["rcb-operator-contact-phone"],this.operatorContactEmail=t["rcb-operator-contact-email"],this.operatorContactFormId=t["rcb-operator-contact-form-id"],this.cookiePolicyId=t["rcb-cookie-policy-id"],this.territorialLegalBasis=t["rcb-territorial-legal-basis"].split(","),this.isAcceptAllForBots=t["rcb-accept-all-for-bots"],this.isRespectDoNotTrack=t["rcb-respect-do-not-track"],this.isBannerLessConsent=t["rcb-banner-less-consent"],this.bannerLessConsentShowOnPageIds=(t["rcb-banner-less-show-on-page-ids"]||"").split(",").map(Number).filter(Boolean),this.cookieDuration=t["rcb-cookie-duration"],this.failedConsentDocumentationHandling=t["rcb-failed-consent-documentation-handling"],this.isSaveIp=t["rcb-save-ip"],this.isDataProcessingInUnsafeCountries=t["rcb-data-processing-in-unsafe-countries"],this.isAgeNotice=t["rcb-age-notice"],this.ageNoticeAgeLimit=t["rcb-age-notice-age-limit"],this.isListServicesNotice=t["rcb-list-services-notice"],this.isConsentForwarding=t["rcb-consent-forwarding"]||!1,this.forwardTo=(t["rcb-forward-to"]||"").split("|").filter(Boolean),this.crossDomains=t["rcb-cross-domains"]||"",this.isCountryBypass=t["rcb-country-bypass"],this.countryBypassCountries=(t["rcb-country-bypass-countries"]||"").split(",").filter(Boolean),this.countryBypassType=t["rcb-country-bypass-type"],this.countryBypassDbDownloadTime=t["rcb-country-bypass-db-download-time"],this.isTcf=t["rcb-tcf"],this.tcfAcceptedTime=t["rcb-tcf-accepted-time"],this.tcfGvlDownloadTime=t["rcb-tcf-gvl-download-time"],this.consentDuration=t["rcb-consent-duration"],yield this.fetchCurrentRevision()}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.fetchBannerLinks=(0,s.flow)((function*(){yield this.bannerLinks.get({request:{status:["draft","publish","private"]},params:{per_page:100,context:"edit"}}),this.fetchedBannerLinks=!0})),this.updateSettings=(0,s.flow)((function*(e,t){let{isBannerActive:o,isBlockerActive:n,hidePageIds:s,setCookiesViaManager:i,operatorCountry:r,operatorContactAddress:l,operatorContactEmail:d,operatorContactFormId:p,operatorContactPhone:u,cookiePolicyId:h,territorialLegalBasis:g,isAcceptAllForBots:b,isRespectDoNotTrack:v,cookieDuration:m,failedConsentDocumentationHandling:f,isSaveIp:S,isDataProcessingInUnsafeCountries:C,isAgeNotice:k,isBannerLessConsent:x,bannerLessConsentShowOnPageIds:j,ageNoticeAgeLimit:w,isListServicesNotice:_,isConsentForwarding:O,forwardTo:P,crossDomains:R,affiliateLink:A,affiliateLabelBehind:T,affiliateLabelDescription:F,isCountryBypass:E,countryBypassCountries:B,countryBypassType:D,isTcf:I,isGcm:M,isGcmShowRecommandationsWithoutConsent:G,isGcmCollectAdditionalDataViaUrlParameters:N,isGcmRedactAdsDataWithoutConsent:U,isGcmListPurposes:L,consentDuration:V,isBannerStickyLinksEnabled:H}=e;this.busySettings=!0;try{const e=yield(0,c.E)({location:y.N,request:{...void 0===o?{}:{"rcb-banner-active":o},...void 0===n?{}:{"rcb-blocker-active":n},...void 0===s?{}:{"rcb-hide-page-ids":s.join(",")},...void 0===i?{}:{"rcb-set-cookies-via-manager":i},...void 0===r?{}:{"rcb-operator-country":r},...void 0===l?{}:{"rcb-operator-contact-address":l},...void 0===u?{}:{"rcb-operator-contact-phone":u},...void 0===d?{}:{"rcb-operator-contact-email":d},...void 0===p?{}:{"rcb-operator-contact-form-id":p},...void 0===h?{}:{"rcb-cookie-policy-id":h},...void 0===g?{}:{"rcb-territorial-legal-basis":g.join(",")},...void 0===b?{}:{"rcb-accept-all-for-bots":b},...void 0===x?{}:{"rcb-banner-less-consent":x},...void 0===j?{}:{"rcb-banner-less-show-on-page-ids":j.join(",")},...void 0===v?{}:{"rcb-respect-do-not-track":v},...void 0===m?{}:{"rcb-cookie-duration":m},...void 0===f?{}:{"rcb-failed-consent-documentation-handling":f},...void 0===S?{}:{"rcb-save-ip":S},...void 0===C?{}:{"rcb-data-processing-in-unsafe-countries":C},...void 0===k?{}:{"rcb-age-notice":k},...void 0===w?{}:{"rcb-age-notice-age-limit":w},...void 0===_?{}:{"rcb-list-services-notice":_},...void 0===O?{}:{"rcb-consent-forwarding":O},...void 0===P?{}:{"rcb-forward-to":P.join("|")},...void 0===R?{}:{"rcb-cross-domains":R},...void 0===E?{}:{"rcb-country-bypass":E},...void 0===B?{}:{"rcb-country-bypass-countries":B.join(",")},...void 0===D?{}:{"rcb-country-bypass-type":D},...void 0===I?{}:{"rcb-tcf":I},...void 0===V?{}:{"rcb-consent-duration":V}}});if(this.fetchedBannerLinks&&t){const e=this.bannerLinks.sortedBannerLinks;for(const o of e)t.find((e=>{let{id:t}=e;return o.data.id===t}))||(yield o.delete());for(let o=0;o<t.length;o++){const{isExternalUrl:n,label:s,pageType:i,externalUrl:r,hideCookieBanner:c,id:l,pageId:d,isTargetBlank:p}=t[o],u={isExternalUrl:n,pageType:i,externalUrl:r,hideCookieBanner:c,pageId:d,isTargetBlank:p};if(l){const t=e.find((e=>{let{data:{id:t}}=e;return l===t}));if(t){const{data:{title:{raw:e},menu_order:a,meta:{isExternalUrl:l,pageType:p,externalUrl:h,hideCookieBanner:g,pageId:y}}}=t;e===s&&a===o&&l===n&&p===i&&y===d&&h===r&&g===c||(t.setLabel(s),t.setOrder(o),t.setMeta(u),yield t.patch())}}else{const e=new a.A(this.bannerLinks,{title:{raw:s},content:{raw:"",protected:!1},status:"publish",menu_order:o,meta:u});yield e.persist()}}}this.fetchSettings(e),this.rootStore.checklistStore.fetchChecklist()}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.fetchCurrentRevision=(0,s.flow)((function*(){this.busySettings=!0;try{this.setFromCurrentRevision(yield(0,c.E)({location:u.q}))}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.updateCurrentRevision=(0,s.flow)((function*(e){this.busySettings=!0;try{this.setFromCurrentRevision(yield(0,c.E)({location:h.D,request:e}))}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.setModalHintSeen=(0,s.flow)((function*(e){this.busySettings=!0;try{this.others.modalHints.push(e),yield(0,c.E)({location:i.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:`modal-hint-${e}`},request:{value:!0}})}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.dismissConfigProNotice=(0,s.flow)((function*(){try{this.others.isConfigProNoticeVisible=!1,yield(0,c.E)({location:i.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:"get-pro-main-button"},request:{value:!0}})}catch(e){throw console.log(e),e}})),this.dismissServiceDataProcessingInUnsafeCountriesNotice=(0,s.flow)((function*(){try{this.servicesDataProcessingInUnsafeCountriesNoticeHtml="",yield(0,c.E)({location:i.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:"service-data-processing-in-unsafe-countries"},request:{value:!1}})}catch(e){throw console.log(e),e}})),this.dismissBannerlessConsentLegitimateServicesNotice=(0,s.flow)((function*(){try{yield(0,c.E)({location:i.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:"dismissed-bannerless-consent-legint-services"},request:{value:!1}}),yield this.fetchCurrentRevision()}catch(e){throw console.log(e),e}})),this.dismissBannerlessConsentServicesWithoutVisualContentBlockerNotice=(0,s.flow)((function*(){try{yield(0,c.E)({location:i.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:"dismissed-bannerless-consent-services-without-visual-content-blocker"},request:{value:!1}}),yield this.fetchCurrentRevision()}catch(e){throw console.log(e),e}})),this.dismissMigration=(0,s.flow)((function*(){try{const{id:e}=this.dashboardMigration;this.dashboardMigration=void 0,yield(0,c.E)({location:d.Z,params:{migration:e}})}catch(e){throw console.log(e),e}})),this.addLinksToNavigationMenu=(0,s.flow)((function*(e){this.busyAddLinksToNavigationMenu=!0;try{const{success:t}=yield(0,c.E)({location:p.o,request:{id:e}});return t&&(this.rootStore.checklistStore.fetchChecklist(),yield this.fetchCurrentRevision()),t}catch(e){throw console.log(e),e}finally{this.busyAddLinksToNavigationMenu=!1}})),this.updateCountryBypassDatabase=(0,s.flow)((function*(){this.busyCountryBypassUpdate=!0;try{const{dbDownloadTime:e}=yield(0,c.E)({location:l.q});this.countryBypassDbDownloadTime=e}catch(e){throw console.log(e),e}finally{this.busyCountryBypassUpdate=!1}})),this.rootStore=e,this.pureSlug=i.BaseOptions.getPureSlug("real-cookie-banner"),this.pureSlugCamelCased=i.BaseOptions.getPureSlug("real-cookie-banner",!0),(0,s.runInAction)((()=>{Object.assign(this,window[this.pureSlugCamelCased]),this.bannerLinks=new r.b(this),this.fomoCoupon=this.others.fomoCoupon}))}setFromCurrentRevision(e){let{contexts:t,created_tag_managers:o,needs_retrigger:n,public_cookie_count:s,all_cookie_count:i,all_blocker_count:r,all_tcf_vendor_configuration_count:a,all_scanner_result_templates_count:c,all_scanner_result_external_urls_count:l,cookie_counts:d,cloud_release_info:p,consents_deleted_at:u,bannerless_consent_checks:h,nav_menus:g,tcf_vendor_configuration_counts:y,dashboard_migration:b,fomo_coupon:v,template_needs_update:m,check_saving_consent_via_rest_api_endpoint_working_html:f,template_update_notice_html:S,template_successors_notice_html:C,google_consent_mode_notices_html:k,services_data_processing_in_unsafe_countries_notice_html:x,services_with_empty_privacy_policy_notice_html:j}=e;this.createdTagManagers=o,this.needsRevisionRetrigger=n,this.publicCookieCount=s,this.allCookieCount=i,this.allBlockerCount=r,this.allTcfVendorConfigurationCount=a,this.allScannerResultTemplatesCount=c,this.allScannerResultExternalUrlsCount=l,this.templateNeedsUpdate=m,this.templateUpdateNoticeHtml=S,this.checkSavingConsentViaRestApiEndpointWorkingHtml=f,this.templateSuccessorsNoticeHtml=C,this.googleConsentModeNoticesHtml=k,this.servicesDataProcessingInUnsafeCountriesNoticeHtml=x,this.servicesWithEmptyPrivacyPolicyNoticeHtml=j,this.cookieCounts=d,this.cloudReleaseInfo=p,this.consentsDeletedAt=u,this.bannerlessConsentChecks=h,this.navMenus=g,this.tcfVendorConfigurationCounts=y,this.contexts=t,this.dashboardMigration=b,this.fomoCoupon=v}setShowLicenseFormImmediate(e,t){this.others.showLicenseFormImmediate=e,this.others.isLicensed=t}}(0,n.Cg)([s.observable],b.prototype,"busySettings",void 0),(0,n.Cg)([s.observable],b.prototype,"busyCountryBypassUpdate",void 0),(0,n.Cg)([s.observable],b.prototype,"busyAddLinksToNavigationMenu",void 0),(0,n.Cg)([s.observable],b.prototype,"needsRevisionRetrigger",void 0),(0,n.Cg)([s.observable],b.prototype,"fetchedBannerLinks",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",void 0===r.b?Object:r.b)],b.prototype,"bannerLinks",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"publicCookieCount",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"allCookieCount",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"allBlockerCount",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"allTcfVendorConfigurationCount",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"allScannerResultTemplatesCount",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"allScannerResultExternalUrlsCount",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"cookieCounts",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"cloudReleaseInfo",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"navMenus",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"tcfVendorConfigurationCounts",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"templateNeedsUpdate",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"checkSavingConsentViaRestApiEndpointWorkingHtml",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"templateUpdateNoticeHtml",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"templateSuccessorsNoticeHtml",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"googleConsentModeNoticesHtml",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"servicesDataProcessingInUnsafeCountriesNoticeHtml",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"servicesWithEmptyPrivacyPolicyNoticeHtml",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"createdTagManagers",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"contexts",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"dashboardMigration",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"fomoCoupon",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isBannerActive",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isBlockerActive",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"hidePageIds",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"setCookiesViaManager",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"operatorCountry",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"operatorContactAddress",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"operatorContactPhone",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"operatorContactEmail",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"operatorContactFormId",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"cookiePolicyId",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"territorialLegalBasis",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isAcceptAllForBots",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isRespectDoNotTrack",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isBannerLessConsent",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"bannerLessConsentShowOnPageIds",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"cookieDuration",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"failedConsentDocumentationHandling",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isSaveIp",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isDataProcessingInUnsafeCountries",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isAgeNotice",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"ageNoticeAgeLimit",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isListServicesNotice",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isConsentForwarding",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"forwardTo",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"crossDomains",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"affiliateLink",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"affiliateLabelBehind",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"affiliateLabelDescription",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isCountryBypass",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"countryBypassCountries",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"countryBypassType",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"countryBypassDbDownloadTime",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isTcf",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"tcfAcceptedTime",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"tcfGvlDownloadTime",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isGcm",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isGcmShowRecommandationsWithoutConsent",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isGcmCollectAdditionalDataViaUrlParameters",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isGcmRedactAdsDataWithoutConsent",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isGcmListPurposes",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"consentDuration",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"consentsDeletedAt",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"bannerlessConsentChecks",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],b.prototype,"isBannerStickyLinksEnabled",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type","undefined"==typeof OtherOptions?Object:OtherOptions)],b.prototype,"others",void 0),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],b.prototype,"isOnlyRcbCookieCreated",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],b.prototype,"areSettingsFetched",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof ResponseRouteRevisionCurrentGet?Object:ResponseRouteRevisionCurrentGet]),(0,n.Sn)("design:returntype",void 0)],b.prototype,"setFromCurrentRevision",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Boolean,Boolean]),(0,n.Sn)("design:returntype",void 0)],b.prototype,"setShowLicenseFormImmediate",null)},55631:(e,t,o)=>{o.d(t,{g:()=>g});var n=o(59670),s=o(44497),i=o(81193),r=o(21169),a=o(56975),c=o(42090),l=o(11437),d=o(68068),p=o(44349),u=o(40976),h=o(50306);class g{get sortedTemplates(){const e=Array.from(this.resultTemplates.values());return e.sort(((e,t)=>(e.data.consumerData.isIgnored,t.data.consumerData.isIgnored,e.data.headline.localeCompare(t.data.headline)))),e}get sortedExternalUrls(){const e=Array.from(this.resultExternalUrls.values());return e.sort(((e,t)=>e.inactive===t.inactive?0:e.inactive?1:-1)),e}get templatesCount(){return this.fetchedAllResultTemplates?this.resultTemplates.size:this.rootStore.optionStore.allScannerResultTemplatesCount}get externalUrlsCount(){return this.fetchedAllResultExternalUrls?this.resultExternalUrls.size:this.rootStore.optionStore.allScannerResultExternalUrlsCount}get canShowResults(){var e;return this.templatesCount+this.externalUrlsCount>0&&(null==(e=this.rootStore.checklistStore.checklist)?void 0:e.items.scanner.checked)&&this.rootStore.optionStore.others.isLicensed}get foundScanResultsCount(){return this.resultTemplates.size+this.resultExternalUrls.size}get needsAttentionCount(){return[...this.resultTemplates.values(),...this.resultExternalUrls.values()].filter((e=>{let{inactive:t}=e;return!t})).length}constructor(e){this.resultTemplates=new Map,this.busyResultTemplates=!1,this.fetchedAllResultTemplates=!1,this.resultExternalUrls=new Map,this.resultAllExternalUrls=new Map,this.busyExternalUrls=!1,this.fetchedAllResultExternalUrls=!1,this.busyMarkup=!1,this.resultMarkup=new Map,this.addUrlsToQueue=(0,s.flow)((function*(e){return yield(0,c.E)({location:l.x,request:e})})),this.fetchResultTemplates=(0,s.flow)((function*(){this.busyResultTemplates=!0;try{this.resultTemplatesFromResponse(yield(0,c.E)({location:h.m})),this.fetchedAllResultTemplates=!0}catch(e){throw console.log(e),e}finally{this.busyResultTemplates=!1}})),this.fetchResultExternals=(0,s.flow)((function*(){this.busyExternalUrls=!0;try{this.resultExternalUrlsFromResponse(yield(0,c.E)({location:p.r})),this.fetchedAllResultExternalUrls=!0}catch(e){throw console.log(e),e}finally{this.busyExternalUrls=!1}})),this.fetchResultAllExternals=(0,s.flow)((function*(e){const t=e instanceof i.e?"host":"template",{identifier:o}=e;e.busy=!0;try{const{items:e}=yield(0,c.E)({location:d.S,params:{type:t,identifier:"host"===t?o.replace(/\./g,"_"):o}});let n=this.resultAllExternalUrls.get(o);if(n){const t=e.map((e=>{let{id:t}=e;return t}));for(const e of n.keys())-1===t.indexOf(e)&&n.delete(e)}else n=new Map;for(const t of Object.values(e))n.set(t.id,new r.W(t,this)),this.resultAllExternalUrls.set(o,n)}catch(e){throw console.log(e),e}finally{e.busy=!1}})),this.fetchMarkup=(0,s.flow)((function*(e){this.busyMarkup=!0;try{const t=yield(0,c.E)({location:u.F,params:{id:e}});this.resultMarkup.set(e,t)}catch(e){throw console.log(e),e}finally{this.busyMarkup=!1}})),this.rootStore=e}resultTemplatesFromResponse(e){let{items:t}=e;const o=Object.keys(t);for(const e of this.resultTemplates.keys())-1===o.indexOf(e)&&this.resultTemplates.delete(e);for(const e of o)this.resultTemplates.set(e,new a.C(t[e],this))}resultExternalUrlsFromResponse(e){let{items:t}=e;const o=Object.keys(t);for(const e of this.resultExternalUrls.keys())-1===o.indexOf(e)&&this.resultExternalUrls.delete(e);for(const e of o){const o=this.resultExternalUrls.get(e);o?(0,s.set)(o,{data:t[e]}):this.resultExternalUrls.set(e,new i.e(t[e],this))}}}(0,n.Cg)([s.observable],g.prototype,"resultTemplates",void 0),(0,n.Cg)([s.observable],g.prototype,"busyResultTemplates",void 0),(0,n.Cg)([s.observable],g.prototype,"fetchedAllResultTemplates",void 0),(0,n.Cg)([s.observable],g.prototype,"resultExternalUrls",void 0),(0,n.Cg)([s.observable],g.prototype,"resultAllExternalUrls",void 0),(0,n.Cg)([s.observable],g.prototype,"busyExternalUrls",void 0),(0,n.Cg)([s.observable],g.prototype,"fetchedAllResultExternalUrls",void 0),(0,n.Cg)([s.observable],g.prototype,"busyMarkup",void 0),(0,n.Cg)([s.observable],g.prototype,"resultMarkup",void 0),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],g.prototype,"sortedTemplates",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],g.prototype,"sortedExternalUrls",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],g.prototype,"templatesCount",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],g.prototype,"externalUrlsCount",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],g.prototype,"canShowResults",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],g.prototype,"foundScanResultsCount",null),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],g.prototype,"needsAttentionCount",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof ResponseRouteScannerResultTemplatesGet?Object:ResponseRouteScannerResultTemplatesGet]),(0,n.Sn)("design:returntype",void 0)],g.prototype,"resultTemplatesFromResponse",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",["undefined"==typeof ResponseRouteScannerResultExternalsGet?Object:ResponseRouteScannerResultExternalsGet]),(0,n.Sn)("design:returntype",void 0)],g.prototype,"resultExternalUrlsFromResponse",null)},45652:(e,t,o)=>{o.d(t,{P:()=>c});var n=o(59670),s=o(44497),i=o(38123),r=o.n(i),a=o(68789);class c extends a.BaseOptions{constructor(e){super(),this.busyStats={main:!1,buttonClicked:!1,customBypass:!1},this.stats=s.observable.object({main:void 0,buttonsClicked:void 0,customBypass:void 0},{},{deep:!1}),this.filters=s.observable.object({dates:void 0,context:void 0},{},{deep:!1}),this.fetchMain=(0,s.flow)((function*(){throw new Error("This feature is not available in the free version.")})),this.fetchButtonsClicked=(0,s.flow)((function*(){throw new Error("This feature is not available in the free version.")})),this.fetchCustomBypass=(0,s.flow)((function*(){throw new Error("This feature is not available in the free version.")})),this.rootStore=e,(0,s.runInAction)((()=>{this.filters.dates=[r()().subtract(30,"days"),r()()],this.filters.context=this.rootStore.optionStore.others.context}))}applyDates(e){this.filters.dates=e}applyContext(e){this.filters.context=e}}(0,n.Cg)([s.observable],c.prototype,"busyStats",void 0),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"applyDates",null),(0,n.Cg)([s.action,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[Object]),(0,n.Sn)("design:returntype",void 0)],c.prototype,"applyContext",null)},71951:(e,t,o)=>{o.d(t,{g:()=>h,y:()=>u});var n=o(68789),s=o(9097),i=o(69555),r=o(72975),a=o(54826),c=o(96254),l=o(55631),d=o(45652),p=o(38494);class u{get context(){return this.contextMemo?this.contextMemo:this.contextMemo=(0,n.createContextFactory)(this)}constructor(){this.optionStore=new c.t(this),this.customizeBannerStore=new a.p(this),this.cookieStore=new r.k(this),this.consentStore=new i.s(this),this.statsStore=new d.P(this),this.checklistStore=new s.W(this),this.tcfStore=new p.X(this),this.scannerStore=new l.g(this)}static get StoreProvider(){return u.get.context.StoreProvider}static get get(){return u.me?u.me:u.me=new u}}const h=()=>u.get.context.useStores()},38494:(e,t,o)=>{o.d(t,{X:()=>y});var n=o(59670),s=o(44497),i=o(68789),r=o(49055),a=o(3153),c=o(67705),l=o(80945),d=o(2183),p=o(42090),u=o(57578),h=o(6775),g=o(83240);class y extends i.BaseOptions{get vendorConfigurationCount(){return this.fetchedAllVendorConfigurations?this.vendorConfigurations.entries.size:this.rootStore.optionStore.allTcfVendorConfigurationCount}constructor(e){super(),this.busyGvl=!1,this.busyVendors=!1,this.busyDeclarations=!1,this.fetchedAllVendorConfigurations=!1,this.vendors=new Map,this.purposes=new Map,this.specialPurposes=new Map,this.features=new Map,this.specialFeatures=new Map,this.dataCategories=new Map,this.fetchVendorConfigurations=(0,s.flow)((function*(){const e=Math.ceil(this.vendorConfigurationCount/100);for(let t=0;t<e;t++)yield this.vendorConfigurations.get({request:{status:["draft","publish","private"]},params:{offset:100*t,per_page:100,context:"edit"}});this.fetchedAllVendorConfigurations=!0})),this.fetchVendors=(0,s.flow)((function*(){this.busyVendors=!0;try{const{vendorListVersion:e,vendors:t}=yield(0,p.E)({location:g._});for(const e of Object.keys(t))this.vendors.set(e,new l.K(t[e],this));this.vendorListVersion=e}catch(e){throw console.log(e),e}finally{this.busyVendors=!1}})),this.fetchDeclarations=(0,s.flow)((function*(){this.busyDeclarations=!0;try{const{gvlSpecificationVersion:e,tcfPolicyVersion:t,purposes:o,specialPurposes:n,features:s,specialFeatures:i,dataCategories:l}=yield(0,p.E)({location:u.O});for(const e of Object.keys(o))this.purposes.set(e,new c.M(o[e],!1,this));for(const e of Object.keys(n))this.specialPurposes.set(e,new c.M(n[e],!0,this));for(const e of Object.keys(s))this.features.set(e,new a.u(s[e],!1,this));for(const e of Object.keys(i))this.specialFeatures.set(e,new a.u(i[e],!0,this));for(const e of Object.keys(l))this.dataCategories.set(e,new r.e(l[e],this));this.declarations={purposes:o,specialPurposes:n,features:s,specialFeatures:i,dataCategories:l},this.gvlSpecificationVersion=e,this.tcfPolicyVersion=t}catch(e){throw console.log(e),e}finally{this.busyDeclarations=!1}})),this.updateGvl=(0,s.flow)((function*(){this.busyGvl=!0;try{const{gvlDownloadTime:e}=yield(0,p.E)({location:h.D});this.rootStore.optionStore.tcfGvlDownloadTime=e}catch(e){throw console.log(e),e}finally{this.busyGvl=!1}})),this.rootStore=e,(0,s.runInAction)((()=>{this.vendorConfigurations=new d.u(this)}))}}(0,n.Cg)([s.observable],y.prototype,"busyGvl",void 0),(0,n.Cg)([s.observable],y.prototype,"busyVendors",void 0),(0,n.Cg)([s.observable],y.prototype,"busyDeclarations",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",void 0===d.u?Object:d.u)],y.prototype,"vendorConfigurations",void 0),(0,n.Cg)([s.observable],y.prototype,"fetchedAllVendorConfigurations",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],y.prototype,"vendorListVersion",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],y.prototype,"gvlSpecificationVersion",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],y.prototype,"tcfPolicyVersion",void 0),(0,n.Cg)([s.observable,(0,n.Sn)("design:type","undefined"==typeof Omit?Object:Omit)],y.prototype,"declarations",void 0),(0,n.Cg)([s.observable],y.prototype,"vendors",void 0),(0,n.Cg)([s.observable],y.prototype,"purposes",void 0),(0,n.Cg)([s.observable],y.prototype,"specialPurposes",void 0),(0,n.Cg)([s.observable],y.prototype,"features",void 0),(0,n.Cg)([s.observable],y.prototype,"specialFeatures",void 0),(0,n.Cg)([s.observable],y.prototype,"dataCategories",void 0),(0,n.Cg)([s.computed,(0,n.Sn)("design:type",Function),(0,n.Sn)("design:paramtypes",[]),(0,n.Sn)("design:returntype",void 0)],y.prototype,"vendorConfigurationCount",null)},33464:(e,t,o)=>{o.d(t,{G7:()=>i,Mv:()=>n,oP:()=>s});const n="rcb-scan",s="rcb-automatic-scan-starter",i="rcb-db-consent-v2"},7519:(e,t,o)=>{let n;function s(){const e=window["real-cookie-banner".replace(/-([a-z])/g,(e=>e[1].toUpperCase()))];if(!e){if(n)return window[n];for(const e in window){const t=window[e];if("real-cookie-banner"===(null==t?void 0:t.textDomain))return n=e,t}}return e}o.d(t,{b:()=>s})},79521:(e,t,o)=>{o.d(t,{j:()=>s});var n=o(7519);function s(){return(0,n.b)().others}},30617:(e,t,o)=>{o.d(t,{__:()=>c,_i:()=>l,_n:()=>r,_x:()=>a});var n=o(68789);let s;function i(){return s||(s=(0,n.createLocalizationFactory)(n.BaseOptions.getPureSlug("real-cookie-banner")))}const r=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return i()._n(...t)},a=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return i()._x(...t)},c=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return i().__(...t)},l=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return i()._i(...t)}},42090:(e,t,o)=>{o.d(t,{E:()=>a,T:()=>r});var n=o(68789);let s;function i(){return s||(s=(0,n.createRequestFactory)(window[n.BaseOptions.getPureSlug("real-cookie-banner",!0)]))}const r=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return i().urlBuilder(...t)},a=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return i().request(...t)}},46504:(e,t,o)=>{o.d(t,{i:()=>n});const n={path:"/banner-links/order",method:o(68789).RouteHttpVerb.PUT}},80106:(e,t,o)=>{o.d(t,{u:()=>n});const n={path:"/checklist",method:o(68789).RouteHttpVerb.GET}},53109:(e,t,o)=>{o.d(t,{L:()=>n});const n={path:"/checklist/:id",method:o(68789).RouteHttpVerb.PUT}},44175:(e,t,o)=>{o.d(t,{L:()=>n});const n={path:"/consent/:id",method:o(68789).RouteHttpVerb.DELETE}},35070:(e,t,o)=>{o.d(t,{M:()=>n});const n={path:"/consent/all",method:o(68789).RouteHttpVerb.DELETE}},64047:(e,t,o)=>{o.d(t,{D:()=>n});const n={path:"/consent/all",method:o(68789).RouteHttpVerb.GET}},24821:(e,t,o)=>{o.d(t,{f:()=>n});const n={path:"/consent/referer",method:o(68789).RouteHttpVerb.GET}},75782:(e,t,o)=>{o.d(t,{q:()=>n});const n={path:"/cookie-groups/order",method:o(68789).RouteHttpVerb.PUT}},14819:(e,t,o)=>{o.d(t,{d:()=>n});const n={path:"/cookies/unassigned",method:o(68789).RouteHttpVerb.GET}},61988:(e,t,o)=>{o.d(t,{a:()=>n});const n={path:"/cookies/order",method:o(68789).RouteHttpVerb.PUT}},56372:(e,t,o)=>{o.d(t,{q:()=>n});const n={path:"/country-bypass/database",method:o(68789).RouteHttpVerb.PUT}},98036:(e,t,o)=>{o.d(t,{A:()=>n});const n={path:"/create-cookie-policy",method:o(68789).RouteHttpVerb.POST}},62434:(e,t,o)=>{o.d(t,{A:()=>n});const n={path:"/export",method:o(68789).RouteHttpVerb.GET}},52135:(e,t,o)=>{o.d(t,{R:()=>n});const n={path:"/export/consents",method:o(68789).RouteHttpVerb.GET}},32991:(e,t,o)=>{o.d(t,{H:()=>n});const n={path:"/forward/cookie/:slug",method:o(68789).RouteHttpVerb.GET}},12975:(e,t,o)=>{o.d(t,{z:()=>n});const n={path:"/forward/endpoints",method:o(68789).RouteHttpVerb.GET}},60789:(e,t,o)=>{o.d(t,{h:()=>n});const n={path:"/import",method:o(68789).RouteHttpVerb.POST}},72955:(e,t,o)=>{o.d(t,{il:()=>Y.i,uI:()=>k.u,LB:()=>x.L,MG:()=>g.M,DW:()=>h.D,Gg:()=>S,LP:()=>K.L,UU:()=>F,kU:()=>d,uk:()=>l,fJ:()=>V.f,qg:()=>r.q,dT:()=>j.d,aF:()=>a.a,qk:()=>E.q,Ac:()=>Z.A,Rh:()=>C.R,Ai:()=>m.A,HD:()=>_.H,zS:()=>w.z,hh:()=>f.h,Zj:()=>D.Z,aH:()=>B.a,oJ:()=>H.o,Kl:()=>i.K,qz:()=>p.q,Dk:()=>u.D,Ft:()=>O.F,$c:()=>P.$,ZU:()=>J,xz:()=>I.x,Sk:()=>U.S,rO:()=>G.r,hN:()=>N.h,F4:()=>L.F,mp:()=>M.m,ko:()=>X,ZC:()=>n.Z,Nq:()=>s.N,BB:()=>v,AD:()=>b,St:()=>y,OE:()=>T.O,D7:()=>R.D,_0:()=>A._,K7:()=>z.K,aq:()=>q.a,lt:()=>$.l,rL:()=>W.r});var n=o(64799),s=o(11341),i=o(81284),r=o(75782),a=o(61988),c=o(68789);const l={path:"/consent",method:c.RouteHttpVerb.POST,obfuscatePath:"keep-last-part"},d={path:"/consent",method:c.RouteHttpVerb.GET,obfuscatePath:"keep-last-part"};var p=o(89552),u=o(38879),h=o(64047),g=o(35070);const y={path:"/stats/main",method:c.RouteHttpVerb.GET},b={path:"/stats/customBypass",method:c.RouteHttpVerb.GET},v={path:"/stats/buttonsClicked",method:c.RouteHttpVerb.GET};var m=o(62434),f=o(60789);const S={path:"/consent/clear",method:c.RouteHttpVerb.DELETE,obfuscatePath:"keep-last-part"};var C=o(52135),k=o(80106),x=o(53109),j=o(14819),w=o(12975),_=o(32991),O=o(16299),P=o(84573),R=o(6775),A=o(83240),T=o(57578);const F={path:"/consent/dynamic-predecision",method:c.RouteHttpVerb.POST,obfuscatePath:"keep-last-part"};var E=o(56372),B=o(23110),D=o(93445),I=o(11437),M=o(50306),G=o(44349),N=o(88291),U=o(68068),L=o(40976),V=o(24821),H=o(33492),q=o(72229),z=o(81472),W=o(6659),$=o(64841),Y=o(46504);const J={path:"/revision/second-view",method:c.RouteHttpVerb.GET,obfuscatePath:"keep-last-part"};var K=o(44175),Z=o(98036);const X={path:"/scanner/scan-without-login",namespace:"real-cookie-banner/v1",method:c.RouteHttpVerb.GET}},93445:(e,t,o)=>{o.d(t,{Z:()=>n});const n={path:"/migration/:migration",method:o(68789).RouteHttpVerb.DELETE}},23110:(e,t,o)=>{o.d(t,{a:()=>n});const n={path:"/migration/:migration/:action",method:o(68789).RouteHttpVerb.POST}},33492:(e,t,o)=>{o.d(t,{o:()=>n});const n={path:"/nav-menu/add-links",method:o(68789).RouteHttpVerb.POST}},81284:(e,t,o)=>{o.d(t,{K:()=>n});const n={path:"/presets/banner",method:o(68789).RouteHttpVerb.GET}},16299:(e,t,o)=>{o.d(t,{F:()=>n});const n={path:"/revision/:hash",method:o(68789).RouteHttpVerb.GET}},89552:(e,t,o)=>{o.d(t,{q:()=>n});const n={path:"/revision/current",method:o(68789).RouteHttpVerb.GET}},38879:(e,t,o)=>{o.d(t,{D:()=>n});const n={path:"/revision/current",method:o(68789).RouteHttpVerb.PUT}},84573:(e,t,o)=>{o.d(t,{$:()=>n});const n={path:"/revision/independent/:hash",method:o(68789).RouteHttpVerb.GET}},11437:(e,t,o)=>{o.d(t,{x:()=>n});const n={path:"/scanner/queue",method:o(68789).RouteHttpVerb.POST}},88291:(e,t,o)=>{o.d(t,{h:()=>n});const n={path:"/scanner/result/ignore",method:o(68789).RouteHttpVerb.POST}},68068:(e,t,o)=>{o.d(t,{S:()=>n});const n={path:"/scanner/result/externals/:type/:identifier",method:o(68789).RouteHttpVerb.GET,obfuscatePath:"full"}},44349:(e,t,o)=>{o.d(t,{r:()=>n});const n={path:"/scanner/result/externals",method:o(68789).RouteHttpVerb.GET}},40976:(e,t,o)=>{o.d(t,{F:()=>n});const n={path:"/scanner/result/markup/:id",method:o(68789).RouteHttpVerb.GET}},50306:(e,t,o)=>{o.d(t,{m:()=>n});const n={path:"/scanner/result/templates",method:o(68789).RouteHttpVerb.GET}},64799:(e,t,o)=>{o.d(t,{Z:()=>n});const n={path:"/settings",method:o(68789).RouteHttpVerb.GET}},11341:(e,t,o)=>{o.d(t,{N:()=>n});const n={path:"/settings",method:o(68789).RouteHttpVerb.PATCH}},57578:(e,t,o)=>{o.d(t,{O:()=>n});const n={path:"/tcf/declarations",method:o(68789).RouteHttpVerb.GET}},6775:(e,t,o)=>{o.d(t,{D:()=>n});const n={path:"/tcf/gvl",method:o(68789).RouteHttpVerb.PUT}},83240:(e,t,o)=>{o.d(t,{_:()=>n});const n={path:"/tcf/vendors",method:o(68789).RouteHttpVerb.GET}},72229:(e,t,o)=>{o.d(t,{a:()=>n});const n={path:"/templates/blocker",method:o(68789).RouteHttpVerb.GET}},81472:(e,t,o)=>{o.d(t,{K:()=>n});const n={path:"/templates/blocker/:identifier",method:o(68789).RouteHttpVerb.GET,obfuscatePath:"full"}},64841:(e,t,o)=>{o.d(t,{l:()=>n});const n={path:"/templates/services/:identifier",method:o(68789).RouteHttpVerb.GET,obfuscatePath:"full"}},6659:(e,t,o)=>{o.d(t,{r:()=>n});const n={path:"/templates/services",method:o(68789).RouteHttpVerb.GET}},2077:()=>{},56719:()=>{},41594:e=>{e.exports=React},75206:e=>{e.exports=ReactDOM},3713:e=>{e.exports=ReactJSXRuntime},59627:e=>{e.exports=devowlWp_customize},37218:e=>{e.exports=devowlWp_realProductManagerWpClient},14383:e=>{e.exports=devowlWp_realQueue},68789:e=>{e.exports=devowlWp_utils},41669:e=>{e.exports=jQuery},44497:e=>{e.exports=mobx},38123:e=>{e.exports=moment},43244:e=>{e.exports=wp}},r={};function a(e){var t=r[e];if(void 0!==t)return t.exports;var o=r[e]={exports:{}};return i[e].call(o.exports,o,o.exports,a),o.exports}a.m=i,e=[],a.O=(t,o,n,s)=>{if(!o){var i=1/0;for(d=0;d<e.length;d++){for(var[o,n,s]=e[d],r=!0,c=0;c<o.length;c++)(!1&s||i>=s)&&Object.keys(a.O).every((e=>a.O[e](o[c])))?o.splice(c--,1):(r=!1,s<i&&(i=s));if(r){e.splice(d--,1);var l=n();void 0!==l&&(t=l)}}return t}s=s||0;for(var d=e.length;d>0&&e[d-1][2]>s;d--)e[d]=e[d-1];e[d]=[o,n,s]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,a.t=function(e,n){if(1&n&&(e=this(e)),8&n)return e;if("object"==typeof e&&e){if(4&n&&e.__esModule)return e;if(16&n&&"function"==typeof e.then)return e}var s=Object.create(null);a.r(s);var i={};t=t||[null,o({}),o([]),o(o)];for(var r=2&n&&e;"object"==typeof r&&!~t.indexOf(r);r=o(r))Object.getOwnPropertyNames(r).forEach((t=>i[t]=()=>e[t]));return i.default=()=>e,a.d(s,i),s},a.d=(e,t)=>{for(var o in t)a.o(t,o)&&!a.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},a.f={},a.e=e=>Promise.all(Object.keys(a.f).reduce(((t,o)=>(a.f[o](e,t),t)),[])),a.u=e=>(({38:"chunk-config-tab-consent",40:"cookie-policy",79:"chunk-config-tab-settings",349:"chunk-config-tab-cookies",362:"chunk-config-tab-scanner",462:"chunk-config-tab-blocker",508:"chunk-config-tab-licensing",655:"chunk-config-tab-import",668:"chunk-config-tab-dashboard",763:"chunk-config-tab-tcf"}[e]||e)+".lite.js?ver="+{38:"8aba1f9998ff7e98",40:"34246544b898bf20",55:"89a513430ffdf366",79:"da969235112c13f5",97:"d8450b9f3c24c304",108:"1eef962bba826977",134:"5af8cc3e1dc4d607",147:"9b5d3829511008e1",171:"1a36003aaa5b6cb9",173:"c8db08f9ea914a91",189:"6837b37447d4c4b8",221:"d2848be78beba418",227:"eb91502fb5aa9e2c",230:"aeed1398c97c429e",241:"d6bd6d9606572f58",276:"b5545b915a6884ca",287:"703eb90f37a225f9",349:"91a676450c44079f",362:"e45dc0c8bd991bd8",386:"c950408f43c3d016",462:"6bd1c0ac1e00e6ad",493:"0bc78570c33b559b",502:"2565c7f675693c51",508:"9a9532e8ee159f38",645:"d9163b106421300d",650:"4fe101c4439894b9",655:"932ae277519c1ac6",668:"db5c397c0105f90c",716:"811e7f67102f27b4",731:"9645f450a460401e",763:"f49cd695ed866bf6",799:"25f4453d853db92d",859:"088549ab2f5d76b0",924:"4d90391c2b2f01f6",985:"22b284346519ffb0"}[e]),a.miniCssF=e=>{},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n={},s="realCookieBanner_:",a.l=(e,t,o,i)=>{if(n[e])n[e].push(t);else{var r,c;if(void 0!==o)for(var l=document.getElementsByTagName("script"),d=0;d<l.length;d++){var p=l[d];if(p.getAttribute("src")==e||p.getAttribute("data-webpack")==s+o){r=p;break}}r||(c=!0,(r=document.createElement("script")).charset="utf-8",r.timeout=120,a.nc&&r.setAttribute("nonce",a.nc),r.setAttribute("data-webpack",s+o),r.src=e),n[e]=[t];var u=(t,o)=>{r.onerror=r.onload=null,clearTimeout(h);var s=n[e];if(delete n[e],r.parentNode&&r.parentNode.removeChild(r),s&&s.forEach((e=>e(o))),t)return t(o)},h=setTimeout(u.bind(null,void 0,{type:"timeout",target:r}),12e4);r.onerror=u.bind(null,r.onerror),r.onload=u.bind(null,r.onload),c&&document.head.appendChild(r)}},a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;a.g.importScripts&&(e=a.g.location+"");var t=a.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var o=t.getElementsByTagName("script");if(o.length)for(var n=o.length-1;n>-1&&(!e||!/^http(s?):/.test(e));)e=o[n--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),a.p=e})(),(()=>{var e={884:0};a.f.j=(t,o)=>{var n=a.o(e,t)?e[t]:void 0;if(0!==n)if(n)o.push(n[2]);else{var s=new Promise(((o,s)=>n=e[t]=[o,s]));o.push(n[2]=s);var i=a.p+a.u(t),r=new Error;a.l(i,(o=>{if(a.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var s=o&&("load"===o.type?"missing":o.type),i=o&&o.target&&o.target.src;r.message="Loading chunk "+t+" failed.\n("+s+": "+i+")",r.name="ChunkLoadError",r.type=s,r.request=i,n[1](r)}}),"chunk-"+t,t)}},a.O.j=t=>0===e[t];var t=(t,o)=>{var n,s,[i,r,c]=o,l=0;if(i.some((t=>0!==e[t]))){for(n in r)a.o(r,n)&&(a.m[n]=r[n]);if(c)var d=c(a)}for(t&&t(o);l<i.length;l++)s=i[l],a.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return a.O(d)},o=self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var c=a.O(void 0,[187],(()=>a(21014)));c=a.O(c),realCookieBanner_admin=c})();
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/a8589fd50c780c207aa7bf087b2f27c0/admin.lite.js.map
