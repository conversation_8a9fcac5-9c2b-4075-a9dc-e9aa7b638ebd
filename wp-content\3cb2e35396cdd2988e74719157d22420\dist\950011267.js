"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[171],{33210:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(2464),o=n(41594);const i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};var a=n(4679),l=function(e,t){return o.createElement(a.A,(0,r.A)({},e,{ref:t,icon:i}))};const s=o.forwardRef(l)},57344:(e,t,n)=>{n.d(t,{Mp:()=>Te,vL:()=>X,Sj:()=>E,fF:()=>Be,PM:()=>ze,zM:()=>Ke});var r=n(41594),o=n.n(r),i=n(75206),a=n(68959);const l={display:"none"};function s(e){let{id:t,value:n}=e;return o().createElement("div",{id:t,style:l},n)}function c(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return o().createElement("div",{id:t,style:{position:"fixed",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}const u=(0,r.createContext)(null),d={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},f={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function v(e){let{announcements:t=f,container:n,hiddenTextDescribedById:l,screenReaderInstructions:v=d}=e;const{announce:h,announcement:g}=function(){const[e,t]=(0,r.useState)("");return{announce:(0,r.useCallback)((e=>{null!=e&&t(e)}),[]),announcement:e}}(),p=(0,a.YG)("DndLiveRegion"),[b,m]=(0,r.useState)(!1);if((0,r.useEffect)((()=>{m(!0)}),[]),function(e){const t=(0,r.useContext)(u);(0,r.useEffect)((()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)}),[e,t])}((0,r.useMemo)((()=>({onDragStart(e){let{active:n}=e;h(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&h(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;h(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;h(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;h(t.onDragCancel({active:n,over:r}))}})),[h,t])),!b)return null;const y=o().createElement(o().Fragment,null,o().createElement(s,{id:l,value:v.draggable}),o().createElement(c,{id:p,announcement:g}));return n?(0,i.createPortal)(y,n):y}var h;function g(){}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(h||(h={}));const p=Object.freeze({x:0,y:0});function b(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function m(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),o=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height),a=o-r,l=i-n;if(r<o&&n<i){const n=t.width*t.height,r=e.width*e.height,o=a*l;return Number((o/(n+r-o)).toFixed(4))}return 0}const y=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const e of r){const{id:r}=e,i=n.get(r);if(i){const n=m(i,t);n>0&&o.push({id:r,data:{droppableContainer:e,value:n}})}}return o.sort(b)};function w(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:p}function x(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x})),{...t})}}const C=x(1);const D={ignoreTransform:!1};function E(e,t){void 0===t&&(t=D);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:r}=(0,a.zk)(e).getComputedStyle(e);t&&(n=function(e,t,n){const r=function(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;const{scaleX:o,scaleY:i,x:a,y:l}=r,s=e.left-a-(1-o)*parseFloat(n),c=e.top-l-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),u=o?e.width/o:e.width,d=i?e.height/i:e.height;return{width:u,height:d,top:c,right:s+u,bottom:c+d,left:s}}(n,t,r))}const{top:r,left:o,width:i,height:l,bottom:s,right:c}=n;return{top:r,left:o,width:i,height:l,bottom:s,right:c}}function R(e){return E(e,{ignoreTransform:!0})}function S(e,t){const n=[];return e?function r(o){if(null!=t&&n.length>=t)return n;if(!o)return n;if((0,a.wz)(o)&&null!=o.scrollingElement&&!n.includes(o.scrollingElement))return n.push(o.scrollingElement),n;if(!(0,a.sb)(o)||(0,a.xZ)(o))return n;if(n.includes(o))return n;const i=(0,a.zk)(e).getComputedStyle(o);return o!==e&&function(e,t){void 0===t&&(t=(0,a.zk)(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some((e=>{const r=t[e];return"string"==typeof r&&n.test(r)}))}(o,i)&&n.push(o),function(e,t){return void 0===t&&(t=(0,a.zk)(e).getComputedStyle(e)),"fixed"===t.position}(o,i)?n:r(o.parentNode)}(e):n}function M(e){const[t]=S(e,1);return null!=t?t:null}function k(e){return a.Sw&&e?(0,a.l6)(e)?e:(0,a.Ll)(e)?(0,a.wz)(e)||e===(0,a.TW)(e).scrollingElement?window:(0,a.sb)(e)?e:null:null:null}function N(e){return(0,a.l6)(e)?e.scrollX:e.scrollLeft}function I(e){return(0,a.l6)(e)?e.scrollY:e.scrollTop}function T(e){return{x:N(e),y:I(e)}}var O;function L(e){return!(!a.Sw||!e)&&e===document.scrollingElement}function A(e){const t={x:0,y:0},n=L(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};return{isTop:e.scrollTop<=t.y,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(O||(O={}));const z={x:.2,y:.2};function B(e,t,n,r,o){let{top:i,left:a,right:l,bottom:s}=n;void 0===r&&(r=10),void 0===o&&(o=z);const{isTop:c,isBottom:u,isLeft:d,isRight:f}=A(e),v={x:0,y:0},h={x:0,y:0},g=t.height*o.y,p=t.width*o.x;return!c&&i<=t.top+g?(v.y=O.Backward,h.y=r*Math.abs((t.top+g-i)/g)):!u&&s>=t.bottom-g&&(v.y=O.Forward,h.y=r*Math.abs((t.bottom-g-s)/g)),!f&&l>=t.right-p?(v.x=O.Forward,h.x=r*Math.abs((t.right-p-l)/p)):!d&&a<=t.left+p&&(v.x=O.Backward,h.x=r*Math.abs((t.left+p-a)/p)),{direction:v,speed:h}}function Y(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function W(e){return e.reduce(((e,t)=>(0,a.WQ)(e,T(t))),p)}const K=[["x",["left","right"],function(e){return e.reduce(((e,t)=>e+N(t)),0)}],["y",["top","bottom"],function(e){return e.reduce(((e,t)=>e+I(t)),0)}]];class F{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const n=S(t),r=W(n);this.rect={...e},this.width=e.width,this.height=e.height;for(const[e,t,o]of K)for(const i of t)Object.defineProperty(this,i,{get:()=>{const t=o(n),a=r[e]-t;return this.rect[i]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class P{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function j(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}var U,X;function G(e){e.preventDefault()}function _(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(U||(U={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter"}(X||(X={}));const H={start:[X.Space,X.Enter],cancel:[X.Esc],end:[X.Space,X.Enter]},q=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case X.Right:return{...n,x:n.x+25};case X.Left:return{...n,x:n.x-25};case X.Down:return{...n,y:n.y+25};case X.Up:return{...n,y:n.y-25}}};class Q{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;const{event:{target:t}}=e;this.props=e,this.listeners=new P((0,a.TW)(t)),this.windowListeners=new P((0,a.zk)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(U.Resize,this.handleCancel),this.windowListeners.add(U.VisibilityChange,this.handleCancel),setTimeout((()=>this.listeners.add(U.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=E),!e)return;const{top:n,left:r,bottom:o,right:i}=t(e);M(e)&&(o<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(p)}handleKeyDown(e){if((0,a.kx)(e)){const{active:t,context:n,options:r}=this.props,{keyboardCodes:o=H,coordinateGetter:i=q,scrollBehavior:l="smooth"}=r,{code:s}=e;if(o.end.includes(s))return void this.handleEnd(e);if(o.cancel.includes(s))return void this.handleCancel(e);const{collisionRect:c}=n.current,u=c?{x:c.left,y:c.top}:p;this.referenceCoordinates||(this.referenceCoordinates=u);const d=i(e,{active:t,context:n.current,currentCoordinates:u});if(d){const t=(0,a.Re)(d,u),r={x:0,y:0},{scrollableAncestors:o}=n.current;for(const n of o){const o=e.code,{isTop:i,isRight:a,isLeft:s,isBottom:c,maxScroll:u,minScroll:f}=A(n),v=Y(n),h={x:Math.min(o===X.Right?v.right-v.width/2:v.right,Math.max(o===X.Right?v.left:v.left+v.width/2,d.x)),y:Math.min(o===X.Down?v.bottom-v.height/2:v.bottom,Math.max(o===X.Down?v.top:v.top+v.height/2,d.y))},g=o===X.Right&&!a||o===X.Left&&!s,p=o===X.Down&&!c||o===X.Up&&!i;if(g&&h.x!==d.x){const e=n.scrollLeft+t.x,i=o===X.Right&&e<=u.x||o===X.Left&&e>=f.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:l});r.x=i?n.scrollLeft-e:o===X.Right?n.scrollLeft-u.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:l});break}if(p&&h.y!==d.y){const e=n.scrollTop+t.y,i=o===X.Down&&e<=u.y||o===X.Up&&e>=f.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:l});r.y=i?n.scrollTop-e:o===X.Down?n.scrollTop-u.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:l});break}}this.handleMove(e,(0,a.WQ)((0,a.Re)(d,this.referenceCoordinates),r))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function J(e){return Boolean(e&&"distance"in e)}function V(e){return Boolean(e&&"delay"in e)}Q.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=H,onActivation:o}=t,{active:i}=n;const{code:a}=e.nativeEvent;if(r.start.includes(a)){const t=i.activatorNode.current;return!(t&&e.target!==t||(e.preventDefault(),null==o||o({event:e.nativeEvent}),0))}return!1}}];class Z{constructor(e,t,n){var r;void 0===n&&(n=function(e){const{EventTarget:t}=(0,a.zk)(e);return e instanceof t?e:(0,a.TW)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:o}=e,{target:i}=o;this.props=e,this.events=t,this.document=(0,a.TW)(i),this.documentListeners=new P(this.document),this.listeners=new P(n),this.windowListeners=new P((0,a.zk)(i)),this.initialCoordinates=null!=(r=(0,a.e_)(o))?r:p,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(U.Resize,this.handleCancel),this.windowListeners.add(U.DragStart,G),this.windowListeners.add(U.VisibilityChange,this.handleCancel),this.windowListeners.add(U.ContextMenu,G),this.documentListeners.add(U.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(V(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(J(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(U.Click,_,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(U.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:o}=this,{onMove:i,options:{activationConstraint:l}}=o;if(!r)return;const s=null!=(t=(0,a.e_)(e))?t:p,c=(0,a.Re)(r,s);if(!n&&l){if(J(l)){if(null!=l.tolerance&&j(c,l.tolerance))return this.handleCancel();if(j(c,l.distance))return this.handleStart()}return V(l)&&j(c,l.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),i(s)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===X.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const $={move:{name:"pointermove"},end:{name:"pointerup"}};class ee extends Z{constructor(e){const{event:t}=e,n=(0,a.TW)(t.target);super(e,$,n)}}ee.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!(!n.isPrimary||0!==n.button||(null==r||r({event:n}),0))}}];const te={move:{name:"mousemove"},end:{name:"mouseup"}};var ne;!function(e){e[e.RightClick=2]="RightClick"}(ne||(ne={})),class extends Z{constructor(e){super(e,te,(0,a.TW)(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==ne.RightClick&&(null==r||r({event:n}),!0)}}];const re={move:{name:"touchmove"},end:{name:"touchend"}};var oe,ie;(class extends Z{constructor(e){super(e,re)}static setup(){return window.addEventListener(re.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(re.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:o}=n;return!(o.length>1||(null==r||r({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(oe||(oe={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(ie||(ie={}));const ae={x:{[O.Backward]:!1,[O.Forward]:!1},y:{[O.Backward]:!1,[O.Forward]:!1}};var le,se;!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(le||(le={})),function(e){e.Optimized="optimized"}(se||(se={}));const ce=new Map;function ue(e,t){return(0,a.KG)((n=>e?n||("function"==typeof t?t(e):e):null),[t,e])}function de(e){let{callback:t,disabled:n}=e;const o=(0,a._q)(t),i=(0,r.useMemo)((()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;const{ResizeObserver:e}=window;return new e(o)}),[n]);return(0,r.useEffect)((()=>()=>null==i?void 0:i.disconnect()),[i]),i}function fe(e){return new F(E(e),e)}function ve(e,t,n){void 0===t&&(t=fe);const[o,i]=(0,r.useReducer)((function(r){if(!e)return null;var o;if(!1===e.isConnected)return null!=(o=null!=r?r:n)?o:null;const i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i}),null),l=function(e){let{callback:t,disabled:n}=e;const o=(0,a._q)(t),i=(0,r.useMemo)((()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;const{MutationObserver:e}=window;return new e(o)}),[o,n]);return(0,r.useEffect)((()=>()=>null==i?void 0:i.disconnect()),[i]),i}({callback(t){if(e)for(const n of t){const{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),s=de({callback:i});return(0,a.Es)((()=>{i(),e?(null==s||s.observe(e),null==l||l.observe(document.body,{childList:!0,subtree:!0})):(null==s||s.disconnect(),null==l||l.disconnect())}),[e]),o}const he=[];function ge(e,t){void 0===t&&(t=[]);const n=(0,r.useRef)(null);return(0,r.useEffect)((()=>{n.current=null}),t),(0,r.useEffect)((()=>{const t=e!==p;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)}),[e]),n.current?(0,a.Re)(e,n.current):p}function pe(e){return(0,r.useMemo)((()=>e?function(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null),[e])}const be=[];const me=[{sensor:ee,options:{}},{sensor:Q,options:{}}],ye={current:{}},we={draggable:{measure:R},droppable:{measure:R,strategy:le.WhileDragging,frequency:se.Optimized},dragOverlay:{measure:E}};class xe extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}const Ce={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new xe,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:g},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:we,measureDroppableContainers:g,windowRect:null,measuringScheduled:!1},De={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:g,draggableNodes:new Map,over:null,measureDroppableContainers:g},Ee=(0,r.createContext)(De),Re=(0,r.createContext)(Ce);function Se(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new xe}}}function Me(e,t){switch(t.type){case h.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case h.DragMove:return e.draggable.active?{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}}:e;case h.DragEnd:case h.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case h.RegisterDroppable:{const{element:n}=t,{id:r}=n,o=new xe(e.droppable.containers);return o.set(r,n),{...e,droppable:{...e.droppable,containers:o}}}case h.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;const a=new xe(e.droppable.containers);return a.set(n,{...i,disabled:o}),{...e,droppable:{...e.droppable,containers:a}}}case h.UnregisterDroppable:{const{id:n,key:r}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const i=new xe(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function ke(e){let{disabled:t}=e;const{active:n,activatorEvent:o,draggableNodes:i}=(0,r.useContext)(Ee),l=(0,a.ZC)(o),s=(0,a.ZC)(null==n?void 0:n.id);return(0,r.useEffect)((()=>{if(!t&&!o&&l&&null!=s){if(!(0,a.kx)(l))return;if(document.activeElement===l.target)return;const e=i.get(s);if(!e)return;const{activatorNode:t,node:n}=e;if(!t.current&&!n.current)return;requestAnimationFrame((()=>{for(const e of[t.current,n.current]){if(!e)continue;const t=(0,a.ag)(e);if(t){t.focus();break}}}))}}),[o,t,i,s,l]),null}const Ne=(0,r.createContext)({...p,scaleX:1,scaleY:1});var Ie;!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(Ie||(Ie={}));const Te=(0,r.memo)((function(e){var t,n,l,s;let{id:c,accessibility:d,autoScroll:f=!0,children:g,sensors:b=me,collisionDetection:m=y,measuring:x,modifiers:D,...R}=e;const N=(0,r.useReducer)(Me,void 0,Se),[I,A]=N,[z,Y]=function(){const[e]=(0,r.useState)((()=>new Set)),t=(0,r.useCallback)((t=>(e.add(t),()=>e.delete(t))),[e]);return[(0,r.useCallback)((t=>{let{type:n,event:r}=t;e.forEach((e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)}))}),[e]),t]}(),[K,P]=(0,r.useState)(Ie.Uninitialized),j=K===Ie.Initialized,{draggable:{active:U,nodes:X,translate:G},droppable:{containers:_}}=I,H=U?X.get(U):null,q=(0,r.useRef)({initial:null,translated:null}),Q=(0,r.useMemo)((()=>{var e;return null!=U?{id:U,data:null!=(e=null==H?void 0:H.data)?e:ye,rect:q}:null}),[U,H]),J=(0,r.useRef)(null),[V,Z]=(0,r.useState)(null),[$,ee]=(0,r.useState)(null),te=(0,a.YN)(R,Object.values(R)),ne=(0,a.YG)("DndDescribedBy",c),re=(0,r.useMemo)((()=>_.getEnabled()),[_]),se=(fe=x,(0,r.useMemo)((()=>({draggable:{...we.draggable,...null==fe?void 0:fe.draggable},droppable:{...we.droppable,...null==fe?void 0:fe.droppable},dragOverlay:{...we.dragOverlay,...null==fe?void 0:fe.dragOverlay}})),[null==fe?void 0:fe.draggable,null==fe?void 0:fe.droppable,null==fe?void 0:fe.dragOverlay]));var fe;const{droppableRects:xe,measureDroppableContainers:Ce,measuringScheduled:De}=function(e,t){let{dragging:n,dependencies:o,config:i}=t;const[l,s]=(0,r.useState)(null),{frequency:c,measure:u,strategy:d}=i,f=(0,r.useRef)(e),v=function(){switch(d){case le.Always:return!1;case le.BeforeDragging:return n;default:return!n}}(),h=(0,a.YN)(v),g=(0,r.useCallback)((function(e){void 0===e&&(e=[]),h.current||s((t=>null===t?e:t.concat(e.filter((e=>!t.includes(e))))))}),[h]),p=(0,r.useRef)(null),b=(0,a.KG)((t=>{if(v&&!n)return ce;if(!t||t===ce||f.current!==e||null!=l){const t=new Map;for(let n of e){if(!n)continue;if(l&&l.length>0&&!l.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}const e=n.node.current,r=e?new F(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t}),[e,l,n,v,u]);return(0,r.useEffect)((()=>{f.current=e}),[e]),(0,r.useEffect)((()=>{v||g()}),[n,v]),(0,r.useEffect)((()=>{l&&l.length>0&&s(null)}),[JSON.stringify(l)]),(0,r.useEffect)((()=>{v||"number"!=typeof c||null!==p.current||(p.current=setTimeout((()=>{g(),p.current=null}),c))}),[c,v,g,...o]),{droppableRects:b,measureDroppableContainers:g,measuringScheduled:null!=l}}(re,{dragging:j,dependencies:[G.x,G.y],config:se.droppable}),Te=function(e,t){const n=null!==t?e.get(t):void 0,r=n?n.node.current:null;return(0,a.KG)((e=>{var n;return null===t?null:null!=(n=null!=r?r:e)?n:null}),[r,t])}(X,U),Oe=(0,r.useMemo)((()=>$?(0,a.e_)($):null),[$]),Le=function(){const e=!1===(null==V?void 0:V.autoScrollEnabled),t="object"==typeof f?!1===f.enabled:!1===f,n=j&&!e&&!t;return"object"==typeof f?{...f,enabled:n}:{enabled:n}}(),Ae=function(e,t){return ue(e,t)}(Te,se.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:o,config:i=!0}=e;const l=(0,r.useRef)(!1),{x:s,y:c}="boolean"==typeof i?{x:i,y:i}:i;(0,a.Es)((()=>{if(!s&&!c||!t)return void(l.current=!1);if(l.current||!o)return;const e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;const r=w(n(e),o);if(s||(r.x=0),c||(r.y=0),l.current=!0,Math.abs(r.x)>0||Math.abs(r.y)>0){const t=M(e);t&&t.scrollBy({top:r.y,left:r.x})}}),[t,s,c,o,n])}({activeNode:U?X.get(U):null,config:Le.layoutShiftCompensation,initialRect:Ae,measure:se.draggable.measure});const ze=ve(Te,se.draggable.measure,Ae),Be=ve(Te?Te.parentElement:null),Ye=(0,r.useRef)({activatorEvent:null,active:null,activeNode:Te,collisionRect:null,collisions:null,droppableRects:xe,draggableNodes:X,draggingNode:null,draggingNodeRect:null,droppableContainers:_,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),We=_.getNodeFor(null==(t=Ye.current.over)?void 0:t.id),Ke=function(e){let{measure:t}=e;const[n,o]=(0,r.useState)(null),i=de({callback:(0,r.useCallback)((e=>{for(const{target:n}of e)if((0,a.sb)(n)){o((e=>{const r=t(n);return e?{...e,width:r.width,height:r.height}:r}));break}}),[t])}),l=(0,r.useCallback)((e=>{const n=function(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return(0,a.sb)(t)?t:e}(e);null==i||i.disconnect(),n&&(null==i||i.observe(n)),o(n?t(n):null)}),[t,i]),[s,c]=(0,a.lk)(l);return(0,r.useMemo)((()=>({nodeRef:s,rect:n,setRef:c})),[n,s,c])}({measure:se.dragOverlay.measure}),Fe=null!=(n=Ke.nodeRef.current)?n:Te,Pe=j?null!=(l=Ke.rect)?l:ze:null,je=Boolean(Ke.nodeRef.current&&Ke.rect),Ue=w(Xe=je?null:ze,ue(Xe));var Xe;const Ge=pe(Fe?(0,a.zk)(Fe):null),_e=function(e){const t=(0,r.useRef)(e),n=(0,a.KG)((n=>e?n&&n!==he&&e&&t.current&&e.parentNode===t.current.parentNode?n:S(e):he),[e]);return(0,r.useEffect)((()=>{t.current=e}),[e]),n}(j?null!=We?We:Te:null),He=function(e,t){void 0===t&&(t=E);const[n]=e,o=pe(n?(0,a.zk)(n):null),[i,l]=(0,r.useReducer)((function(){return e.length?e.map((e=>L(e)?o:new F(t(e),e))):be}),be),s=de({callback:l});return e.length>0&&i===be&&l(),(0,a.Es)((()=>{e.length?e.forEach((e=>null==s?void 0:s.observe(e))):(null==s||s.disconnect(),l())}),[e]),i}(_e),qe=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce(((e,t)=>t({transform:e,...r})),n):n}(D,{transform:{x:G.x-Ue.x,y:G.y-Ue.y,scaleX:1,scaleY:1},activatorEvent:$,active:Q,activeNodeRect:ze,containerNodeRect:Be,draggingNodeRect:Pe,over:Ye.current.over,overlayNodeRect:Ke.rect,scrollableAncestors:_e,scrollableAncestorRects:He,windowRect:Ge}),Qe=Oe?(0,a.WQ)(Oe,G):null,Je=function(e){const[t,n]=(0,r.useState)(null),o=(0,r.useRef)(e),i=(0,r.useCallback)((e=>{const t=k(e.target);t&&n((e=>e?(e.set(t,T(t)),new Map(e)):null))}),[]);return(0,r.useEffect)((()=>{const t=o.current;if(e!==t){r(t);const a=e.map((e=>{const t=k(e);return t?(t.addEventListener("scroll",i,{passive:!0}),[t,T(t)]):null})).filter((e=>null!=e));n(a.length?new Map(a):null),o.current=e}return()=>{r(e),r(t)};function r(e){e.forEach((e=>{const t=k(e);null==t||t.removeEventListener("scroll",i)}))}}),[i,e]),(0,r.useMemo)((()=>e.length?t?Array.from(t.values()).reduce(((e,t)=>(0,a.WQ)(e,t)),p):W(e):p),[e,t])}(_e),Ve=ge(Je),Ze=ge(Je,[ze]),$e=(0,a.WQ)(qe,Ve),et=Pe?C(Pe,qe):null,tt=Q&&et?m({active:Q,collisionRect:et,droppableRects:xe,droppableContainers:re,pointerCoordinates:Qe}):null,nt=function(e,t){if(!e||0===e.length)return null;const[n]=e;return n.id}(tt),[rt,ot]=(0,r.useState)(null),it=function(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}(je?qe:(0,a.WQ)(qe,Ze),null!=(s=null==rt?void 0:rt.rect)?s:null,ze),at=(0,r.useCallback)(((e,t)=>{let{sensor:n,options:r}=t;if(null==J.current)return;const o=X.get(J.current);if(!o)return;const a=e.nativeEvent,l=new n({active:J.current,activeNode:o,event:a,options:r,context:Ye,onStart(e){const t=J.current;if(null==t)return;const n=X.get(t);if(!n)return;const{onDragStart:r}=te.current,o={active:{id:t,data:n.data,rect:q}};(0,i.unstable_batchedUpdates)((()=>{null==r||r(o),P(Ie.Initializing),A({type:h.DragStart,initialCoordinates:e,active:t}),z({type:"onDragStart",event:o})}))},onMove(e){A({type:h.DragMove,coordinates:e})},onEnd:s(h.DragEnd),onCancel:s(h.DragCancel)});function s(e){return async function(){const{active:t,collisions:n,over:r,scrollAdjustedTranslate:o}=Ye.current;let l=null;if(t&&o){const{cancelDrop:i}=te.current;l={activatorEvent:a,active:t,collisions:n,delta:o,over:r},e===h.DragEnd&&"function"==typeof i&&await Promise.resolve(i(l))&&(e=h.DragCancel)}J.current=null,(0,i.unstable_batchedUpdates)((()=>{A({type:e}),P(Ie.Uninitialized),ot(null),Z(null),ee(null);const t=e===h.DragEnd?"onDragEnd":"onDragCancel";if(l){const e=te.current[t];null==e||e(l),z({type:t,event:l})}}))}}(0,i.unstable_batchedUpdates)((()=>{Z(l),ee(e.nativeEvent)}))}),[X]),lt=(0,r.useCallback)(((e,t)=>(n,r)=>{const o=n.nativeEvent,i=X.get(r);if(null!==J.current||!i||o.dndKit||o.defaultPrevented)return;const a={active:i};!0===e(n,t.options,a)&&(o.dndKit={capturedBy:t.sensor},J.current=r,at(n,t))}),[X,at]),st=function(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{const{sensor:r}=n;return[...e,...r.activators.map((e=>({eventName:e.eventName,handler:t(e.handler,n)})))]}),[])),[e,t])}(b,lt);!function(e){(0,r.useEffect)((()=>{if(!a.Sw)return;const t=e.map((e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()}));return()=>{for(const e of t)null==e||e()}}),e.map((e=>{let{sensor:t}=e;return t})))}(b),(0,a.Es)((()=>{ze&&K===Ie.Initializing&&P(Ie.Initialized)}),[ze,K]),(0,r.useEffect)((()=>{const{onDragMove:e}=te.current,{active:t,activatorEvent:n,collisions:r,over:o}=Ye.current;if(!t||!n)return;const a={active:t,activatorEvent:n,collisions:r,delta:{x:$e.x,y:$e.y},over:o};(0,i.unstable_batchedUpdates)((()=>{null==e||e(a),z({type:"onDragMove",event:a})}))}),[$e.x,$e.y]),(0,r.useEffect)((()=>{const{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:o}=Ye.current;if(!e||null==J.current||!t||!o)return;const{onDragOver:a}=te.current,l=r.get(nt),s=l&&l.rect.current?{id:l.id,rect:l.rect.current,data:l.data,disabled:l.disabled}:null,c={active:e,activatorEvent:t,collisions:n,delta:{x:o.x,y:o.y},over:s};(0,i.unstable_batchedUpdates)((()=>{ot(s),null==a||a(c),z({type:"onDragOver",event:c})}))}),[nt]),(0,a.Es)((()=>{Ye.current={activatorEvent:$,active:Q,activeNode:Te,collisionRect:et,collisions:tt,droppableRects:xe,draggableNodes:X,draggingNode:Fe,draggingNodeRect:Pe,droppableContainers:_,over:rt,scrollableAncestors:_e,scrollAdjustedTranslate:$e},q.current={initial:Pe,translated:et}}),[Q,Te,tt,et,X,Fe,Pe,xe,_,rt,_e,$e]),function(e){let{acceleration:t,activator:n=oe.Pointer,canScroll:o,draggingRect:i,enabled:l,interval:s=5,order:c=ie.TreeOrder,pointerCoordinates:u,scrollableAncestors:d,scrollableAncestorRects:f,delta:v,threshold:h}=e;const g=function(e){let{delta:t,disabled:n}=e;const r=(0,a.ZC)(t);return(0,a.KG)((e=>{if(n||!r||!e)return ae;const o=Math.sign(t.x-r.x),i=Math.sign(t.y-r.y);return{x:{[O.Backward]:e.x[O.Backward]||-1===o,[O.Forward]:e.x[O.Forward]||1===o},y:{[O.Backward]:e.y[O.Backward]||-1===i,[O.Forward]:e.y[O.Forward]||1===i}}}),[n,t,r])}({delta:v,disabled:!l}),[p,b]=(0,a.$$)(),m=(0,r.useRef)({x:0,y:0}),y=(0,r.useRef)({x:0,y:0}),w=(0,r.useMemo)((()=>{switch(n){case oe.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case oe.DraggableRect:return i}}),[n,i,u]),x=(0,r.useRef)(null),C=(0,r.useCallback)((()=>{const e=x.current;if(!e)return;const t=m.current.x*y.current.x,n=m.current.y*y.current.y;e.scrollBy(t,n)}),[]),D=(0,r.useMemo)((()=>c===ie.TreeOrder?[...d].reverse():d),[c,d]);(0,r.useEffect)((()=>{if(l&&d.length&&w){for(const e of D){if(!1===(null==o?void 0:o(e)))continue;const n=d.indexOf(e),r=f[n];if(!r)continue;const{direction:i,speed:a}=B(e,r,w,t,h);for(const e of["x","y"])g[e][i[e]]||(a[e]=0,i[e]=0);if(a.x>0||a.y>0)return b(),x.current=e,p(C,s),m.current=a,void(y.current=i)}m.current={x:0,y:0},y.current={x:0,y:0},b()}else b()}),[t,C,o,b,l,s,JSON.stringify(w),JSON.stringify(g),p,d,D,f,JSON.stringify(h)])}({...Le,delta:G,draggingRect:et,pointerCoordinates:Qe,scrollableAncestors:_e,scrollableAncestorRects:He});const ct=(0,r.useMemo)((()=>({active:Q,activeNode:Te,activeNodeRect:ze,activatorEvent:$,collisions:tt,containerNodeRect:Be,dragOverlay:Ke,draggableNodes:X,droppableContainers:_,droppableRects:xe,over:rt,measureDroppableContainers:Ce,scrollableAncestors:_e,scrollableAncestorRects:He,measuringConfiguration:se,measuringScheduled:De,windowRect:Ge})),[Q,Te,ze,$,tt,Be,Ke,X,_,xe,rt,Ce,_e,He,se,De,Ge]),ut=(0,r.useMemo)((()=>({activatorEvent:$,activators:st,active:Q,activeNodeRect:ze,ariaDescribedById:{draggable:ne},dispatch:A,draggableNodes:X,over:rt,measureDroppableContainers:Ce})),[$,st,Q,ze,A,ne,X,rt,Ce]);return o().createElement(u.Provider,{value:Y},o().createElement(Ee.Provider,{value:ut},o().createElement(Re.Provider,{value:ct},o().createElement(Ne.Provider,{value:it},g)),o().createElement(ke,{disabled:!1===(null==d?void 0:d.restoreFocus)})),o().createElement(v,{...d,hiddenTextDescribedById:ne}))})),Oe=(0,r.createContext)(null),Le="button",Ae="Droppable";function ze(e){let{id:t,data:n,disabled:o=!1,attributes:i}=e;const l=(0,a.YG)(Ae),{activators:s,activatorEvent:c,active:u,activeNodeRect:d,ariaDescribedById:f,draggableNodes:v,over:h}=(0,r.useContext)(Ee),{role:g=Le,roleDescription:p="draggable",tabIndex:b=0}=null!=i?i:{},m=(null==u?void 0:u.id)===t,y=(0,r.useContext)(m?Ne:Oe),[w,x]=(0,a.lk)(),[C,D]=(0,a.lk)(),E=function(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{let{eventName:r,handler:o}=n;return e[r]=e=>{o(e,t)},e}),{})),[e,t])}(s,t),R=(0,a.YN)(n);return(0,a.Es)((()=>(v.set(t,{id:t,key:l,node:w,activatorNode:C,data:R}),()=>{const e=v.get(t);e&&e.key===l&&v.delete(t)})),[v,t]),{active:u,activatorEvent:c,activeNodeRect:d,attributes:(0,r.useMemo)((()=>({role:g,tabIndex:b,"aria-disabled":o,"aria-pressed":!(!m||g!==Le)||void 0,"aria-roledescription":p,"aria-describedby":f.draggable})),[o,g,b,m,p,f.draggable]),isDragging:m,listeners:o?void 0:E,node:w,over:h,setNodeRef:x,setActivatorNodeRef:D,transform:y}}function Be(){return(0,r.useContext)(Re)}const Ye="Droppable",We={timeout:25};function Ke(e){let{data:t,disabled:n=!1,id:o,resizeObserverConfig:i}=e;const l=(0,a.YG)(Ye),{active:s,dispatch:c,over:u,measureDroppableContainers:d}=(0,r.useContext)(Ee),f=(0,r.useRef)({disabled:n}),v=(0,r.useRef)(!1),g=(0,r.useRef)(null),p=(0,r.useRef)(null),{disabled:b,updateMeasurementsFor:m,timeout:y}={...We,...i},w=(0,a.YN)(null!=m?m:o),x=de({callback:(0,r.useCallback)((()=>{v.current?(null!=p.current&&clearTimeout(p.current),p.current=setTimeout((()=>{d(Array.isArray(w.current)?w.current:[w.current]),p.current=null}),y)):v.current=!0}),[y]),disabled:b||!s}),C=(0,r.useCallback)(((e,t)=>{x&&(t&&(x.unobserve(t),v.current=!1),e&&x.observe(e))}),[x]),[D,E]=(0,a.lk)(C),R=(0,a.YN)(t);return(0,r.useEffect)((()=>{x&&D.current&&(x.disconnect(),v.current=!1,x.observe(D.current))}),[D,x]),(0,a.Es)((()=>(c({type:h.RegisterDroppable,element:{id:o,key:l,disabled:n,node:D,rect:g,data:R}}),()=>c({type:h.UnregisterDroppable,key:l,id:o}))),[o]),(0,r.useEffect)((()=>{n!==f.current.disabled&&(c({type:h.SetDroppableDisabled,id:o,key:l,disabled:n}),f.current.disabled=n)}),[o,l,n,c]),{active:s,rect:g,isOver:(null==u?void 0:u.id)===o,node:D,over:u,setNodeRef:E}}},2986:(e,t,n)=>{n.d(t,{FN:()=>r}),n(68959);const r=e=>{let{transform:t}=e;return{...t,x:0}}},22969:(e,t,n)=>{n.d(t,{_G:()=>f,gB:()=>g,gl:()=>C});var r=n(41594),o=n.n(r),i=n(57344),a=n(68959);function l(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function s(e,t){return e.reduce(((e,n,r)=>{const o=t.get(n);return o&&(e[r]=o),e}),Array(e.length))}function c(e){return null!==e&&e>=0}const u=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e;const i=l(t,r,n),a=t[o],s=i[o];return s&&a?{x:s.left-a.left,y:s.top-a.top,scaleX:s.width/a.width,scaleY:s.height/a.height}:null},d={scaleX:1,scaleY:1},f=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:o,rects:i,overIndex:a}=e;const l=null!=(t=i[n])?t:r;if(!l)return null;if(o===n){const e=i[a];return e?{x:0,y:n<a?e.top+e.height-(l.top+l.height):e.top-l.top,...d}:null}const s=function(e,t,n){const r=e[t],o=e[t-1],i=e[t+1];return r?n<t?o?r.top-(o.top+o.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):o?r.top-(o.top+o.height):0:0}(i,o,n);return o>n&&o<=a?{x:0,y:-l.height-s,...d}:o<n&&o>=a?{x:0,y:l.height+s,...d}:{x:0,y:0,...d}},v="Sortable",h=o().createContext({activeIndex:-1,containerId:v,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:u,disabled:{draggable:!1,droppable:!1}});function g(e){let{children:t,id:n,items:l,strategy:c=u,disabled:d=!1}=e;const{active:f,dragOverlay:g,droppableRects:p,over:b,measureDroppableContainers:m}=(0,i.fF)(),y=(0,a.YG)(v,n),w=Boolean(null!==g.rect),x=(0,r.useMemo)((()=>l.map((e=>"object"==typeof e&&"id"in e?e.id:e))),[l]),C=null!=f,D=f?x.indexOf(f.id):-1,E=b?x.indexOf(b.id):-1,R=(0,r.useRef)(x),S=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(x,R.current),M=-1!==E&&-1===D||S,k=function(e){return"boolean"==typeof e?{draggable:e,droppable:e}:e}(d);(0,a.Es)((()=>{S&&C&&m(x)}),[S,x,C,m]),(0,r.useEffect)((()=>{R.current=x}),[x]);const N=(0,r.useMemo)((()=>({activeIndex:D,containerId:y,disabled:k,disableTransforms:M,items:x,overIndex:E,useDragOverlay:w,sortedRects:s(x,p),strategy:c})),[D,y,k.draggable,k.droppable,M,x,E,p,w,c]);return o().createElement(h.Provider,{value:N},t)}const p=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return l(n,r,o).indexOf(t)},b=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:i,newIndex:a,previousItems:l,previousContainerId:s,transition:c}=e;return!(!c||!r||l!==i&&o===a||!n&&(a===o||t!==s))},m={duration:200,easing:"ease"},y="transform",w=a.Ks.Transition.toString({property:y,duration:0,easing:"linear"}),x={roleDescription:"sortable"};function C(e){let{animateLayoutChanges:t=b,attributes:n,disabled:o,data:l,getNewIndex:s=p,id:u,strategy:d,resizeObserverConfig:f,transition:v=m}=e;const{items:g,containerId:C,activeIndex:D,disabled:E,disableTransforms:R,sortedRects:S,overIndex:M,useDragOverlay:k,strategy:N}=(0,r.useContext)(h),I=function(e,t){var n,r;return"boolean"==typeof e?{draggable:e,droppable:!1}:{draggable:null!=(n=null==e?void 0:e.draggable)?n:t.draggable,droppable:null!=(r=null==e?void 0:e.droppable)?r:t.droppable}}(o,E),T=g.indexOf(u),O=(0,r.useMemo)((()=>({sortable:{containerId:C,index:T,items:g},...l})),[C,l,T,g]),L=(0,r.useMemo)((()=>g.slice(g.indexOf(u))),[g,u]),{rect:A,node:z,isOver:B,setNodeRef:Y}=(0,i.zM)({id:u,data:O,disabled:I.droppable,resizeObserverConfig:{updateMeasurementsFor:L,...f}}),{active:W,activatorEvent:K,activeNodeRect:F,attributes:P,setNodeRef:j,listeners:U,isDragging:X,over:G,setActivatorNodeRef:_,transform:H}=(0,i.PM)({id:u,data:O,attributes:{...x,...n},disabled:I.draggable}),q=(0,a.jn)(Y,j),Q=Boolean(W),J=Q&&!R&&c(D)&&c(M),V=!k&&X,Z=V&&J?H:null,$=J?null!=Z?Z:(null!=d?d:N)({rects:S,activeNodeRect:F,activeIndex:D,overIndex:M,index:T}):null,ee=c(D)&&c(M)?s({id:u,items:g,activeIndex:D,overIndex:M}):T,te=null==W?void 0:W.id,ne=(0,r.useRef)({activeId:te,items:g,newIndex:ee,containerId:C}),re=g!==ne.current.items,oe=t({active:W,containerId:C,isDragging:X,isSorting:Q,id:u,index:T,items:g,newIndex:ne.current.newIndex,previousItems:ne.current.items,previousContainerId:ne.current.containerId,transition:v,wasDragging:null!=ne.current.activeId}),ie=function(e){let{disabled:t,index:n,node:o,rect:l}=e;const[s,c]=(0,r.useState)(null),u=(0,r.useRef)(n);return(0,a.Es)((()=>{if(!t&&n!==u.current&&o.current){const e=l.current;if(e){const t=(0,i.Sj)(o.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&c(n)}}n!==u.current&&(u.current=n)}),[t,n,o,l]),(0,r.useEffect)((()=>{s&&c(null)}),[s]),s}({disabled:!oe,index:T,node:z,rect:A});return(0,r.useEffect)((()=>{Q&&ne.current.newIndex!==ee&&(ne.current.newIndex=ee),C!==ne.current.containerId&&(ne.current.containerId=C),g!==ne.current.items&&(ne.current.items=g)}),[Q,ee,C,g]),(0,r.useEffect)((()=>{if(te===ne.current.activeId)return;if(te&&!ne.current.activeId)return void(ne.current.activeId=te);const e=setTimeout((()=>{ne.current.activeId=te}),50);return()=>clearTimeout(e)}),[te]),{active:W,activeIndex:D,attributes:P,data:O,rect:A,index:T,newIndex:ee,items:g,isOver:B,isSorting:Q,isDragging:X,listeners:U,node:z,overIndex:M,over:G,setNodeRef:q,setActivatorNodeRef:_,setDroppableNodeRef:Y,setDraggableNodeRef:j,transform:null!=ie?ie:$,transition:ie||re&&ne.current.newIndex===T?w:V&&!(0,a.kx)(K)||!v?void 0:Q||oe?a.Ks.Transition.toString({...v,property:y}):void 0}}i.vL.Down,i.vL.Right,i.vL.Up,i.vL.Left},68959:(e,t,n)=>{n.d(t,{$$:()=>g,Es:()=>v,KG:()=>b,Ks:()=>M,Ll:()=>l,Re:()=>E,Sw:()=>i,TW:()=>f,WQ:()=>D,YG:()=>x,YN:()=>p,ZC:()=>y,_q:()=>h,ag:()=>N,e_:()=>S,jn:()=>o,kx:()=>R,l6:()=>a,lk:()=>m,sb:()=>u,wz:()=>c,xZ:()=>d,zk:()=>s});var r=n(41594);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)((()=>e=>{t.forEach((t=>t(e)))}),t)}const i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function a(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function l(e){return"nodeType"in e}function s(e){var t,n;return e?a(e)?e:l(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function c(e){const{Document:t}=s(e);return e instanceof t}function u(e){return!a(e)&&e instanceof s(e).HTMLElement}function d(e){return e instanceof s(e).SVGElement}function f(e){return e?a(e)?e.document:l(e)?c(e)?e:u(e)||d(e)?e.ownerDocument:document:document:document}const v=i?r.useLayoutEffect:r.useEffect;function h(e){const t=(0,r.useRef)(e);return v((()=>{t.current=e})),(0,r.useCallback)((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}function g(){const e=(0,r.useRef)(null);return[(0,r.useCallback)(((t,n)=>{e.current=setInterval(t,n)}),[]),(0,r.useCallback)((()=>{null!==e.current&&(clearInterval(e.current),e.current=null)}),[])]}function p(e,t){void 0===t&&(t=[e]);const n=(0,r.useRef)(e);return v((()=>{n.current!==e&&(n.current=e)}),t),n}function b(e,t){const n=(0,r.useRef)();return(0,r.useMemo)((()=>{const t=e(n.current);return n.current=t,t}),[...t])}function m(e){const t=h(e),n=(0,r.useRef)(null),o=(0,r.useCallback)((e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e}),[]);return[n,o]}function y(e){const t=(0,r.useRef)();return(0,r.useEffect)((()=>{t.current=e}),[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)((()=>{if(t)return t;const n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n}),[e,t])}function C(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>{const r=Object.entries(n);for(const[n,o]of r){const r=t[n];null!=r&&(t[n]=r+e*o)}return t}),{...t})}}const D=C(1),E=C(-1);function R(e){if(!e)return!1;const{KeyboardEvent:t}=s(e.target);return t&&e instanceof t}function S(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=s(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}const M=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[M.Translate.toString(e),M.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),k="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function N(e){return e.matches(k)?e:e.querySelector(k)}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.19/1dc8ee22476692d61212507ea4f6bcbc/171.lite.js.map
