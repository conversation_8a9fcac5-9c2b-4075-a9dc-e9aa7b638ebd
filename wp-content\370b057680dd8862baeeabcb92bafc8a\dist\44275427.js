"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[452],{5285:(e,t,n)=>{var o,r,i,a,s;n.d(t,{ak:()=>i,iQ:()=>r,um:()=>a}),Object.freeze(["name","headline","subHeadline","providerNotice","groupNotice","legalBasisNotice","technicalHandlingNotice","createContentBlockerNotice","sccConclusionInstructionsNotice"]),Object.freeze(["codeOnPageLoad","codeOptIn","codeOptOut","createContentBlockerNotice","deleteTechnicalDefinitionsAfterOptOut","dynamicFields","executeCodeOptInWhenNoTagManagerConsentIsGiven","executeCodeOptOutWhenNoTagManagerConsentIsGiven","googleConsentModeConsentTypes","groupNotice","isProviderCurrentWebsite","legalBasis","legalBasisNotice","provider","providerContact","providerLegalNoticeUrl","providerNotice","providerPrivacyPolicyUrl","providerText","purposes","purposeText","shouldUncheckContentBlockerCheckbox","shouldUncheckContentBlockerCheckboxWhenOneOf","tagManagerOptInEventName","tagManagerOptOutEventName","technicalHandlingNotice"]),Object.freeze(["name","codeOnPageLoad","googleConsentModeConsentTypes","codeOptIn","codeOptOut","createContentBlockerNotice","dataProcessingInCountries","dataProcessingInCountriesSpecialTreatments","deleteTechnicalDefinitionsAfterOptOut","dynamicFields","executeCodeOptInWhenNoTagManagerConsentIsGiven","executeCodeOptOutWhenNoTagManagerConsentIsGiven","executePriority","group","groupNotice","isCdn","isEmbeddingOnlyExternalResources","isProviderCurrentWebsite","legalBasis","legalBasisNotice","provider","providerNotice","providerPrivacyPolicyUrl","providerLegalNoticeUrl","purposes","sccConclusionInstructionsNotice","shouldUncheckContentBlockerCheckbox","shouldUncheckContentBlockerCheckboxWhenOneOf","tagManagerOptInEventName","tagManagerOptOutEventName","technicalDefinitions","technicalHandlingNotice"]),function(e){e.Essential="essential",e.Functional="functional",e.Statistics="statistics",e.Marketing="marketing"}(o||(o={})),function(e){e.Consent="consent",e.LegitimateInterest="legitimate-interest",e.LegalRequirement="legal-requirement"}(r||(r={})),function(e){e.ContractualAssurancesWithSubProcessors="contractual-assurances-with-sub-processors",e.ProviderIsSelfCertifiedTransAtlanticDataPrivacyFramework="provider-is-self-certified-trans-atlantic-data-privacy-framework",e.StandardContractualClauses="standard-contractual-clauses",e.BindingCorporateRules="binding-corporate-rules"}(i||(i={})),function(e){e.AdStorage="ad_storage",e.AdUserData="ad_user_data",e.AdPersonalization="ad_personalization",e.AnalyticsStorage="analytics_storage",e.FunctionalityStorage="functionality_storage",e.PersonalizationStorage="personalization_storage",e.SecurityStorage="security_storage"}(a||(a={})),function(e){e.CodeOnPageLoad="codeOnPageLoad",e.CodeOptIn="codeOptIn",e.CodeOptOut="codeOptOut",e.CreateContentBlockerNotice="createContentBlockerNotice",e.CreateContentBlockerNoticeTranslationFlags="createContentBlockerNoticeTranslationFlags",e.DataProcessingInCountries="dataProcessingInCountries",e.DataProcessingInCountriesSpecialTreatments="dataProcessingInCountriesSpecialTreatments",e.DeleteTechnicalDefinitionsAfterOptOut="deleteTechnicalDefinitionsAfterOptOut",e.DynamicFields="dynamicFields",e.DynamicFieldIds="dynamicFieldIds",e.ExecuteCodeOptInWhenNoTagManagerConsentIsGiven="executeCodeOptInWhenNoTagManagerConsentIsGiven",e.ExecuteCodeOptOutWhenNoTagManagerConsentIsGiven="executeCodeOptOutWhenNoTagManagerConsentIsGiven",e.ExecutePriority="executePriority",e.GoogleConsentModeConsentTypes="googleConsentModeConsentTypes",e.Group="group",e.GroupNotice="groupNotice",e.GroupNoticeTranslationFlags="groupNoticeTranslationFlags",e.IsCdn="isCdn",e.IsEmbeddingOnlyExternalResources="isEmbeddingOnlyExternalResources",e.IsProviderCurrentWebsite="isProviderCurrentWebsite",e.LegalBasis="legalBasis",e.LegalBasisNotice="legalBasisNotice",e.LegalBasisNoticeTranslationFlags="legalBasisNoticeTranslationFlags",e.Provider="provider",e.ProviderContact="providerContact",e.ProviderLegalNoticeUrl="providerLegalNoticeUrl",e.ProviderLegalNoticeUrlTranslationFlags="providerLegalNoticeUrlTranslationFlags",e.ProviderNotice="providerNotice",e.ProviderNoticeTranslationFlags="providerNoticeTranslationFlags",e.ProviderPrivacyPolicyUrl="providerPrivacyPolicyUrl",e.ProviderPrivacyPolicyUrlTranslationFlags="providerPrivacyPolicyUrlTranslationFlags",e.ProviderText="providerText",e.ProviderTranslationFlags="providerTranslationFlags",e.Purposes="purposes",e.PurposeIds="purposeIds",e.PurposeText="purposeText",e.SccConclusionInstructionsNotice="sccConclusionInstructionsNotice",e.SccConclusionInstructionsNoticeTranslationFlags="sccConclusionInstructionsNoticeTranslationFlags",e.ShouldUncheckContentBlockerCheckbox="shouldUncheckContentBlockerCheckbox",e.ShouldUncheckContentBlockerCheckboxWhenOneOf="shouldUncheckContentBlockerCheckboxWhenOneOf",e.TagManagerOptInEventName="tagManagerOptInEventName",e.TagManagerOptOutEventName="tagManagerOptOutEventName",e.TechnicalDefinitions="technicalDefinitions",e.TechnicalDefinitionIds="technicalDefinitionIds",e.TechnicalHandlingNotice="technicalHandlingNotice",e.TechnicalHandlingNoticeTranslationFlags="technicalHandlingNoticeTranslationFlags"}(s||(s={})),Object.freeze(["id","logo","logoId","release","releaseId","extends","next","nextId","pre","preId","extendsId","translationIds","extendedTemplateId","translationInfo","purposeIds","dynamicFieldIds","technicalDefinitionIds","translatableRequiredFields","translatedRequiredFields","translatableOptionalFields","translatedOptionalFields","translationFlaggedFields","version"])},9081:(e,t,n)=>{function o(e){return[...new Set(e.map((e=>{let{googleConsentModeConsentTypes:t}=e;return[...t]})).flat())]}n.d(t,{h:()=>o})},3016:(e,t,n)=>{n.d(t,{r:()=>o});const o=e=>{let{icon:t,...o}=e;const r=(0,n(7936).Kr)((()=>(0,n(1503).Q)(t,{extraSVGAttrs:{style:"width:auto;height:100%;",fill:"currentColor"}})),[t]);return(0,n(6425).Y)("div",{...o,dangerouslySetInnerHTML:{__html:r}})}},4094:(e,t,n)=>{n.d(t,{Y:()=>r,d:()=>i});const o=Symbol(),r=()=>(0,n(3179).NV)(o),i=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n(3179).gm)(o,...t)}},1208:(e,t,n)=>{n.d(t,{K:()=>r,t:()=>i});const o=Symbol(),r=()=>(0,n(3179).NV)(o),i=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n(3179).gm)(o,...t)}},8700:(e,t,n)=>{n.d(t,{d:()=>i,o:()=>r});const o=Symbol(),r=()=>(0,n(3179).NV)(o),i=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n(3179).gm)(o,...t)}},1477:(e,t,n)=>{n.d(t,{JY:()=>m,BP:()=>x,$D:()=>b,bM:()=>I});var o=n(6425),r=n(5285),i=n(9081),a=n(8489),s=n(5922),c=n(7936),l=n(5746),d=n(8700),u=n(7140),g=n(3016);const p=e=>{let{children:t}=e;const{AccordionList:n}=(0,d.o)().extend(...l.I);return(0,o.Y)(n,{children:t})},f=e=>{let{children:t,title:n,icon:r,iconExpanded:i,expandable:a=!0}=e;const{accordionArrow:s,AccordionDescription:p,AccordionItem:f,AccordionButton:v,AccordionTitle:h,accordionItemActive:C,accordionItemDisabled:b}=(0,d.o)().extend(...l.I),[m,x]=(0,c.J0)(!1),y=(0,u.p)();return(0,o.FD)(f,{onClick:()=>a&&x(!m),className:[m&&C,!a&&b].filter(Boolean).join(" "),children:[(0,o.FD)(v,{...a?{}:{disabled:"disabled"},"aria-expanded":m,"aria-controls":y,href:"#",onClick:e=>e.preventDefault(),children:[!!r&&(0,o.Y)(g.r,{icon:m&&i?i:r,className:s}),(0,o.Y)(h,{children:n})]}),(0,o.Y)("div",{id:y,children:m&&a&&(0,o.Y)(p,{children:t})})]})},v=e=>{let{services:t}=e;const n=(0,i.h)(t),{i18n:{gcm:{purposes:r}}}=(0,s.b)();return(0,o.Y)(p,{children:n.map((e=>(0,o.Y)(f,{title:r[e],icon:a.A,expandable:!1},e)))})};var h=n(1917),C=n(4349);function b(e,t){const n=e.filter(Boolean);return n.length>1&&n.splice(n.length-1,0,"{{andSeparator}}"),n.join(", ").replace(/,\s+{{andSeparator}},\s+/g,t)}const m=e=>(t,n)=>{void 0===n&&(n="");const o=t.filter(Boolean),r=o.map((e=>{let[t]=e;return t})),i=o.map((e=>{let[,t]=e;return t})).filter(Boolean);return 0===r.length?n:`${n}${y}${r.join(",")}</sup>${i.length?`<span class="${e}">(${i.join(", ")})</span>`:""}`};function x(e,t,n){return`${e} ${e>1?n:t}`}const y='<sup aria-hidden="true" style="vertical-align:top;line-height:100%;position:initial;">';function I(e){let{services:t,disableListServicesNotice:n,disableTcfPurposes:a}=e;const{blocker:c,groups:l,isAgeNotice:u,isGcm:g,isGcmListPurposes:p,ageNoticeAgeLimit:f,isListServicesNotice:x,isDataProcessingInUnsafeCountries:y,dataProcessingInUnsafeCountriesSafeCountries:I,texts:{description:O,dataProcessingInUnsafeCountries:N,ageNoticeBanner:k,ageNoticeBlocker:P,listServicesNotice:T,listServicesLegitimateInterestNotice:S,consentForwardingExternalHosts:B},tcf:$,consentForwardingExternalHosts:A,individualPrivacyOpen:D,individualTexts:{description:F},designVersion:w,territorialLegalBasis:E,predefinedDataProcessingInSafeCountriesLists:L,i18n:{andSeparator:U,territorialLegalBasisArticles:W,gcm:M,tcf:z,deprecated:{dataProcessingInUnsafeCountries:G}},keepVariablesInTexts:j}=(0,s.b)(),{privacyPolicy:H}=(0,C.s)(),{screenReaderOnlyClass:R}=(0,d.o)(),[_,Y]=[[],[]],V=m(R),q=w>9?"D":"U",K=l.map(((e,t)=>e.items.map((n=>{const{legalBasis:o}=n;return{service:n,legalBasis:e.isEssential&&w>=4&&"consent"===o?r.iQ.LegitimateInterest:o,group:e,groupIdx:t}})))).flat();let Q="";A&&!j&&(Q=B.replace(/{{websites}}/g,A.join(", ")));let J=[c?[c.description,w>2&&O].filter(Boolean).join("\n\n"):D?F:O,Q].filter(Boolean).join(" ");j||(J=J.replace(/{{privacyPolicy}}(.*){{\/privacyPolicy}}/gi,H?`<a href="${H.url}" target="_blank">$1</a>`:"$1"));const Z=t.filter((e=>{let{dataProcessingInCountries:t,dataProcessingInCountriesSpecialTreatments:n}=e;return(w>9?(0,h.F)({predefinedDataProcessingInSafeCountriesLists:L,isDataProcessingInUnsafeCountries:y,territorialLegalBasis:E,service:{dataProcessingInCountries:t,dataProcessingInCountriesSpecialTreatments:n}}).filter((e=>e.startsWith("D"))):(0,h.z)({dataProcessingInCountries:t,safeCountries:I,specialTreatments:n})).length>0})),X=(null==$?void 0:$.gvl)?Object.values($.gvl.vendors).filter((e=>{const{dataProcessingInCountries:t,dataProcessingInCountriesSpecialTreatments:n}=$.original.vendorConfigurations[e.id];return(w>9?(0,h.F)({predefinedDataProcessingInSafeCountriesLists:L,isDataProcessingInUnsafeCountries:y,territorialLegalBasis:E,service:{dataProcessingInCountries:t,dataProcessingInCountriesSpecialTreatments:n}}).filter((e=>e.startsWith("D"))):(0,h.z)({dataProcessingInCountries:t,safeCountries:I,specialTreatments:n})).length>0})):[];let ee=(Z.length>0||X.length>0)&&(y?N:"");ee&&!j&&(ee=ee.replace(/{{legalBasis}}/g,(()=>b(E.map((e=>W[e].dataProcessingInUnsafeCountries||"")),U))));let te=u?c?P:k:"";te&&!j&&(te=te.replace(/{{minAge}}/gi,`${f}`));let ne="";if(x&&!n){const e=T.toLowerCase(),t=e.indexOf("{{services}}")>-1&&e.indexOf("{{servicegroups}}")>-1,n=b(K.map((e=>{let{service:n,legalBasis:o,groupIdx:r,group:{name:i,isEssential:a}}=e;const{name:s}=n;if(!("legal-requirement"===o||w<4&&a||S&&"consent"!==o))return V([t&&[`${r+1}`,i],ee&&Z.indexOf(n)>-1&&[q,G]],s)})),U),o=S?b(K.map((e=>{let{service:n,legalBasis:o,groupIdx:r,group:{name:i}}=e;const{name:a}=n;if("legitimate-interest"===o)return V([t&&[`${r+1}`,i],ee&&Z.indexOf(n)>-1&&[q,G]],a)})),U):"";if(n){const e=`${n}${o}`,r=b(l.map(((n,o)=>{let{name:r}=n;const i=`${o+1}`;return-1===e.indexOf(`>${i}`)?"":V([t&&[i]],r)})),U);ne=`<span>${T}</span>`,j||(ne=ne.replace(/{{services}}/gi,n).replace(/{{serviceGroups}}/gi,r)),ee&&(ee+=V([[q]]))}o&&(ne+=` <span>${S}</span>`,j||(ne=ne.replace(/{{services}}/gi,o)))}const oe=[];return!D&&g&&p&&(0,i.h)(t).length&&oe.push(M.teaching,(0,o.Y)(v,{services:t},"gcm")),{description:J,teachings:[ee,te,ne,[],oe].flat().filter(Boolean)}}},7140:(e,t,n)=>{function o(e){const{functions:{className:t}}=e||(0,n(8700).o)();return(0,n(7936).Kr)((()=>t()),[])}n.d(t,{p:()=>o})},5922:(e,t,n)=>{function o(){const e=(0,n(4094).Y)(),t=(0,n(1208).K)();return e.groups?e:t}n.d(t,{b:()=>o})},4349:(e,t,n)=>{function o(){const{links:e=[],websiteOperator:t}=(0,n(5922).b)(),o=e.filter((e=>{let{label:t,url:n}=e;return t&&n})),r=o.find((e=>{let{pageType:t}=e;return"privacyPolicy"===t})),i=o.find((e=>{let{pageType:t}=e;return"legalNotice"===t}));return{privacyPolicy:!!r&&{url:r.url,label:r.label},legalNotice:!!i&&{url:i.url,label:i.label},contactForm:(null==t?void 0:t.contactFormUrl)||void 0}}n.d(t,{s:()=>o})},1917:(e,t,n)=>{function o(e){let{predefinedDataProcessingInSafeCountriesLists:t,isDataProcessingInUnsafeCountries:o,territorialLegalBasis:r,service:i}=e,a=[];const s=r.indexOf("gdpr-eprivacy")>-1,c=r.indexOf("dsg-switzerland")>-1,{GDPR:l,DSG:d,"GDPR+DSG":u,ADEQUACY_CH:g,ADEQUACY_EU:p}=t;c&&s?a=u:s?a=l:c&&(a=d);let f=[];const v=[],h={};if(i){const{dataProcessingInCountries:e,dataProcessingInCountriesSpecialTreatments:t}=i;f=e.filter((e=>-1===a.indexOf(e)));const r=f.indexOf("US")>-1;if(r&&v.push(n(5285).ak.ProviderIsSelfCertifiedTransAtlanticDataPrivacyFramework),f.length>0){const e=[...s?p:[],...c?g:[]];(f.filter((t=>-1===e.indexOf(t))).length>0||r)&&(v.push(n(5285).ak.StandardContractualClauses),-1===t.indexOf(n(5285).ak.StandardContractualClauses)&&v.push(n(5285).ak.ContractualAssurancesWithSubProcessors));const i=t.indexOf(n(5285).ak.StandardContractualClauses)>-1,a=!i&&t.indexOf(n(5285).ak.ContractualAssurancesWithSubProcessors)>-1,u=r&&t.indexOf(n(5285).ak.ProviderIsSelfCertifiedTransAtlanticDataPrivacyFramework)>-1,C=t.indexOf(n(5285).ak.BindingCorporateRules)>-1,b=e=>!s&&!c||"US"!==e||u,m=i?"B":a?"C":C?"E":o?"D":void 0;for(const e of f){const t=[],n=p.filter(b).indexOf(e)>-1,o=g.filter(b).indexOf(e)>-1;c&&s?(-1===l.indexOf(e)&&t.push(n?"A-EU":"D"===m?"D-EU":m),-1===d.indexOf(e)&&t.push(o?"A-CH":"D"===m?"D-CH":m)):s?t.push(n?"A":m):c&&t.push(o?"A":m),h[e]=[...new Set(t.filter(Boolean))]}}}return{isGdpr:s,isDsg:c,safeCountries:a,unsafeCountries:f,allowedSpecialTreatments:v,result:h,filter:e=>Object.entries(h).map((t=>{let[n,o]=t;return o.some(e)?n:void 0})).filter(Boolean)}}function r(e){let{dataProcessingInCountries:t,safeCountries:o=[],specialTreatments:r=[],isDisplay:i}=e,a=r;return i&&(a=a.filter((e=>n(5285).ak.StandardContractualClauses!==e))),t.filter((e=>!(a.indexOf(n(5285).ak.StandardContractualClauses)>-1)&&(-1===o.indexOf(e)||"US"===e&&-1===a.indexOf(n(5285).ak.ProviderIsSelfCertifiedTransAtlanticDataPrivacyFramework))))}n.d(t,{F:()=>o,z:()=>r})},5360:(e,t,n)=>{function o(e,t){const o=e.filter(Boolean);return 0===o.length?null:o.reduce(((e,r,i)=>e.length?[...e,(0,n(6425).Y)(n(7936).FK,{children:"function"==typeof t?t(i,o.length):t},i),r]:[r]),[])}n.d(t,{i:()=>o})},5746:(e,t,n)=>{n.d(t,{I:()=>o});const o=[Symbol("extendCommonStylesheet"),(e,t)=>{let{control:o,className:r,rule:i,boolIf:a,jsx:s}=e,{a11yFocusStyle:c,boolLargeOrMobile:l,bodyDesign:d,design:u,group:g,layout:p,screenReaderOnlyClass:f,scaleHorizontal:v,isMobile:h}=t;const[C]=s("a",{all:"unset",cursor:"pointer",color:g.linkColor(),textDecoration:u.linkTextDecoration(),pseudos:{":hover":{color:g.linkHoverColor(),textDecoration:"none"}}}),[b]=s("label",{all:"unset"}),[m,x]=o({fontSize:15},{fontSize:n(5914).dD},(e=>{let{fontSize:t}=e;const n=l(g.checkboxBorderWidth,a),[,o]=i({classNames:["checkbox",f],pseudos:{"+div":{aspectRatio:"1/1",height:`calc((${t()} + ${n} * 2 + 6px) * ${a(h,v(),"1")})`,boxSizing:"border-box",display:"inline-block",marginRight:"10px",lineHeight:0,verticalAlign:"middle",padding:"3px",borderRadius:p.borderRadius(),cursor:"pointer",borderStyle:"solid",borderWidth:n,backgroundColor:g.checkboxBg(),color:g.checkboxBg(),borderColor:g.checkboxBorderColor()},":checked+div":{backgroundColor:g.checkboxActiveBg(),color:g.checkboxActiveColor(),borderColor:g.checkboxActiveBorderColor()},"[disabled]+div":{cursor:"not-allowed",opacity:"0.5"},":focus-visible+div":c.outline,"+div+span":{verticalAlign:"middle",cursor:"pointer"},":focus-visible+div+span>span:first-of-type":c.text}});return o})),{fontColor:y}=u,[I]=s("select",{background:"transparent",border:0,fontSize:a(d.descriptionInheritFontSize,l(u.fontSize,a),l(d.descriptionFontSize,a)),color:y("hex"),borderBottom:`1px solid rgba(${y("r")} ${y("g")} ${y("b")} / 50%)`,pseudos:{">option":{background:u.bg()}}}),[O]=s("fieldset",{classNames:"group-button",all:"unset",pseudos:{">label":{all:"unset"}}}),[,N]=i({classNames:["group-button-item",f],pseudos:{"+span":{padding:"5px 10px",color:g.linkColor(),borderRadius:p.borderRadius(),textDecoration:u.linkTextDecoration(),opacity:.8,cursor:"pointer",borderWidth:l(d.acceptAllBorderWidth,a),borderStyle:"solid",borderColor:"transparent"},":checked+span":{opacity:1,cursor:"initial",textDecoration:"initial",background:d.acceptAllBg(),color:d.acceptAllFontColor(),borderColor:d.acceptAllBorderColor()},":not(:checked)+span:hover,:focus-visible+span":{opacity:1,textDecoration:u.linkTextDecoration()},":focus-visible+span":c.outline}}),k=l(d.accordionBorderWidth,a),P=l(d.accordionTitleFontSize,a),[T]=s("div",{classNames:"accordions",margin:l(d.accordionMargin,a),textAlign:"left",lineHeight:"1.5",pseudos:{">div":{borderWidth:"0px",borderTopWidth:k,borderStyle:"solid",borderColor:d.accordionBorderColor()},">div:last-of-type":{borderBottomWidth:k},"+p":{marginTop:"15px"}}}),S=r(),B=r(),[$]=s("div",{classNames:"accordion-item",cursor:"pointer",padding:d.accordionPadding("l"),background:d.accordionBg(),pseudos:{[`.${S},:has(>a:focus-visible)`]:{background:d.accordionActiveBg()},[`:hover:not(.${S},.${B})`]:{background:d.accordionHoverBg()},">a":{display:"flex",alignItems:"center"},[`.${B}`]:{cursor:"initial"}}}),[A]=s("a",{classNames:"accordion-button",all:"unset"}),[,D]=i({classNames:"accordion-arrow",width:P,height:P,flex:`0 0 ${P}`,lineHeight:P,float:"left",marginRight:"10px",color:d.accordionArrowColor()}),[F]=s("div",{classNames:"accordion-title",fontSize:P,color:d.accordionTitleFontColor(),fontWeight:d.accordionTitleFontWeight()}),[w]=s("div",{classNames:"accordion-description",fontSize:l(d.accordionDescriptionFontSize,a),color:d.accordionDescriptionFontColor(),fontWeight:d.accordionDescriptionFontWeight(),margin:l(d.accordionDescriptionMargin,a)});return{checkbox:{style:m,className:x},Link:C,Label:b,Select:I,ButtonGroup:O,buttonGroupItem:N,AccordionList:T,AccordionItem:$,AccordionButton:A,AccordionTitle:F,AccordionDescription:w,accordionArrow:D,accordionItemActive:S,accordionItemDisabled:B}}]},3511:(e,t,n)=>{n.d(t,{G:()=>i,u:()=>r});var o=n(8084);const r=(e,t)=>{let{mainElement:n}=t;n.dispatchEvent(new CustomEvent(`${o._2}${e}`,{detail:{}}))},i=(e,t)=>{let{mainElement:n,varsVal:r}=e,{variable:i,vars:a}=t;return(e,t,s,c)=>{let l;const d=e.map((e=>"function"==typeof e?e(!1):void 0)),u=()=>t(d.map((e=>r.get(e)))),g=((e,t)=>{if("raf"===e){let e=!1;return()=>{e||(window.requestAnimationFrame((()=>{t(),e=!1})),e=!0)}}{let n;return()=>{clearTimeout(n),n=setTimeout(t,e)}}})(c||0,(()=>l(u())));for(const e of d)n.addEventListener(`${o._2}${e}`,g);const p=u(),f="object"!=typeof p||Array.isArray(p)?(()=>{const e=i(p,void 0,s);return l=e.update,e})():(()=>{const e=a(p,void 0);return[,l]=e,e[0]})();return f.update=()=>g(),f}}},7996:(e,t,n)=>{n.d(t,{G:()=>o,s:()=>r});const o=" ",r=(e,t)=>{let{variable:n,vars:r}=t;const i=(e,t,a)=>{let s,c,l;if("object"!=typeof e||Array.isArray(e))s=e,c=t,l=a;else{const{when:t,then:n,or:o}=e;s=t,c=n,l=o}if(l=l||o,Array.isArray(s)){const e={when:void 0,then:void 0,or:void 0};let t=e;const{length:n}=s;for(let e=0;e<n;e++)t.when=s[e],t.or=l,e===n-1?t.then=c:(t.then={when:void 0,then:void 0,or:l},t=t.then);return i(e)}{"string"==typeof s&&s.startsWith("--")&&(s=`var(${s})`);const[e]=r({true:"object"==typeof c?i(c):c,false:`${"function"==typeof s?s():s} ${"object"==typeof l?i(l):l}`});if("inherit"===l)throw new Error('Due to the nature how conditionals work in CSS, it is not allowed to use "inherit" as a falsy value. Please reverse your condition with the help of "boolNot" or use another value.');return n(e.false(!0,e.true()))()}},a=(e,t)=>{const n={when:void 0,then:void 0,or:void 0},{length:o}=e;let r=n;for(let n=0;n<o;n++){const[i,a]=e[n];r.when=i,r.then=a,n===o-1?r.or=t:(r.or={when:void 0,then:void 0,or:void 0},r=r.or)}return i(n)};return{boolIf:i,boolSwitch:a,boolNot:e=>{let t=e;return"string"==typeof t&&t.startsWith("var(")&&(t=t.slice(4,-1)),`var(${"function"==typeof t?t(!1):t}-not)`},boolOr:e=>a(e.map((e=>[e,"initial"])),o)}}},5914:(e,t,n)=>{n.d(t,{yq:()=>g,gJ:()=>P,Kn:()=>x,xj:()=>k,tZ:()=>N,g9:()=>I,$S:()=>y,a$:()=>O,tD:()=>C,dD:()=>b,g$:()=>m});var o=n(3511),r=n(7996),i=n(8084),a=n(6256);const s={},c="àáäâèéëêìíïîòóöôùúüûñç·/_,:;",l=c.replace(/\//g,"\\/"),d=new RegExp(`[${l}]`,"g");function u(e){if(s[e])return s[e];const t=e.trim().toLowerCase().replace(d,(e=>"aaaaeeeeiiiioooouuuunc------".charAt(c.indexOf(e)))).replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-");return s[e]=t,t}const g=(e,t)=>{const{className:n,isExtension:a,rules:s,id:c,element:l}=e,d=a&&!t?n.split("-ext")[0]:n,g=t?c.split("-ext")[0]:c,v=t=>`--${g}-${e.inc++}${t?`-${t}`:""}`,C=(t,n,r)=>{const a=v(r);e.varsVal.set(a,t),s.set(d,s.get(d)||{});const c=s.get(d),l=f(t,n);return h(a,l,((e,t)=>{c[e]=t})),((e,t,n,r)=>{const{element:a}=e,s=(e,n)=>{void 0===e&&(e=!0);const o=`${t}${["number","string"].indexOf(typeof e)>-1?`-${e}`:""}`;return["boolean","number","string"].indexOf(typeof e)>-1&&!1!==e?`var(${o}${n?`, ${n}`:""})`:t},c=new Map;return h(t,n,((e,t,n)=>{void 0!==n&&(s[n]=e),c.set(e,t)})),s.update=(n,l)=>{let d=l||a.textContent;if(!l&&!a.textContent)return a.addEventListener(i.Iy,(()=>s.update(n)),{once:!0}),d;let u=!1;const g=f(n,r);return h(t,g,((e,t)=>{c.get(e)!==t&&(c.set(e,t),d=p(d,e,t),u=!0)})),u&&(l||(a.textContent=d),e.varsVal.set(t,n),(0,o.u)(t,e)),d},s})(e,a,l,n)};return{varName:v,variable:C,vars:(e,t,n)=>{void 0===n&&(n=!0);const o={};for(const r in e){const i=e[r],a=null==t?void 0:t[r];o[r]=C(i,a,n?u(r):void 0)}return[o,e=>{let{textContent:t}=l;for(const r in e){var n;t=null==(n=o[r])?void 0:n.update(e[r],t)}return t!==l.textContent&&(l.textContent=t),t},e=>{const n={},i=(e,t)=>{if(e.endsWith("-not"))throw new Error(`Boolish variable "${e}" cannot be created as style-attribute in your HTML tag as this is not supported by browsers. Alternatively, use a classname and pseudos to toggle styles.`);n[e]=""===t?r.G:t};for(const n in e){const r=o[n];if(!r)continue;const a=r(!1),s=null==t?void 0:t[n];h(a,f(e[n],s),i)}return n}]}}},p=(e,t,n)=>e.replace(new RegExp(`^((?:    |      )${t}: )(.*)?;$`,"m"),`$1${n};`),f=(e,t)=>"string"==typeof e&&e.startsWith("var(--")?e:t?t(e):e,v=e=>"boolean"==typeof e?e?"initial":"":Array.isArray(e)?e.join(" "):e,h=(e,t,n)=>{const o=[],r=(e,t)=>{"boolean"==typeof t&&n(`${e}-not`,v(!t))},i=(e,t,o)=>{if("string"==typeof t&&t.indexOf("function () {")>-1)throw new Error(`${e} contains a serialized function ("${t}").`);n(e,t,o)};if(Array.isArray(t)){i(e,t.map(v).join(" "));for(let n=0;n<t.length;n++){const a=`${e}-${n}`;r(a,t[n]),i(a,v(t[n]),n),o.push(n)}}else if("object"==typeof t)for(const n in t){const a=`${e}-${u(n)}`;r(a,t[n]),i(a,v(t[n]),n),o.push(n)}else r(e,t),i(e,v(t));return o},C=e=>t=>`${t}${e}`,b=C("px"),m=("px",e=>e.map((e=>`${e}px`)));const x=e=>{const{r:t,g:n,b:o}=(0,a.E)(e);return{r:t,g:n,b:o,hex:e}},y=(e,t)=>e=>({...t.reduce(((t,n)=>(t[`is-${n.toLowerCase()}`]=e===n,t)),{}),...O(!1)(e)}),I=(e,t)=>e=>({...t.reduce(((t,n)=>(t[`has-${n.toLowerCase()}`]=e.indexOf(n)>-1,t)),{})}),O=e=>(void 0===e&&(e=!0),t=>{const n=null==t?void 0:t.length,o=t||"";return{"is-empty":!n,"is-filled":!!n,val:e?JSON.stringify(o):o}}),N=e=>({"is-set":void 0!==e}),k=e=>'"undefined"',P=function(e,t){return Object.keys(e).reduce(((e,n)=>(e[n]=t,e)),{})}},6256:(e,t,n)=>{n.d(t,{E:()=>o});const o=e=>{const t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:{r:0,g:0,b:0}}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.19/bcb271ac66352577850406bc8f730c86/banner-lite-452.lite.js.map
