"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[349],{53810:(e,t,n)=>{n.d(t,{Z:()=>i});var s=n(41594);function i(e,t){(0,s.useEffect)((()=>{const t=new AbortController,n=[()=>t.abort()];return(async()=>{try{const s=await e({abortController:t,aborted:()=>t.signal.aborted});"function"==typeof s&&n.push(s)}catch(e){if("AbortError"!==e.name)throw e}})(),()=>n.forEach((e=>e()))}),t)}},82706:(e,t,n)=>{n.r(t),n.d(t,{CookieGroupsTabLayout:()=>B});var s=n(3713),i=n(18197),r=n(5190),o=n(30338),a=n(57922),l=n(41594),c=n(27667),d=n(53810),u=n(19117),p=n(91386),g=n(32150),h=n(3820),m=n(59726),x=n(52113);const v=Symbol(),f=()=>(0,x.NV)(v);var j=n(55531),y=n(9551);const b=()=>{const{__:e}=(0,m.s)(),{defaultTemplateValues:{description:t}}=f();return(0,s.jsx)(p.A.Item,{label:(0,s.jsxs)(s.Fragment,{children:[e("Description"),!!t&&(0,s.jsx)(p.A.Item,{shouldUpdate:(e,t)=>e.name!==t.name,noStyle:!0,children:e=>{let{getFieldValue:n,setFieldsValue:i}=e;return n("description")!==t&&(0,s.jsx)("a",{style:{marginLeft:10},onClick:()=>i({description:t}),children:(0,s.jsx)(j.A,{})})}})]}),name:"description",required:!0,style:{marginBottom:12},rules:[{required:!0,message:e("Please enter a description!")}],children:(0,s.jsx)(y.A.TextArea,{autoSize:!0,onKeyDown:e=>e.stopPropagation()})})},C=()=>{const{__:e}=(0,m.s)();return(0,s.jsx)(p.A.Item,{label:e("Name"),name:"name",required:!0,style:{marginBottom:12},rules:[{required:!0,message:e("Please enter a name!")}],children:(0,s.jsx)(y.A,{autoFocus:!0,onKeyDown:e=>e.stopPropagation()})})};var k=n(17312);const A=()=>{const{__:e}=(0,m.s)(),{onCancel:t,recordId:n,languages:i,languageOnClick:r}=f();return(0,s.jsxs)("div",{children:[(null==i?void 0:i.length)>0&&(0,s.jsx)(p.A.Item,{label:e("Translations"),style:{marginBottom:12},children:(0,s.jsx)(k.r,{recordId:n,languages:i,onClick:r})}),(0,s.jsx)(C,{}),(0,s.jsx)(b,{}),(0,s.jsxs)(p.A.Item,{children:[!!t&&(0,s.jsx)("button",{className:"button",onClick:e=>{e.preventDefault(),t()},children:e("Cancel")}),(0,s.jsx)("input",{type:"submit",className:"button button-primary right",value:e("Save")})]})]})};var w=n(65216),O=n(71951),S=n(30617);const N=(0,a.PA)((e=>{let{onClose:t,edit:n,navigateAfterCreation:r=!0}=e;const{message:o}=u.A.useApp(),a=(0,c.Zp)(),{cookieStore:l,optionStore:{others:{defaultCookieGroupTexts:{[(null==n?void 0:n.data.name)||""]:d}}}}=(0,O.g)(),f=null==n?void 0:n.data,{form:j,defaultValues:y,isBusy:b,onFinish:C,onFinishFailed:k,onValuesChange:N,contextValue:T}=function(e){const{__:t}=(0,m.s)(),{attributes:n,template:s}=e,i={name:(null==n?void 0:n.name)||(null==s?void 0:s.name)||"",description:(null==n?void 0:n.description)||(null==s?void 0:s.description)||""};return{...(0,h.S)({...e,defaultValues:i,i18n:{successMessage:t("You have successfully saved the service group."),validationError:t("The service group could not be saved due to missing/invalid form values."),unloadConfirm:t("You have unsaved changes. If you leave this page, your changes will be discarded.")}}),defaultValues:i,contextValue:{defaultTemplateValues:s?i:{}}}}({template:d?{identifier:"none",version:1,name:"",description:d}:void 0,handleSave:async e=>{try{if(n)return n.setName(e.name),n.setDescription(e.description),await n.patch(),()=>{};{const n=new w.r(l.groups,{name:e.name,description:e.description,meta:{order:l.groups.entries.size,isEssential:!1,isDefault:!1}});return await n.persist(),null==t||t(),()=>r&&a(`/cookies/${n.key.toString()}`)}}catch(e){const{code:t,...n}=e.responseJSON;throw["term_exists","duplicate_term_slug"].indexOf(t)>-1?(0,S.__)("The service group already exists!"):n.message}}}),I=n?{name:f.name,description:f.description}:y,[_,E]=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,x.gm)(v,...t)}({...T,recordId:null==f?void 0:f.id,languages:null==f?void 0:f.multilingual,languageOnClick:async(e,t)=>{let{code:n,id:s}=t;try{const t=!1===s?(await(0,g.C)("rcb-cookie-group",e,n)).id:s,i=new URL(window.location.href);i.hash=`#/cookies/${t}`,i.searchParams.set("lang",n),window.location.href=i.toString()}catch(e){var i;if(!(null==(i=e.responseJSON)?void 0:i.message))throw e;o.error(e.responseJSON.message)}},onCancel:t});return(0,s.jsx)(_,{value:E,children:(0,s.jsxs)(i.A,{spinning:b,children:[(0,s.jsx)("strong",{children:n?(0,S.__)("Edit service group"):(0,S.__)("Add new service group")}),(0,s.jsx)("hr",{}),(0,s.jsx)(p.A,{name:"edit-group-form",form:j,style:{width:300},layout:"vertical",initialValues:I,onFinish:C,onFinishFailed:k,onValuesChange:N,labelWrap:!0,children:(0,s.jsx)(A,{})})]})})}));var T=n(53029),I=n(43012),_=n(64715),E=n(24262),D=n(55924),G=n(69609);const L=(0,a.PA)((e=>{let{group:t}=e;const{optionStore:{others:{hints:{deleteCookieGroup:n}}},cookieStore:{essentialGroup:r}}=(0,O.g)(),{busy:a,data:{id:d,name:u,description:p}}=t,[g,h]=(0,l.useState)(!1),m=(0,G.K)().group.key===t.key,x=d===r.key,v=(0,c.Zp)(),f=(0,l.useCallback)((async()=>{await t.delete({force:!0}),v(`/cookies/${r.key.toString()}`,{replace:!0})}),[t,r,m]);return(0,s.jsxs)(i.A,{spinning:a,children:[(0,s.jsx)(_.A,{title:p,placement:"bottomLeft",children:(0,s.jsx)("span",{children:u})}),m&&(0,s.jsx)(o.A,{open:g,arrow:{pointAtCenter:!0},content:(0,s.jsx)(N,{edit:t,onClose:()=>h(!1)}),placement:"bottomLeft",children:(0,s.jsx)(T.A,{onClick:()=>h(!0),style:{margin:0,marginLeft:5}})}),m&&(x?(0,s.jsx)(_.A,{placement:"bottomLeft",arrow:{pointAtCenter:!0},overlay:(0,S.__)('The "%s" service group cannot be deleted.',u),children:(0,s.jsx)(I.A,{style:{margin:0,marginLeft:5,color:"#d2d2d2"}})}):(0,s.jsx)(E.A,{title:(0,D.g)([(0,S.__)("Are you sure you want to delete this service group? All services within this group will also be deleted. Consider moving them before you delete the service group."),...n].join("\n\n")),placement:"bottom",onConfirm:f,okText:(0,S.__)("Delete"),cancelText:(0,S.__)("Cancel"),overlayStyle:{maxWidth:350},onOpenChange:()=>h(!1),children:(0,s.jsx)(I.A,{style:{margin:0,marginLeft:5}})}))]})}));var M=n(41669),P=n.n(M),F=n(89657);const B=(0,a.PA)((()=>{const{cookieStore:e,optionStore:t}=(0,O.g)(),{essentialGroup:n,groups:{sortedGroups:a}}=e,u=(0,c.g)(),{pathname:p}=(0,c.zy)(),g=p.indexOf("tcf-vendors")>-1,[h,m]=(0,l.useState)(!1),x=(g?"tcf-vendors":u.cookieGroup)||(null==n?void 0:n.key.toString()),v=!g&&!u.cookieGroup,f=(0,c.Zp)(),{isBannerActive:j,isOnlyRcbCookieCreated:y,isTcf:b}=t,{groups:C}=e,k=(e=>{const t=(0,l.useRef)(),{cookieStore:n}=(0,O.g)(),{groups:{entries:{size:s}},essentialGroup:i}=n;return(0,l.useLayoutEffect)((()=>{if(t.current&&e&&s>1){const e=P()(t.current).find(".rcb-antd-tabs-nav-wrap > div:first");return e.sortable({items:"> .rcb-antd-tabs-tab",helper:"clone",cancel:"",axis:"x",start:(e,t)=>{t.placeholder.height("auto")},update:()=>{const t=e.find("> div > [aria-controls]").toArray().map((e=>+e.getAttribute("aria-controls").split("-").pop()));n.groups.orderCookieGroups(t)}}),()=>e.sortable("destroy")}return()=>{}}),[t.current,s,n,i]),t})(!v),A=(0,l.useCallback)((()=>{v&&n&&f(b?"tcf-vendors":n.key.toString(),{replace:!0})}),[v,n,b]);return(0,d.Z)((async t=>{let{aborted:s}=t;n||(await e.fetchGroups(),s()||A())}),[n,A,b]),(0,l.useEffect)((()=>{A()}),[A]),v||0===a.length?(0,s.jsx)(i.A,{style:{margin:"auto",marginTop:15}}):(0,s.jsx)(i.A,{spinning:C.busy,children:(0,s.jsxs)("div",{ref:k,children:[!j&&!y&&(0,s.jsx)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:{margin:"10px 0 0 0"},children:(0,s.jsxs)("p",{children:[(0,S.__)("The cookie banner is globally deactivated in the settings and is therefore not displayed on your website.")," ","• ",(0,s.jsx)("a",{href:"#/settings",children:(0,S.__)("Enable now")})]})}),(0,s.jsx)(r.A,{destroyInactiveTabPane:!0,activeKey:x,tabBarExtraContent:-1===["tcf-vendors"].indexOf(x)&&(0,s.jsx)(o.A,{open:h,content:(0,s.jsx)(N,{onClose:()=>m(!1)}),placement:"bottomRight",children:(0,s.jsx)("button",{className:"button button-primary button-large",style:{marginTop:6},onClick:()=>m(!h),children:(0,S.__)("Add group")})}),onTabClick:e=>{f(`/cookies/${e}`)},items:[b&&{key:"tcf-vendors",label:(0,S.__)("TCF Vendors"),children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.sv,{}),(0,s.jsx)(F.b,{identifier:"tcf-vendor"})]})},b&&{key:"tcf-vendors-split",label:(0,s.jsx)("span",{children:"•"}),disabled:!0,children:(0,s.jsx)("div",{})},...a.map((e=>({key:e.key.toString(),label:(0,s.jsx)(L,{group:e}),children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.sv,{}),(0,s.jsx)(F.b,{identifier:"cookie"})]})})))].filter(Boolean)})]})})}))},2493:(e,t,n)=>{n.r(t),n.d(t,{CookiesList:()=>B});var s=n(3713),i=n(53573),r=n(19117),o=n(95962),a=n(73491),l=n(57922),c=n(41594),d=n(32150),u=n(52113);const p=Symbol(),g=()=>(0,u.NV)(p);var h=n(34650),m=n(75792),x=n(28101),v=n(19488),f=n(33210),j=n(74072),y=n(24262),b=n(64715),C=n(18197),k=n(36086),A=n(39555),w=n(43181),O=n(55924),S=n(59726),N=n(24325),T=n(17312);const{Paragraph:I}=j.A,_=e=>{let{busy:t,attributes:{isEssential:n,deleteHint:i,deletable:r,id:o,name:l,purpose:c,status:d,isEmbeddingOnlyExternalResources:u,uniqueName:p,technicalDefinitions:m,legalBasis:x,tagManagerOptInEventName:j,tagManagerOptOutEventName:_,executeCodeOptInWhenNoTagManagerConsentIsGiven:E,executeCodeOptOutWhenNoTagManagerConsentIsGiven:D,codeOptIn:G,codeOptOut:L,deleteTechnicalDefinitionsAfterOptOut:M,codeOnPageLoad:P,presetId:F},avatarUrl:B,isUpdateAvailable:R,languages:V,languageOnClick:U,dragHandle:W}=e;const{__:q}=(0,S.s)(),{isLicensed:$}=(0,N.J)(),{isConsentForwarding:z,onEdit:J,onDelete:H,setCookiesViaManager:K,isGcm:Z}=g(),Y=(0,w.G)(),X=null==m?void 0:m[0],{managerLabel:Q,serviceIsManager:ee,features:te}=(0,A.XR)(K,{isGcm:Z,presetId:F});return(0,s.jsx)(h.A.Item,{itemID:o.toString(),actions:[(0,s.jsx)("a",{onClick:()=>J(o),children:q(R?"Edit and update":"Edit")},"edit"),r?(0,s.jsx)(y.A,{title:(0,O.g)([q("Are you sure you want to delete this service?"),...i].join("\n\n")),placement:"bottomRight",onConfirm:()=>H(o),okText:q("Delete"),cancelText:q("Cancel"),overlayStyle:{maxWidth:350},children:(0,s.jsx)("a",{style:{cursor:"pointer"},children:q("Delete")})},"delete"):(0,s.jsx)(b.A,{placement:"topRight",arrow:{pointAtCenter:!0},overlay:q('The "%s" service cannot be deleted.',l),children:(0,s.jsx)("a",{style:{opacity:.5},children:q("Delete")})}),(null==V?void 0:V.length)&&(0,s.jsx)(T.r,{recordId:o,languages:V,onClick:U},"languages"),(0,s.jsx)("a",{children:W},"drag")].filter(Boolean),children:(0,s.jsx)(C.A,{spinning:t,children:(0,s.jsx)(h.A.Item.Meta,{avatar:B?(0,s.jsx)(k.A,{size:"large",src:B,shape:"square"}):(0,s.jsx)(k.A,{size:"large",style:{backgroundColor:u?"#4ea29a":Y[X.type].backgroundColor},children:u?q("None"):Y[X.type].abbr}),title:(0,s.jsxs)("span",{children:[l," ","draft"===d?(0,s.jsx)(a.A,{color:"orange",children:q("Draft")}):"private"===d?(0,s.jsx)(a.A,{color:"red",children:q("Disabled")}):null,!!u&&(0,s.jsx)(a.A,{children:q("No technical cookies")}),!n&&"legitimate-interest"===x&&(0,s.jsx)(a.A,{children:q("Legitimate interest (Opt-out)")}),n&&"legal-requirement"===x&&(0,s.jsx)(a.A,{children:q("Compliance with a legal obligation")}),!!F&&(0,s.jsx)(a.A,{children:q("Created from template")}),!!F&&!B&&(0,s.jsx)(b.A,{title:q($?"There is no longer a service template for this service. Probably the service has been discontinued. Please look for alternatives!":"This service was created from a template. As you do not have a license activated at the moment, updates that are potentially available cannot be downloaded."),children:(0,s.jsx)(a.A,{color:"red",children:q($?"No longer supported":"Possibly outdated")})}),!!R&&(0,s.jsx)(b.A,{title:q("The service template has been updated to provide current legal and technical information."),children:(0,s.jsx)(a.A,{color:"green",children:q("Update available")})})]}),description:(0,s.jsxs)("div",{children:[!!c&&(0,s.jsx)(I,{style:{color:"inherit",marginBottom:0},ellipsis:{rows:3},children:(0,O.g)(c)}),!!(G||L||P)&&(0,s.jsxs)("div",{style:{paddingTop:5},children:[q("Executes code on"),":"," ",!!G&&(0,s.jsxs)(a.A,{children:[q("Opt-in"),te.executeCodeWhenNoTagManagerConsentIsGiven&&E&&(0,s.jsx)(b.A,{title:q("Only run this script if the user has not consented to use %s",Q),children:(0,s.jsxs)("span",{children:[" ",(0,s.jsx)(v.A,{})]})})]}),(!!L||M)&&!n&&(0,s.jsxs)(a.A,{children:[q("Opt-out"),!1!==te.executeCodeWhenNoTagManagerConsentIsGiven&&D&&(0,s.jsx)(b.A,{title:q("Only run this script if the user has not consented to use %s",Q),children:(0,s.jsxs)("span",{children:[" ",(0,s.jsx)(v.A,{})]})}),M&&(0,s.jsx)(b.A,{title:q("Delete all first-party cookies after opt-out. First-party cookies are only cookies that are set by or for this domain."),children:(0,s.jsxs)("span",{children:[" ",(0,s.jsx)(f.A,{})]})})]}),!!P&&(0,s.jsx)(a.A,{children:q("Page load")})]}),!!Q&&(0,s.jsx)("div",{style:{paddingTop:5},children:ee?(0,s.jsxs)("span",{children:[q("Opt-in script loads"),": ",(0,s.jsx)(a.A,{children:Q})]}):te.events&&(0,s.jsxs)("span",{children:[q("%s Events",Q),":"," ",(0,s.jsxs)(a.A,{children:[(0,s.jsxs)("strong",{children:[q("Opt-in"),": "]}),j||q("None")]}),(0,s.jsxs)(a.A,{children:[(0,s.jsxs)("strong",{children:[q("Opt-out"),": "]}),_||q("None")]})]})}),z&&(0,s.jsxs)("div",{style:{paddingTop:5},children:[q("Consent Forwarding Unique Name"),": ",(0,s.jsx)(a.A,{children:p})]})]})})})})};var E=n(60111);const D=()=>{const{__:e,_i:t}=(0,S.s)(),{isEssential:n,isOnlyRealCookieBannerServiceCreated:i,busy:r,rows:o,onSort:a,onCreate:l,servicesCount:d,groupName:u}=g(),p=(0,c.useRef)(),{SortableContext:v,SortableRow:f,DragHandle:j}=(0,E.E)(),y=(0,c.useMemo)((()=>{const e=[];for(let t=0;t<d;t++)e.push({key:t});return e}),[d]),b=e("Add service"),C=n&&i?d>1:d>0,k=o.map((e=>{let{attributes:{id:t}}=e;return`${t}`}));return C?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"wp-clearfix",children:(0,s.jsx)("a",{onClick:l,className:"button button-primary right",style:{marginBottom:10},children:b})}),r?(0,s.jsx)(h.A,{dataSource:y,renderItem:()=>(0,s.jsx)(h.A.Item,{children:(0,s.jsx)(m.A,{loading:!0,active:!0,paragraph:{rows:1}})})}):(0,s.jsx)(v,{items:k,onDragEnd:e=>{let{active:t,over:n}=e;const s=o.findIndex((e=>e.attributes.id===+t.id)),i=o.findIndex((e=>e.attributes.id===+(null==n?void 0:n.id)));a(k.map(Number),s,i)},elementType:"div",children:(0,s.jsx)(h.A,{children:(0,s.jsx)("div",{ref:p,children:o.map(((e,t)=>(0,s.jsx)(f,{"data-row-key":`${e.attributes.id}`,children:(0,c.createElement)(_,{...e,key:e.attributes.id.toString(),dragHandle:(0,s.jsx)(j,{})})},e.attributes.id)))})})})]}):(0,s.jsx)(x.A,{description:t(e("You have not yet created a service in {{strong}}%s{{/strong}}.",u),{strong:(0,s.jsx)("strong",{})}),children:(0,s.jsx)("a",{className:"button button-primary",onClick:l,children:b})})};var G=n(53603),L=n(69609),M=n(71951),P=n(30617),F=n(40164);const B=(0,l.PA)((()=>{const{message:e}=r.A.useApp(),{optionStore:{isOnlyRcbCookieCreated:t,isConsentForwarding:n,isDataProcessingInUnsafeCountries:l,setCookiesViaManager:g,isGcm:h,others:{hints:{deleteCookie:m}}},cookieStore:{unassignedCookies:x,essentialGroup:v}}=(0,M.g)(),f=(0,G.m)("cookie"),{group:j,addCookieLink:y,editLink:b}=(0,L.K)(),{cookies:C,cookiesCount:k,data:{name:A}}=j,{busy:w,sortedCookies:O,entries:S}=C;(0,c.useEffect)((()=>{j.fetchCookies()}),[]);const[N,T]=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,u.gm)(p,...t)}({isConsentForwarding:n,isDataProcessingInUnsafeCountries:l,isOnlyRealCookieBannerServiceCreated:t,isEssential:v.key===j.key,setCookiesViaManager:g,isGcm:h,groupName:A,busy:w,servicesCount:k,rows:O.map((t=>{const{key:n,busy:s,data:i,templateModel:r,technicalDefinitions:o,isUpdateAvailable:a}=t,{title:{raw:l},content:{raw:c},status:u,meta:{presetId:p,codeOptIn:g,codeOptOut:h,codeOnPageLoad:x,uniqueName:f,deleteTechnicalDefinitionsAfterOptOut:j,executeCodeOptInWhenNoTagManagerConsentIsGiven:y,executeCodeOptOutWhenNoTagManagerConsentIsGiven:b,isEmbeddingOnlyExternalResources:C,legalBasis:k,tagManagerOptInEventName:A,tagManagerOptOutEventName:w}}=i;return{busy:s,attributes:{id:n,name:l,purpose:c,status:u,codeOptIn:g,codeOptOut:h,codeOnPageLoad:x,uniqueName:f,deletable:"real-cookie-banner"!==p,deleteHint:m,deleteTechnicalDefinitionsAfterOptOut:j,executeCodeOptInWhenNoTagManagerConsentIsGiven:y,executeCodeOptOutWhenNoTagManagerConsentIsGiven:b,isEmbeddingOnlyExternalResources:C,isEssential:i["rcb-cookie-group"][0]===v.key,legalBasis:k,presetId:p,tagManagerOptInEventName:A,tagManagerOptOutEventName:w,technicalDefinitions:o},avatarUrl:null==r?void 0:r.data.logoUrl,isUpdateAvailable:a,languages:i.multilingual,languageOnClick:async(t,n)=>{let{code:s,id:i,taxonomies:r}=n;try{let e,n;if(!1===i){const{id:i,taxonomies:{"rcb-cookie-group":r}}=await(0,d.C)("rcb-cookie",t,s);e=i,[n]=r}else e=i,[n]=r["rcb-cookie-group"];const o=new URL(window.location.href);o.hash=`#/cookies/${n}/edit/${e}`,o.searchParams.set("lang",s),window.location.href=o.toString()}catch(t){var o;if(!(null==(o=t.responseJSON)?void 0:o.message))throw t;e.error(t.responseJSON.message)}}}}))},{onSort:(e,t,n,s)=>{!function(e,t,n){const{length:s}=e,i=t<0?s+t:t;if(i>=0&&i<s){const i=n<0?s+n:n,[r]=e.splice(t,1);e.splice(i,0,r)}}(t,n,s),j.cookies.orderCookies(t)},onDelete:(e,t)=>S.get(t).delete({force:!0}),onEdit:(e,t)=>{window.location.href=b(S.get(t))},onCreate:()=>{window.location.href=y}},{inherit:["busy","groupName","isEssential","isOnlyRealCookieBannerServiceCreated","rows","servicesCount"]});return(0,s.jsxs)(F.e,{children:[x.size>0&&(0,s.jsx)("div",{style:{textAlign:"center",marginBottom:15},children:(0,s.jsx)(o.A,{placement:"bottomRight",menu:{items:Array.from(x.values()).map((e=>{let{id:t,title:n}=e;return{key:t,label:(0,s.jsx)("a",{href:`#/cookies/${v.key}/edit/${t}`,children:n})}}))},children:(0,s.jsx)("a",{children:(0,s.jsxs)(a.A,{color:"red",children:[(0,s.jsx)(i.A,{})," ",(0,P._n)("One unassigned service","%d unassigned services",x.size,x.size)]})})})}),(0,s.jsx)(N,{value:T,children:(0,s.jsx)(D,{})}),(0,s.jsx)("p",{className:"description",style:{maxWidth:800,margin:"30px auto 0",textAlign:"center"},children:f})]})}))},41122:(e,t,n)=>{n.r(t),n.d(t,{CookieTemplateCenter:()=>p});var s=n(3713),i=n(57922),r=n(41594),o=n(93859),a=n(62789),l=n(67120),c=n(76576),d=n(71951),u=n(40164);const p=(0,i.PA)((()=>{const{cookieStore:e}=(0,d.g)(),[t,n]=(0,r.useState)(!1),[i,p]=(0,r.useState)(),{force:g,attributes:h,navigateAfterCreation:m=!0}=(0,c.f)(),x=(0,r.useCallback)((async()=>{t||(n(!0),await e.fetchTemplatesServices());const s=Array.from(e.templatesServices.values()).map((e=>{let{data:t}=e;return t}));return s.sort(((e,t)=>e.headline.localeCompare(t.headline))),s}),[t]),[v,f]=(0,o.m)({type:"service",quickLinks:["service-individual","service-scanner","cookie-experts"],enableLocalFilter:!0,syncTemplates:()=>e.fetchTemplatesServices({storage:"redownload"}),fetchTemplates:x,fetchUse:async t=>(await x(),e.templatesServices.get(t).fetchUse()),initialSelection:g,onSelect:(e,t)=>p({identifier:null==e?void 0:e.identifier,version:null==e?void 0:e.version,overwriteAttributes:t&&h?JSON.parse(h):void 0})});return void 0===i?(0,s.jsx)(u.e,{children:(0,s.jsx)(v,{value:f,children:(0,s.jsx)(a.q,{})})}):(0,s.jsx)(u.e,{maxWidth:"fixed",children:(0,s.jsx)(l.CookieEditForm,{template:i.identifier?{identifier:i.identifier,version:i.version}:void 0,navigateAfterCreation:m,overwriteAttributes:i.overwriteAttributes})})}))}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/c5d7a09bcd567e3aa7a87f39d922a8e4/chunk-config-tab-cookies.lite.js.map
