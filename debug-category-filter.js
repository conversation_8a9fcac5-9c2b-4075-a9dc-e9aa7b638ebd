// Debug script to understand category filtering issue
// This should be run in the browser console on the events calendar page

console.log('=== Category Filter Debug ===');

// Check if the category filter exists
const picker = document.querySelector('.tec-events-category-color-filter');
console.log('Category picker found:', !!picker);

if (picker) {
    // Check checkboxes
    const checkboxes = document.querySelectorAll('.tec-events-category-color-filter__checkbox');
    console.log('Number of checkboxes:', checkboxes.length);
    
    checkboxes.forEach((checkbox, index) => {
        const label = checkbox.closest('label');
        const category = label ? label.dataset.category : 'unknown';
        console.log(`Checkbox ${index}: category="${category}", checked=${checkbox.checked}`);
    });
    
    // Check events and their classes
    const events = document.querySelectorAll('[class*="tribe_events_cat-"]');
    console.log('Events with tribe_events_cat- classes:', events.length);
    
    events.forEach((event, index) => {
        const classes = [...event.classList].filter(cls => cls.startsWith('tribe_events_cat-'));
        const hasHideClass = event.classList.contains('tec-category-filtered-hide');
        console.log(`Event ${index}:`, {
            categoryClasses: classes,
            isHidden: hasHideClass,
            element: event
        });
    });
    
    // Check for events with old format classes
    const oldFormatEvents = document.querySelectorAll('[class*="cat_"]');
    console.log('Events with cat_ classes:', oldFormatEvents.length);
    
    // Check what categories are currently selected
    const selectedCategories = [];
    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            const label = checkbox.closest('label');
            if (label && label.dataset.category) {
                selectedCategories.push(label.dataset.category);
            }
        }
    });
    console.log('Selected categories:', selectedCategories);
    
    // Check if the filtering logic is working
    if (selectedCategories.length > 0) {
        console.log('=== Filtering Analysis ===');
        events.forEach((event, index) => {
            const eventCategories = [...event.classList]
                .filter(cls => cls.startsWith('tribe_events_cat-'))
                .map(cls => cls.replace('tribe_events_cat-', ''));
            
            const shouldBeVisible = eventCategories.some(cat => selectedCategories.includes(cat));
            const isCurrentlyHidden = event.classList.contains('tec-category-filtered-hide');
            
            console.log(`Event ${index}:`, {
                eventCategories,
                shouldBeVisible,
                isCurrentlyHidden,
                status: shouldBeVisible === !isCurrentlyHidden ? 'CORRECT' : 'WRONG'
            });
        });
    }
} else {
    console.log('Category picker not found - feature may not be enabled');
}

// Check if the JavaScript is loaded
console.log('tribe.events.categoryColors:', typeof tribe?.events?.categoryColors);
console.log('categoryPicker function:', typeof tribe?.events?.categoryColors?.categoryPicker);
