<?php
/**
 * Simple test to check if post classes are being generated correctly
 * Run this from WordPress admin or via WP-CLI
 */

// Test a specific event ID - replace with an actual event ID from your site
$test_event_id = 1234; // Replace with a real event ID

// Get the event
$event = get_post($test_event_id);

if (!$event || $event->post_type !== 'tribe_events') {
    echo "Event not found or not a tribe_events post type\n";
    
    // Let's find a real event
    $events = get_posts([
        'post_type' => 'tribe_events',
        'posts_per_page' => 1,
        'post_status' => 'publish'
    ]);
    
    if (empty($events)) {
        echo "No events found in the database\n";
        exit;
    }
    
    $event = $events[0];
    echo "Using event: {$event->post_title} (ID: {$event->ID})\n";
}

echo "Testing post classes for event: {$event->post_title} (ID: {$event->ID})\n";

// Get categories for this event
$categories = get_the_terms($event->ID, 'tribe_events_cat');

if ($categories && !is_wp_error($categories)) {
    echo "Categories found:\n";
    foreach ($categories as $category) {
        echo "  - {$category->name} (slug: {$category->slug})\n";
    }
} else {
    echo "No categories found for this event\n";
}

// Get post classes using WordPress function
echo "\nPost classes from get_post_class():\n";
$classes = get_post_class([], $event->ID);
foreach ($classes as $class) {
    if (strpos($class, 'cat_') === 0 || strpos($class, 'tribe_events_cat-') === 0) {
        echo "  ✓ {$class}\n";
    } else {
        echo "    {$class}\n";
    }
}

// Test our specific function
echo "\nPost classes from tribe_get_post_class():\n";
if (function_exists('tribe_get_post_class')) {
    $tribe_classes = tribe_get_post_class(['test-class'], $event);
    foreach ($tribe_classes as $class) {
        if (strpos($class, 'cat_') === 0 || strpos($class, 'tribe_events_cat-') === 0) {
            echo "  ✓ {$class}\n";
        } else {
            echo "    {$class}\n";
        }
    }
} else {
    echo "tribe_get_post_class function not found\n";
}

// Check if the main class method is working
echo "\nTesting Tribe__Events__Main::post_class() method:\n";
if (class_exists('Tribe__Events__Main')) {
    $main = Tribe__Events__Main::instance();
    if (method_exists($main, 'post_class')) {
        $test_classes = ['test-class'];
        $result_classes = $main->post_class($test_classes, $event->ID);
        
        echo "Input classes: " . implode(', ', $test_classes) . "\n";
        echo "Output classes: " . implode(', ', $result_classes) . "\n";
        
        $category_classes = array_filter($result_classes, function($class) {
            return strpos($class, 'cat_') === 0 || strpos($class, 'tribe_events_cat-') === 0;
        });
        
        if (!empty($category_classes)) {
            echo "Category classes found: " . implode(', ', $category_classes) . "\n";
        } else {
            echo "No category classes found in output\n";
        }
    } else {
        echo "post_class method not found\n";
    }
} else {
    echo "Tribe__Events__Main class not found\n";
}
?>
