/*! For license information please see 502.lite.js.LICENSE.txt */
"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[502],{79272:(e,t,n)=>{n.d(t,{A:()=>c});var o=n(2464),r=n(41594);const l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"};var a=n(4679),i=function(e,t){return r.createElement(a.A,(0,o.A)({},e,{ref:t,icon:l}))};const c=r.forwardRef(i)},82031:(e,t,n)=>{n.d(t,{A:()=>c});var o=n(2464),r=n(41594);const l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"};var a=n(4679),i=function(e,t){return r.createElement(a.A,(0,o.A)({},e,{ref:t,icon:l}))};const c=r.forwardRef(i)},91502:(e,t,n)=>{n.d(t,{A:()=>Hr});var o=n(41594),r=n.n(o),l={},a="rc-table-internal-hook",i=n(61129),c=n(35649),d=n(78294),s=n(65033),u=n(75206);function f(e){var t=o.createContext(void 0);return{Context:t,Provider:function(e){var n=e.value,r=e.children,l=o.useRef(n);l.current=n;var a=o.useState((function(){return{getValue:function(){return l.current},listeners:new Set}})),c=(0,i.A)(a,1)[0];return(0,d.A)((function(){(0,u.unstable_batchedUpdates)((function(){c.listeners.forEach((function(e){e(n)}))}))}),[n]),o.createElement(t.Provider,{value:c},r)},defaultValue:e}}function p(e,t){var n=(0,c.A)("function"==typeof t?t:function(e){if(void 0===t)return e;if(!Array.isArray(t))return e[t];var n={};return t.forEach((function(t){n[t]=e[t]})),n}),r=o.useContext(null==e?void 0:e.Context),l=r||{},a=l.listeners,u=l.getValue,f=o.useRef();f.current=n(r?u():null==e?void 0:e.defaultValue);var p=o.useState({}),h=(0,i.A)(p,2)[1];return(0,d.A)((function(){if(r)return a.add(e),function(){a.delete(e)};function e(e){var t=n(e);(0,s.A)(f.current,t,!0)||h({})}}),[r]),f.current}var h=n(2464),m=n(2620);function g(){var e=o.createContext(null);function t(){return o.useContext(e)}return{makeImmutable:function(n,r){var l=(0,m.f3)(n),a=function(a,i){var c=l?{ref:i}:{},d=o.useRef(0),s=o.useRef(a);return null!==t()?o.createElement(n,(0,h.A)({},a,c)):(r&&!r(s.current,a)||(d.current+=1),s.current=a,o.createElement(e.Provider,{value:d.current},o.createElement(n,(0,h.A)({},a,c))))};return l?o.forwardRef(a):a},responseImmutable:function(e,n){var r=(0,m.f3)(e),l=function(n,l){var a=r?{ref:l}:{};return t(),o.createElement(e,(0,h.A)({},n,a))};return r?o.memo(o.forwardRef(l),n):o.memo(l,n)},useImmutableMark:t}}var v=g(),y=(v.makeImmutable,v.responseImmutable,v.useImmutableMark,g()),b=y.makeImmutable,x=y.responseImmutable,A=y.useImmutableMark;const C=f();var w=n(81188),k=n(58187),S=n(21483),E=n(65924),N=n.n(E),$=n(87031),K=n(25279),I=n(33717);const O=o.createContext({renderWithProps:!1});var R="RC_TABLE_KEY";function D(e){var t=[],n={};return e.forEach((function(e){for(var o,r=e||{},l=r.key,a=r.dataIndex,i=l||(o=a,null==o?[]:Array.isArray(o)?o:[o]).join("-")||R;n[i];)i="".concat(i,"_next");n[i]=!0,t.push(i)})),t}function P(e){return null!=e}var T=n(52733);function M(e){var t,n,r,l,a,c,d,u,f=e.component,m=e.children,g=e.ellipsis,v=e.scope,y=e.prefixCls,b=e.className,x=e.align,E=e.record,I=e.render,R=e.dataIndex,D=e.renderIndex,M=e.shouldCellUpdate,z=e.index,B=e.rowType,H=e.colSpan,L=e.rowSpan,j=e.fixLeft,F=e.fixRight,W=e.firstFixLeft,_=e.lastFixLeft,q=e.firstFixRight,V=e.lastFixRight,X=e.appendNode,U=e.additionalProps,G=void 0===U?{}:U,Y=e.isSticky,Q="".concat(y,"-cell"),J=p(C,["supportSticky","allColumnsFixedLeft","rowHoverable"]),Z=J.supportSticky,ee=J.allColumnsFixedLeft,te=J.rowHoverable,ne=function(e,t,n,r,l,a){var c=o.useContext(O),d=A();return(0,$.A)((function(){if(P(r))return[r];var a,i=null==t||""===t?[]:Array.isArray(t)?t:[t],d=(0,K.A)(e,i),s=d,u=void 0;if(l){var f=l(d,e,n);!(a=f)||"object"!==(0,w.A)(a)||Array.isArray(a)||o.isValidElement(a)?s=f:(s=f.children,u=f.props,c.renderWithProps=!0)}return[s,u]}),[d,e,r,t,l,n],(function(e,t){if(a){var n=(0,i.A)(e,2)[1],o=(0,i.A)(t,2)[1];return a(o,n)}return!!c.renderWithProps||!(0,s.A)(e,t,!0)}))}(E,R,D,m,I,M),oe=(0,i.A)(ne,2),re=oe[0],le=oe[1],ae={},ie="number"==typeof j&&Z,ce="number"==typeof F&&Z;ie&&(ae.position="sticky",ae.left=j),ce&&(ae.position="sticky",ae.right=F);var de=null!==(t=null!==(n=null!==(r=null==le?void 0:le.colSpan)&&void 0!==r?r:G.colSpan)&&void 0!==n?n:H)&&void 0!==t?t:1,se=null!==(l=null!==(a=null!==(c=null==le?void 0:le.rowSpan)&&void 0!==c?c:G.rowSpan)&&void 0!==a?a:L)&&void 0!==l?l:1,ue=function(e,t){return p(C,(function(n){var o,r,l,a;return[(o=e,r=t||1,l=n.hoverStartRow,a=n.hoverEndRow,o<=a&&o+r-1>=l),n.onHover]}))}(z,se),fe=(0,i.A)(ue,2),pe=fe[0],he=fe[1],me=(0,T._q)((function(e){var t;E&&he(z,z+se-1),null==G||null===(t=G.onMouseEnter)||void 0===t||t.call(G,e)})),ge=(0,T._q)((function(e){var t;E&&he(-1,-1),null==G||null===(t=G.onMouseLeave)||void 0===t||t.call(G,e)}));if(0===de||0===se)return null;var ve=null!==(d=G.title)&&void 0!==d?d:function(e){var t,n=e.ellipsis,r=e.rowType,l=e.children,a=!0===n?{showTitle:!0}:n;return a&&(a.showTitle||"header"===r)&&("string"==typeof l||"number"==typeof l?t=l.toString():o.isValidElement(l)&&"string"==typeof l.props.children&&(t=l.props.children)),t}({rowType:B,ellipsis:g,children:re}),ye=N()(Q,b,(u={},(0,S.A)(u,"".concat(Q,"-fix-left"),ie&&Z),(0,S.A)(u,"".concat(Q,"-fix-left-first"),W&&Z),(0,S.A)(u,"".concat(Q,"-fix-left-last"),_&&Z),(0,S.A)(u,"".concat(Q,"-fix-left-all"),_&&ee&&Z),(0,S.A)(u,"".concat(Q,"-fix-right"),ce&&Z),(0,S.A)(u,"".concat(Q,"-fix-right-first"),q&&Z),(0,S.A)(u,"".concat(Q,"-fix-right-last"),V&&Z),(0,S.A)(u,"".concat(Q,"-ellipsis"),g),(0,S.A)(u,"".concat(Q,"-with-append"),X),(0,S.A)(u,"".concat(Q,"-fix-sticky"),(ie||ce)&&Y&&Z),(0,S.A)(u,"".concat(Q,"-row-hover"),!le&&pe),u),G.className,null==le?void 0:le.className),be={};x&&(be.textAlign=x);var xe=(0,k.A)((0,k.A)((0,k.A)((0,k.A)({},ae),G.style),be),null==le?void 0:le.style),Ae=re;return"object"!==(0,w.A)(Ae)||Array.isArray(Ae)||o.isValidElement(Ae)||(Ae=null),g&&(_||q)&&(Ae=o.createElement("span",{className:"".concat(Q,"-content")},Ae)),o.createElement(f,(0,h.A)({},le,G,{className:ye,style:xe,title:ve,scope:v,onMouseEnter:te?me:void 0,onMouseLeave:te?ge:void 0,colSpan:1!==de?de:null,rowSpan:1!==se?se:null}),X,Ae)}const z=o.memo(M);function B(e,t,n,o,r){var l,a,i=n[e]||{},c=n[t]||{};"left"===i.fixed?l=o.left["rtl"===r?t:e]:"right"===c.fixed&&(a=o.right["rtl"===r?e:t]);var d=!1,s=!1,u=!1,f=!1,p=n[t+1],h=n[e-1],m=p&&!p.fixed||h&&!h.fixed||n.every((function(e){return"left"===e.fixed}));return"rtl"===r?void 0!==l?f=!(h&&"left"===h.fixed)&&m:void 0!==a&&(u=!(p&&"right"===p.fixed)&&m):void 0!==l?d=!(p&&"left"===p.fixed)&&m:void 0!==a&&(s=!(h&&"right"===h.fixed)&&m),{fixLeft:l,fixRight:a,lastFixLeft:d,firstFixRight:s,lastFixRight:u,firstFixLeft:f,isSticky:o.isSticky}}const H=o.createContext({});var L=n(4105),j=["children"];function F(e){return e.children}F.Row=function(e){var t=e.children,n=(0,L.A)(e,j);return o.createElement("tr",n,t)},F.Cell=function(e){var t=e.className,n=e.index,r=e.children,l=e.colSpan,a=void 0===l?1:l,i=e.rowSpan,c=e.align,d=p(C,["prefixCls","direction"]),s=d.prefixCls,u=d.direction,f=o.useContext(H),m=f.scrollColumnIndex,g=f.stickyOffsets,v=n+a-1+1===m?a+1:a,y=B(n,n+v-1,f.flattenColumns,g,u);return o.createElement(z,(0,h.A)({className:t,index:n,component:"td",prefixCls:s,record:null,dataIndex:null,align:c,colSpan:v,rowSpan:i,render:function(){return r}},y))};const W=F,_=x((function(e){var t=e.children,n=e.stickyOffsets,r=e.flattenColumns,l=p(C,"prefixCls"),a=r.length-1,i=r[a],c=o.useMemo((function(){return{stickyOffsets:n,flattenColumns:r,scrollColumnIndex:null!=i&&i.scrollbar?a:null}}),[i,r,a,n]);return o.createElement(H.Provider,{value:c},o.createElement("tfoot",{className:"".concat(l,"-summary")},t))}));var q=W,V=n(87458),X=n(23948),U=n(78255),G=n(72054),Y=n(35658);function Q(e,t,n,o,r,l,a){e.push({record:t,indent:n,index:a});var i=l(t),c=null==r?void 0:r.has(i);if(t&&Array.isArray(t[o])&&c)for(var d=0;d<t[o].length;d+=1)Q(e,t[o][d],n+1,o,r,l,d)}function J(e,t,n,r){return o.useMemo((function(){if(null!=n&&n.size){for(var o=[],l=0;l<(null==e?void 0:e.length);l+=1)Q(o,e[l],0,t,n,r,l);return o}return null==e?void 0:e.map((function(e,t){return{record:e,indent:0,index:t}}))}),[e,t,n,r])}function Z(e,t,n,o){var r,l=p(C,["prefixCls","fixedInfoList","flattenColumns","expandableType","expandRowByClick","onTriggerExpand","rowClassName","expandedRowClassName","indentSize","expandIcon","expandedRowRender","expandIconColumnIndex","expandedKeys","childrenColumnName","rowExpandable","onRow"]),a=l.flattenColumns,i=l.expandableType,c=l.expandedKeys,d=l.childrenColumnName,s=l.onTriggerExpand,u=l.rowExpandable,f=l.onRow,h=l.expandRowByClick,m=l.rowClassName,g="nest"===i,v="row"===i&&(!u||u(e)),y=v||g,b=c&&c.has(t),x=d&&e&&e[d],A=(0,T._q)(s),w=null==f?void 0:f(e,n),S=null==w?void 0:w.onClick;"string"==typeof m?r=m:"function"==typeof m&&(r=m(e,n,o));var E=D(a);return(0,k.A)((0,k.A)({},l),{},{columnsKey:E,nestExpandable:g,expanded:b,hasNestChildren:x,record:e,onTriggerExpand:A,rowSupportExpand:v,expandable:y,rowProps:(0,k.A)((0,k.A)({},w),{},{className:N()(r,null==w?void 0:w.className),onClick:function(t){h&&y&&s(e,t);for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];null==S||S.apply(void 0,[t].concat(o))}})})}const ee=function(e){var t=e.prefixCls,n=e.children,r=e.component,l=e.cellComponent,a=e.className,i=e.expanded,c=e.colSpan,d=e.isEmpty,s=p(C,["scrollbarSize","fixHeader","fixColumn","componentWidth","horizonScroll"]),u=s.scrollbarSize,f=s.fixHeader,h=s.fixColumn,m=s.componentWidth,g=s.horizonScroll,v=n;return(d?g&&m:h)&&(v=o.createElement("div",{style:{width:m-(f?u:0),position:"sticky",left:0,overflow:"hidden"},className:"".concat(t,"-expanded-row-fixed")},v)),o.createElement(r,{className:a,style:{display:i?null:"none"}},o.createElement(z,{component:l,prefixCls:t,colSpan:c},v))};function te(e,t,n,r,l){var a,i,c=e.record,d=e.prefixCls,s=e.columnsKey,u=e.fixedInfoList,f=e.expandIconColumnIndex,p=e.nestExpandable,h=e.indentSize,m=e.expandIcon,g=e.expanded,v=e.hasNestChildren,y=e.onTriggerExpand,b=s[n],x=u[n];return n===(f||0)&&p&&(a=o.createElement(o.Fragment,null,o.createElement("span",{style:{paddingLeft:"".concat(h*r,"px")},className:"".concat(d,"-row-indent indent-level-").concat(r)}),m({prefixCls:d,expanded:g,expandable:v,record:c,onExpand:y}))),t.onCell&&(i=t.onCell(c,l)),{key:b,fixedInfo:x,appendCellNode:a,additionalCellProps:i||{}}}const ne=x((function(e){var t=e.className,n=e.style,r=e.record,l=e.index,a=e.renderIndex,i=e.rowKey,c=e.indent,d=void 0===c?0:c,s=e.rowComponent,u=e.cellComponent,f=e.scopeCellComponent,p=Z(r,i,l,d),m=p.prefixCls,g=p.flattenColumns,v=p.expandedRowClassName,y=p.expandedRowRender,b=p.rowProps,x=p.expanded,A=p.rowSupportExpand,C=o.useRef(!1);C.current||(C.current=x);var w,S=v&&v(r,l,d),E=o.createElement(s,(0,h.A)({},b,{"data-row-key":i,className:N()(t,"".concat(m,"-row"),"".concat(m,"-row-level-").concat(d),null==b?void 0:b.className,d>=1?S:""),style:(0,k.A)((0,k.A)({},n),null==b?void 0:b.style)}),g.map((function(e,t){var n=e.render,i=e.dataIndex,c=e.className,s=te(p,e,t,d,l),g=s.key,v=s.fixedInfo,y=s.appendCellNode,b=s.additionalCellProps;return o.createElement(z,(0,h.A)({className:c,ellipsis:e.ellipsis,align:e.align,scope:e.rowScope,component:e.rowScope?f:u,prefixCls:m,key:g,record:r,index:l,renderIndex:a,dataIndex:i,render:n,shouldCellUpdate:e.shouldCellUpdate},v,{appendNode:y,additionalProps:b}))})));if(A&&(C.current||x)){var $=y(r,l,d+1,x);w=o.createElement(ee,{expanded:x,className:N()("".concat(m,"-expanded-row"),"".concat(m,"-expanded-row-level-").concat(d+1),S),prefixCls:m,component:s,cellComponent:u,colSpan:g.length,isEmpty:!1},$)}return o.createElement(o.Fragment,null,E,w)}));function oe(e){var t=e.columnKey,n=e.onColumnResize,r=o.useRef();return o.useEffect((function(){r.current&&n(t,r.current.offsetWidth)}),[]),o.createElement(V.A,{data:t},o.createElement("td",{ref:r,style:{padding:0,border:0,height:0}},o.createElement("div",{style:{height:0,overflow:"hidden"}}," ")))}function re(e){var t=e.prefixCls,n=e.columnsKey,r=e.onColumnResize;return o.createElement("tr",{"aria-hidden":"true",className:"".concat(t,"-measure-row"),style:{height:0,fontSize:0}},o.createElement(V.A.Collection,{onBatchResize:function(e){e.forEach((function(e){var t=e.data,n=e.size;r(t,n.offsetWidth)}))}},n.map((function(e){return o.createElement(oe,{key:e,columnKey:e,onColumnResize:r})}))))}const le=x((function(e){var t,n=e.data,r=e.measureColumnWidth,l=p(C,["prefixCls","getComponent","onColumnResize","flattenColumns","getRowKey","expandedKeys","childrenColumnName","emptyNode"]),a=l.prefixCls,i=l.getComponent,c=l.onColumnResize,d=l.flattenColumns,s=l.getRowKey,u=l.expandedKeys,f=l.childrenColumnName,h=l.emptyNode,m=J(n,f,u,s),g=o.useRef({renderWithProps:!1}),v=i(["body","wrapper"],"tbody"),y=i(["body","row"],"tr"),b=i(["body","cell"],"td"),x=i(["body","cell"],"th");t=n.length?m.map((function(e,t){var n=e.record,r=e.indent,l=e.index,a=s(n,t);return o.createElement(ne,{key:a,rowKey:a,record:n,index:t,renderIndex:l,rowComponent:y,cellComponent:b,scopeCellComponent:x,getRowKey:s,indent:r})})):o.createElement(ee,{expanded:!0,className:"".concat(a,"-placeholder"),prefixCls:a,component:y,cellComponent:b,colSpan:d.length,isEmpty:!0},h);var A=D(d);return o.createElement(O.Provider,{value:g.current},o.createElement(v,{className:"".concat(a,"-tbody")},r&&o.createElement(re,{prefixCls:a,columnsKey:A,onColumnResize:c}),t))}));var ae=["expandable"],ie="RC_TABLE_INTERNAL_COL_DEFINE",ce=["columnType"];const de=function(e){for(var t=e.colWidths,n=e.columns,r=[],l=!1,a=(e.columCount||n.length)-1;a>=0;a-=1){var i=t[a],c=n&&n[a],d=c&&c[ie];if(i||d||l){var s=d||{},u=(s.columnType,(0,L.A)(s,ce));r.unshift(o.createElement("col",(0,h.A)({key:a,style:{width:i}},u))),l=!0}}return o.createElement("colgroup",null,r)};var se=n(18539),ue=["className","noData","columns","flattenColumns","colWidths","columCount","stickyOffsets","direction","fixHeader","stickyTopOffset","stickyBottomOffset","stickyClassName","onScroll","maxContentScroll","children"],fe=o.forwardRef((function(e,t){var n=e.className,r=e.noData,l=e.columns,a=e.flattenColumns,i=e.colWidths,c=e.columCount,d=e.stickyOffsets,s=e.direction,u=e.fixHeader,f=e.stickyTopOffset,h=e.stickyBottomOffset,g=e.stickyClassName,v=e.onScroll,y=e.maxContentScroll,b=e.children,x=(0,L.A)(e,ue),A=p(C,["prefixCls","scrollbarSize","isSticky","getComponent"]),w=A.prefixCls,E=A.scrollbarSize,$=A.isSticky,K=(0,A.getComponent)(["header","table"],"table"),I=$&&!u?0:E,O=o.useRef(null),R=o.useCallback((function(e){(0,m.Xf)(t,e),(0,m.Xf)(O,e)}),[]);o.useEffect((function(){var e;function t(e){var t=e,n=t.currentTarget,o=t.deltaX;o&&(v({currentTarget:n,scrollLeft:n.scrollLeft+o}),e.preventDefault())}return null===(e=O.current)||void 0===e||e.addEventListener("wheel",t,{passive:!1}),function(){var e;null===(e=O.current)||void 0===e||e.removeEventListener("wheel",t)}}),[]);var D=o.useMemo((function(){return a.every((function(e){return e.width}))}),[a]),P=a[a.length-1],T={fixed:P?P.fixed:null,scrollbar:!0,onHeaderCell:function(){return{className:"".concat(w,"-cell-scrollbar")}}},M=(0,o.useMemo)((function(){return I?[].concat((0,se.A)(l),[T]):l}),[I,l]),z=(0,o.useMemo)((function(){return I?[].concat((0,se.A)(a),[T]):a}),[I,a]),B=(0,o.useMemo)((function(){var e=d.right,t=d.left;return(0,k.A)((0,k.A)({},d),{},{left:"rtl"===s?[].concat((0,se.A)(t.map((function(e){return e+I}))),[0]):t,right:"rtl"===s?e:[].concat((0,se.A)(e.map((function(e){return e+I}))),[0]),isSticky:$})}),[I,d,$]),H=function(e,t){return(0,o.useMemo)((function(){for(var n=[],o=0;o<t;o+=1){var r=e[o];if(void 0===r)return null;n[o]=r}return n}),[e.join("_"),t])}(i,c);return o.createElement("div",{style:(0,k.A)({overflow:"hidden"},$?{top:f,bottom:h}:{}),ref:R,className:N()(n,(0,S.A)({},g,!!g))},o.createElement(K,{style:{tableLayout:"fixed",visibility:r||H?null:"hidden"}},(!r||!y||D)&&o.createElement(de,{colWidths:H?[].concat((0,se.A)(H),[I]):[],columCount:c+1,columns:z}),b((0,k.A)((0,k.A)({},x),{},{stickyOffsets:B,columns:M,flattenColumns:z}))))}));const pe=o.memo(fe),he=function(e){var t,n=e.cells,r=e.stickyOffsets,l=e.flattenColumns,a=e.rowComponent,i=e.cellComponent,c=e.onHeaderRow,d=e.index,s=p(C,["prefixCls","direction"]),u=s.prefixCls,f=s.direction;c&&(t=c(n.map((function(e){return e.column})),d));var m=D(n.map((function(e){return e.column})));return o.createElement(a,t,n.map((function(e,t){var n,a=e.column,c=B(e.colStart,e.colEnd,l,r,f);return a&&a.onHeaderCell&&(n=e.column.onHeaderCell(a)),o.createElement(z,(0,h.A)({},e,{scope:a.title?e.colSpan>1?"colgroup":"col":null,ellipsis:a.ellipsis,align:a.align,component:i,prefixCls:u,key:m[t]},c,{additionalProps:n,rowType:"header"}))})))},me=x((function(e){var t=e.stickyOffsets,n=e.columns,r=e.flattenColumns,l=e.onHeaderRow,a=p(C,["prefixCls","getComponent"]),i=a.prefixCls,c=a.getComponent,d=o.useMemo((function(){return function(e){var t=[];!function e(n,o){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;t[r]=t[r]||[];var l=o;return n.filter(Boolean).map((function(n){var o={key:n.key,className:n.className||"",children:n.title,column:n,colStart:l},a=1,i=n.children;return i&&i.length>0&&(a=e(i,l,r+1).reduce((function(e,t){return e+t}),0),o.hasSubColumns=!0),"colSpan"in n&&(a=n.colSpan),"rowSpan"in n&&(o.rowSpan=n.rowSpan),o.colSpan=a,o.colEnd=o.colStart+a-1,t[r].push(o),l+=a,a}))}(e,0);for(var n=t.length,o=function(e){t[e].forEach((function(t){"rowSpan"in t||t.hasSubColumns||(t.rowSpan=n-e)}))},r=0;r<n;r+=1)o(r);return t}(n)}),[n]),s=c(["header","wrapper"],"thead"),u=c(["header","row"],"tr"),f=c(["header","cell"],"th");return o.createElement(s,{className:"".concat(i,"-thead")},d.map((function(e,n){return o.createElement(he,{key:n,flattenColumns:r,cells:e,stickyOffsets:t,rowComponent:u,cellComponent:f,onHeaderRow:l,index:n})})))}));var ge=n(51963);function ve(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"number"==typeof t?t:t.endsWith("%")?e*parseFloat(t)/100:null}var ye=["children"],be=["fixed"];function xe(e){return(0,ge.A)(e).filter((function(e){return o.isValidElement(e)})).map((function(e){var t=e.key,n=e.props,o=n.children,r=(0,L.A)(n,ye),l=(0,k.A)({key:t},r);return o&&(l.children=xe(o)),l}))}function Ae(e){return e.filter((function(e){return e&&"object"===(0,w.A)(e)&&!e.hidden})).map((function(e){var t=e.children;return t&&t.length>0?(0,k.A)((0,k.A)({},e),{},{children:Ae(t)}):e}))}function Ce(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"key";return e.filter((function(e){return e&&"object"===(0,w.A)(e)})).reduce((function(e,n,o){var r=n.fixed,l=!0===r?"left":r,a="".concat(t,"-").concat(o),i=n.children;return i&&i.length>0?[].concat((0,se.A)(e),(0,se.A)(Ce(i,a).map((function(e){return(0,k.A)({fixed:l},e)})))):[].concat((0,se.A)(e),[(0,k.A)((0,k.A)({key:a},n),{},{fixed:l})])}),[])}const we=function(e,t){var n=e.prefixCls,r=e.columns,a=e.children,c=e.expandable,d=e.expandedKeys,s=e.columnTitle,u=e.getRowKey,f=e.onTriggerExpand,p=e.expandIcon,h=e.rowExpandable,m=e.expandIconColumnIndex,g=e.direction,v=e.expandRowByClick,y=e.columnWidth,b=e.fixed,x=e.scrollWidth,A=e.clientWidth,C=o.useMemo((function(){return Ae((r||xe(a)||[]).slice())}),[r,a]),w=o.useMemo((function(){if(c){var e,t=C.slice();if(!t.includes(l)){var r=m||0;r>=0&&t.splice(r,0,l)}var a=t.indexOf(l);t=t.filter((function(e,t){return e!==l||t===a}));var i,g=C[a];i="left"!==b&&!b||m?"right"!==b&&!b||m!==C.length?g?g.fixed:null:"right":"left";var x=(e={},(0,S.A)(e,ie,{className:"".concat(n,"-expand-icon-col"),columnType:"EXPAND_COLUMN"}),(0,S.A)(e,"title",s),(0,S.A)(e,"fixed",i),(0,S.A)(e,"className","".concat(n,"-row-expand-icon-cell")),(0,S.A)(e,"width",y),(0,S.A)(e,"render",(function(e,t,r){var l=u(t,r),a=d.has(l),i=!h||h(t),c=p({prefixCls:n,expanded:a,expandable:i,record:t,onExpand:f});return v?o.createElement("span",{onClick:function(e){return e.stopPropagation()}},c):c})),e);return t.map((function(e){return e===l?x:e}))}return C.filter((function(e){return e!==l}))}),[c,C,u,d,p,g]),E=o.useMemo((function(){var e=w;return t&&(e=t(e)),e.length||(e=[{render:function(){return null}}]),e}),[t,w,g]),N=o.useMemo((function(){return"rtl"===g?function(e){return e.map((function(e){var t=e.fixed,n=(0,L.A)(e,be),o=t;return"left"===t?o="right":"right"===t&&(o="left"),(0,k.A)({fixed:o},n)}))}(Ce(E)):Ce(E)}),[E,g,x]),$=o.useMemo((function(){for(var e=-1,t=N.length-1;t>=0;t-=1){var n=N[t].fixed;if("left"===n||!0===n){e=t;break}}if(e>=0)for(var o=0;o<=e;o+=1){var r=N[o].fixed;if("left"!==r&&!0!==r)return!0}var l=N.findIndex((function(e){return"right"===e.fixed}));if(l>=0)for(var a=l;a<N.length;a+=1)if("right"!==N[a].fixed)return!0;return!1}),[N]),K=function(e,t,n){return o.useMemo((function(){if(t&&t>0){var o=0,r=0;e.forEach((function(e){var n=ve(t,e.width);n?o+=n:r+=1}));var l=Math.max(t,n),a=Math.max(l-o,r),i=r,c=a/r,d=0,s=e.map((function(e){var n=(0,k.A)({},e),o=ve(t,n.width);if(o)n.width=o;else{var r=Math.floor(c);n.width=1===i?a:r,a-=r,i-=1}return d+=n.width,n}));if(d<l){var u=l/d;a=l,s.forEach((function(e,t){var n=Math.floor(e.width*u);e.width=t===s.length-1?a:n,a-=n}))}return[s,Math.max(d,l)]}return[e,t]}),[e,t,n])}(N,x,A),I=(0,i.A)(K,2),O=I[0],R=I[1];return[E,O,R,$]};function ke(e){var t,n=e.prefixCls,r=e.record,l=e.onExpand,a=e.expanded,i=e.expandable,c="".concat(n,"-row-expand-icon");return i?o.createElement("span",{className:N()(c,(t={},(0,S.A)(t,"".concat(n,"-row-expanded"),a),(0,S.A)(t,"".concat(n,"-row-collapsed"),!a),t)),onClick:function(e){l(r,e),e.stopPropagation()}}):o.createElement("span",{className:N()(c,"".concat(n,"-row-spaced"))})}function Se(e){var t=(0,o.useRef)(e),n=(0,o.useState)({}),r=(0,i.A)(n,2)[1],l=(0,o.useRef)(null),a=(0,o.useRef)([]);return(0,o.useEffect)((function(){return function(){l.current=null}}),[]),[t.current,function(e){a.current.push(e);var n=Promise.resolve();l.current=n,n.then((function(){if(l.current===n){var e=a.current,o=t.current;a.current=[],e.forEach((function(e){t.current=e(t.current)})),l.current=null,o!==t.current&&r({})}}))}]}var Ee=(0,n(39017).A)()?window:null;const Ne=function(e){var t=e.className,n=e.children;return o.createElement("div",{className:t},n)};var $e=n(16741),Ke=n(23797),Ie=n(32664),Oe=function(e,t){var n,r,l=e.scrollBodyRef,a=e.onScroll,c=e.offsetScroll,d=e.container,s=p(C,"prefixCls"),u=(null===(n=l.current)||void 0===n?void 0:n.scrollWidth)||0,f=(null===(r=l.current)||void 0===r?void 0:r.clientWidth)||0,h=u&&f*(f/u),m=o.useRef(),g=Se({scrollLeft:0,isHiddenScrollBar:!1}),v=(0,i.A)(g,2),y=v[0],b=v[1],x=o.useRef({delta:0,x:0}),A=o.useState(!1),w=(0,i.A)(A,2),E=w[0],$=w[1],K=o.useRef(null);o.useEffect((function(){return function(){Ie.A.cancel(K.current)}}),[]);var I=function(){$(!1)},O=function(e){var t,n=(e||(null===(t=window)||void 0===t?void 0:t.event)).buttons;if(E&&0!==n){var o=x.current.x+e.pageX-x.current.x-x.current.delta;o<=0&&(o=0),o+h>=f&&(o=f-h),a({scrollLeft:o/f*(u+2)}),x.current.x=e.pageX}else E&&$(!1)},R=function(){K.current=(0,Ie.A)((function(){if(l.current){var e=(0,Ke.A3)(l.current).top,t=e+l.current.offsetHeight,n=d===window?document.documentElement.scrollTop+window.innerHeight:(0,Ke.A3)(d).top+d.clientHeight;t-(0,G.A)()<=n||e>=n-c?b((function(e){return(0,k.A)((0,k.A)({},e),{},{isHiddenScrollBar:!0})})):b((function(e){return(0,k.A)((0,k.A)({},e),{},{isHiddenScrollBar:!1})}))}}))},D=function(e){b((function(t){return(0,k.A)((0,k.A)({},t),{},{scrollLeft:e/u*f||0})}))};return o.useImperativeHandle(t,(function(){return{setScrollLeft:D,checkScrollBarVisible:R}})),o.useEffect((function(){var e=(0,$e.A)(document.body,"mouseup",I,!1),t=(0,$e.A)(document.body,"mousemove",O,!1);return R(),function(){e.remove(),t.remove()}}),[h,E]),o.useEffect((function(){var e=(0,$e.A)(d,"scroll",R,!1),t=(0,$e.A)(window,"resize",R,!1);return function(){e.remove(),t.remove()}}),[d]),o.useEffect((function(){y.isHiddenScrollBar||b((function(e){var t=l.current;return t?(0,k.A)((0,k.A)({},e),{},{scrollLeft:t.scrollLeft/t.scrollWidth*t.clientWidth}):e}))}),[y.isHiddenScrollBar]),u<=f||!h||y.isHiddenScrollBar?null:o.createElement("div",{style:{height:(0,G.A)(),width:f,bottom:c},className:"".concat(s,"-sticky-scroll")},o.createElement("div",{onMouseDown:function(e){e.persist(),x.current.delta=e.pageX-y.scrollLeft,x.current.x=0,$(!0),e.preventDefault()},ref:m,className:N()("".concat(s,"-sticky-scroll-bar"),(0,S.A)({},"".concat(s,"-sticky-scroll-bar-active"),E)),style:{width:"".concat(h,"px"),transform:"translate3d(".concat(y.scrollLeft,"px, 0, 0)")}}))};const Re=o.forwardRef(Oe);var De="rc-table",Pe=[],Te={};function Me(){return"No Data"}function ze(e,t){var n,r=(0,k.A)({rowKey:"key",prefixCls:De,emptyText:Me},e),l=r.prefixCls,d=r.className,u=r.rowClassName,f=r.style,p=r.data,m=r.rowKey,g=r.scroll,v=r.tableLayout,y=r.direction,b=r.title,x=r.footer,A=r.summary,E=r.caption,I=r.id,O=r.showHeader,R=r.components,T=r.emptyText,M=r.onRow,z=r.onHeaderRow,H=r.onScroll,j=r.internalHooks,F=r.transformColumns,q=r.internalRefs,Q=r.tailor,J=r.getContainerWidth,Z=r.sticky,ee=r.rowHoverable,te=void 0===ee||ee,ne=p||Pe,oe=!!ne.length,re=j===a,ie=o.useCallback((function(e,t){return(0,K.A)(R,e)||t}),[R]),ce=o.useMemo((function(){return"function"==typeof m?m:function(e){return e&&e[m]}}),[m]),ue=ie(["body"]),fe=function(){var e=o.useState(-1),t=(0,i.A)(e,2),n=t[0],r=t[1],l=o.useState(-1),a=(0,i.A)(l,2),c=a[0],d=a[1];return[n,c,o.useCallback((function(e,t){r(e),d(t)}),[])]}(),he=(0,i.A)(fe,3),ge=he[0],ve=he[1],ye=he[2],be=function(e,t,n){var r=function(e){var t,n=e.expandable,o=(0,L.A)(e,ae);return!1===(t="expandable"in e?(0,k.A)((0,k.A)({},o),n):o).showExpandColumn&&(t.expandIconColumnIndex=-1),t}(e),l=r.expandIcon,c=r.expandedRowKeys,d=r.defaultExpandedRowKeys,s=r.defaultExpandAllRows,u=r.expandedRowRender,f=r.onExpand,p=r.onExpandedRowsChange,h=l||ke,m=r.childrenColumnName||"children",g=o.useMemo((function(){return u?"row":!!(e.expandable&&e.internalHooks===a&&e.expandable.__PARENT_RENDER_ICON__||t.some((function(e){return e&&"object"===(0,w.A)(e)&&e[m]})))&&"nest"}),[!!u,t]),v=o.useState((function(){return d||(s?function(e,t,n){var o=[];return function e(r){(r||[]).forEach((function(r,l){o.push(t(r,l)),e(r[n])}))}(e),o}(t,n,m):[])})),y=(0,i.A)(v,2),b=y[0],x=y[1],A=o.useMemo((function(){return new Set(c||b||[])}),[c,b]),C=o.useCallback((function(e){var o,r=n(e,t.indexOf(e)),l=A.has(r);l?(A.delete(r),o=(0,se.A)(A)):o=[].concat((0,se.A)(A),[r]),x(o),f&&f(!l,e),p&&p(o)}),[n,A,t,f,p]);return[r,g,A,h,m,C]}(r,ne,ce),xe=(0,i.A)(be,6),Ae=xe[0],Ce=xe[1],$e=xe[2],Ke=xe[3],Ie=xe[4],Oe=xe[5],ze=null==g?void 0:g.x,Be=o.useState(0),He=(0,i.A)(Be,2),Le=He[0],je=He[1],Fe=we((0,k.A)((0,k.A)((0,k.A)({},r),Ae),{},{expandable:!!Ae.expandedRowRender,columnTitle:Ae.columnTitle,expandedKeys:$e,getRowKey:ce,onTriggerExpand:Oe,expandIcon:Ke,expandIconColumnIndex:Ae.expandIconColumnIndex,direction:y,scrollWidth:re&&Q&&"number"==typeof ze?ze:null,clientWidth:Le}),re?F:null),We=(0,i.A)(Fe,4),_e=We[0],qe=We[1],Ve=We[2],Xe=We[3],Ue=null!=Ve?Ve:ze,Ge=o.useMemo((function(){return{columns:_e,flattenColumns:qe}}),[_e,qe]),Ye=o.useRef(),Qe=o.useRef(),Je=o.useRef(),Ze=o.useRef();o.useImperativeHandle(t,(function(){return{nativeElement:Ye.current,scrollTo:function(e){var t;if(Je.current instanceof HTMLElement){var n=e.index,o=e.top,r=e.key;if(o){var l;null===(l=Je.current)||void 0===l||l.scrollTo({top:o})}else{var a,i=null!=r?r:ce(ne[n]);null===(a=Je.current.querySelector('[data-row-key="'.concat(i,'"]')))||void 0===a||a.scrollIntoView()}}else null!==(t=Je.current)&&void 0!==t&&t.scrollTo&&Je.current.scrollTo(e)}}}));var et,tt,nt,ot=o.useRef(),rt=o.useState(!1),lt=(0,i.A)(rt,2),at=lt[0],it=lt[1],ct=o.useState(!1),dt=(0,i.A)(ct,2),st=dt[0],ut=dt[1],ft=Se(new Map),pt=(0,i.A)(ft,2),ht=pt[0],mt=pt[1],gt=D(qe).map((function(e){return ht.get(e)})),vt=o.useMemo((function(){return gt}),[gt.join("_")]),yt=function(e,t,n){return(0,o.useMemo)((function(){var o=t.length,r=function(n,o,r){for(var l=[],a=0,i=n;i!==o;i+=r)l.push(a),t[i].fixed&&(a+=e[i]||0);return l},l=r(0,o,1),a=r(o-1,-1,-1).reverse();return"rtl"===n?{left:a,right:l}:{left:l,right:a}}),[e,t,n])}(vt,qe,y),bt=g&&P(g.y),xt=g&&P(Ue)||Boolean(Ae.fixed),At=xt&&qe.some((function(e){return e.fixed})),Ct=o.useRef(),wt=function(e,t){var n="object"===(0,w.A)(e)?e:{},r=n.offsetHeader,l=void 0===r?0:r,a=n.offsetSummary,i=void 0===a?0:a,c=n.offsetScroll,d=void 0===c?0:c,s=n.getContainer,u=(void 0===s?function(){return Ee}:s)()||Ee;return o.useMemo((function(){var n=!!e;return{isSticky:n,stickyClassName:n?"".concat(t,"-sticky-holder"):"",offsetHeader:l,offsetSummary:i,offsetScroll:d,container:u}}),[d,l,i,t,u])}(Z,l),kt=wt.isSticky,St=wt.offsetHeader,Et=wt.offsetSummary,Nt=wt.offsetScroll,$t=wt.stickyClassName,Kt=wt.container,It=o.useMemo((function(){return null==A?void 0:A(ne)}),[A,ne]),Ot=(bt||kt)&&o.isValidElement(It)&&It.type===W&&It.props.fixed;bt&&(tt={overflowY:"scroll",maxHeight:g.y}),xt&&(et={overflowX:"auto"},bt||(tt={overflowY:"hidden"}),nt={width:!0===Ue?"auto":Ue,minWidth:"100%"});var Rt=o.useCallback((function(e,t){(0,X.A)(Ye.current)&&mt((function(n){if(n.get(e)!==t){var o=new Map(n);return o.set(e,t),o}return n}))}),[]),Dt=function(e){var t=(0,o.useRef)(null),n=(0,o.useRef)();function r(){window.clearTimeout(n.current)}return(0,o.useEffect)((function(){return r}),[]),[function(e){t.current=e,r(),n.current=window.setTimeout((function(){t.current=null,n.current=void 0}),100)},function(){return t.current}]}(),Pt=(0,i.A)(Dt,2),Tt=Pt[0],Mt=Pt[1];function zt(e,t){t&&("function"==typeof t?t(e):t.scrollLeft!==e&&(t.scrollLeft=e,t.scrollLeft!==e&&setTimeout((function(){t.scrollLeft=e}),0)))}var Bt=(0,c.A)((function(e){var t,n=e.currentTarget,o=e.scrollLeft,r="rtl"===y,l="number"==typeof o?o:n.scrollLeft,a=n||Te;Mt()&&Mt()!==a||(Tt(a),zt(l,Qe.current),zt(l,Je.current),zt(l,ot.current),zt(l,null===(t=Ct.current)||void 0===t?void 0:t.setScrollLeft));var i=n||Qe.current;if(i){var c=i.scrollWidth,d=i.clientWidth;if(c===d)return it(!1),void ut(!1);r?(it(-l<c-d),ut(-l>0)):(it(l>0),ut(l<c-d))}})),Ht=(0,c.A)((function(e){Bt(e),null==H||H(e)})),Lt=function(){xt&&Je.current?Bt({currentTarget:Je.current}):(it(!1),ut(!1))},jt=o.useRef(!1);o.useEffect((function(){jt.current&&Lt()}),[xt,p,_e.length]),o.useEffect((function(){jt.current=!0}),[]);var Ft=o.useState(0),Wt=(0,i.A)(Ft,2),_t=Wt[0],qt=Wt[1],Vt=o.useState(!0),Xt=(0,i.A)(Vt,2),Ut=Xt[0],Gt=Xt[1];o.useEffect((function(){Q&&re||(Je.current instanceof Element?qt((0,G.V)(Je.current).width):qt((0,G.V)(Ze.current).width)),Gt((0,U.F)("position","sticky"))}),[]),o.useEffect((function(){re&&q&&(q.body.current=Je.current)}));var Yt,Qt=o.useCallback((function(e){return o.createElement(o.Fragment,null,o.createElement(me,e),"top"===Ot&&o.createElement(_,e,It))}),[Ot,It]),Jt=o.useCallback((function(e){return o.createElement(_,e,It)}),[It]),Zt=ie(["table"],"table"),en=o.useMemo((function(){return v||(At?"max-content"===Ue?"auto":"fixed":bt||kt||qe.some((function(e){return e.ellipsis}))?"fixed":"auto")}),[bt,At,qe,v,kt]),tn={colWidths:vt,columCount:qe.length,stickyOffsets:yt,onHeaderRow:z,fixHeader:bt,scroll:g},nn=o.useMemo((function(){return oe?null:"function"==typeof T?T():T}),[oe,T]),on=o.createElement(le,{data:ne,measureColumnWidth:bt||xt||kt}),rn=o.createElement(de,{colWidths:qe.map((function(e){return e.width})),columns:qe}),ln=null!=E?o.createElement("caption",{className:"".concat(l,"-caption")},E):void 0,an=(0,Y.A)(r,{data:!0}),cn=(0,Y.A)(r,{aria:!0});if(bt||kt){var dn;"function"==typeof ue?(dn=ue(ne,{scrollbarSize:_t,ref:Je,onScroll:Bt}),tn.colWidths=qe.map((function(e,t){var n=e.width,o=t===qe.length-1?n-_t:n;return"number"!=typeof o||Number.isNaN(o)?0:o}))):dn=o.createElement("div",{style:(0,k.A)((0,k.A)({},et),tt),onScroll:Ht,ref:Je,className:N()("".concat(l,"-body"))},o.createElement(Zt,(0,h.A)({style:(0,k.A)((0,k.A)({},nt),{},{tableLayout:en})},cn),ln,rn,on,!Ot&&It&&o.createElement(_,{stickyOffsets:yt,flattenColumns:qe},It)));var sn=(0,k.A)((0,k.A)((0,k.A)({noData:!ne.length,maxContentScroll:xt&&"max-content"===Ue},tn),Ge),{},{direction:y,stickyClassName:$t,onScroll:Bt});Yt=o.createElement(o.Fragment,null,!1!==O&&o.createElement(pe,(0,h.A)({},sn,{stickyTopOffset:St,className:"".concat(l,"-header"),ref:Qe}),Qt),dn,Ot&&"top"!==Ot&&o.createElement(pe,(0,h.A)({},sn,{stickyBottomOffset:Et,className:"".concat(l,"-summary"),ref:ot}),Jt),kt&&Je.current&&Je.current instanceof Element&&o.createElement(Re,{ref:Ct,offsetScroll:Nt,scrollBodyRef:Je,onScroll:Bt,container:Kt}))}else Yt=o.createElement("div",{style:(0,k.A)((0,k.A)({},et),tt),className:N()("".concat(l,"-content")),onScroll:Bt,ref:Je},o.createElement(Zt,(0,h.A)({style:(0,k.A)((0,k.A)({},nt),{},{tableLayout:en})},cn),ln,rn,!1!==O&&o.createElement(me,(0,h.A)({},tn,Ge)),on,It&&o.createElement(_,{stickyOffsets:yt,flattenColumns:qe},It)));var un=o.createElement("div",(0,h.A)({className:N()(l,d,(n={},(0,S.A)(n,"".concat(l,"-rtl"),"rtl"===y),(0,S.A)(n,"".concat(l,"-ping-left"),at),(0,S.A)(n,"".concat(l,"-ping-right"),st),(0,S.A)(n,"".concat(l,"-layout-fixed"),"fixed"===v),(0,S.A)(n,"".concat(l,"-fixed-header"),bt),(0,S.A)(n,"".concat(l,"-fixed-column"),At),(0,S.A)(n,"".concat(l,"-fixed-column-gapped"),At&&Xe),(0,S.A)(n,"".concat(l,"-scroll-horizontal"),xt),(0,S.A)(n,"".concat(l,"-has-fix-left"),qe[0]&&qe[0].fixed),(0,S.A)(n,"".concat(l,"-has-fix-right"),qe[qe.length-1]&&"right"===qe[qe.length-1].fixed),n)),style:f,id:I,ref:Ye},an),b&&o.createElement(Ne,{className:"".concat(l,"-title")},b(ne)),o.createElement("div",{ref:Ze,className:"".concat(l,"-container")},Yt),x&&o.createElement(Ne,{className:"".concat(l,"-footer")},x(ne)));xt&&(un=o.createElement(V.A,{onResize:function(e){var t,n=e.width;null===(t=Ct.current)||void 0===t||t.checkScrollBarVisible();var o=Ye.current?Ye.current.offsetWidth:n;re&&J&&Ye.current&&(o=J(Ye.current,o)||o),o!==Le&&(Lt(),je(o))}},un));var fn=function(e,t,n){var o=e.map((function(o,r){return B(r,r,e,t,n)}));return(0,$.A)((function(){return o}),[o],(function(e,t){return!(0,s.A)(e,t)}))}(qe,yt,y),pn=o.useMemo((function(){return{scrollX:Ue,prefixCls:l,getComponent:ie,scrollbarSize:_t,direction:y,fixedInfoList:fn,isSticky:kt,supportSticky:Ut,componentWidth:Le,fixHeader:bt,fixColumn:At,horizonScroll:xt,tableLayout:en,rowClassName:u,expandedRowClassName:Ae.expandedRowClassName,expandIcon:Ke,expandableType:Ce,expandRowByClick:Ae.expandRowByClick,expandedRowRender:Ae.expandedRowRender,onTriggerExpand:Oe,expandIconColumnIndex:Ae.expandIconColumnIndex,indentSize:Ae.indentSize,allColumnsFixedLeft:qe.every((function(e){return"left"===e.fixed})),emptyNode:nn,columns:_e,flattenColumns:qe,onColumnResize:Rt,hoverStartRow:ge,hoverEndRow:ve,onHover:ye,rowExpandable:Ae.rowExpandable,onRow:M,getRowKey:ce,expandedKeys:$e,childrenColumnName:Ie,rowHoverable:te}}),[Ue,l,ie,_t,y,fn,kt,Ut,Le,bt,At,xt,en,u,Ae.expandedRowClassName,Ke,Ce,Ae.expandRowByClick,Ae.expandedRowRender,Oe,Ae.expandIconColumnIndex,Ae.indentSize,nn,_e,qe,Rt,ge,ve,ye,Ae.rowExpandable,M,ce,$e,Ie,te]);return o.createElement(C.Provider,{value:pn},un)}var Be=o.forwardRef(ze);function He(e){return b(Be,e)}var Le=He();Le.EXPAND_COLUMN=l,Le.INTERNAL_HOOKS=a,Le.Column=function(e){return null},Le.ColumnGroup=function(e){return null},Le.Summary=q;const je=Le;var Fe=n(87158),We=f(null),_e=f(null);const qe=function(e){var t=e.rowInfo,n=e.column,r=e.colIndex,l=e.indent,a=e.index,i=e.component,c=e.renderIndex,d=e.record,s=e.style,u=e.className,f=e.inverse,m=e.getHeight,g=n.render,v=n.dataIndex,y=n.className,b=n.width,x=p(_e,["columnsOffset"]).columnsOffset,A=te(t,n,r,l,a),C=A.key,w=A.fixedInfo,S=A.appendCellNode,E=A.additionalCellProps,$=E.style,K=E.colSpan,I=void 0===K?1:K,O=E.rowSpan,R=void 0===O?1:O,D=function(e,t,n){return n[e+(t||1)]-(n[e]||0)}(r-1,I,x),P=I>1?b-D:0,T=(0,k.A)((0,k.A)((0,k.A)({},$),s),{},{flex:"0 0 ".concat(D,"px"),width:"".concat(D,"px"),marginRight:P,pointerEvents:"auto"}),M=o.useMemo((function(){return f?R<=1:0===I||0===R||R>1}),[R,I,f]);M?T.visibility="hidden":f&&(T.height=null==m?void 0:m(R));var B=M?function(){return null}:g,H={};return 0!==R&&0!==I||(H.rowSpan=1,H.colSpan=1),o.createElement(z,(0,h.A)({className:N()(y,u),ellipsis:n.ellipsis,align:n.align,scope:n.rowScope,component:i,prefixCls:t.prefixCls,key:C,record:d,index:a,renderIndex:c,dataIndex:v,render:B,shouldCellUpdate:n.shouldCellUpdate},w,{appendNode:S,additionalProps:(0,k.A)((0,k.A)({},E),{},{style:T},H)}))};var Ve=["data","index","className","rowKey","style","extra","getHeight"],Xe=o.forwardRef((function(e,t){var n,r=e.data,l=e.index,a=e.className,i=e.rowKey,c=e.style,d=e.extra,s=e.getHeight,u=(0,L.A)(e,Ve),f=r.record,m=r.indent,g=r.index,v=p(C,["prefixCls","flattenColumns","fixColumn","componentWidth","scrollX"]),y=v.scrollX,b=v.flattenColumns,x=v.prefixCls,A=v.fixColumn,w=v.componentWidth,E=p(We,["getComponent"]).getComponent,$=Z(f,i,l,m),K=E(["body","row"],"div"),I=E(["body","cell"],"div"),O=$.rowSupportExpand,R=$.expanded,D=$.rowProps,P=$.expandedRowRender,T=$.expandedRowClassName;if(O&&R){var M=P(f,l,m+1,R),B=null==T?void 0:T(f,l,m),H={};A&&(H={style:(0,S.A)({},"--virtual-width","".concat(w,"px"))});var j="".concat(x,"-expanded-row-cell");n=o.createElement(K,{className:N()("".concat(x,"-expanded-row"),"".concat(x,"-expanded-row-level-").concat(m+1),B)},o.createElement(z,{component:I,prefixCls:x,className:N()(j,(0,S.A)({},"".concat(j,"-fixed"),A)),additionalProps:H},M))}var F=(0,k.A)((0,k.A)({},c),{},{width:y});d&&(F.position="absolute",F.pointerEvents="none");var W=o.createElement(K,(0,h.A)({},D,u,{"data-row-key":i,ref:O?null:t,className:N()(a,"".concat(x,"-row"),null==D?void 0:D.className,(0,S.A)({},"".concat(x,"-row-extra"),d)),style:(0,k.A)((0,k.A)({},F),null==D?void 0:D.style)}),b.map((function(e,t){return o.createElement(qe,{key:t,component:I,rowInfo:$,column:e,colIndex:t,indent:m,index:l,renderIndex:g,record:f,inverse:d,getHeight:s})})));return O?o.createElement("div",{ref:t},W,n):W}));const Ue=x(Xe);var Ge=o.forwardRef((function(e,t){var n=e.data,r=e.onScroll,l=p(C,["flattenColumns","onColumnResize","getRowKey","prefixCls","expandedKeys","childrenColumnName","emptyNode","scrollX"]),a=l.flattenColumns,c=l.onColumnResize,d=l.getRowKey,s=l.expandedKeys,u=l.prefixCls,f=l.childrenColumnName,h=l.emptyNode,m=l.scrollX,g=p(We),v=g.sticky,y=g.scrollY,b=g.listItemHeight,x=g.getComponent,A=g.onScroll,k=o.useRef(),S=J(n,f,s,d),E=o.useMemo((function(){var e=0;return a.map((function(t){var n=t.width;return[t.key,n,e+=n]}))}),[a]),$=o.useMemo((function(){return E.map((function(e){return e[2]}))}),[E]);o.useEffect((function(){E.forEach((function(e){var t=(0,i.A)(e,2),n=t[0],o=t[1];c(n,o)}))}),[E]),o.useImperativeHandle(t,(function(){var e={scrollTo:function(e){var t;null===(t=k.current)||void 0===t||t.scrollTo(e)}};return Object.defineProperty(e,"scrollLeft",{get:function(){var e;return(null===(e=k.current)||void 0===e?void 0:e.getScrollInfo().x)||0},set:function(e){var t;null===(t=k.current)||void 0===t||t.scrollTo({left:e})}}),e}));var K,I=function(e,t){var n,o=null===(n=S[t])||void 0===n?void 0:n.record,r=e.onCell;if(r){var l,a=r(o,t);return null!==(l=null==a?void 0:a.rowSpan)&&void 0!==l?l:1}return 1},O=o.useMemo((function(){return{columnsOffset:$}}),[$]),R="".concat(u,"-tbody"),D=x(["body","wrapper"]),P=x(["body","row"],"div"),T=x(["body","cell"],"div");if(S.length){var M={};v&&(M.position="sticky",M.bottom=0,"object"===(0,w.A)(v)&&v.offsetScroll&&(M.bottom=v.offsetScroll)),K=o.createElement(Fe.A,{fullHeight:!1,ref:k,prefixCls:"".concat(R,"-virtual"),styles:{horizontalScrollBar:M},className:R,height:y,itemHeight:b||24,data:S,itemKey:function(e){return d(e.record)},component:D,scrollWidth:m,onVirtualScroll:function(e){var t=e.x;r({scrollLeft:t})},onScroll:A,extraRender:function(e){var t=e.start,n=e.end,r=e.getSize,l=e.offsetY;if(n<0)return null;for(var i=a.filter((function(e){return 0===I(e,t)})),c=t,s=function(e){if(!(i=i.filter((function(t){return 0===I(t,e)}))).length)return c=e,1},u=t;u>=0&&!s(u);u-=1);for(var f=a.filter((function(e){return 1!==I(e,n)})),p=n,h=function(e){if(!(f=f.filter((function(t){return 1!==I(t,e)}))).length)return p=Math.max(e-1,n),1},m=n;m<S.length&&!h(m);m+=1);for(var g=[],v=function(e){if(!S[e])return 1;a.some((function(t){return I(t,e)>1}))&&g.push(e)},y=c;y<=p;y+=1)v(y);return g.map((function(e){var t=S[e],n=d(t.record,e),a=r(n);return o.createElement(Ue,{key:e,data:t,rowKey:n,index:e,style:{top:-l+a.top},extra:!0,getHeight:function(t){var o=e+t-1,l=d(S[o].record,o),a=r(n,l);return a.bottom-a.top}})}))}},(function(e,t,n){var r=d(e.record,t);return o.createElement(Ue,{data:e,rowKey:r,index:t,style:n.style})}))}else K=o.createElement(P,{className:N()("".concat(u,"-placeholder"))},o.createElement(z,{component:T,prefixCls:u},h));return o.createElement(_e.Provider,{value:O},K)}));const Ye=x(Ge);var Qe=function(e,t){var n=t.ref,r=t.onScroll;return o.createElement(Ye,{ref:n,data:e,onScroll:r})};function Je(e,t){var n=e.columns,r=e.scroll,l=e.sticky,i=e.prefixCls,c=void 0===i?De:i,d=e.className,s=e.listItemHeight,u=e.components,f=e.onScroll,p=r||{},m=p.x,g=p.y;"number"!=typeof m&&(m=1),"number"!=typeof g&&(g=500);var v=(0,T._q)((function(e,t){return(0,K.A)(u,e)||t})),y=(0,T._q)(f),b=o.useMemo((function(){return{sticky:l,scrollY:g,listItemHeight:s,getComponent:v,onScroll:y}}),[l,g,s,v,y]);return o.createElement(We.Provider,{value:b},o.createElement(je,(0,h.A)({},e,{className:N()(d,"".concat(c,"-virtual")),scroll:(0,k.A)((0,k.A)({},r),{},{x:m}),components:(0,k.A)((0,k.A)({},u),{},{body:Qe}),columns:n,internalHooks:a,tailor:!0,ref:t})))}var Ze=o.forwardRef(Je);function et(e){return b(Ze,e)}et();var tt=n(60531),nt=n(78493),ot=n(48253),rt=n(57505),lt=n(47285),at=n(44762),it=o.createContext(null),ct=function(e){for(var t=e.prefixCls,n=e.level,r=e.isStart,l=e.isEnd,a="".concat(t,"-indent-unit"),i=[],c=0;c<n;c+=1)i.push(o.createElement("span",{key:c,className:N()(a,(0,S.A)((0,S.A)({},"".concat(a,"-start"),r[c]),"".concat(a,"-end"),l[c]))}));return o.createElement("span",{"aria-hidden":"true",className:"".concat(t,"-indent")},i)};const dt=o.memo(ct);function st(e,t){return e[t]}var ut=n(15220),ft=["children"];function pt(e,t){return"".concat(e,"-").concat(t)}function ht(e,t){return null!=e?e:t}function mt(e){var t=e||{},n=t.title||"title";return{title:n,_title:t._title||[n],key:t.key||"key",children:t.children||"children"}}function gt(e){return function e(t){return(0,ge.A)(t).map((function(t){if(!function(e){return e&&e.type&&e.type.isTreeNode}(t))return(0,I.Ay)(!t,"Tree/TreeNode can only accept TreeNode as children."),null;var n=t.key,o=t.props,r=o.children,l=(0,L.A)(o,ft),a=(0,k.A)({key:n},l),i=e(r);return i.length&&(a.children=i),a})).filter((function(e){return e}))}(e)}function vt(e,t,n){var o=mt(n),r=o._title,l=o.key,a=o.children,i=new Set(!0===t?[]:t),c=[];return function e(n){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return n.map((function(d,s){for(var u,f=pt(o?o.pos:"0",s),p=ht(d[l],f),h=0;h<r.length;h+=1){var m=r[h];if(void 0!==d[m]){u=d[m];break}}var g=Object.assign((0,ut.A)(d,[].concat((0,se.A)(r),[l,a])),{title:u,key:p,parent:o,pos:f,children:null,data:d,isStart:[].concat((0,se.A)(o?o.isStart:[]),[0===s]),isEnd:[].concat((0,se.A)(o?o.isEnd:[]),[s===n.length-1])});return c.push(g),!0===t||i.has(p)?g.children=e(d[a]||[],g):g.children=[],g}))}(e),c}function yt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.initWrapper,o=t.processEntity,r=t.onProcessFinished,l=t.externalGetKey,a=t.childrenPropName,i=t.fieldNames,c=l||(arguments.length>2?arguments[2]:void 0),d={},s={},u={posEntities:d,keyEntities:s};return n&&(u=n(u)||u),function(e,t,n){var r,l=("object"===(0,w.A)(n)?n:{externalGetKey:n})||{},a=l.childrenPropName,i=l.externalGetKey,c=mt(l.fieldNames),f=c.key,p=c.children,h=a||p;i?"string"==typeof i?r=function(e){return e[i]}:"function"==typeof i&&(r=function(e){return i(e)}):r=function(e,t){return ht(e[f],t)},function t(n,l,a,i){var c=n?n[h]:e,f=n?pt(a.pos,l):"0",p=n?[].concat((0,se.A)(i),[n]):[];if(n){var m=r(n,f);!function(e){var t=e.node,n=e.index,r=e.pos,l=e.key,a=e.parentPos,i=e.level,c={node:t,nodes:e.nodes,index:n,key:l,pos:r,level:i},f=ht(l,r);d[r]=c,s[f]=c,c.parent=d[a],c.parent&&(c.parent.children=c.parent.children||[],c.parent.children.push(c)),o&&o(c,u)}({node:n,index:l,pos:f,key:m,parentPos:a.node?a.pos:null,level:a.level+1,nodes:p})}c&&c.forEach((function(e,o){t(e,o,{node:n,pos:f,level:a?a.level+1:-1},p)}))}(null)}(e,0,{externalGetKey:c,childrenPropName:a,fieldNames:i}),r&&r(u),u}function bt(e,t){var n=t.expandedKeys,o=t.selectedKeys,r=t.loadedKeys,l=t.loadingKeys,a=t.checkedKeys,i=t.halfCheckedKeys,c=t.dragOverNodeKey,d=t.dropPosition,s=st(t.keyEntities,e);return{eventKey:e,expanded:-1!==n.indexOf(e),selected:-1!==o.indexOf(e),loaded:-1!==r.indexOf(e),loading:-1!==l.indexOf(e),checked:-1!==a.indexOf(e),halfChecked:-1!==i.indexOf(e),pos:String(s?s.pos:""),dragOver:c===e&&0===d,dragOverGapTop:c===e&&-1===d,dragOverGapBottom:c===e&&1===d}}function xt(e){var t=e.data,n=e.expanded,o=e.selected,r=e.checked,l=e.loaded,a=e.loading,i=e.halfChecked,c=e.dragOver,d=e.dragOverGapTop,s=e.dragOverGapBottom,u=e.pos,f=e.active,p=e.eventKey,h=(0,k.A)((0,k.A)({},t),{},{expanded:n,selected:o,checked:r,loaded:l,loading:a,halfChecked:i,dragOver:c,dragOverGapTop:d,dragOverGapBottom:s,pos:u,active:f,key:p});return"props"in h||Object.defineProperty(h,"props",{get:function(){return(0,I.Ay)(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),e}}),h}var At=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],Ct="open",wt="close",kt=function(e){(0,lt.A)(n,e);var t=(0,at.A)(n);function n(){var e;(0,nt.A)(this,n);for(var r=arguments.length,l=new Array(r),a=0;a<r;a++)l[a]=arguments[a];return e=t.call.apply(t,[this].concat(l)),(0,S.A)((0,rt.A)(e),"state",{dragNodeHighlight:!1}),(0,S.A)((0,rt.A)(e),"selectHandle",void 0),(0,S.A)((0,rt.A)(e),"cacheIndent",void 0),(0,S.A)((0,rt.A)(e),"onSelectorClick",(function(t){(0,e.props.context.onNodeClick)(t,xt(e.props)),e.isSelectable()?e.onSelect(t):e.onCheck(t)})),(0,S.A)((0,rt.A)(e),"onSelectorDoubleClick",(function(t){(0,e.props.context.onNodeDoubleClick)(t,xt(e.props))})),(0,S.A)((0,rt.A)(e),"onSelect",(function(t){e.isDisabled()||(0,e.props.context.onNodeSelect)(t,xt(e.props))})),(0,S.A)((0,rt.A)(e),"onCheck",(function(t){if(!e.isDisabled()){var n=e.props,o=n.disableCheckbox,r=n.checked,l=e.props.context.onNodeCheck;if(e.isCheckable()&&!o){var a=!r;l(t,xt(e.props),a)}}})),(0,S.A)((0,rt.A)(e),"onMouseEnter",(function(t){(0,e.props.context.onNodeMouseEnter)(t,xt(e.props))})),(0,S.A)((0,rt.A)(e),"onMouseLeave",(function(t){(0,e.props.context.onNodeMouseLeave)(t,xt(e.props))})),(0,S.A)((0,rt.A)(e),"onContextMenu",(function(t){(0,e.props.context.onNodeContextMenu)(t,xt(e.props))})),(0,S.A)((0,rt.A)(e),"onDragStart",(function(t){var n=e.props.context.onNodeDragStart;t.stopPropagation(),e.setState({dragNodeHighlight:!0}),n(t,(0,rt.A)(e));try{t.dataTransfer.setData("text/plain","")}catch(e){}})),(0,S.A)((0,rt.A)(e),"onDragEnter",(function(t){var n=e.props.context.onNodeDragEnter;t.preventDefault(),t.stopPropagation(),n(t,(0,rt.A)(e))})),(0,S.A)((0,rt.A)(e),"onDragOver",(function(t){var n=e.props.context.onNodeDragOver;t.preventDefault(),t.stopPropagation(),n(t,(0,rt.A)(e))})),(0,S.A)((0,rt.A)(e),"onDragLeave",(function(t){var n=e.props.context.onNodeDragLeave;t.stopPropagation(),n(t,(0,rt.A)(e))})),(0,S.A)((0,rt.A)(e),"onDragEnd",(function(t){var n=e.props.context.onNodeDragEnd;t.stopPropagation(),e.setState({dragNodeHighlight:!1}),n(t,(0,rt.A)(e))})),(0,S.A)((0,rt.A)(e),"onDrop",(function(t){var n=e.props.context.onNodeDrop;t.preventDefault(),t.stopPropagation(),e.setState({dragNodeHighlight:!1}),n(t,(0,rt.A)(e))})),(0,S.A)((0,rt.A)(e),"onExpand",(function(t){var n=e.props,o=n.loading,r=n.context.onNodeExpand;o||r(t,xt(e.props))})),(0,S.A)((0,rt.A)(e),"setSelectHandle",(function(t){e.selectHandle=t})),(0,S.A)((0,rt.A)(e),"getNodeState",(function(){var t=e.props.expanded;return e.isLeaf()?null:t?Ct:wt})),(0,S.A)((0,rt.A)(e),"hasChildren",(function(){var t=e.props.eventKey;return!!((st(e.props.context.keyEntities,t)||{}).children||[]).length})),(0,S.A)((0,rt.A)(e),"isLeaf",(function(){var t=e.props,n=t.isLeaf,o=t.loaded,r=e.props.context.loadData,l=e.hasChildren();return!1!==n&&(n||!r&&!l||r&&o&&!l)})),(0,S.A)((0,rt.A)(e),"isDisabled",(function(){var t=e.props.disabled;return!(!e.props.context.disabled&&!t)})),(0,S.A)((0,rt.A)(e),"isCheckable",(function(){var t=e.props.checkable,n=e.props.context.checkable;return!(!n||!1===t)&&n})),(0,S.A)((0,rt.A)(e),"syncLoadData",(function(t){var n=t.expanded,o=t.loading,r=t.loaded,l=e.props.context,a=l.loadData,i=l.onNodeLoad;o||a&&n&&!e.isLeaf()&&!r&&i(xt(e.props))})),(0,S.A)((0,rt.A)(e),"isDraggable",(function(){var t=e.props,n=t.data,o=t.context.draggable;return!(!o||o.nodeDraggable&&!o.nodeDraggable(n))})),(0,S.A)((0,rt.A)(e),"renderDragHandler",(function(){var t=e.props.context,n=t.draggable,r=t.prefixCls;return null!=n&&n.icon?o.createElement("span",{className:"".concat(r,"-draggable-icon")},n.icon):null})),(0,S.A)((0,rt.A)(e),"renderSwitcherIconDom",(function(t){var n=e.props.switcherIcon,o=e.props.context.switcherIcon,r=n||o;return"function"==typeof r?r((0,k.A)((0,k.A)({},e.props),{},{isLeaf:t})):r})),(0,S.A)((0,rt.A)(e),"renderSwitcher",(function(){var t=e.props.expanded,n=e.props.context.prefixCls;if(e.isLeaf()){var r=e.renderSwitcherIconDom(!0);return!1!==r?o.createElement("span",{className:N()("".concat(n,"-switcher"),"".concat(n,"-switcher-noop"))},r):null}var l=N()("".concat(n,"-switcher"),"".concat(n,"-switcher_").concat(t?Ct:wt)),a=e.renderSwitcherIconDom(!1);return!1!==a?o.createElement("span",{onClick:e.onExpand,className:l},a):null})),(0,S.A)((0,rt.A)(e),"renderCheckbox",(function(){var t=e.props,n=t.checked,r=t.halfChecked,l=t.disableCheckbox,a=e.props.context.prefixCls,i=e.isDisabled(),c=e.isCheckable();if(!c)return null;var d="boolean"!=typeof c?c:null;return o.createElement("span",{className:N()("".concat(a,"-checkbox"),n&&"".concat(a,"-checkbox-checked"),!n&&r&&"".concat(a,"-checkbox-indeterminate"),(i||l)&&"".concat(a,"-checkbox-disabled")),onClick:e.onCheck},d)})),(0,S.A)((0,rt.A)(e),"renderIcon",(function(){var t=e.props.loading,n=e.props.context.prefixCls;return o.createElement("span",{className:N()("".concat(n,"-iconEle"),"".concat(n,"-icon__").concat(e.getNodeState()||"docu"),t&&"".concat(n,"-icon_loading"))})})),(0,S.A)((0,rt.A)(e),"renderSelector",(function(){var t,n,r=e.state.dragNodeHighlight,l=e.props,a=l.title,i=void 0===a?"---":a,c=l.selected,d=l.icon,s=l.loading,u=l.data,f=e.props.context,p=f.prefixCls,h=f.showIcon,m=f.icon,g=f.loadData,v=f.titleRender,y=e.isDisabled(),b="".concat(p,"-node-content-wrapper");if(h){var x=d||m;t=x?o.createElement("span",{className:N()("".concat(p,"-iconEle"),"".concat(p,"-icon__customize"))},"function"==typeof x?x(e.props):x):e.renderIcon()}else g&&s&&(t=e.renderIcon());n="function"==typeof i?i(u):v?v(u):i;var A=o.createElement("span",{className:"".concat(p,"-title")},n);return o.createElement("span",{ref:e.setSelectHandle,title:"string"==typeof i?i:"",className:N()("".concat(b),"".concat(b,"-").concat(e.getNodeState()||"normal"),!y&&(c||r)&&"".concat(p,"-node-selected")),onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onContextMenu:e.onContextMenu,onClick:e.onSelectorClick,onDoubleClick:e.onSelectorDoubleClick},t,A,e.renderDropIndicator())})),(0,S.A)((0,rt.A)(e),"renderDropIndicator",(function(){var t=e.props,n=t.disabled,o=t.eventKey,r=e.props.context,l=r.draggable,a=r.dropLevelOffset,i=r.dropPosition,c=r.prefixCls,d=r.indent,s=r.dropIndicatorRender,u=r.dragOverNodeKey,f=r.direction,p=!n&&!!l&&u===o,h=null!=d?d:e.cacheIndent;return e.cacheIndent=d,p?s({dropPosition:i,dropLevelOffset:a,indent:h,prefixCls:c,direction:f}):null})),e}return(0,ot.A)(n,[{key:"componentDidMount",value:function(){this.syncLoadData(this.props)}},{key:"componentDidUpdate",value:function(){this.syncLoadData(this.props)}},{key:"isSelectable",value:function(){var e=this.props.selectable,t=this.props.context.selectable;return"boolean"==typeof e?e:t}},{key:"render",value:function(){var e,t=this.props,n=t.eventKey,r=t.className,l=t.style,a=t.dragOver,i=t.dragOverGapTop,c=t.dragOverGapBottom,d=t.isLeaf,s=t.isStart,u=t.isEnd,f=t.expanded,p=t.selected,m=t.checked,g=t.halfChecked,v=t.loading,y=t.domRef,b=t.active,x=(t.data,t.onMouseMove),A=t.selectable,C=(0,L.A)(t,At),w=this.props.context,k=w.prefixCls,E=w.filterTreeNode,$=w.keyEntities,K=w.dropContainerKey,I=w.dropTargetKey,O=w.draggingNodeKey,R=this.isDisabled(),D=(0,Y.A)(C,{aria:!0,data:!0}),P=(st($,n)||{}).level,T=u[u.length-1],M=this.isDraggable(),z=!R&&M,B=O===n,H=void 0!==A?{"aria-selected":!!A}:void 0;return o.createElement("div",(0,h.A)({ref:y,className:N()(r,"".concat(k,"-treenode"),(e={},(0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)(e,"".concat(k,"-treenode-disabled"),R),"".concat(k,"-treenode-switcher-").concat(f?"open":"close"),!d),"".concat(k,"-treenode-checkbox-checked"),m),"".concat(k,"-treenode-checkbox-indeterminate"),g),"".concat(k,"-treenode-selected"),p),"".concat(k,"-treenode-loading"),v),"".concat(k,"-treenode-active"),b),"".concat(k,"-treenode-leaf-last"),T),"".concat(k,"-treenode-draggable"),M),"dragging",B),(0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)((0,S.A)(e,"drop-target",I===n),"drop-container",K===n),"drag-over",!R&&a),"drag-over-gap-top",!R&&i),"drag-over-gap-bottom",!R&&c),"filter-node",E&&E(xt(this.props))))),style:l,draggable:z,"aria-grabbed":B,onDragStart:z?this.onDragStart:void 0,onDragEnter:M?this.onDragEnter:void 0,onDragOver:M?this.onDragOver:void 0,onDragLeave:M?this.onDragLeave:void 0,onDrop:M?this.onDrop:void 0,onDragEnd:M?this.onDragEnd:void 0,onMouseMove:x},H,D),o.createElement(dt,{prefixCls:k,level:P,isStart:s,isEnd:u}),this.renderDragHandler(),this.renderSwitcher(),this.renderCheckbox(),this.renderSelector())}}]),n}(o.Component),St=function(e){return o.createElement(it.Consumer,null,(function(t){return o.createElement(kt,(0,h.A)({},e,{context:t}))}))};St.displayName="TreeNode",St.isTreeNode=1;const Et=St;function Nt(e,t){if(!e)return[];var n=e.slice(),o=n.indexOf(t);return o>=0&&n.splice(o,1),n}function $t(e,t){var n=(e||[]).slice();return-1===n.indexOf(t)&&n.push(t),n}function Kt(e){return e.split("-")}function It(e,t){var n=[];return function e(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach((function(t){var o=t.key,r=t.children;n.push(o),e(r)}))}(st(t,e).children),n}function Ot(e){if(e.parent){var t=Kt(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}function Rt(e,t,n,o,r,l,a,i,c,d){var s,u=e.clientX,f=e.clientY,p=e.target.getBoundingClientRect(),h=p.top,m=p.height,g=(("rtl"===d?-1:1)*(((null==r?void 0:r.x)||0)-u)-12)/o,v=c.filter((function(e){var t;return null===(t=i[e])||void 0===t||null===(t=t.children)||void 0===t?void 0:t.length})),y=st(i,n.props.eventKey);if(f<h+m/2){var b=a.findIndex((function(e){return e.key===y.key})),x=a[b<=0?0:b-1].key;y=st(i,x)}var A=y.key,C=y,w=y.key,k=0,S=0;if(!v.includes(A))for(var E=0;E<g&&Ot(y);E+=1)y=y.parent,S+=1;var N,$=t.props.data,K=y.node,I=!0;return N=Kt(y.pos),0===Number(N[N.length-1])&&0===y.level&&f<h+m/2&&l({dragNode:$,dropNode:K,dropPosition:-1})&&y.key===n.props.eventKey?k=-1:(C.children||[]).length&&v.includes(w)?l({dragNode:$,dropNode:K,dropPosition:0})?k=0:I=!1:0===S?g>-1.5?l({dragNode:$,dropNode:K,dropPosition:1})?k=1:I=!1:l({dragNode:$,dropNode:K,dropPosition:0})?k=0:l({dragNode:$,dropNode:K,dropPosition:1})?k=1:I=!1:l({dragNode:$,dropNode:K,dropPosition:1})?k=1:I=!1,{dropPosition:k,dropLevelOffset:S,dropTargetKey:y.key,dropTargetPos:y.pos,dragOverNodeKey:w,dropContainerKey:0===k?null:(null===(s=y.parent)||void 0===s?void 0:s.key)||null,dropAllowed:I}}function Dt(e,t){if(e)return t.multiple?e.slice():e.length?[e[0]]:e}function Pt(e){if(!e)return null;var t;if(Array.isArray(e))t={checkedKeys:e,halfCheckedKeys:void 0};else{if("object"!==(0,w.A)(e))return(0,I.Ay)(!1,"`checkedKeys` is not an array or an object"),null;t={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0}}return t}function Tt(e,t){var n=new Set;function o(e){if(!n.has(e)){var r=st(t,e);if(r){n.add(e);var l=r.parent;r.node.disabled||l&&o(l.key)}}}return(e||[]).forEach((function(e){o(e)})),(0,se.A)(n)}function Mt(e,t){var n=new Set;return e.forEach((function(e){t.has(e)||n.add(e)})),n}function zt(e){var t=e||{},n=t.disabled,o=t.disableCheckbox,r=t.checkable;return!(!n&&!o)||!1===r}function Bt(e,t,n,o){var r,l=[];r=o||zt;var a,i=new Set(e.filter((function(e){var t=!!st(n,e);return t||l.push(e),t}))),c=new Map,d=0;return Object.keys(n).forEach((function(e){var t=n[e],o=t.level,r=c.get(o);r||(r=new Set,c.set(o,r)),r.add(t),d=Math.max(d,o)})),(0,I.Ay)(!l.length,"Tree missing follow keys: ".concat(l.slice(0,100).map((function(e){return"'".concat(e,"'")})).join(", "))),a=!0===t?function(e,t,n,o){for(var r=new Set(e),l=new Set,a=0;a<=n;a+=1)(t.get(a)||new Set).forEach((function(e){var t=e.key,n=e.node,l=e.children,a=void 0===l?[]:l;r.has(t)&&!o(n)&&a.filter((function(e){return!o(e.node)})).forEach((function(e){r.add(e.key)}))}));for(var i=new Set,c=n;c>=0;c-=1)(t.get(c)||new Set).forEach((function(e){var t=e.parent,n=e.node;if(!o(n)&&e.parent&&!i.has(e.parent.key))if(o(e.parent.node))i.add(t.key);else{var a=!0,c=!1;(t.children||[]).filter((function(e){return!o(e.node)})).forEach((function(e){var t=e.key,n=r.has(t);a&&!n&&(a=!1),c||!n&&!l.has(t)||(c=!0)})),a&&r.add(t.key),c&&l.add(t.key),i.add(t.key)}}));return{checkedKeys:Array.from(r),halfCheckedKeys:Array.from(Mt(l,r))}}(i,c,d,r):function(e,t,n,o,r){for(var l=new Set(e),a=new Set(t),i=0;i<=o;i+=1)(n.get(i)||new Set).forEach((function(e){var t=e.key,n=e.node,o=e.children,i=void 0===o?[]:o;l.has(t)||a.has(t)||r(n)||i.filter((function(e){return!r(e.node)})).forEach((function(e){l.delete(e.key)}))}));a=new Set;for(var c=new Set,d=o;d>=0;d-=1)(n.get(d)||new Set).forEach((function(e){var t=e.parent,n=e.node;if(!r(n)&&e.parent&&!c.has(e.parent.key))if(r(e.parent.node))c.add(t.key);else{var o=!0,i=!1;(t.children||[]).filter((function(e){return!r(e.node)})).forEach((function(e){var t=e.key,n=l.has(t);o&&!n&&(o=!1),i||!n&&!a.has(t)||(i=!0)})),o||l.delete(t.key),i&&a.add(t.key),c.add(t.key)}}));return{checkedKeys:Array.from(l),halfCheckedKeys:Array.from(Mt(a,l))}}(i,t.halfCheckedKeys,c,d,r),a}var Ht=n(74188),Lt=n(82606),jt=n(45854),Ft=n(95962),Wt=n(81533);const _t={},qt="SELECT_ALL",Vt="SELECT_INVERT",Xt="SELECT_NONE",Ut=[],Gt=(e,t)=>{let n=[];return(t||[]).forEach((t=>{n.push(t),t&&"object"==typeof t&&e in t&&(n=[].concat((0,se.A)(n),(0,se.A)(Gt(e,t[e]))))})),n};function Yt(e){return null!=e&&e===e.window}var Qt=n(80840),Jt=n(84017),Zt=n(51471),en=n(31754),tn=n(58678),nn=n(81396),on=n(86310),rn=n(18197),ln=n(50969);function an(e,t){return"key"in e&&void 0!==e.key&&null!==e.key?e.key:e.dataIndex?Array.isArray(e.dataIndex)?e.dataIndex.join("."):e.dataIndex:t}function cn(e,t){return t?`${t}-${e}`:`${e}`}function dn(e,t){return"function"==typeof e?e(t):e}const sn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M349 838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V642H349v196zm531.1-684H143.9c-24.5 0-39.8 26.7-27.5 48l221.3 376h348.8l221.3-376c12.1-21.3-3.2-48-27.7-48z"}}]},name:"filter",theme:"filled"};var un=n(4679),fn=function(e,t){return o.createElement(un.A,(0,h.A)({},e,{ref:t,icon:sn}))};const pn=o.forwardRef(fn);var hn=n(90890),mn=n(57333),gn=n(28101),vn=n(3589),yn=n(30941),bn=n(81739);function xn(e){if(null==e)throw new TypeError("Cannot destructure "+e)}var An=n(88816),Cn=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],wn=function(e,t){var n=e.className,r=e.style,l=e.motion,a=e.motionNodes,c=e.motionType,s=e.onMotionStart,u=e.onMotionEnd,f=e.active,p=e.treeNodeRequiredProps,m=(0,L.A)(e,Cn),g=o.useState(!0),v=(0,i.A)(g,2),y=v[0],b=v[1],x=o.useContext(it).prefixCls,A=a&&"hide"!==c;(0,d.A)((function(){a&&A!==y&&b(A)}),[a]);var C=o.useRef(!1),w=function(){a&&!C.current&&(C.current=!0,u())};return function(e,t){var n=o.useState(!1),r=(0,i.A)(n,2),l=r[0],c=r[1];(0,d.A)((function(){if(l)return a&&s(),function(){t()}}),[l]),(0,d.A)((function(){return c(!0),function(){c(!1)}}),[])}(0,w),a?o.createElement(An.Ay,(0,h.A)({ref:t,visible:y},l,{motionAppear:"show"===c,onVisibleChanged:function(e){A===e&&w()}}),(function(e,t){var n=e.className,r=e.style;return o.createElement("div",{ref:t,className:N()("".concat(x,"-treenode-motion"),n),style:r},a.map((function(e){var t=Object.assign({},(xn(e.data),e.data)),n=e.title,r=e.key,l=e.isStart,a=e.isEnd;delete t.children;var i=bt(r,p);return o.createElement(Et,(0,h.A)({},t,i,{title:n,active:f,data:e.data,key:r,isStart:l,isEnd:a}))})))})):o.createElement(Et,(0,h.A)({domRef:t,className:n,style:r},m,{active:f}))};wn.displayName="MotionTreeNode";const kn=o.forwardRef(wn);function Sn(e,t,n){var o=e.findIndex((function(e){return e.key===n})),r=e[o+1],l=t.findIndex((function(e){return e.key===n}));if(r){var a=t.findIndex((function(e){return e.key===r.key}));return t.slice(l+1,a)}return t.slice(l+1)}var En=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],Nn={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},$n=function(){},Kn="RC_TREE_MOTION_".concat(Math.random()),In={key:Kn},On={key:Kn,level:0,index:0,pos:"0",node:In,nodes:[In]},Rn={parent:null,children:[],pos:On.pos,data:In,title:null,key:Kn,isStart:[],isEnd:[]};function Dn(e,t,n,o){return!1!==t&&n?e.slice(0,Math.ceil(n/o)+1):e}function Pn(e){return ht(e.key,e.pos)}var Tn=o.forwardRef((function(e,t){var n=e.prefixCls,r=e.data,l=(e.selectable,e.checkable,e.expandedKeys),a=e.selectedKeys,c=e.checkedKeys,s=e.loadedKeys,u=e.loadingKeys,f=e.halfCheckedKeys,p=e.keyEntities,m=e.disabled,g=e.dragging,v=e.dragOverNodeKey,y=e.dropPosition,b=e.motion,x=e.height,A=e.itemHeight,C=e.virtual,w=e.focusable,k=e.activeItem,S=e.focused,E=e.tabIndex,N=e.onKeyDown,$=e.onFocus,K=e.onBlur,I=e.onActiveChange,O=e.onListChangeStart,R=e.onListChangeEnd,D=(0,L.A)(e,En),P=o.useRef(null),T=o.useRef(null);o.useImperativeHandle(t,(function(){return{scrollTo:function(e){P.current.scrollTo(e)},getIndentWidth:function(){return T.current.offsetWidth}}}));var M=o.useState(l),z=(0,i.A)(M,2),B=z[0],H=z[1],j=o.useState(r),F=(0,i.A)(j,2),W=F[0],_=F[1],q=o.useState(r),V=(0,i.A)(q,2),X=V[0],U=V[1],G=o.useState([]),Y=(0,i.A)(G,2),Q=Y[0],J=Y[1],Z=o.useState(null),ee=(0,i.A)(Z,2),te=ee[0],ne=ee[1],oe=o.useRef(r);function re(){var e=oe.current;_(e),U(e),J([]),ne(null),R()}oe.current=r,(0,d.A)((function(){H(l);var e=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=e.length,o=t.length;if(1!==Math.abs(n-o))return{add:!1,key:null};function r(e,t){var n=new Map;e.forEach((function(e){n.set(e,!0)}));var o=t.filter((function(e){return!n.has(e)}));return 1===o.length?o[0]:null}return n<o?{add:!0,key:r(e,t)}:{add:!1,key:r(t,e)}}(B,l);if(null!==e.key)if(e.add){var t=W.findIndex((function(t){return t.key===e.key})),n=Dn(Sn(W,r,e.key),C,x,A),o=W.slice();o.splice(t+1,0,Rn),U(o),J(n),ne("show")}else{var a=r.findIndex((function(t){return t.key===e.key})),i=Dn(Sn(r,W,e.key),C,x,A),c=r.slice();c.splice(a+1,0,Rn),U(c),J(i),ne("hide")}else W!==r&&(_(r),U(r))}),[l,r]),o.useEffect((function(){g||re()}),[g]);var le=b?X:r,ae={expandedKeys:l,selectedKeys:a,loadedKeys:s,loadingKeys:u,checkedKeys:c,halfCheckedKeys:f,dragOverNodeKey:v,dropPosition:y,keyEntities:p};return o.createElement(o.Fragment,null,S&&k&&o.createElement("span",{style:Nn,"aria-live":"assertive"},function(e){for(var t=String(e.data.key),n=e;n.parent;)n=n.parent,t="".concat(n.data.key," > ").concat(t);return t}(k)),o.createElement("div",null,o.createElement("input",{style:Nn,disabled:!1===w||m,tabIndex:!1!==w?E:null,onKeyDown:N,onFocus:$,onBlur:K,value:"",onChange:$n,"aria-label":"for screen reader"})),o.createElement("div",{className:"".concat(n,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},o.createElement("div",{className:"".concat(n,"-indent")},o.createElement("div",{ref:T,className:"".concat(n,"-indent-unit")}))),o.createElement(Fe.A,(0,h.A)({},D,{data:le,itemKey:Pn,height:x,fullHeight:!1,virtual:C,itemHeight:A,prefixCls:"".concat(n,"-list"),ref:P,onVisibleChange:function(e,t){var n=new Set(e);t.filter((function(e){return!n.has(e)})).some((function(e){return Pn(e)===Kn}))&&re()}}),(function(e){var t=e.pos,n=Object.assign({},(xn(e.data),e.data)),r=e.title,l=e.key,a=e.isStart,i=e.isEnd,c=ht(l,t);delete n.key,delete n.children;var d=bt(c,ae);return o.createElement(kn,(0,h.A)({},n,d,{title:r,active:!!k&&l===k.key,pos:t,data:e.data,isStart:a,isEnd:i,motion:b,motionNodes:l===Kn?Q:null,motionType:te,onMotionStart:O,onMotionEnd:re,treeNodeRequiredProps:ae,onMouseMove:function(){I(null)}}))})))}));Tn.displayName="NodeList";const Mn=Tn;var zn=function(e){(0,lt.A)(n,e);var t=(0,at.A)(n);function n(){var e;(0,nt.A)(this,n);for(var r=arguments.length,l=new Array(r),a=0;a<r;a++)l[a]=arguments[a];return e=t.call.apply(t,[this].concat(l)),(0,S.A)((0,rt.A)(e),"destroyed",!1),(0,S.A)((0,rt.A)(e),"delayedDragEnterLogic",void 0),(0,S.A)((0,rt.A)(e),"loadingRetryTimes",{}),(0,S.A)((0,rt.A)(e),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:mt()}),(0,S.A)((0,rt.A)(e),"dragStartMousePosition",null),(0,S.A)((0,rt.A)(e),"dragNode",void 0),(0,S.A)((0,rt.A)(e),"currentMouseOverDroppableNodeKey",null),(0,S.A)((0,rt.A)(e),"listRef",o.createRef()),(0,S.A)((0,rt.A)(e),"onNodeDragStart",(function(t,n){var o=e.state,r=o.expandedKeys,l=o.keyEntities,a=e.props.onDragStart,i=n.props.eventKey;e.dragNode=n,e.dragStartMousePosition={x:t.clientX,y:t.clientY};var c=Nt(r,i);e.setState({draggingNodeKey:i,dragChildrenKeys:It(i,l),indent:e.listRef.current.getIndentWidth()}),e.setExpandedKeys(c),window.addEventListener("dragend",e.onWindowDragEnd),null==a||a({event:t,node:xt(n.props)})})),(0,S.A)((0,rt.A)(e),"onNodeDragEnter",(function(t,n){var o=e.state,r=o.expandedKeys,l=o.keyEntities,a=o.dragChildrenKeys,i=o.flattenNodes,c=o.indent,d=e.props,s=d.onDragEnter,u=d.onExpand,f=d.allowDrop,p=d.direction,h=n.props,m=h.pos,g=h.eventKey,v=(0,rt.A)(e).dragNode;if(e.currentMouseOverDroppableNodeKey!==g&&(e.currentMouseOverDroppableNodeKey=g),v){var y=Rt(t,v,n,c,e.dragStartMousePosition,f,i,l,r,p),b=y.dropPosition,x=y.dropLevelOffset,A=y.dropTargetKey,C=y.dropContainerKey,w=y.dropTargetPos,k=y.dropAllowed,S=y.dragOverNodeKey;-1===a.indexOf(A)&&k?(e.delayedDragEnterLogic||(e.delayedDragEnterLogic={}),Object.keys(e.delayedDragEnterLogic).forEach((function(t){clearTimeout(e.delayedDragEnterLogic[t])})),v.props.eventKey!==n.props.eventKey&&(t.persist(),e.delayedDragEnterLogic[m]=window.setTimeout((function(){if(null!==e.state.draggingNodeKey){var o=(0,se.A)(r),a=st(l,n.props.eventKey);a&&(a.children||[]).length&&(o=$t(r,n.props.eventKey)),"expandedKeys"in e.props||e.setExpandedKeys(o),null==u||u(o,{node:xt(n.props),expanded:!0,nativeEvent:t.nativeEvent})}}),800)),v.props.eventKey!==A||0!==x?(e.setState({dragOverNodeKey:S,dropPosition:b,dropLevelOffset:x,dropTargetKey:A,dropContainerKey:C,dropTargetPos:w,dropAllowed:k}),null==s||s({event:t,node:xt(n.props),expandedKeys:r})):e.resetDragState()):e.resetDragState()}else e.resetDragState()})),(0,S.A)((0,rt.A)(e),"onNodeDragOver",(function(t,n){var o=e.state,r=o.dragChildrenKeys,l=o.flattenNodes,a=o.keyEntities,i=o.expandedKeys,c=o.indent,d=e.props,s=d.onDragOver,u=d.allowDrop,f=d.direction,p=(0,rt.A)(e).dragNode;if(p){var h=Rt(t,p,n,c,e.dragStartMousePosition,u,l,a,i,f),m=h.dropPosition,g=h.dropLevelOffset,v=h.dropTargetKey,y=h.dropContainerKey,b=h.dropAllowed,x=h.dropTargetPos,A=h.dragOverNodeKey;-1===r.indexOf(v)&&b&&(p.props.eventKey===v&&0===g?null===e.state.dropPosition&&null===e.state.dropLevelOffset&&null===e.state.dropTargetKey&&null===e.state.dropContainerKey&&null===e.state.dropTargetPos&&!1===e.state.dropAllowed&&null===e.state.dragOverNodeKey||e.resetDragState():m===e.state.dropPosition&&g===e.state.dropLevelOffset&&v===e.state.dropTargetKey&&y===e.state.dropContainerKey&&x===e.state.dropTargetPos&&b===e.state.dropAllowed&&A===e.state.dragOverNodeKey||e.setState({dropPosition:m,dropLevelOffset:g,dropTargetKey:v,dropContainerKey:y,dropTargetPos:x,dropAllowed:b,dragOverNodeKey:A}),null==s||s({event:t,node:xt(n.props)}))}})),(0,S.A)((0,rt.A)(e),"onNodeDragLeave",(function(t,n){e.currentMouseOverDroppableNodeKey!==n.props.eventKey||t.currentTarget.contains(t.relatedTarget)||(e.resetDragState(),e.currentMouseOverDroppableNodeKey=null);var o=e.props.onDragLeave;null==o||o({event:t,node:xt(n.props)})})),(0,S.A)((0,rt.A)(e),"onWindowDragEnd",(function(t){e.onNodeDragEnd(t,null,!0),window.removeEventListener("dragend",e.onWindowDragEnd)})),(0,S.A)((0,rt.A)(e),"onNodeDragEnd",(function(t,n){var o=e.props.onDragEnd;e.setState({dragOverNodeKey:null}),e.cleanDragState(),null==o||o({event:t,node:xt(n.props)}),e.dragNode=null,window.removeEventListener("dragend",e.onWindowDragEnd)})),(0,S.A)((0,rt.A)(e),"onNodeDrop",(function(t,n){var o,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],l=e.state,a=l.dragChildrenKeys,i=l.dropPosition,c=l.dropTargetKey,d=l.dropTargetPos;if(l.dropAllowed){var s=e.props.onDrop;if(e.setState({dragOverNodeKey:null}),e.cleanDragState(),null!==c){var u=(0,k.A)((0,k.A)({},bt(c,e.getTreeNodeRequiredProps())),{},{active:(null===(o=e.getActiveItem())||void 0===o?void 0:o.key)===c,data:st(e.state.keyEntities,c).node}),f=-1!==a.indexOf(c);(0,I.Ay)(!f,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var p=Kt(d),h={event:t,node:xt(u),dragNode:e.dragNode?xt(e.dragNode.props):null,dragNodesKeys:[e.dragNode.props.eventKey].concat(a),dropToGap:0!==i,dropPosition:i+Number(p[p.length-1])};r||null==s||s(h),e.dragNode=null}}})),(0,S.A)((0,rt.A)(e),"cleanDragState",(function(){null!==e.state.draggingNodeKey&&e.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),e.dragStartMousePosition=null,e.currentMouseOverDroppableNodeKey=null})),(0,S.A)((0,rt.A)(e),"triggerExpandActionExpand",(function(t,n){var o=e.state,r=o.expandedKeys,l=o.flattenNodes,a=n.expanded,i=n.key;if(!(n.isLeaf||t.shiftKey||t.metaKey||t.ctrlKey)){var c=l.filter((function(e){return e.key===i}))[0],d=xt((0,k.A)((0,k.A)({},bt(i,e.getTreeNodeRequiredProps())),{},{data:c.data}));e.setExpandedKeys(a?Nt(r,i):$t(r,i)),e.onNodeExpand(t,d)}})),(0,S.A)((0,rt.A)(e),"onNodeClick",(function(t,n){var o=e.props,r=o.onClick;"click"===o.expandAction&&e.triggerExpandActionExpand(t,n),null==r||r(t,n)})),(0,S.A)((0,rt.A)(e),"onNodeDoubleClick",(function(t,n){var o=e.props,r=o.onDoubleClick;"doubleClick"===o.expandAction&&e.triggerExpandActionExpand(t,n),null==r||r(t,n)})),(0,S.A)((0,rt.A)(e),"onNodeSelect",(function(t,n){var o=e.state.selectedKeys,r=e.state,l=r.keyEntities,a=r.fieldNames,i=e.props,c=i.onSelect,d=i.multiple,s=n.selected,u=n[a.key],f=!s,p=(o=f?d?$t(o,u):[u]:Nt(o,u)).map((function(e){var t=st(l,e);return t?t.node:null})).filter((function(e){return e}));e.setUncontrolledState({selectedKeys:o}),null==c||c(o,{event:"select",selected:f,node:n,selectedNodes:p,nativeEvent:t.nativeEvent})})),(0,S.A)((0,rt.A)(e),"onNodeCheck",(function(t,n,o){var r,l=e.state,a=l.keyEntities,i=l.checkedKeys,c=l.halfCheckedKeys,d=e.props,s=d.checkStrictly,u=d.onCheck,f=n.key,p={event:"check",node:n,checked:o,nativeEvent:t.nativeEvent};if(s){var h=o?$t(i,f):Nt(i,f);r={checked:h,halfChecked:Nt(c,f)},p.checkedNodes=h.map((function(e){return st(a,e)})).filter((function(e){return e})).map((function(e){return e.node})),e.setUncontrolledState({checkedKeys:h})}else{var m=Bt([].concat((0,se.A)(i),[f]),!0,a),g=m.checkedKeys,v=m.halfCheckedKeys;if(!o){var y=new Set(g);y.delete(f);var b=Bt(Array.from(y),{checked:!1,halfCheckedKeys:v},a);g=b.checkedKeys,v=b.halfCheckedKeys}r=g,p.checkedNodes=[],p.checkedNodesPositions=[],p.halfCheckedKeys=v,g.forEach((function(e){var t=st(a,e);if(t){var n=t.node,o=t.pos;p.checkedNodes.push(n),p.checkedNodesPositions.push({node:n,pos:o})}})),e.setUncontrolledState({checkedKeys:g},!1,{halfCheckedKeys:v})}null==u||u(r,p)})),(0,S.A)((0,rt.A)(e),"onNodeLoad",(function(t){var n,o=t.key,r=st(e.state.keyEntities,o);if(null==r||null===(n=r.children)||void 0===n||!n.length){var l=new Promise((function(n,r){e.setState((function(l){var a=l.loadedKeys,i=void 0===a?[]:a,c=l.loadingKeys,d=void 0===c?[]:c,s=e.props,u=s.loadData,f=s.onLoad;return u&&-1===i.indexOf(o)&&-1===d.indexOf(o)?(u(t).then((function(){var r=$t(e.state.loadedKeys,o);null==f||f(r,{event:"load",node:t}),e.setUncontrolledState({loadedKeys:r}),e.setState((function(e){return{loadingKeys:Nt(e.loadingKeys,o)}})),n()})).catch((function(t){if(e.setState((function(e){return{loadingKeys:Nt(e.loadingKeys,o)}})),e.loadingRetryTimes[o]=(e.loadingRetryTimes[o]||0)+1,e.loadingRetryTimes[o]>=10){var l=e.state.loadedKeys;(0,I.Ay)(!1,"Retry for `loadData` many times but still failed. No more retry."),e.setUncontrolledState({loadedKeys:$t(l,o)}),n()}r(t)})),{loadingKeys:$t(d,o)}):null}))}));return l.catch((function(){})),l}})),(0,S.A)((0,rt.A)(e),"onNodeMouseEnter",(function(t,n){var o=e.props.onMouseEnter;null==o||o({event:t,node:n})})),(0,S.A)((0,rt.A)(e),"onNodeMouseLeave",(function(t,n){var o=e.props.onMouseLeave;null==o||o({event:t,node:n})})),(0,S.A)((0,rt.A)(e),"onNodeContextMenu",(function(t,n){var o=e.props.onRightClick;o&&(t.preventDefault(),o({event:t,node:n}))})),(0,S.A)((0,rt.A)(e),"onFocus",(function(){var t=e.props.onFocus;e.setState({focused:!0});for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];null==t||t.apply(void 0,o)})),(0,S.A)((0,rt.A)(e),"onBlur",(function(){var t=e.props.onBlur;e.setState({focused:!1}),e.onActiveChange(null);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];null==t||t.apply(void 0,o)})),(0,S.A)((0,rt.A)(e),"getTreeNodeRequiredProps",(function(){var t=e.state;return{expandedKeys:t.expandedKeys||[],selectedKeys:t.selectedKeys||[],loadedKeys:t.loadedKeys||[],loadingKeys:t.loadingKeys||[],checkedKeys:t.checkedKeys||[],halfCheckedKeys:t.halfCheckedKeys||[],dragOverNodeKey:t.dragOverNodeKey,dropPosition:t.dropPosition,keyEntities:t.keyEntities}})),(0,S.A)((0,rt.A)(e),"setExpandedKeys",(function(t){var n=e.state,o=vt(n.treeData,t,n.fieldNames);e.setUncontrolledState({expandedKeys:t,flattenNodes:o},!0)})),(0,S.A)((0,rt.A)(e),"onNodeExpand",(function(t,n){var o=e.state.expandedKeys,r=e.state,l=r.listChanging,a=r.fieldNames,i=e.props,c=i.onExpand,d=i.loadData,s=n.expanded,u=n[a.key];if(!l){var f=o.indexOf(u),p=!s;if((0,I.Ay)(s&&-1!==f||!s&&-1===f,"Expand state not sync with index check"),o=p?$t(o,u):Nt(o,u),e.setExpandedKeys(o),null==c||c(o,{node:n,expanded:p,nativeEvent:t.nativeEvent}),p&&d){var h=e.onNodeLoad(n);h&&h.then((function(){var t=vt(e.state.treeData,o,a);e.setUncontrolledState({flattenNodes:t})})).catch((function(){var t=Nt(e.state.expandedKeys,u);e.setExpandedKeys(t)}))}}})),(0,S.A)((0,rt.A)(e),"onListChangeStart",(function(){e.setUncontrolledState({listChanging:!0})})),(0,S.A)((0,rt.A)(e),"onListChangeEnd",(function(){setTimeout((function(){e.setUncontrolledState({listChanging:!1})}))})),(0,S.A)((0,rt.A)(e),"onActiveChange",(function(t){var n=e.state.activeKey,o=e.props,r=o.onActiveChange,l=o.itemScrollOffset,a=void 0===l?0:l;n!==t&&(e.setState({activeKey:t}),null!==t&&e.scrollTo({key:t,offset:a}),null==r||r(t))})),(0,S.A)((0,rt.A)(e),"getActiveItem",(function(){var t=e.state,n=t.activeKey,o=t.flattenNodes;return null===n?null:o.find((function(e){return e.key===n}))||null})),(0,S.A)((0,rt.A)(e),"offsetActiveKey",(function(t){var n=e.state,o=n.flattenNodes,r=n.activeKey,l=o.findIndex((function(e){return e.key===r}));-1===l&&t<0&&(l=o.length);var a=o[l=(l+t+o.length)%o.length];if(a){var i=a.key;e.onActiveChange(i)}else e.onActiveChange(null)})),(0,S.A)((0,rt.A)(e),"onKeyDown",(function(t){var n=e.state,o=n.activeKey,r=n.expandedKeys,l=n.checkedKeys,a=n.fieldNames,i=e.props,c=i.onKeyDown,d=i.checkable,s=i.selectable;switch(t.which){case bn.A.UP:e.offsetActiveKey(-1),t.preventDefault();break;case bn.A.DOWN:e.offsetActiveKey(1),t.preventDefault()}var u=e.getActiveItem();if(u&&u.data){var f=e.getTreeNodeRequiredProps(),p=!1===u.data.isLeaf||!!(u.data[a.children]||[]).length,h=xt((0,k.A)((0,k.A)({},bt(o,f)),{},{data:u.data,active:!0}));switch(t.which){case bn.A.LEFT:p&&r.includes(o)?e.onNodeExpand({},h):u.parent&&e.onActiveChange(u.parent.key),t.preventDefault();break;case bn.A.RIGHT:p&&!r.includes(o)?e.onNodeExpand({},h):u.children&&u.children.length&&e.onActiveChange(u.children[0].key),t.preventDefault();break;case bn.A.ENTER:case bn.A.SPACE:!d||h.disabled||!1===h.checkable||h.disableCheckbox?d||!s||h.disabled||!1===h.selectable||e.onNodeSelect({},h):e.onNodeCheck({},h,!l.includes(o))}}null==c||c(t)})),(0,S.A)((0,rt.A)(e),"setUncontrolledState",(function(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!e.destroyed){var r=!1,l=!0,a={};Object.keys(t).forEach((function(n){n in e.props?l=!1:(r=!0,a[n]=t[n])})),!r||n&&!l||e.setState((0,k.A)((0,k.A)({},a),o))}})),(0,S.A)((0,rt.A)(e),"scrollTo",(function(t){e.listRef.current.scrollTo(t)})),e}return(0,ot.A)(n,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var e=this.props,t=e.activeKey,n=e.itemScrollOffset,o=void 0===n?0:n;void 0!==t&&t!==this.state.activeKey&&(this.setState({activeKey:t}),null!==t&&this.scrollTo({key:t,offset:o}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var e,t=this.state,n=t.focused,r=t.flattenNodes,l=t.keyEntities,a=t.draggingNodeKey,i=t.activeKey,c=t.dropLevelOffset,d=t.dropContainerKey,s=t.dropTargetKey,u=t.dropPosition,f=t.dragOverNodeKey,p=t.indent,m=this.props,g=m.prefixCls,v=m.className,y=m.style,b=m.showLine,x=m.focusable,A=m.tabIndex,C=void 0===A?0:A,k=m.selectable,E=m.showIcon,$=m.icon,K=m.switcherIcon,I=m.draggable,O=m.checkable,R=m.checkStrictly,D=m.disabled,P=m.motion,T=m.loadData,M=m.filterTreeNode,z=m.height,B=m.itemHeight,H=m.virtual,L=m.titleRender,j=m.dropIndicatorRender,F=m.onContextMenu,W=m.onScroll,_=m.direction,q=m.rootClassName,V=m.rootStyle,X=(0,Y.A)(this.props,{aria:!0,data:!0});return I&&(e="object"===(0,w.A)(I)?I:"function"==typeof I?{nodeDraggable:I}:{}),o.createElement(it.Provider,{value:{prefixCls:g,selectable:k,showIcon:E,icon:$,switcherIcon:K,draggable:e,draggingNodeKey:a,checkable:O,checkStrictly:R,disabled:D,keyEntities:l,dropLevelOffset:c,dropContainerKey:d,dropTargetKey:s,dropPosition:u,dragOverNodeKey:f,indent:p,direction:_,dropIndicatorRender:j,loadData:T,filterTreeNode:M,titleRender:L,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop}},o.createElement("div",{role:"tree",className:N()(g,v,q,(0,S.A)((0,S.A)((0,S.A)({},"".concat(g,"-show-line"),b),"".concat(g,"-focused"),n),"".concat(g,"-active-focused"),null!==i)),style:V},o.createElement(Mn,(0,h.A)({ref:this.listRef,prefixCls:g,style:y,data:r,disabled:D,selectable:k,checkable:!!O,motion:P,dragging:null!==a,height:z,itemHeight:B,virtual:H,focusable:x,focused:n,tabIndex:C,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:F,onScroll:W},this.getTreeNodeRequiredProps(),X))))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n,o=t.prevProps,r={prevProps:e};function l(t){return!o&&t in e||o&&o[t]!==e[t]}var a=t.fieldNames;if(l("fieldNames")&&(a=mt(e.fieldNames),r.fieldNames=a),l("treeData")?n=e.treeData:l("children")&&((0,I.Ay)(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),n=gt(e.children)),n){r.treeData=n;var i=yt(n,{fieldNames:a});r.keyEntities=(0,k.A)((0,S.A)({},Kn,On),i.keyEntities)}var c,d=r.keyEntities||t.keyEntities;if(l("expandedKeys")||o&&l("autoExpandParent"))r.expandedKeys=e.autoExpandParent||!o&&e.defaultExpandParent?Tt(e.expandedKeys,d):e.expandedKeys;else if(!o&&e.defaultExpandAll){var s=(0,k.A)({},d);delete s[Kn],r.expandedKeys=Object.keys(s).map((function(e){return s[e].key}))}else!o&&e.defaultExpandedKeys&&(r.expandedKeys=e.autoExpandParent||e.defaultExpandParent?Tt(e.defaultExpandedKeys,d):e.defaultExpandedKeys);if(r.expandedKeys||delete r.expandedKeys,n||r.expandedKeys){var u=vt(n||t.treeData,r.expandedKeys||t.expandedKeys,a);r.flattenNodes=u}if(e.selectable&&(l("selectedKeys")?r.selectedKeys=Dt(e.selectedKeys,e):!o&&e.defaultSelectedKeys&&(r.selectedKeys=Dt(e.defaultSelectedKeys,e))),e.checkable&&(l("checkedKeys")?c=Pt(e.checkedKeys)||{}:!o&&e.defaultCheckedKeys?c=Pt(e.defaultCheckedKeys)||{}:n&&(c=Pt(e.checkedKeys)||{checkedKeys:t.checkedKeys,halfCheckedKeys:t.halfCheckedKeys}),c)){var f=c,p=f.checkedKeys,h=void 0===p?[]:p,m=f.halfCheckedKeys,g=void 0===m?[]:m;if(!e.checkStrictly){var v=Bt(h,!0,d);h=v.checkedKeys,g=v.halfCheckedKeys}r.checkedKeys=h,r.halfCheckedKeys=g}return l("loadedKeys")&&(r.loadedKeys=e.loadedKeys),r}}]),n}(o.Component);(0,S.A)(zn,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:function(e){var t=e.dropPosition,n=e.dropLevelOffset,r=e.indent,l={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(t){case-1:l.top=0,l.left=-n*r;break;case 1:l.bottom=0,l.left=-n*r;break;case 0:l.bottom=0,l.left=r}return o.createElement("div",{style:l})},allowDrop:function(){return!0},expandAction:!1}),(0,S.A)(zn,"TreeNode",Et);const Bn=zn,Hn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"};var Ln=function(e,t){return o.createElement(un.A,(0,h.A)({},e,{ref:t,icon:Hn}))};const jn=o.forwardRef(Ln),Fn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"};var Wn=function(e,t){return o.createElement(un.A,(0,h.A)({},e,{ref:t,icon:Fn}))};const _n=o.forwardRef(Wn),qn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"};var Vn=function(e,t){return o.createElement(un.A,(0,h.A)({},e,{ref:t,icon:qn}))};const Xn=o.forwardRef(Vn);var Un=n(4509),Gn=n(17826),Yn=n(78052),Qn=n(46854),Jn=n(71094),Zn=n(81170),eo=n(63829),to=n(52146);const no=new Yn.Mo("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),oo=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),ro=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${(0,Yn.zA)(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),lo=(e,t)=>{const{treeCls:n,treeNodeCls:o,treeNodePadding:r,titleHeight:l,nodeSelectedBg:a,nodeHoverBg:i}=t,c=t.paddingXS;return{[n]:Object.assign(Object.assign({},(0,Jn.dF)(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,[`&${n}-rtl`]:{[`${n}-switcher`]:{"&_close":{[`${n}-switcher-icon`]:{svg:{transform:"rotate(90deg)"}}}}},[`&-focused:not(:hover):not(${n}-active-focused)`]:Object.assign({},(0,Jn.jk)(t)),[`${n}-list-holder-inner`]:{alignItems:"flex-start"},[`&${n}-block-node`]:{[`${n}-list-holder-inner`]:{alignItems:"stretch",[`${n}-node-content-wrapper`]:{flex:"auto"},[`${o}.dragging`]:{position:"relative","&:after":{position:"absolute",top:0,insetInlineEnd:0,bottom:r,insetInlineStart:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:no,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none"}}}},[`${o}`]:{display:"flex",alignItems:"flex-start",padding:`0 0 ${(0,Yn.zA)(r)} 0`,outline:"none","&-rtl":{direction:"rtl"},"&-disabled":{[`${n}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}}},[`&-active ${n}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${o}-disabled).filter-node ${n}-title`]:{color:"inherit",fontWeight:500},"&-draggable":{cursor:"grab",[`${n}-draggable-icon`]:{flexShrink:0,width:l,lineHeight:`${(0,Yn.zA)(l)}`,textAlign:"center",visibility:"visible",opacity:.2,transition:`opacity ${t.motionDurationSlow}`,[`${o}:hover &`]:{opacity:.45}},[`&${o}-disabled`]:{[`${n}-draggable-icon`]:{visibility:"hidden"}}}},[`${n}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:l}},[`${n}-draggable-icon`]:{visibility:"hidden"},[`${n}-switcher`]:Object.assign(Object.assign({},oo(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:l,margin:0,lineHeight:`${(0,Yn.zA)(l)}`,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,borderRadius:t.borderRadius,"&-noop":{cursor:"unset"},[`&:not(${n}-switcher-noop):hover`]:{backgroundColor:t.colorBgTextHover},"&_close":{[`${n}-switcher-icon`]:{svg:{transform:"rotate(-90deg)"}}},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(l).div(2).equal(),bottom:t.calc(r).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(l).div(2).equal()).mul(.8).equal(),height:t.calc(l).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${n}-checkbox`]:{top:"initial",marginInlineEnd:c,alignSelf:"flex-start",marginTop:t.marginXXS},[`${n}-node-content-wrapper, ${n}-checkbox + span`]:{position:"relative",zIndex:"auto",minHeight:l,margin:0,padding:`0 ${(0,Yn.zA)(t.calc(t.paddingXS).div(2).equal())}`,color:"inherit",lineHeight:`${(0,Yn.zA)(l)}`,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`,"&:hover":{backgroundColor:i},[`&${n}-node-selected`]:{backgroundColor:a},[`${n}-iconEle`]:{display:"inline-block",width:l,height:l,lineHeight:`${(0,Yn.zA)(l)}`,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}},[`${n}-unselectable ${n}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${n}-node-content-wrapper`]:Object.assign({lineHeight:`${(0,Yn.zA)(l)}`,userSelect:"none"},ro(e,t)),[`${o}.drop-container`]:{"> [draggable]":{boxShadow:`0 0 0 2px ${t.colorPrimary}`}},"&-show-line":{[`${n}-indent`]:{"&-unit":{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(l).div(2).equal(),bottom:t.calc(r).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end":{"&:before":{display:"none"}}}},[`${n}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${o}-leaf-last`]:{[`${n}-switcher`]:{"&-leaf-line":{"&:before":{top:"auto !important",bottom:"auto !important",height:`${(0,Yn.zA)(t.calc(l).div(2).equal())} !important`}}}}})}},ao=e=>{const{treeCls:t,treeNodeCls:n,treeNodePadding:o,directoryNodeSelectedBg:r,directoryNodeSelectedColor:l}=e;return{[`${t}${t}-directory`]:{[n]:{position:"relative","&:before":{position:"absolute",top:0,insetInlineEnd:0,bottom:o,insetInlineStart:0,transition:`background-color ${e.motionDurationMid}`,content:'""',pointerEvents:"none"},"&:hover":{"&:before":{background:e.controlItemBgHover}},"> *":{zIndex:1},[`${t}-switcher`]:{transition:`color ${e.motionDurationMid}`},[`${t}-node-content-wrapper`]:{borderRadius:0,userSelect:"none","&:hover":{background:"transparent"},[`&${t}-node-selected`]:{color:l,background:"transparent"}},"&-selected":{"\n            &:hover::before,\n            &::before\n          ":{background:r},[`${t}-switcher`]:{color:l},[`${t}-node-content-wrapper`]:{color:l,background:"transparent"}}}}}},io=(e,t)=>{const n=`.${e}`,o=`${n}-treenode`,r=t.calc(t.paddingXS).div(2).equal(),l=(0,eo.h1)(t,{treeCls:n,treeNodeCls:o,treeNodePadding:r});return[lo(e,l),ao(l)]},co=(0,to.OF)("Tree",((e,t)=>{let{prefixCls:n}=t;return[{[e.componentCls]:(0,Qn.gd)(`${n}-checkbox`,e)},io(n,e),(0,Zn.A)(e)]}),(e=>{const{colorTextLightSolid:t,colorPrimary:n}=e;return Object.assign(Object.assign({},(e=>{const{controlHeightSM:t}=e;return{titleHeight:t,nodeHoverBg:e.controlItemBgHover,nodeSelectedBg:e.controlItemBgActive}})(e)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:n})}));function so(e){const{dropPosition:t,dropLevelOffset:n,prefixCls:o,indent:l,direction:a="ltr"}=e,i="ltr"===a?"left":"right",c="ltr"===a?"right":"left",d={[i]:-n*l+4,[c]:0};switch(t){case-1:d.top=-3;break;case 1:d.bottom=-3;break;default:d.bottom=-3,d[i]=l+4}return r().createElement("div",{style:d,className:`${o}-drop-indicator`})}const uo={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"};var fo=function(e,t){return o.createElement(un.A,(0,h.A)({},e,{ref:t,icon:uo}))};const po=o.forwardRef(fo);var ho=n(9066),mo=n(79272),go=n(82031),vo=n(79045);const yo=e=>{const{prefixCls:t,switcherIcon:n,treeNodeProps:r,showLine:l}=e,{isLeaf:a,expanded:i,loading:c}=r;if(c)return o.createElement(ho.A,{className:`${t}-switcher-loading-icon`});let d;if(l&&"object"==typeof l&&(d=l.showLeafIcon),a){if(!l)return null;if("boolean"!=typeof d&&d){const e="function"==typeof d?d(r):d,n=`${t}-switcher-line-custom-icon`;return o.isValidElement(e)?(0,vo.Ob)(e,{className:N()(e.props.className||"",n)}):e}return d?o.createElement(jn,{className:`${t}-switcher-line-icon`}):o.createElement("span",{className:`${t}-switcher-leaf-line`})}const s=`${t}-switcher-icon`,u="function"==typeof n?n(r):n;return o.isValidElement(u)?(0,vo.Ob)(u,{className:N()(u.props.className||"",s)}):void 0!==u?u:l?i?o.createElement(mo.A,{className:`${t}-switcher-line-icon`}):o.createElement(go.A,{className:`${t}-switcher-line-icon`}):o.createElement(po,{className:s})},bo=r().forwardRef(((e,t)=>{var n;const{getPrefixCls:o,direction:l,virtual:a,tree:i}=r().useContext(Qt.QO),{prefixCls:c,className:d,showIcon:s=!1,showLine:u,switcherIcon:f,blockNode:p=!1,children:h,checkable:m=!1,selectable:g=!0,draggable:v,motion:y,style:b}=e,x=o("tree",c),A=o(),C=null!=y?y:Object.assign(Object.assign({},(0,Gn.A)(A)),{motionAppear:!1}),w=Object.assign(Object.assign({},e),{checkable:m,selectable:g,showIcon:s,motion:C,blockNode:p,showLine:Boolean(u),dropIndicatorRender:so}),[k,S,E]=co(x),[,$]=(0,ln.Ay)(),K=$.paddingXS/2+((null===(n=$.Tree)||void 0===n?void 0:n.titleHeight)||$.controlHeightSM),I=r().useMemo((()=>{if(!v)return!1;let e={};switch(typeof v){case"function":e.nodeDraggable=v;break;case"object":e=Object.assign({},v)}return!1!==e.icon&&(e.icon=e.icon||r().createElement(Un.A,null)),e}),[v]);return k(r().createElement(Bn,Object.assign({itemHeight:K,ref:t,virtual:a},w,{style:Object.assign(Object.assign({},null==i?void 0:i.style),b),prefixCls:x,className:N()({[`${x}-icon-hide`]:!s,[`${x}-block-node`]:p,[`${x}-unselectable`]:!g,[`${x}-rtl`]:"rtl"===l},null==i?void 0:i.className,d,S,E),direction:l,checkable:m?r().createElement("span",{className:`${x}-checkbox-inner`}):m,selectable:g,switcherIcon:e=>r().createElement(yo,{prefixCls:x,switcherIcon:f,treeNodeProps:e,showLine:u}),draggable:I}),h))})),xo=bo;function Ao(e,t,n){const{key:o,children:r}=n;e.forEach((function(e){const l=e[o],a=e[r];!1!==t(l,e)&&Ao(a||[],t,n)}))}function Co(e,t,n){const o=(0,se.A)(t),r=[];return Ao(e,((e,t)=>{const n=o.indexOf(e);return-1!==n&&(r.push(t),o.splice(n,1)),!!o.length}),mt(n)),r}var wo=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function ko(e){const{isLeaf:t,expanded:n}=e;return t?o.createElement(jn,null):n?o.createElement(_n,null):o.createElement(Xn,null)}function So(e){let{treeData:t,children:n}=e;return t||gt(n)}const Eo=(e,t)=>{var{defaultExpandAll:n,defaultExpandParent:r,defaultExpandedKeys:l}=e,a=wo(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const i=o.useRef(),c=o.useRef(),[d,s]=o.useState(a.selectedKeys||a.defaultSelectedKeys||[]),[u,f]=o.useState((()=>(()=>{const{keyEntities:e}=yt(So(a));let t;return t=n?Object.keys(e):r?Tt(a.expandedKeys||l||[],e):a.expandedKeys||l||[],t})()));o.useEffect((()=>{"selectedKeys"in a&&s(a.selectedKeys)}),[a.selectedKeys]),o.useEffect((()=>{"expandedKeys"in a&&f(a.expandedKeys)}),[a.expandedKeys]);const{getPrefixCls:p,direction:h}=o.useContext(Qt.QO),{prefixCls:m,className:g,showIcon:v=!0,expandAction:y="click"}=a,b=wo(a,["prefixCls","className","showIcon","expandAction"]),x=p("tree",m),A=N()(`${x}-directory`,{[`${x}-directory-rtl`]:"rtl"===h},g);return o.createElement(xo,Object.assign({icon:ko,ref:t,blockNode:!0},b,{showIcon:v,expandAction:y,prefixCls:x,className:A,expandedKeys:u,selectedKeys:d,onSelect:(e,t)=>{var n;const{multiple:o,fieldNames:r}=a,{node:l,nativeEvent:d}=t,{key:f=""}=l,p=So(a),h=Object.assign(Object.assign({},t),{selected:!0}),m=(null==d?void 0:d.ctrlKey)||(null==d?void 0:d.metaKey),g=null==d?void 0:d.shiftKey;let v;o&&m?(v=e,i.current=f,c.current=v,h.selectedNodes=Co(p,v,r)):o&&g?(v=Array.from(new Set([].concat((0,se.A)(c.current||[]),(0,se.A)(function(e){let{treeData:t,expandedKeys:n,startKey:o,endKey:r,fieldNames:l}=e;const a=[];let i=0;return o&&o===r?[o]:o&&r?(Ao(t,(e=>{if(2===i)return!1;if(function(e){return e===o||e===r}(e)){if(a.push(e),0===i)i=1;else if(1===i)return i=2,!1}else 1===i&&a.push(e);return n.includes(e)}),mt(l)),a):[]}({treeData:p,expandedKeys:u,startKey:f,endKey:i.current,fieldNames:r}))))),h.selectedNodes=Co(p,v,r)):(v=[f],i.current=f,c.current=v,h.selectedNodes=Co(p,v,r)),null===(n=a.onSelect)||void 0===n||n.call(a,v,h),"selectedKeys"in a||s(v)},onExpand:(e,t)=>{var n;return"expandedKeys"in a||f(e),null===(n=a.onExpand)||void 0===n?void 0:n.call(a,e,t)}}))},No=o.forwardRef(Eo),$o=xo;$o.DirectoryTree=No,$o.TreeNode=Et;const Ko=$o;var Io=n(37269),Oo=n(9551);const Ro=function(e){let{value:t,onChange:n,filterSearch:r,tablePrefixCls:l,locale:a}=e;return r?o.createElement("div",{className:`${l}-filter-dropdown-search`},o.createElement(Oo.A,{prefix:o.createElement(Io.A,null),placeholder:a.filterSearchPlaceholder,onChange:n,value:t,htmlSize:1,className:`${l}-filter-dropdown-search-input`})):null},Do=e=>{const{keyCode:t}=e;t===bn.A.ENTER&&e.stopPropagation()},Po=o.forwardRef(((e,t)=>o.createElement("div",{className:e.className,onClick:e=>e.stopPropagation(),onKeyDown:Do,ref:t},e.children)));function To(e){let t=[];return(e||[]).forEach((e=>{let{value:n,children:o}=e;t.push(n),o&&(t=[].concat((0,se.A)(t),(0,se.A)(To(o))))})),t}function Mo(e,t){return("string"==typeof t||"number"==typeof t)&&(null==t?void 0:t.toString().toLowerCase().includes(e.trim().toLowerCase()))}function zo(e){let{filters:t,prefixCls:n,filteredKeys:r,filterMultiple:l,searchValue:a,filterSearch:i}=e;return t.map(((e,t)=>{const c=String(e.value);if(e.children)return{key:c||t,label:e.text,popupClassName:`${n}-dropdown-submenu`,children:zo({filters:e.children,prefixCls:n,filteredKeys:r,filterMultiple:l,searchValue:a,filterSearch:i})};const d=l?jt.A:Wt.Ay,s={key:void 0!==e.value?c:t,label:o.createElement(o.Fragment,null,o.createElement(d,{checked:r.includes(c)}),o.createElement("span",null,e.text))};return a.trim()?"function"==typeof i?i(a,e)?s:null:Mo(a,e.text)?s:null:s}))}function Bo(e){return e||[]}const Ho=function(e){var t,n;const{tablePrefixCls:r,prefixCls:l,column:a,dropdownPrefixCls:i,columnKey:c,filterOnClose:d,filterMultiple:u,filterMode:f="menu",filterSearch:p=!1,filterState:h,triggerFilter:m,locale:g,children:v,getPopupContainer:y,rootClassName:b}=e,{filterDropdownOpen:x,onFilterDropdownOpenChange:A,filterResetToDefaultFilteredValue:C,defaultFilteredValue:w,filterDropdownVisible:k,onFilterDropdownVisibleChange:S}=a,[E,$]=o.useState(!1),K=!(!h||!(null===(t=h.filteredKeys)||void 0===t?void 0:t.length)&&!h.forceFiltered),I=e=>{$(e),null==A||A(e),null==S||S(e)},O=null!==(n=null!=x?x:k)&&void 0!==n?n:E,R=null==h?void 0:h.filteredKeys,[D,P]=function(e){const t=o.useRef(e),n=(0,hn.A)();return[()=>t.current,e=>{t.current=e,n()}]}(Bo(R)),T=e=>{let{selectedKeys:t}=e;P(t)},M=(e,t)=>{let{node:n,checked:o}=t;T(u?{selectedKeys:e}:{selectedKeys:o&&n.key?[n.key]:[]})};o.useEffect((()=>{E&&T({selectedKeys:Bo(R)})}),[R]);const[z,B]=o.useState([]),H=e=>{B(e)},[L,j]=o.useState(""),F=e=>{const{value:t}=e.target;j(t)};o.useEffect((()=>{E||j("")}),[E]);const W=e=>{const t=e&&e.length?e:null;return null!==t||h&&h.filteredKeys?(0,s.A)(t,null==h?void 0:h.filteredKeys,!0)?null:void m({column:a,key:c,filteredKeys:t}):null},_=()=>{I(!1),W(D())},q=function(){let{confirm:e,closeDropdown:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{confirm:!1,closeDropdown:!1};e&&W([]),t&&I(!1),j(""),P(C?(w||[]).map((e=>String(e))):[])},V=N()({[`${i}-menu-without-submenu`]:(X=a.filters||[],!X.some((e=>{let{children:t}=e;return t})))});var X;const U=e=>{if(e.target.checked){const e=To(null==a?void 0:a.filters).map((e=>String(e)));P(e)}else P([])},G=e=>{let{filters:t}=e;return(t||[]).map(((e,t)=>{const n=String(e.value),o={title:e.text,key:void 0!==e.value?n:String(t)};return e.children&&(o.children=G({filters:e.children})),o}))},Y=e=>{var t;return Object.assign(Object.assign({},e),{text:e.title,value:e.key,children:(null===(t=e.children)||void 0===t?void 0:t.map((e=>Y(e))))||[]})};let Q,J;if("function"==typeof a.filterDropdown)Q=a.filterDropdown({prefixCls:`${i}-custom`,setSelectedKeys:e=>T({selectedKeys:e}),selectedKeys:D(),confirm:function(){let{closeDropdown:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{closeDropdown:!0};e&&I(!1),W(D())},clearFilters:q,filters:a.filters,visible:O,close:()=>{I(!1)}});else if(a.filterDropdown)Q=a.filterDropdown;else{const e=D()||[],t=()=>{const t=o.createElement(gn.A,{image:gn.A.PRESENTED_IMAGE_SIMPLE,description:g.filterEmptyText,imageStyle:{height:24},style:{margin:0,padding:"16px 0"}});if(0===(a.filters||[]).length)return t;if("tree"===f)return o.createElement(o.Fragment,null,o.createElement(Ro,{filterSearch:p,value:L,onChange:F,tablePrefixCls:r,locale:g}),o.createElement("div",{className:`${r}-filter-dropdown-tree`},u?o.createElement(jt.A,{checked:e.length===To(a.filters).length,indeterminate:e.length>0&&e.length<To(a.filters).length,className:`${r}-filter-dropdown-checkall`,onChange:U},g.filterCheckall):null,o.createElement(Ko,{checkable:!0,selectable:!1,blockNode:!0,multiple:u,checkStrictly:!u,className:`${i}-menu`,onCheck:M,checkedKeys:e,selectedKeys:e,showIcon:!1,treeData:G({filters:a.filters}),autoExpandParent:!0,defaultExpandAll:!0,filterTreeNode:L.trim()?e=>"function"==typeof p?p(L,Y(e)):Mo(L,e.title):void 0})));const n=zo({filters:a.filters||[],filterSearch:p,prefixCls:l,filteredKeys:D(),filterMultiple:u,searchValue:L}),c=n.every((e=>null===e));return o.createElement(o.Fragment,null,o.createElement(Ro,{filterSearch:p,value:L,onChange:F,tablePrefixCls:r,locale:g}),c?t:o.createElement(vn.A,{selectable:!0,multiple:u,prefixCls:`${i}-menu`,className:V,onSelect:T,onDeselect:T,selectedKeys:e,getPopupContainer:y,openKeys:z,onOpenChange:H,items:n}))},n=()=>C?(0,s.A)((w||[]).map((e=>String(e))),e,!0):0===e.length;Q=o.createElement(o.Fragment,null,t(),o.createElement("div",{className:`${l}-dropdown-btns`},o.createElement(mn.Ay,{type:"link",size:"small",disabled:n(),onClick:()=>q()},g.filterReset),o.createElement(mn.Ay,{type:"primary",size:"small",onClick:_},g.filterConfirm)))}a.filterDropdown&&(Q=o.createElement(yn.A,{selectable:void 0},Q)),J="function"==typeof a.filterIcon?a.filterIcon(K):a.filterIcon?a.filterIcon:o.createElement(pn,null);const{direction:Z}=o.useContext(Qt.QO);return o.createElement("div",{className:`${l}-column`},o.createElement("span",{className:`${r}-column-title`},v),o.createElement(Ft.A,{dropdownRender:()=>o.createElement(Po,{className:`${l}-dropdown`},Q),trigger:["click"],open:O,onOpenChange:(e,t)=>{"trigger"===t.source&&(e&&void 0!==R&&P(Bo(R)),I(e),e||a.filterDropdown||!d||_())},getPopupContainer:y,placement:"rtl"===Z?"bottomLeft":"bottomRight",rootClassName:b},o.createElement("span",{role:"button",tabIndex:-1,className:N()(`${l}-trigger`,{active:K}),onClick:e=>{e.stopPropagation()}},J)))};function Lo(e,t,n){let o=[];return(e||[]).forEach(((e,r)=>{var l;const a=cn(r,n);if(e.filters||"filterDropdown"in e||"onFilter"in e)if("filteredValue"in e){let t=e.filteredValue;"filterDropdown"in e||(t=null!==(l=null==t?void 0:t.map(String))&&void 0!==l?l:t),o.push({column:e,key:an(e,a),filteredKeys:t,forceFiltered:e.filtered})}else o.push({column:e,key:an(e,a),filteredKeys:t&&e.defaultFilteredValue?e.defaultFilteredValue:void 0,forceFiltered:e.filtered});"children"in e&&(o=[].concat((0,se.A)(o),(0,se.A)(Lo(e.children,t,a))))})),o}function jo(e,t,n,r,l,a,i,c,d){return n.map(((n,s)=>{const u=cn(s,c),{filterOnClose:f=!0,filterMultiple:p=!0,filterMode:h,filterSearch:m}=n;let g=n;if(g.filters||g.filterDropdown){const c=an(g,u),s=r.find((e=>{let{key:t}=e;return c===t}));g=Object.assign(Object.assign({},g),{title:r=>o.createElement(Ho,{tablePrefixCls:e,prefixCls:`${e}-filter`,dropdownPrefixCls:t,column:g,columnKey:c,filterState:s,filterOnClose:f,filterMultiple:p,filterMode:h,filterSearch:m,triggerFilter:a,locale:l,getPopupContainer:i,rootClassName:d},dn(n.title,r))})}return"children"in g&&(g=Object.assign(Object.assign({},g),{children:jo(e,t,g.children,r,l,a,i,u,d)})),g}))}function Fo(e){const t={};return e.forEach((e=>{let{key:n,filteredKeys:o,column:r}=e;const l=n,{filters:a,filterDropdown:i}=r;if(i)t[l]=o||null;else if(Array.isArray(o)){const e=To(a);t[l]=e.filter((e=>o.includes(String(e))))}else t[l]=null})),t}function Wo(e,t,n){return t.reduce(((e,o)=>{const{column:{onFilter:r,filters:l},filteredKeys:a}=o;return r&&a&&a.length?e.map((e=>Object.assign({},e))).filter((e=>a.some((o=>{const a=To(l),i=a.findIndex((e=>String(e)===String(o))),c=-1!==i?a[i]:o;return e[n]&&(e[n]=Wo(e[n],t,n)),r(c,e)})))):e}),e)}const _o=e=>e.flatMap((e=>"children"in e?[e].concat((0,se.A)(_o(e.children||[]))):[e])),qo=function(e){let{prefixCls:t,dropdownPrefixCls:n,mergedColumns:r,onFilterChange:l,getPopupContainer:a,locale:i,rootClassName:c}=e;(0,Lt.rJ)("Table");const d=o.useMemo((()=>_o(r||[])),[r]),[s,u]=o.useState((()=>Lo(d,!0))),f=o.useMemo((()=>{const e=Lo(d,!1);if(0===e.length)return e;let t=!0,n=!0;if(e.forEach((e=>{let{filteredKeys:o}=e;void 0!==o?t=!1:n=!1})),t){const e=(d||[]).map(((e,t)=>an(e,cn(t))));return s.filter((t=>{let{key:n}=t;return e.includes(n)})).map((t=>{const n=d[e.findIndex((e=>e===t.key))];return Object.assign(Object.assign({},t),{column:Object.assign(Object.assign({},t.column),n),forceFiltered:n.filtered})}))}return e}),[d,s]),p=o.useMemo((()=>Fo(f)),[f]),h=e=>{const t=f.filter((t=>{let{key:n}=t;return n!==e.key}));t.push(e),u(t),l(Fo(t),t)};return[e=>jo(t,n,e,f,i,h,a,void 0,c),f,p]};var Vo=n(82868);const Xo=10,Uo=function(e,t,n){const r=n&&"object"==typeof n?n:{},{total:l=0}=r,a=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(r,["total"]),[i,c]=(0,o.useState)((()=>({current:"defaultCurrent"in a?a.defaultCurrent:1,pageSize:"defaultPageSize"in a?a.defaultPageSize:Xo}))),d=(0,Vo.A)(i,a,{total:l>0?l:e}),s=Math.ceil((l||e)/d.pageSize);d.current>s&&(d.current=s||1);const u=(e,t)=>{c({current:null!=e?e:1,pageSize:t||d.pageSize})};return!1===n?[{},()=>{}]:[Object.assign(Object.assign({},d),{onChange:(e,o)=>{var r;n&&(null===(r=n.onChange)||void 0===r||r.call(n,e,o)),u(e,o),t(e,o||(null==d?void 0:d.pageSize))}}),u]},Go={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"outlined"};var Yo=function(e,t){return o.createElement(un.A,(0,h.A)({},e,{ref:t,icon:Go}))};const Qo=o.forwardRef(Yo),Jo={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"}}]},name:"caret-up",theme:"outlined"};var Zo=function(e,t){return o.createElement(un.A,(0,h.A)({},e,{ref:t,icon:Jo}))};const er=o.forwardRef(Zo);var tr=n(64715);const nr="ascend",or="descend";function rr(e){return"object"==typeof e.sorter&&"number"==typeof e.sorter.multiple&&e.sorter.multiple}function lr(e){return"function"==typeof e?e:!(!e||"object"!=typeof e||!e.compare)&&e.compare}function ar(e,t,n){let o=[];function r(e,t){o.push({column:e,key:an(e,t),multiplePriority:rr(e),sortOrder:e.sortOrder})}return(e||[]).forEach(((e,l)=>{const a=cn(l,n);e.children?("sortOrder"in e&&r(e,a),o=[].concat((0,se.A)(o),(0,se.A)(ar(e.children,t,a)))):e.sorter&&("sortOrder"in e?r(e,a):t&&e.defaultSortOrder&&o.push({column:e,key:an(e,a),multiplePriority:rr(e),sortOrder:e.defaultSortOrder}))})),o}function ir(e,t,n,r,l,a,i,c){return(t||[]).map(((t,d)=>{const s=cn(d,c);let u=t;if(u.sorter){const c=u.sortDirections||l,d=void 0===u.showSorterTooltip?i:u.showSorterTooltip,f=an(u,s),p=n.find((e=>{let{key:t}=e;return t===f})),h=p?p.sortOrder:null,m=function(e,t){return t?e[e.indexOf(t)+1]:e[0]}(c,h);let g;if(t.sortIcon)g=t.sortIcon({sortOrder:h});else{const t=c.includes(nr)&&o.createElement(er,{className:N()(`${e}-column-sorter-up`,{active:h===nr})}),n=c.includes(or)&&o.createElement(Qo,{className:N()(`${e}-column-sorter-down`,{active:h===or})});g=o.createElement("span",{className:N()(`${e}-column-sorter`,{[`${e}-column-sorter-full`]:!(!t||!n)})},o.createElement("span",{className:`${e}-column-sorter-inner`,"aria-hidden":"true"},t,n))}const{cancelSort:v,triggerAsc:y,triggerDesc:b}=a||{};let x=v;m===or?x=b:m===nr&&(x=y);const A="object"==typeof d?Object.assign({title:x},d):{title:x};u=Object.assign(Object.assign({},u),{className:N()(u.className,{[`${e}-column-sort`]:h}),title:n=>{const r=`${e}-column-sorters`,l=o.createElement("span",{className:`${e}-column-title`},dn(t.title,n)),a=o.createElement("div",{className:r},l,g);return d?"boolean"!=typeof d&&"sorter-icon"===(null==d?void 0:d.target)?o.createElement("div",{className:`${r} ${e}-column-sorters-tooltip-target-sorter`},l,o.createElement(tr.A,Object.assign({},A),g)):o.createElement(tr.A,Object.assign({},A),a):a},onHeaderCell:n=>{const o=t.onHeaderCell&&t.onHeaderCell(n)||{},l=o.onClick,a=o.onKeyDown;o.onClick=e=>{r({column:t,key:f,sortOrder:m,multiplePriority:rr(t)}),null==l||l(e)},o.onKeyDown=e=>{e.keyCode===bn.A.ENTER&&(r({column:t,key:f,sortOrder:m,multiplePriority:rr(t)}),null==a||a(e))};const i=function(e,t){const n=dn(e,{});return"[object Object]"===Object.prototype.toString.call(n)?"":n}(t.title),c=null==i?void 0:i.toString();return h?o["aria-sort"]="ascend"===h?"ascending":"descending":o["aria-label"]=c||"",o.className=N()(o.className,`${e}-column-has-sorters`),o.tabIndex=0,t.ellipsis&&(o.title=(null!=i?i:"").toString()),o}})}return"children"in u&&(u=Object.assign(Object.assign({},u),{children:ir(e,u.children,n,r,l,a,i,s)})),u}))}const cr=e=>{const{column:t,sortOrder:n}=e;return{column:t,order:n,field:t.dataIndex,columnKey:t.key}},dr=e=>{const t=e.filter((e=>{let{sortOrder:t}=e;return t})).map(cr);if(0===t.length&&e.length){const t=e.length-1;return Object.assign(Object.assign({},cr(e[t])),{column:void 0})}return t.length<=1?t[0]||{}:t};function sr(e,t,n){const o=t.slice().sort(((e,t)=>t.multiplePriority-e.multiplePriority)),r=e.slice(),l=o.filter((e=>{let{column:{sorter:t},sortOrder:n}=e;return lr(t)&&n}));return l.length?r.sort(((e,t)=>{for(let n=0;n<l.length;n+=1){const o=l[n],{column:{sorter:r},sortOrder:a}=o,i=lr(r);if(i&&a){const n=i(e,t,a);if(0!==n)return a===nr?n:-n}}return 0})).map((e=>{const o=e[n];return o?Object.assign(Object.assign({},e),{[n]:sr(o,t,n)}):e})):r}function ur(e,t){return e.map((e=>{const n=Object.assign({},e);return n.title=dn(e.title,t),"children"in n&&(n.children=ur(n.children,t)),n}))}function fr(e){return[o.useCallback((t=>ur(t,e)),[e])]}const pr=He(((e,t)=>{const{_renderTimes:n}=e,{_renderTimes:o}=t;return n!==o})),hr=et(((e,t)=>{const{_renderTimes:n}=e,{_renderTimes:o}=t;return n!==o}));var mr=n(26411);const gr=e=>{const{componentCls:t,lineWidth:n,lineType:o,tableBorderColor:r,tableHeaderBg:l,tablePaddingVertical:a,tablePaddingHorizontal:i,calc:c}=e,d=`${(0,Yn.zA)(n)} ${o} ${r}`,s=(e,o,r)=>({[`&${t}-${e}`]:{[`> ${t}-container`]:{[`> ${t}-content, > ${t}-body`]:{"\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          ":{[`> ${t}-expanded-row-fixed`]:{margin:`${(0,Yn.zA)(c(o).mul(-1).equal())}\n              ${(0,Yn.zA)(c(c(r).add(n)).mul(-1).equal())}`}}}}}});return{[`${t}-wrapper`]:{[`${t}${t}-bordered`]:Object.assign(Object.assign(Object.assign({[`> ${t}-title`]:{border:d,borderBottom:0},[`> ${t}-container`]:{borderInlineStart:d,borderTop:d,[`\n            > ${t}-content,\n            > ${t}-header,\n            > ${t}-body,\n            > ${t}-summary\n          `]:{"> table":{"\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              ":{borderInlineEnd:d},"> thead":{"> tr:not(:last-child) > th":{borderBottom:d},"> tr > th::before":{backgroundColor:"transparent !important"}},"\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              ":{[`> ${t}-cell-fix-right-first::after`]:{borderInlineEnd:d}},"\n                > tbody > tr > th,\n                > tbody > tr > td\n              ":{[`> ${t}-expanded-row-fixed`]:{margin:`${(0,Yn.zA)(c(a).mul(-1).equal())} ${(0,Yn.zA)(c(c(i).add(n)).mul(-1).equal())}`,"&::after":{position:"absolute",top:0,insetInlineEnd:n,bottom:0,borderInlineEnd:d,content:'""'}}}}}},[`&${t}-scroll-horizontal`]:{[`> ${t}-container > ${t}-body`]:{"> table > tbody":{[`\n                > tr${t}-expanded-row,\n                > tr${t}-placeholder\n              `]:{"> th, > td":{borderInlineEnd:0}}}}}},s("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle)),s("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall)),{[`> ${t}-footer`]:{border:d,borderTop:0}}),[`${t}-cell`]:{[`${t}-container:first-child`]:{borderTop:0},"&-scrollbar:not([rowspan])":{boxShadow:`0 ${(0,Yn.zA)(n)} 0 ${(0,Yn.zA)(n)} ${l}`}},[`${t}-bordered ${t}-cell-scrollbar`]:{borderInlineEnd:d}}}},vr=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-cell-ellipsis`]:Object.assign(Object.assign({},Jn.L9),{wordBreak:"keep-all",[`\n          &${t}-cell-fix-left-last,\n          &${t}-cell-fix-right-first\n        `]:{overflow:"visible",[`${t}-cell-content`]:{display:"block",overflow:"hidden",textOverflow:"ellipsis"}},[`${t}-column-title`]:{overflow:"hidden",textOverflow:"ellipsis",wordBreak:"keep-all"}})}}},yr=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-tbody > tr${t}-placeholder`]:{textAlign:"center",color:e.colorTextDisabled,"\n          &:hover > th,\n          &:hover > td,\n        ":{background:e.colorBgContainer}}}}};var br=n(11981);const xr=e=>{const{componentCls:t,antCls:n,motionDurationSlow:o,lineWidth:r,paddingXS:l,lineType:a,tableBorderColor:i,tableExpandIconBg:c,tableExpandColumnWidth:d,borderRadius:s,tablePaddingVertical:u,tablePaddingHorizontal:f,tableExpandedRowBg:p,paddingXXS:h,expandIconMarginTop:m,expandIconSize:g,expandIconHalfInner:v,expandIconScale:y,calc:b}=e,x=`${(0,Yn.zA)(r)} ${a} ${i}`,A=b(h).sub(r).equal();return{[`${t}-wrapper`]:{[`${t}-expand-icon-col`]:{width:d},[`${t}-row-expand-icon-cell`]:{textAlign:"center",[`${t}-row-expand-icon`]:{display:"inline-flex",float:"none",verticalAlign:"sub"}},[`${t}-row-indent`]:{height:1,float:"left"},[`${t}-row-expand-icon`]:Object.assign(Object.assign({},(0,br.Y)(e)),{position:"relative",float:"left",boxSizing:"border-box",width:g,height:g,padding:0,color:"inherit",lineHeight:(0,Yn.zA)(g),background:c,border:x,borderRadius:s,transform:`scale(${y})`,transition:`all ${o}`,userSelect:"none","&:focus, &:hover, &:active":{borderColor:"currentcolor"},"&::before, &::after":{position:"absolute",background:"currentcolor",transition:`transform ${o} ease-out`,content:'""'},"&::before":{top:v,insetInlineEnd:A,insetInlineStart:A,height:r},"&::after":{top:A,bottom:A,insetInlineStart:v,width:r,transform:"rotate(90deg)"},"&-collapsed::before":{transform:"rotate(-180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"},"&-spaced":{"&::before, &::after":{display:"none",content:"none"},background:"transparent",border:0,visibility:"hidden"}}),[`${t}-row-indent + ${t}-row-expand-icon`]:{marginTop:m,marginInlineEnd:l},[`tr${t}-expanded-row`]:{"&, &:hover":{"> th, > td":{background:p}},[`${n}-descriptions-view`]:{display:"flex",table:{flex:"auto",width:"auto"}}},[`${t}-expanded-row-fixed`]:{position:"relative",margin:`${(0,Yn.zA)(b(u).mul(-1).equal())} ${(0,Yn.zA)(b(f).mul(-1).equal())}`,padding:`${(0,Yn.zA)(u)} ${(0,Yn.zA)(f)}`}}}},Ar=e=>{const{componentCls:t,antCls:n,iconCls:o,tableFilterDropdownWidth:r,tableFilterDropdownSearchWidth:l,paddingXXS:a,paddingXS:i,colorText:c,lineWidth:d,lineType:s,tableBorderColor:u,headerIconColor:f,fontSizeSM:p,tablePaddingHorizontal:h,borderRadius:m,motionDurationSlow:g,colorTextDescription:v,colorPrimary:y,tableHeaderFilterActiveBg:b,colorTextDisabled:x,tableFilterDropdownBg:A,tableFilterDropdownHeight:C,controlItemBgHover:w,controlItemBgActive:k,boxShadowSecondary:S,filterDropdownMenuBg:E,calc:N}=e,$=`${n}-dropdown`,K=`${t}-filter-dropdown`,I=`${n}-tree`,O=`${(0,Yn.zA)(d)} ${s} ${u}`;return[{[`${t}-wrapper`]:{[`${t}-filter-column`]:{display:"flex",justifyContent:"space-between"},[`${t}-filter-trigger`]:{position:"relative",display:"flex",alignItems:"center",marginBlock:N(a).mul(-1).equal(),marginInline:`${(0,Yn.zA)(a)} ${(0,Yn.zA)(N(h).div(2).mul(-1).equal())}`,padding:`0 ${(0,Yn.zA)(a)}`,color:f,fontSize:p,borderRadius:m,cursor:"pointer",transition:`all ${g}`,"&:hover":{color:v,background:b},"&.active":{color:y}}}},{[`${n}-dropdown`]:{[K]:Object.assign(Object.assign({},(0,Jn.dF)(e)),{minWidth:r,backgroundColor:A,borderRadius:m,boxShadow:S,overflow:"hidden",[`${$}-menu`]:{maxHeight:C,overflowX:"hidden",border:0,boxShadow:"none",borderRadius:"unset",backgroundColor:E,"&:empty::after":{display:"block",padding:`${(0,Yn.zA)(i)} 0`,color:x,fontSize:p,textAlign:"center",content:'"Not Found"'}},[`${K}-tree`]:{paddingBlock:`${(0,Yn.zA)(i)} 0`,paddingInline:i,[I]:{padding:0},[`${I}-treenode ${I}-node-content-wrapper:hover`]:{backgroundColor:w},[`${I}-treenode-checkbox-checked ${I}-node-content-wrapper`]:{"&, &:hover":{backgroundColor:k}}},[`${K}-search`]:{padding:i,borderBottom:O,"&-input":{input:{minWidth:l},[o]:{color:x}}},[`${K}-checkall`]:{width:"100%",marginBottom:a,marginInlineStart:a},[`${K}-btns`]:{display:"flex",justifyContent:"space-between",padding:`${(0,Yn.zA)(N(i).sub(d).equal())} ${(0,Yn.zA)(i)}`,overflow:"hidden",borderTop:O}})}},{[`${n}-dropdown ${K}, ${K}-submenu`]:{[`${n}-checkbox-wrapper + span`]:{paddingInlineStart:i,color:c},"> ul":{maxHeight:"calc(100vh - 130px)",overflowX:"hidden",overflowY:"auto"}}}]},Cr=e=>{const{componentCls:t,lineWidth:n,colorSplit:o,motionDurationSlow:r,zIndexTableFixed:l,tableBg:a,zIndexTableSticky:i,calc:c}=e,d=o;return{[`${t}-wrapper`]:{[`\n        ${t}-cell-fix-left,\n        ${t}-cell-fix-right\n      `]:{position:"sticky !important",zIndex:l,background:a},[`\n        ${t}-cell-fix-left-first::after,\n        ${t}-cell-fix-left-last::after\n      `]:{position:"absolute",top:0,right:{_skip_check_:!0,value:0},bottom:c(n).mul(-1).equal(),width:30,transform:"translateX(100%)",transition:`box-shadow ${r}`,content:'""',pointerEvents:"none"},[`${t}-cell-fix-left-all::after`]:{display:"none"},[`\n        ${t}-cell-fix-right-first::after,\n        ${t}-cell-fix-right-last::after\n      `]:{position:"absolute",top:0,bottom:c(n).mul(-1).equal(),left:{_skip_check_:!0,value:0},width:30,transform:"translateX(-100%)",transition:`box-shadow ${r}`,content:'""',pointerEvents:"none"},[`${t}-container`]:{position:"relative","&::before, &::after":{position:"absolute",top:0,bottom:0,zIndex:c(i).add(1).equal({unit:!1}),width:30,transition:`box-shadow ${r}`,content:'""',pointerEvents:"none"},"&::before":{insetInlineStart:0},"&::after":{insetInlineEnd:0}},[`${t}-ping-left`]:{[`&:not(${t}-has-fix-left) ${t}-container::before`]:{boxShadow:`inset 10px 0 8px -8px ${d}`},[`\n          ${t}-cell-fix-left-first::after,\n          ${t}-cell-fix-left-last::after\n        `]:{boxShadow:`inset 10px 0 8px -8px ${d}`},[`${t}-cell-fix-left-last::before`]:{backgroundColor:"transparent !important"}},[`${t}-ping-right`]:{[`&:not(${t}-has-fix-right) ${t}-container::after`]:{boxShadow:`inset -10px 0 8px -8px ${d}`},[`\n          ${t}-cell-fix-right-first::after,\n          ${t}-cell-fix-right-last::after\n        `]:{boxShadow:`inset -10px 0 8px -8px ${d}`}},[`${t}-fixed-column-gapped`]:{[`\n        ${t}-cell-fix-left-first::after,\n        ${t}-cell-fix-left-last::after,\n        ${t}-cell-fix-right-first::after,\n        ${t}-cell-fix-right-last::after\n      `]:{boxShadow:"none"}}}}},wr=e=>{const{componentCls:t,antCls:n,margin:o}=e;return{[`${t}-wrapper`]:{[`${t}-pagination${n}-pagination`]:{margin:`${(0,Yn.zA)(o)} 0`},[`${t}-pagination`]:{display:"flex",flexWrap:"wrap",rowGap:e.paddingXS,"> *":{flex:"none"},"&-left":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-right":{justifyContent:"flex-end"}}}}},kr=e=>{const{componentCls:t,tableRadius:n}=e;return{[`${t}-wrapper`]:{[t]:{[`${t}-title, ${t}-header`]:{borderRadius:`${(0,Yn.zA)(n)} ${(0,Yn.zA)(n)} 0 0`},[`${t}-title + ${t}-container`]:{borderStartStartRadius:0,borderStartEndRadius:0,[`${t}-header, table`]:{borderRadius:0},"table > thead > tr:first-child":{"th:first-child, th:last-child, td:first-child, td:last-child":{borderRadius:0}}},"&-container":{borderStartStartRadius:n,borderStartEndRadius:n,"table > thead > tr:first-child":{"> *:first-child":{borderStartStartRadius:n},"> *:last-child":{borderStartEndRadius:n}}},"&-footer":{borderRadius:`0 0 ${(0,Yn.zA)(n)} ${(0,Yn.zA)(n)}`}}}}},Sr=e=>{const{componentCls:t}=e;return{[`${t}-wrapper-rtl`]:{direction:"rtl",table:{direction:"rtl"},[`${t}-pagination-left`]:{justifyContent:"flex-end"},[`${t}-pagination-right`]:{justifyContent:"flex-start"},[`${t}-row-expand-icon`]:{float:"right","&::after":{transform:"rotate(-90deg)"},"&-collapsed::before":{transform:"rotate(180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"}},[`${t}-container`]:{"&::before":{insetInlineStart:"unset",insetInlineEnd:0},"&::after":{insetInlineStart:0,insetInlineEnd:"unset"},[`${t}-row-indent`]:{float:"right"}}}}},Er=e=>{const{componentCls:t,antCls:n,iconCls:o,fontSizeIcon:r,padding:l,paddingXS:a,headerIconColor:i,headerIconHoverColor:c,tableSelectionColumnWidth:d,tableSelectedRowBg:s,tableSelectedRowHoverBg:u,tableRowHoverBg:f,tablePaddingHorizontal:p,calc:h}=e;return{[`${t}-wrapper`]:{[`${t}-selection-col`]:{width:d,[`&${t}-selection-col-with-dropdown`]:{width:h(d).add(r).add(h(l).div(4)).equal()}},[`${t}-bordered ${t}-selection-col`]:{width:h(d).add(h(a).mul(2)).equal(),[`&${t}-selection-col-with-dropdown`]:{width:h(d).add(r).add(h(l).div(4)).add(h(a).mul(2)).equal()}},[`\n        table tr th${t}-selection-column,\n        table tr td${t}-selection-column,\n        ${t}-selection-column\n      `]:{paddingInlineEnd:e.paddingXS,paddingInlineStart:e.paddingXS,textAlign:"center",[`${n}-radio-wrapper`]:{marginInlineEnd:0}},[`table tr th${t}-selection-column${t}-cell-fix-left`]:{zIndex:e.zIndexTableFixed+1},[`table tr th${t}-selection-column::after`]:{backgroundColor:"transparent !important"},[`${t}-selection`]:{position:"relative",display:"inline-flex",flexDirection:"column"},[`${t}-selection-extra`]:{position:"absolute",top:0,zIndex:1,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,marginInlineStart:"100%",paddingInlineStart:(0,Yn.zA)(h(p).div(4).equal()),[o]:{color:i,fontSize:r,verticalAlign:"baseline","&:hover":{color:c}}},[`${t}-tbody`]:{[`${t}-row`]:{[`&${t}-row-selected`]:{[`> ${t}-cell`]:{background:s,"&-row-hover":{background:u}}},[`> ${t}-cell-row-hover`]:{background:f}}}}}},Nr=e=>{const{componentCls:t,tableExpandColumnWidth:n,calc:o}=e,r=(e,r,l,a)=>({[`${t}${t}-${e}`]:{fontSize:a,[`\n        ${t}-title,\n        ${t}-footer,\n        ${t}-cell,\n        ${t}-thead > tr > th,\n        ${t}-tbody > tr > th,\n        ${t}-tbody > tr > td,\n        tfoot > tr > th,\n        tfoot > tr > td\n      `]:{padding:`${(0,Yn.zA)(r)} ${(0,Yn.zA)(l)}`},[`${t}-filter-trigger`]:{marginInlineEnd:(0,Yn.zA)(o(l).div(2).mul(-1).equal())},[`${t}-expanded-row-fixed`]:{margin:`${(0,Yn.zA)(o(r).mul(-1).equal())} ${(0,Yn.zA)(o(l).mul(-1).equal())}`},[`${t}-tbody`]:{[`${t}-wrapper:only-child ${t}`]:{marginBlock:(0,Yn.zA)(o(r).mul(-1).equal()),marginInline:`${(0,Yn.zA)(o(n).sub(l).equal())} ${(0,Yn.zA)(o(l).mul(-1).equal())}`}},[`${t}-selection-extra`]:{paddingInlineStart:(0,Yn.zA)(o(l).div(4).equal())}}});return{[`${t}-wrapper`]:Object.assign(Object.assign({},r("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle,e.tableFontSizeMiddle)),r("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall,e.tableFontSizeSmall))}},$r=e=>{const{componentCls:t,marginXXS:n,fontSizeIcon:o,headerIconColor:r,headerIconHoverColor:l}=e;return{[`${t}-wrapper`]:{[`${t}-thead th${t}-column-has-sorters`]:{outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{background:e.tableHeaderSortHoverBg,"&::before":{backgroundColor:"transparent !important"}},"&:focus-visible":{color:e.colorPrimary},[`\n          &${t}-cell-fix-left:hover,\n          &${t}-cell-fix-right:hover\n        `]:{background:e.tableFixedHeaderSortActiveBg}},[`${t}-thead th${t}-column-sort`]:{background:e.tableHeaderSortBg,"&::before":{backgroundColor:"transparent !important"}},[`td${t}-column-sort`]:{background:e.tableBodySortBg},[`${t}-column-title`]:{position:"relative",zIndex:1,flex:1},[`${t}-column-sorters`]:{display:"flex",flex:"auto",alignItems:"center",justifyContent:"space-between","&::after":{position:"absolute",inset:0,width:"100%",height:"100%",content:'""'}},[`${t}-column-sorters-tooltip-target-sorter`]:{"&::after":{content:"none"}},[`${t}-column-sorter`]:{marginInlineStart:n,color:r,fontSize:0,transition:`color ${e.motionDurationSlow}`,"&-inner":{display:"inline-flex",flexDirection:"column",alignItems:"center"},"&-up, &-down":{fontSize:o,"&.active":{color:e.colorPrimary}},[`${t}-column-sorter-up + ${t}-column-sorter-down`]:{marginTop:"-0.3em"}},[`${t}-column-sorters:hover ${t}-column-sorter`]:{color:l}}}},Kr=e=>{const{componentCls:t,opacityLoading:n,tableScrollThumbBg:o,tableScrollThumbBgHover:r,tableScrollThumbSize:l,tableScrollBg:a,zIndexTableSticky:i,stickyScrollBarBorderRadius:c,lineWidth:d,lineType:s,tableBorderColor:u}=e,f=`${(0,Yn.zA)(d)} ${s} ${u}`;return{[`${t}-wrapper`]:{[`${t}-sticky`]:{"&-holder":{position:"sticky",zIndex:i,background:e.colorBgContainer},"&-scroll":{position:"sticky",bottom:0,height:`${(0,Yn.zA)(l)} !important`,zIndex:i,display:"flex",alignItems:"center",background:a,borderTop:f,opacity:n,"&:hover":{transformOrigin:"center bottom"},"&-bar":{height:l,backgroundColor:o,borderRadius:c,transition:`all ${e.motionDurationSlow}, transform none`,position:"absolute",bottom:0,"&:hover, &-active":{backgroundColor:r}}}}}}},Ir=e=>{const{componentCls:t,lineWidth:n,tableBorderColor:o,calc:r}=e,l=`${(0,Yn.zA)(n)} ${e.lineType} ${o}`;return{[`${t}-wrapper`]:{[`${t}-summary`]:{position:"relative",zIndex:e.zIndexTableFixed,background:e.tableBg,"> tr":{"> th, > td":{borderBottom:l}}},[`div${t}-summary`]:{boxShadow:`0 ${(0,Yn.zA)(r(n).mul(-1).equal())} 0 ${o}`}}}},Or=e=>{const{componentCls:t,motionDurationMid:n,lineWidth:o,lineType:r,tableBorderColor:l,calc:a}=e,i=`${(0,Yn.zA)(o)} ${r} ${l}`,c=`${t}-expanded-row-cell`;return{[`${t}-wrapper`]:{[`${t}-tbody-virtual`]:{[`${t}-row:not(tr)`]:{display:"flex",boxSizing:"border-box",width:"100%"},[`${t}-cell`]:{borderBottom:i,transition:`background ${n}`},[`${t}-expanded-row`]:{[`${c}${c}-fixed`]:{position:"sticky",insetInlineStart:0,overflow:"hidden",width:`calc(var(--virtual-width) - ${(0,Yn.zA)(o)})`,borderInlineEnd:"none"}}},[`${t}-bordered`]:{[`${t}-tbody-virtual`]:{"&:after":{content:'""',insetInline:0,bottom:0,borderBottom:i,position:"absolute"},[`${t}-cell`]:{borderInlineEnd:i,[`&${t}-cell-fix-right-first:before`]:{content:'""',position:"absolute",insetBlock:0,insetInlineStart:a(o).mul(-1).equal(),borderInlineStart:i}}},[`&${t}-virtual`]:{[`${t}-placeholder ${t}-cell`]:{borderInlineEnd:i,borderBottom:i}}}}}},Rr=e=>{const{componentCls:t,fontWeightStrong:n,tablePaddingVertical:o,tablePaddingHorizontal:r,tableExpandColumnWidth:l,lineWidth:a,lineType:i,tableBorderColor:c,tableFontSize:d,tableBg:s,tableRadius:u,tableHeaderTextColor:f,motionDurationMid:p,tableHeaderBg:h,tableHeaderCellSplitColor:m,tableFooterTextColor:g,tableFooterBg:v,calc:y}=e,b=`${(0,Yn.zA)(a)} ${i} ${c}`;return{[`${t}-wrapper`]:Object.assign(Object.assign({clear:"both",maxWidth:"100%"},(0,Jn.t6)()),{[t]:Object.assign(Object.assign({},(0,Jn.dF)(e)),{fontSize:d,background:s,borderRadius:`${(0,Yn.zA)(u)} ${(0,Yn.zA)(u)} 0 0`,scrollbarColor:`${e.tableScrollThumbBg} ${e.tableScrollBg}`}),table:{width:"100%",textAlign:"start",borderRadius:`${(0,Yn.zA)(u)} ${(0,Yn.zA)(u)} 0 0`,borderCollapse:"separate",borderSpacing:0},[`\n          ${t}-cell,\n          ${t}-thead > tr > th,\n          ${t}-tbody > tr > th,\n          ${t}-tbody > tr > td,\n          tfoot > tr > th,\n          tfoot > tr > td\n        `]:{position:"relative",padding:`${(0,Yn.zA)(o)} ${(0,Yn.zA)(r)}`,overflowWrap:"break-word"},[`${t}-title`]:{padding:`${(0,Yn.zA)(o)} ${(0,Yn.zA)(r)}`},[`${t}-thead`]:{"\n          > tr > th,\n          > tr > td\n        ":{position:"relative",color:f,fontWeight:n,textAlign:"start",background:h,borderBottom:b,transition:`background ${p} ease`,"&[colspan]:not([colspan='1'])":{textAlign:"center"},[`&:not(:last-child):not(${t}-selection-column):not(${t}-row-expand-icon-cell):not([colspan])::before`]:{position:"absolute",top:"50%",insetInlineEnd:0,width:1,height:"1.6em",backgroundColor:m,transform:"translateY(-50%)",transition:`background-color ${p}`,content:'""'}},"> tr:not(:last-child) > th[colspan]":{borderBottom:0}},[`${t}-tbody`]:{"> tr":{"> th, > td":{transition:`background ${p}, border-color ${p}`,borderBottom:b,[`\n              > ${t}-wrapper:only-child,\n              > ${t}-expanded-row-fixed > ${t}-wrapper:only-child\n            `]:{[t]:{marginBlock:(0,Yn.zA)(y(o).mul(-1).equal()),marginInline:`${(0,Yn.zA)(y(l).sub(r).equal())}\n                ${(0,Yn.zA)(y(r).mul(-1).equal())}`,[`${t}-tbody > tr:last-child > td`]:{borderBottom:0,"&:first-child, &:last-child":{borderRadius:0}}}}},"> th":{position:"relative",color:f,fontWeight:n,textAlign:"start",background:h,borderBottom:b,transition:`background ${p} ease`}}},[`${t}-footer`]:{padding:`${(0,Yn.zA)(o)} ${(0,Yn.zA)(r)}`,color:g,background:v}})}},Dr=(0,to.OF)("Table",(e=>{const{colorTextHeading:t,colorSplit:n,colorBgContainer:o,controlInteractiveSize:r,headerBg:l,headerColor:a,headerSortActiveBg:i,headerSortHoverBg:c,bodySortBg:d,rowHoverBg:s,rowSelectedBg:u,rowSelectedHoverBg:f,rowExpandedBg:p,cellPaddingBlock:h,cellPaddingInline:m,cellPaddingBlockMD:g,cellPaddingInlineMD:v,cellPaddingBlockSM:y,cellPaddingInlineSM:b,borderColor:x,footerBg:A,footerColor:C,headerBorderRadius:w,cellFontSize:k,cellFontSizeMD:S,cellFontSizeSM:E,headerSplitColor:N,fixedHeaderSortActiveBg:$,headerFilterHoverBg:K,filterDropdownBg:I,expandIconBg:O,selectionColumnWidth:R,stickyScrollBarBg:D,calc:P}=e,T=(0,eo.h1)(e,{tableFontSize:k,tableBg:o,tableRadius:w,tablePaddingVertical:h,tablePaddingHorizontal:m,tablePaddingVerticalMiddle:g,tablePaddingHorizontalMiddle:v,tablePaddingVerticalSmall:y,tablePaddingHorizontalSmall:b,tableBorderColor:x,tableHeaderTextColor:a,tableHeaderBg:l,tableFooterTextColor:C,tableFooterBg:A,tableHeaderCellSplitColor:N,tableHeaderSortBg:i,tableHeaderSortHoverBg:c,tableBodySortBg:d,tableFixedHeaderSortActiveBg:$,tableHeaderFilterActiveBg:K,tableFilterDropdownBg:I,tableRowHoverBg:s,tableSelectedRowBg:u,tableSelectedRowHoverBg:f,zIndexTableFixed:2,zIndexTableSticky:3,tableFontSizeMiddle:S,tableFontSizeSmall:E,tableSelectionColumnWidth:R,tableExpandIconBg:O,tableExpandColumnWidth:P(r).add(P(e.padding).mul(2)).equal(),tableExpandedRowBg:p,tableFilterDropdownWidth:120,tableFilterDropdownHeight:264,tableFilterDropdownSearchWidth:140,tableScrollThumbSize:8,tableScrollThumbBg:D,tableScrollThumbBgHover:t,tableScrollBg:n});return[Rr(T),wr(T),Ir(T),$r(T),Ar(T),gr(T),kr(T),xr(T),Ir(T),yr(T),Er(T),Cr(T),Kr(T),vr(T),Nr(T),Sr(T),Or(T)]}),(e=>{const{colorFillAlter:t,colorBgContainer:n,colorTextHeading:o,colorFillSecondary:r,colorFillContent:l,controlItemBgActive:a,controlItemBgActiveHover:i,padding:c,paddingSM:d,paddingXS:s,colorBorderSecondary:u,borderRadiusLG:f,controlHeight:p,colorTextPlaceholder:h,fontSize:m,fontSizeSM:g,lineHeight:v,lineWidth:y,colorIcon:b,colorIconHover:x,opacityLoading:A,controlInteractiveSize:C}=e,w=new mr.q(r).onBackground(n).toHexShortString(),k=new mr.q(l).onBackground(n).toHexShortString(),S=new mr.q(t).onBackground(n).toHexShortString(),E=new mr.q(b),N=new mr.q(x),$=C/2-y,K=2*$+3*y;return{headerBg:S,headerColor:o,headerSortActiveBg:w,headerSortHoverBg:k,bodySortBg:S,rowHoverBg:S,rowSelectedBg:a,rowSelectedHoverBg:i,rowExpandedBg:t,cellPaddingBlock:c,cellPaddingInline:c,cellPaddingBlockMD:d,cellPaddingInlineMD:s,cellPaddingBlockSM:s,cellPaddingInlineSM:s,borderColor:u,headerBorderRadius:f,footerBg:S,footerColor:o,cellFontSize:m,cellFontSizeMD:m,cellFontSizeSM:m,headerSplitColor:u,fixedHeaderSortActiveBg:w,headerFilterHoverBg:l,filterDropdownMenuBg:n,filterDropdownBg:n,expandIconBg:n,selectionColumnWidth:p,stickyScrollBarBg:h,stickyScrollBarBorderRadius:100,expandIconMarginTop:(m*v-3*y)/2-Math.ceil((1.4*g-3*y)/2),headerIconColor:E.clone().setAlpha(E.getAlpha()*A).toRgbString(),headerIconHoverColor:N.clone().setAlpha(N.getAlpha()*A).toRgbString(),expandIconHalfInner:$,expandIconSize:K,expandIconScale:C/K}}),{unitless:{expandIconScale:!0}}),Pr=[],Tr=(e,t)=>{var n,r;const{prefixCls:l,className:i,rootClassName:c,style:d,size:s,bordered:u,dropdownPrefixCls:f,dataSource:p,pagination:h,rowSelection:m,rowKey:g="key",rowClassName:v,columns:y,children:b,childrenColumnName:x,onChange:A,getPopupContainer:C,loading:w,expandIcon:k,expandable:S,expandedRowRender:E,expandIconColumnIndex:$,indentSize:K,scroll:I,sortDirections:O,locale:R,showSorterTooltip:D={target:"full-header"},virtual:P}=e;(0,Lt.rJ)("Table");const T=o.useMemo((()=>y||xe(b)),[y,b]),M=o.useMemo((()=>T.some((e=>e.responsive))),[T]),z=(0,tn.A)(M),B=o.useMemo((()=>{const e=new Set(Object.keys(z).filter((e=>z[e])));return T.filter((t=>!t.responsive||t.responsive.some((t=>e.has(t)))))}),[T,z]),H=(0,ut.A)(e,["className","style","columns"]),{locale:L=nn.A,direction:j,table:F,renderEmpty:W,getPrefixCls:_,getPopupContainer:q}=o.useContext(Qt.QO),V=(0,en.A)(s),X=Object.assign(Object.assign({},L.Table),R),U=p||Pr,G=_("table",l),Y=_("dropdown",f),[,Q]=(0,ln.Ay)(),J=(0,Zt.A)(G),[Z,ee,te]=Dr(G,J),ne=Object.assign(Object.assign({childrenColumnName:x,expandIconColumnIndex:$},S),{expandIcon:null!==(n=null==S?void 0:S.expandIcon)&&void 0!==n?n:null===(r=null==F?void 0:F.expandable)||void 0===r?void 0:r.expandIcon}),{childrenColumnName:oe="children"}=ne,re=o.useMemo((()=>U.some((e=>null==e?void 0:e[oe]))?"nest":E||S&&S.expandedRowRender?"row":null),[U]),le={body:o.useRef()},ae=function(e){return(t,n)=>{const o=t.querySelector(`.${e}-container`);let r=n;if(o){const e=getComputedStyle(o);r=n-parseInt(e.borderLeftWidth,10)-parseInt(e.borderRightWidth,10)}return r}}(G),ce=o.useRef(null),de=o.useRef(null);!function(e,t){(0,o.useImperativeHandle)(e,(()=>{const e=t(),{nativeElement:n}=e;return"undefined"!=typeof Proxy?new Proxy(n,{get:(t,n)=>e[n]?e[n]:Reflect.get(t,n)}):(r=e,(o=n)._antProxy=o._antProxy||{},Object.keys(r).forEach((e=>{if(!(e in o._antProxy)){const t=o[e];o._antProxy[e]=t,o[e]=r[e]}})),o);var o,r}))}(t,(()=>Object.assign(Object.assign({},de.current),{nativeElement:ce.current})));const ue=o.useMemo((()=>"function"==typeof g?g:e=>null==e?void 0:e[g]),[g]),[fe]=function(e,t,n){const r=o.useRef({});return[function(o){if(!r.current||r.current.data!==e||r.current.childrenColumnName!==t||r.current.getRowKey!==n){const l=new Map;function a(e){e.forEach(((e,o)=>{const r=n(e,o);l.set(r,e),e&&"object"==typeof e&&t in e&&a(e[t]||[])}))}a(e),r.current={data:e,childrenColumnName:t,kvMap:l,getRowKey:n}}return r.current.kvMap.get(o)}]}(U,oe,ue),pe={},he=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];var o,r,l;const a=Object.assign(Object.assign({},pe),e);n&&(null===(o=pe.resetPagination)||void 0===o||o.call(pe),(null===(r=a.pagination)||void 0===r?void 0:r.current)&&(a.pagination.current=1),h&&h.onChange&&h.onChange(1,null===(l=a.pagination)||void 0===l?void 0:l.pageSize)),I&&!1!==I.scrollToFirstRowOnChange&&le.body.current&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{getContainer:n=(()=>window),callback:o,duration:r=450}=t,l=n(),a=function(e,t){var n,o;if("undefined"==typeof window)return 0;const r="scrollTop";let l=0;return Yt(e)?l=e.pageYOffset:e instanceof Document?l=e.documentElement[r]:(e instanceof HTMLElement||e)&&(l=e[r]),e&&!Yt(e)&&"number"!=typeof l&&(l=null===(o=(null!==(n=e.ownerDocument)&&void 0!==n?n:e).documentElement)||void 0===o?void 0:o[r]),l}(l),i=Date.now(),c=()=>{const t=Date.now()-i,n=function(e,t,n,o){const r=n-t;return(e/=o/2)<1?r/2*e*e*e+t:r/2*((e-=2)*e*e+2)+t}(t>r?r:t,a,e,r);Yt(l)?l.scrollTo(window.pageXOffset,n):l instanceof Document||"HTMLDocument"===l.constructor.name?l.documentElement.scrollTop=n:l.scrollTop=n,t<r?(0,Ie.A)(c):"function"==typeof o&&o()};(0,Ie.A)(c)}(0,{getContainer:()=>le.body.current}),null==A||A(a.pagination,a.filters,a.sorter,{currentDataSource:Wo(sr(U,a.sorterStates,oe),a.filterStates,oe),action:t})},[me,ge,ve,ye]=function(e){let{prefixCls:t,mergedColumns:n,onSorterChange:r,sortDirections:l,tableLocale:a,showSorterTooltip:i}=e;const[c,d]=o.useState(ar(n,!0)),s=o.useMemo((()=>{let e=!0;const t=ar(n,!1);if(!t.length)return c;const o=[];function r(t){e?o.push(t):o.push(Object.assign(Object.assign({},t),{sortOrder:null}))}let l=null;return t.forEach((t=>{null===l?(r(t),t.sortOrder&&(!1===t.multiplePriority?e=!1:l=!0)):(l&&!1!==t.multiplePriority||(e=!1),r(t))})),o}),[n,c]),u=o.useMemo((()=>{const e=s.map((e=>{let{column:t,sortOrder:n}=e;return{column:t,order:n}}));return{sortColumns:e,sortColumn:e[0]&&e[0].column,sortOrder:e[0]&&e[0].order}}),[s]),f=e=>{let t;t=!1!==e.multiplePriority&&s.length&&!1!==s[0].multiplePriority?[].concat((0,se.A)(s.filter((t=>{let{key:n}=t;return n!==e.key}))),[e]):[e],d(t),r(dr(t),t)};return[e=>ir(t,e,s,f,l,a,i),s,u,()=>dr(s)]}({prefixCls:G,mergedColumns:B,onSorterChange:(e,t)=>{he({sorter:e,sorterStates:t},"sort",!1)},sortDirections:O||["ascend","descend"],tableLocale:X,showSorterTooltip:D}),be=o.useMemo((()=>sr(U,ge,oe)),[U,ge]);pe.sorter=ye(),pe.sorterStates=ge;const[Ae,Ce,we]=qo({prefixCls:G,locale:X,dropdownPrefixCls:Y,mergedColumns:B,onFilterChange:(e,t)=>{he({filters:e,filterStates:t},"filter",!0)},getPopupContainer:C||q,rootClassName:N()(c,J)}),ke=Wo(be,Ce,oe);pe.filters=we,pe.filterStates=Ce;const Se=o.useMemo((()=>{const e={};return Object.keys(we).forEach((t=>{null!==we[t]&&(e[t]=we[t])})),Object.assign(Object.assign({},ve),{filters:e})}),[ve,we]),[Ee]=fr(Se),[Ne,$e]=Uo(ke.length,((e,t)=>{he({pagination:Object.assign(Object.assign({},pe.pagination),{current:e,pageSize:t})},"paginate")}),h);pe.pagination=!1===h?{}:function(e,t){const n={current:e.current,pageSize:e.pageSize},o=t&&"object"==typeof t?t:{};return Object.keys(o).forEach((t=>{const o=e[t];"function"!=typeof o&&(n[t]=o)})),n}(Ne,h),pe.resetPagination=$e;const Ke=o.useMemo((()=>{if(!1===h||!Ne.pageSize)return ke;const{current:e=1,total:t,pageSize:n=Xo}=Ne;return ke.length<t?ke.length>n?ke.slice((e-1)*n,e*n):ke:ke.slice((e-1)*n,e*n)}),[!!h,ke,Ne&&Ne.current,Ne&&Ne.pageSize,Ne&&Ne.total]),[Oe,Re]=((e,t)=>{const{preserveSelectedRowKeys:n,selectedRowKeys:r,defaultSelectedRowKeys:l,getCheckboxProps:a,onChange:i,onSelect:c,onSelectAll:d,onSelectInvert:s,onSelectNone:u,onSelectMultiple:f,columnWidth:p,type:h,selections:m,fixed:g,renderCell:v,hideSelectAll:y,checkStrictly:b=!0}=t||{},{prefixCls:x,data:A,pageData:C,getRecordByKey:w,getRowKey:k,expandType:S,childrenColumnName:E,locale:$,getPopupContainer:K}=e,I=(0,Lt.rJ)("Table"),[O,R]=function(e){const[t,n]=(0,o.useState)(null);return[(0,o.useCallback)(((e,o,r)=>{const l=null!=t?t:e,a=Math.min(l||0,e),i=Math.max(l||0,e),c=o.slice(a,i+1).map((e=>e)),d=c.some((e=>!r.has(e))),s=[];return c.forEach((e=>{d?(r.has(e)||s.push(e),r.add(e)):(r.delete(e),s.push(e))})),n(d?i:null),s}),[t]),e=>{n(e)}]}(),[D,P]=(0,Ht.A)(r||l||Ut,{value:r}),T=o.useRef(new Map),M=(0,o.useCallback)((e=>{if(n){const t=new Map;e.forEach((e=>{let n=w(e);!n&&T.current.has(e)&&(n=T.current.get(e)),t.set(e,n)})),T.current=t}}),[w,n]);o.useEffect((()=>{M(D)}),[D]);const{keyEntities:z}=(0,o.useMemo)((()=>{if(b)return{keyEntities:null};let e=A;if(n){const t=new Set(A.map(((e,t)=>k(e,t)))),n=Array.from(T.current).reduce(((e,n)=>{let[o,r]=n;return t.has(o)?e:e.concat(r)}),[]);e=[].concat((0,se.A)(e),(0,se.A)(n))}return yt(e,{externalGetKey:k,childrenPropName:E})}),[A,k,b,E,n]),B=(0,o.useMemo)((()=>Gt(E,C)),[E,C]),H=(0,o.useMemo)((()=>{const e=new Map;return B.forEach(((t,n)=>{const o=k(t,n),r=(a?a(t):null)||{};e.set(o,r)})),e}),[B,k,a]),L=(0,o.useCallback)((e=>{var t;return!!(null===(t=H.get(k(e)))||void 0===t?void 0:t.disabled)}),[H,k]),[j,F]=(0,o.useMemo)((()=>{if(b)return[D||[],[]];const{checkedKeys:e,halfCheckedKeys:t}=Bt(D,!0,z,L);return[e||[],t]}),[D,b,z,L]),W=(0,o.useMemo)((()=>{const e="radio"===h?j.slice(0,1):j;return new Set(e)}),[j,h]),_=(0,o.useMemo)((()=>"radio"===h?new Set:new Set(F)),[F,h]);o.useEffect((()=>{t||P(Ut)}),[!!t]);const q=(0,o.useCallback)(((e,t)=>{let o,r;M(e),n?(o=e,r=e.map((e=>T.current.get(e)))):(o=[],r=[],e.forEach((e=>{const t=w(e);void 0!==t&&(o.push(e),r.push(t))}))),P(o),null==i||i(o,r,{type:t})}),[P,w,i,n]),V=(0,o.useCallback)(((e,t,n,o)=>{if(c){const r=n.map((e=>w(e)));c(w(e),t,r,o)}q(n,"single")}),[c,w,q]),X=(0,o.useMemo)((()=>!m||y?null:(!0===m?[qt,Vt,Xt]:m).map((e=>e===qt?{key:"all",text:$.selectionAll,onSelect(){q(A.map(((e,t)=>k(e,t))).filter((e=>{const t=H.get(e);return!(null==t?void 0:t.disabled)||W.has(e)})),"all")}}:e===Vt?{key:"invert",text:$.selectInvert,onSelect(){const e=new Set(W);C.forEach(((t,n)=>{const o=k(t,n),r=H.get(o);(null==r?void 0:r.disabled)||(e.has(o)?e.delete(o):e.add(o))}));const t=Array.from(e);s&&(I.deprecated(!1,"onSelectInvert","onChange"),s(t)),q(t,"invert")}}:e===Xt?{key:"none",text:$.selectNone,onSelect(){null==u||u(),q(Array.from(W).filter((e=>{const t=H.get(e);return null==t?void 0:t.disabled})),"none")}}:e)).map((e=>Object.assign(Object.assign({},e),{onSelect:function(){for(var t,n,o=arguments.length,r=new Array(o),l=0;l<o;l++)r[l]=arguments[l];null===(n=e.onSelect)||void 0===n||(t=n).call.apply(t,[e].concat(r)),R(null)}})))),[m,W,C,k,s,q]),U=(0,o.useCallback)((e=>{var n;if(!t)return e.filter((e=>e!==_t));let r=(0,se.A)(e);const l=new Set(W),a=B.map(k).filter((e=>!H.get(e).disabled)),i=a.every((e=>l.has(e))),c=a.some((e=>l.has(e))),s=()=>{const e=[];i?a.forEach((t=>{l.delete(t),e.push(t)})):a.forEach((t=>{l.has(t)||(l.add(t),e.push(t))}));const t=Array.from(l);null==d||d(!i,t.map((e=>w(e))),e.map((e=>w(e)))),q(t,"all"),R(null)};let u,A,C;if("radio"!==h){let e;if(X){const t={getPopupContainer:K,items:X.map(((e,t)=>{const{key:n,text:o,onSelect:r}=e;return{key:null!=n?n:t,onClick:()=>{null==r||r(a)},label:o}}))};e=o.createElement("div",{className:`${x}-selection-extra`},o.createElement(Ft.A,{menu:t,getPopupContainer:K},o.createElement("span",null,o.createElement(tt.A,null))))}const t=B.map(((e,t)=>{const n=k(e,t),o=H.get(n)||{};return Object.assign({checked:l.has(n)},o)})).filter((e=>{let{disabled:t}=e;return t})),n=!!t.length&&t.length===B.length,r=n&&t.every((e=>{let{checked:t}=e;return t})),d=n&&t.some((e=>{let{checked:t}=e;return t}));A=o.createElement(jt.A,{checked:n?r:!!B.length&&i,indeterminate:n?!r&&d:!i&&c,onChange:s,disabled:0===B.length||n,"aria-label":e?"Custom selection":"Select all",skipGroup:!0}),u=!y&&o.createElement("div",{className:`${x}-selection`},A,e)}if(C="radio"===h?(e,t,n)=>{const r=k(t,n),a=l.has(r);return{node:o.createElement(Wt.Ay,Object.assign({},H.get(r),{checked:a,onClick:e=>e.stopPropagation(),onChange:e=>{l.has(r)||V(r,!0,[r],e.nativeEvent)}})),checked:a}}:(e,t,n)=>{var r;const i=k(t,n),c=l.has(i),d=_.has(i),s=H.get(i);let u;return u="nest"===S?d:null!==(r=null==s?void 0:s.indeterminate)&&void 0!==r?r:d,{node:o.createElement(jt.A,Object.assign({},s,{indeterminate:u,checked:c,skipGroup:!0,onClick:e=>e.stopPropagation(),onChange:e=>{let{nativeEvent:t}=e;const{shiftKey:n}=t,o=a.findIndex((e=>e===i)),r=j.some((e=>a.includes(e)));if(n&&b&&r){const e=O(o,a,l),t=Array.from(l);null==f||f(!c,t.map((e=>w(e))),e.map((e=>w(e)))),q(t,"multiple")}else{const e=j;if(b){const n=c?Nt(e,i):$t(e,i);V(i,!c,n,t)}else{const n=Bt([].concat((0,se.A)(e),[i]),!0,z,L),{checkedKeys:o,halfCheckedKeys:r}=n;let l=o;if(c){const e=new Set(o);e.delete(i),l=Bt(Array.from(e),{checked:!1,halfCheckedKeys:r},z,L).checkedKeys}V(i,!c,l,t)}}R(c?null:o)}})),checked:c}},!r.includes(_t))if(0===r.findIndex((e=>{var t;return"EXPAND_COLUMN"===(null===(t=e[ie])||void 0===t?void 0:t.columnType)}))){const[e,...t]=r;r=[e,_t].concat((0,se.A)(t))}else r=[_t].concat((0,se.A)(r));const E=r.indexOf(_t);r=r.filter(((e,t)=>e!==_t||t===E));const $=r[E-1],I=r[E+1];let D=g;void 0===D&&(void 0!==(null==I?void 0:I.fixed)?D=I.fixed:void 0!==(null==$?void 0:$.fixed)&&(D=$.fixed)),D&&$&&"EXPAND_COLUMN"===(null===(n=$[ie])||void 0===n?void 0:n.columnType)&&void 0===$.fixed&&($.fixed=D);const P=N()(`${x}-selection-col`,{[`${x}-selection-col-with-dropdown`]:m&&"checkbox"===h}),T={fixed:D,width:p,className:`${x}-selection-column`,title:(null==t?void 0:t.columnTitle)?"function"==typeof t.columnTitle?t.columnTitle(A):t.columnTitle:u,render:(e,t,n)=>{const{node:o,checked:r}=C(e,t,n);return v?v(r,t,n,o):o},onCell:t.onCell,[ie]:{className:P}};return r.map((e=>e===_t?T:e))}),[k,B,t,j,W,_,p,X,S,H,f,V,L]);return[U,W]})({prefixCls:G,data:ke,pageData:Ke,getRowKey:ue,getRecordByKey:fe,expandType:re,childrenColumnName:oe,locale:X,getPopupContainer:C||q},m);ne.__PARENT_RENDER_ICON__=ne.expandIcon,ne.expandIcon=ne.expandIcon||k||function(e){return function(t){let{prefixCls:n,onExpand:r,record:l,expanded:a,expandable:i}=t;const c=`${n}-row-expand-icon`;return o.createElement("button",{type:"button",onClick:e=>{r(l,e),e.stopPropagation()},className:N()(c,{[`${c}-spaced`]:!i,[`${c}-expanded`]:i&&a,[`${c}-collapsed`]:i&&!a}),"aria-label":a?e.collapse:e.expand,"aria-expanded":a})}}(X),"nest"===re&&void 0===ne.expandIconColumnIndex?ne.expandIconColumnIndex=m?1:0:ne.expandIconColumnIndex>0&&m&&(ne.expandIconColumnIndex-=1),"number"!=typeof ne.indentSize&&(ne.indentSize="number"==typeof K?K:15);const De=o.useCallback((e=>Ee(Oe(Ae(me(e))))),[me,Ae,Oe]);let Pe,Te,Me;if(!1!==h&&(null==Ne?void 0:Ne.total)){let e;e=Ne.size?Ne.size:"small"===V||"middle"===V?"small":void 0;const t=t=>o.createElement(on.A,Object.assign({},Ne,{className:N()(`${G}-pagination ${G}-pagination-${t}`,Ne.className),size:e})),n="rtl"===j?"left":"right",{position:r}=Ne;if(null!==r&&Array.isArray(r)){const e=r.find((e=>e.includes("top"))),o=r.find((e=>e.includes("bottom"))),l=r.every((e=>"none"==`${e}`));e||o||l||(Te=t(n)),e&&(Pe=t(e.toLowerCase().replace("top",""))),o&&(Te=t(o.toLowerCase().replace("bottom","")))}else Te=t(n)}"boolean"==typeof w?Me={spinning:w}:"object"==typeof w&&(Me=Object.assign({spinning:!0},w));const ze=N()(te,J,`${G}-wrapper`,null==F?void 0:F.className,{[`${G}-wrapper-rtl`]:"rtl"===j},i,c,ee),Be=Object.assign(Object.assign({},null==F?void 0:F.style),d),He=R&&R.emptyText||(null==W?void 0:W("Table"))||o.createElement(Jt.A,{componentName:"Table"}),Le=P?hr:pr,je={},Fe=o.useMemo((()=>{const{fontSize:e,lineHeight:t,padding:n,paddingXS:o,paddingSM:r}=Q,l=Math.floor(e*t);switch(V){case"large":return 2*n+l;case"small":return 2*o+l;default:return 2*r+l}}),[Q,V]);return P&&(je.listItemHeight=Fe),Z(o.createElement("div",{ref:ce,className:ze,style:Be},o.createElement(rn.A,Object.assign({spinning:!1},Me),Pe,o.createElement(Le,Object.assign({},je,H,{ref:de,columns:B,direction:j,expandable:ne,prefixCls:G,className:N()({[`${G}-middle`]:"middle"===V,[`${G}-small`]:"small"===V,[`${G}-bordered`]:u,[`${G}-empty`]:0===U.length},te,J,ee),data:Ke,rowKey:ue,rowClassName:(e,t,n)=>{let o;return o="function"==typeof v?N()(v(e,t,n)):N()(v),N()({[`${G}-row-selected`]:Re.has(ue(e,t))},o)},emptyText:He,internalHooks:a,internalRefs:le,transformColumns:De,getContainerWidth:ae})),Te)))},Mr=o.forwardRef(Tr),zr=(e,t)=>{const n=o.useRef(0);return n.current+=1,o.createElement(Mr,Object.assign({},e,{ref:t,_renderTimes:n.current}))},Br=o.forwardRef(zr);Br.SELECTION_COLUMN=_t,Br.EXPAND_COLUMN=l,Br.SELECTION_ALL=qt,Br.SELECTION_INVERT=Vt,Br.SELECTION_NONE=Xt,Br.Column=function(e){return null},Br.ColumnGroup=function(e){return null},Br.Summary=q;const Hr=Br},16741:(e,t,n)=>{n.d(t,{A:()=>l});var o=n(75206),r=n.n(o);function l(e,t,n,o){var l=r().unstable_batchedUpdates?function(e){r().unstable_batchedUpdates(n,e)}:n;return null!=e&&e.addEventListener&&e.addEventListener(t,l,o),{remove:function(){null!=e&&e.removeEventListener&&e.removeEventListener(t,l,o)}}}},23797:(e,t,n)=>{function o(){return{width:document.documentElement.clientWidth,height:window.innerHeight||document.documentElement.clientHeight}}function r(e){var t=e.getBoundingClientRect(),n=document.documentElement;return{left:t.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:t.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}}n.d(t,{A3:()=>r,XV:()=>o})}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.19/e599a38a6652872cd0b2f0d134c11e13/502.lite.js.map
