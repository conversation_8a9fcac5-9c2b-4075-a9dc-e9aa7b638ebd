/**
 * Enhanced category filter fix for The Events Calendar
 * This script ensures that category filtering works correctly by:
 * 1. Adding missing tribe_events_cat- classes to events
 * 2. Fixing the filtering logic to work with the actual HTML structure
 */

(function() {
    'use strict';
    
    // Wait for DOM to be ready
    function ready(fn) {
        if (document.readyState !== 'loading') {
            fn();
        } else {
            document.addEventListener('DOMContentLoaded', fn);
        }
    }
    
    // Function to add missing category classes to events
    function addMissingCategoryClasses() {
        // Find all events that have cat_ classes but missing tribe_events_cat- classes
        const events = document.querySelectorAll('[class*="cat_"]');
        
        events.forEach(event => {
            const classes = Array.from(event.classList);
            const catClasses = classes.filter(cls => cls.startsWith('cat_'));
            
            catClasses.forEach(catClass => {
                // Convert cat_slug to tribe_events_cat-slug
                const slug = catClass.replace('cat_', '');
                const newClass = 'tribe_events_cat-' + slug;
                
                if (!event.classList.contains(newClass)) {
                    event.classList.add(newClass);
                    console.log('Added missing class:', newClass, 'to event:', event);
                }
            });
        });
    }
    
    // Enhanced filtering function that works with the actual HTML structure
    function enhancedCategoryFilter() {
        const picker = document.querySelector('.tec-events-category-color-filter');
        if (!picker) {
            console.log('Category picker not found');
            return;
        }
        
        const checkboxes = document.querySelectorAll('.tec-events-category-color-filter__checkbox');
        if (checkboxes.length === 0) {
            console.log('No category checkboxes found');
            return;
        }
        
        // Get selected categories
        function getSelectedCategories() {
            const selected = [];
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    const label = checkbox.closest('label');
                    if (label && label.dataset.category) {
                        selected.push(label.dataset.category);
                    }
                }
            });
            return selected;
        }
        
        // Apply filtering
        function applyFiltering() {
            const selectedCategories = getSelectedCategories();
            console.log('Selected categories:', selectedCategories);
            
            // Find all events with category classes
            const allEvents = document.querySelectorAll('[class*="tribe_events_cat-"], [class*="cat_"]');
            console.log('Found events:', allEvents.length);
            
            allEvents.forEach(event => {
                // Get event categories from both class formats
                const eventCategories = [];
                const classes = Array.from(event.classList);
                
                // Extract from tribe_events_cat- classes
                classes.forEach(cls => {
                    if (cls.startsWith('tribe_events_cat-')) {
                        eventCategories.push(cls.replace('tribe_events_cat-', ''));
                    } else if (cls.startsWith('cat_')) {
                        eventCategories.push(cls.replace('cat_', ''));
                    }
                });
                
                // Determine if event should be visible
                let shouldBeVisible = true;
                if (selectedCategories.length > 0) {
                    shouldBeVisible = eventCategories.some(cat => selectedCategories.includes(cat));
                }
                
                // Apply or remove the hide class
                const hideClass = 'tec-category-filtered-hide';
                if (shouldBeVisible) {
                    event.classList.remove(hideClass);
                } else {
                    event.classList.add(hideClass);
                }
                
                console.log('Event categories:', eventCategories, 'Should be visible:', shouldBeVisible);
            });
        }
        
        // Add event listeners to checkboxes
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', applyFiltering);
        });
        
        // Initial filtering
        applyFiltering();
        
        console.log('Enhanced category filter initialized');
    }
    
    // Function to override the original category picker if it exists
    function overrideOriginalCategoryPicker() {
        // Check if the original category picker exists
        if (window.tribe && window.tribe.events && window.tribe.events.categoryColors) {
            console.log('Original category picker found, enhancing...');
            
            // Store original function
            const originalPicker = window.tribe.events.categoryColors.categoryPicker;
            
            // Override with enhanced version
            window.tribe.events.categoryColors.categoryPicker = function() {
                // Call original if it exists
                if (typeof originalPicker === 'function') {
                    try {
                        originalPicker();
                    } catch (e) {
                        console.log('Original picker failed:', e);
                    }
                }
                
                // Add our enhancements
                setTimeout(() => {
                    addMissingCategoryClasses();
                    enhancedCategoryFilter();
                }, 100);
                
                return {
                    init: function() {
                        addMissingCategoryClasses();
                        enhancedCategoryFilter();
                    }
                };
            };
        }
    }
    
    // Main initialization
    ready(function() {
        console.log('Category filter fix initializing...');
        
        // Add missing classes first
        addMissingCategoryClasses();
        
        // Override original picker
        overrideOriginalCategoryPicker();
        
        // Initialize enhanced filter
        enhancedCategoryFilter();
        
        // Re-run when new content is loaded (for AJAX)
        const observer = new MutationObserver(function(mutations) {
            let shouldRerun = false;
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1 && (
                            node.classList.contains('tribe-events-calendar-month__calendar-event') ||
                            node.querySelector && node.querySelector('[class*="tribe-events-calendar-month__calendar-event"]')
                        )) {
                            shouldRerun = true;
                        }
                    });
                }
            });
            
            if (shouldRerun) {
                setTimeout(() => {
                    addMissingCategoryClasses();
                    enhancedCategoryFilter();
                }, 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('Category filter fix initialized');
    });
})();
