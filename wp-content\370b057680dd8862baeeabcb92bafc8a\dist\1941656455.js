"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[4],{93:(e,t,n)=>{n.r(t),n.d(t,{apply:()=>k});var a=n(2834),o=n(6399),i=n(1714),r=n(9408);const s="Google Tag Manager",l="Matomo Tag Manager",c="gtm",d="mtm";function u(e,t){let n,a,o,{presetId:i,isGcm:r}=t,u=!1,g="";const p={events:!0,executeCodeWhenNoTagManagerConsentIsGiven:!0};let h=e||"none";switch("googleTagManagerWithGcm"!==h||r||(h="googleTagManager"),h){case"googleTagManager":case"googleTagManagerWithGcm":o=c,n="dataLayer",g=s,p.events="googleTagManagerWithGcm"!==h;break;case"matomoTagManager":o=d,n="_mtm",g=l;break;default:p.events=!1,p.executeCodeWhenNoTagManagerConsentIsGiven=!1}return n&&(a=()=>(window[n]=window[n]||[],window[n])),o&&i===o&&(u=!0,p.events=!1,p.executeCodeWhenNoTagManagerConsentIsGiven=!1),{getDataLayer:a,useManager:h,serviceIsManager:u,managerLabel:g,expectedManagerPresetId:o,features:p}}function g(e){let t,{decisionCookieName:n,setCookiesViaManager:a,isGcm:o,groups:i,type:s}=e;const{useManager:l}=u(a,{isGcm:o,presetId:""}),c=i.find((e=>{let{isEssential:t}=e;return t})),d={[c.id]:c.items.map((e=>{let{id:t}=e;return t}))};if("consent"===s){const e=(0,r.y)(n);!1!==e?t=e.consent:(console.warn("Something went wrong while reading the cookie, fallback to essentials only..."),t=d)}return"essentials"===s&&(t=d),{isManagerActive:"none"!==l,selectedGroups:t,iterateServices:async function(e){const n=i.map((e=>e.items.map((t=>[e,t])))).flat();n.sort(((e,t)=>e[1].executePriority-t[1].executePriority));for(const[i,l]of n){var r;const n="all"===s||(null==(r=t[i.id])?void 0:r.indexOf(l.id))>-1,c=u(a,{presetId:l.presetId,isGcm:o});await e(i,l,n,c)}}}}var p=n(7400),h=n(7177),m=n(4766),v=n(729),C=n(6145);var f=n(72),y=n(9179),b=n(6336);async function k(e){const t=[];await g(e).iterateServices((async(e,n,a)=>{a&&t.push({group:e,service:n})})),document.dispatchEvent(new CustomEvent(f.r,{detail:{services:t,triggeredByOtherTab:e.triggeredByOtherTab}})),await(0,a.G)();const{dataLayer:n,isManagerOptOut:r,services:s,ready:l}=await async function(e){const t=[],{isManagerActive:n,iterateServices:a}=g(e),{skipOptIn:o}=e;const r=[];return await a((async(e,a,s,l)=>{let{getDataLayer:c,serviceIsManager:d}=l;const{codeDynamics:u,codeOptIn:g,executeCodeOptInWhenNoTagManagerConsentIsGiven:h}=a;if(s){const s=n&&h,l="function"==typeof o&&o(a);s||l||!g||r.push((0,i.l)(g,u));const c={group:e,service:a};document.dispatchEvent(new CustomEvent(p.D,{detail:c})),t.push(c)}})),{isManagerOptOut:!1,dataLayer:undefined,services:t,ready:Promise.all(r)}}(e),{ready:c}=await async function(e,t,n){const a=[],{isManagerActive:o,iterateServices:r}=g(e);return t?(r(((e,n,a,o)=>{let{tagManagerOptInEventName:i}=n,{features:r}=o;a&&i&&r.events&&t.push({event:i})})),setTimeout((()=>r(((e,n,a,o)=>{let{tagManagerOptOutEventName:i}=n,{features:r}=o;!a&&i&&r.events&&t.push({event:i})}))),1e3)):o&&n&&await r((async(e,t,n)=>{let{codeDynamics:o,codeOptIn:r,executeCodeOptInWhenNoTagManagerConsentIsGiven:s}=t;n&&s&&a.push((0,i.l)(r,o))})),{ready:Promise.all(a)}}(e,n,r),d=Promise.all([l,c]);await(0,o.P)(),document.dispatchEvent(new CustomEvent(y.T,{detail:{services:s,ready:d}}));const{deleteHttpCookies:u,services:k,ready:D}=await async function(e,t){const n=[],{isManagerActive:a,iterateServices:o}=g(e),r=[],s=[];return await o((async(e,o,l)=>{const{id:c,codeDynamics:d,codeOptOut:u,deleteTechnicalDefinitionsAfterOptOut:g,isEmbeddingOnlyExternalResources:p,technicalDefinitions:f,executeCodeOptOutWhenNoTagManagerConsentIsGiven:y}=o;if(!l){const l=a&&y;(l&&t||!l)&&r.push((0,i.l)(u,d)),g&&!p&&(function(e,t){for(const{type:n,name:a}of e){const e=(0,m.t)(a,t);if("*"===e)continue;const o=new RegExp((0,v.Z)(e),"g");switch(n){case"http":for(const e of Object.keys(h.A.get()))o.test(e)&&h.A.remove(e);break;case"local":case"session":try{const e="local"===n?window.localStorage:window.sessionStorage;if(e)for(const t of Object.keys(e))if(o.test(t)){try{e.setItem(t,null)}catch(e){}let n=0;for(;e.getItem(t)&&n<100;)n++,e.removeItem(t)}}catch(e){continue}}}}(f,d),f.some((e=>{let{type:t}=e;return"http"===t}))&&s.push(c));const b={group:e,service:o};document.dispatchEvent(new CustomEvent(C.G,{detail:b})),n.push(b)}})),{services:n,ready:Promise.all(r),deleteHttpCookies:s}}(e,r);document.dispatchEvent(new CustomEvent(b.a,{detail:{services:k,deleteHttpCookies:u,ready:Promise.all([d,D])}}))}},6264:(e,t,n)=>{async function a(e,t){e.createdClientTime=(new Date).toISOString();const a=t.getConsentQueue();a.push(e),t.setConsentQueue(a);try{await t.getOption("persistConsent")(e,!0),t.setConsentQueue(t.getConsentQueue().filter((t=>{let{createdClientTime:n}=t;return e.createdClientTime!==n})))}catch(a){const{groups:o,decisionCookieName:i,tcfCookieName:r,gcmCookieName:s,failedConsentDocumentationHandling:l,revisionHash:c}=t.getOptions(),d="optimistic"===l,{decision:u,createdClientTime:g,tcfString:p,gcmConsent:h,buttonClicked:m}=e,v={consent:d?"all"===u?o.reduce(((e,t)=>(e[t.id]=t.items.map((e=>{let{id:t}=e;return t})),e)),{}):"essentials"===u?(0,n(5974).w)(o,!1):u:(0,n(5974).w)(o,!1),previousUuids:[],revision:c,uuid:g,created:new Date(g),buttonClicked:m};localStorage.setItem(i,JSON.stringify(v)),p&&localStorage.setItem(r,d?p:""),h&&localStorage.setItem(s,d?JSON.stringify(h):"[]")}}n.d(t,{persistWithQueueFallback:()=>a})},7724:(e,t,n)=>{function a(e,t){void 0===t&&(t=!1);const{decisionCookieName:o,tcfCookieName:i,gcmCookieName:r}=e.getOptions(),s=()=>{localStorage.removeItem(o),localStorage.removeItem(i),localStorage.removeItem(r),localStorage.removeItem(e.getConsentQueueName())},l=document.querySelector('a[href*="rcb-clear-current-cookie=1"]');if(null==l||l.addEventListener("click",s),e.isConsentQueueLocked()){const t=t=>{t.key!==e.getConsentQueueName(!0)||t.newValue||a(e)};return window.addEventListener("storage",t),()=>{window.removeEventListener("storage",t),null==l||l.removeEventListener("click",s)}}{let a,i=0;const r=async()=>{e.isConsentQueueLocked(!0);const t=e.getConsentQueue();let l=15e3;if(t.length>0){i++;try{const a=t.shift(),r=0===t.length||!n(7177).A.get(o),c=await e.getOption("persistConsent")(a,r),d=n(7177).A.get(o);d&&-1===d.indexOf(c)&&n(7177).A.set(o,d.replace(/^(.*?:.*?):/gm,`$1,${c}:`)),e.setConsentQueue(t),0===t.length&&s(),i=0,l=1500}catch(e){l=15*i*1e3}}a=setTimeout(r,l)};return e.isConsentQueueLocked(!0),a=setTimeout(r,t?0:15e3),()=>{e.isConsentQueueLocked(!1),clearTimeout(a),null==l||l.removeEventListener("click",s)}}}n.d(t,{retryPersistFromQueue:()=>a})},9558:(e,t,n)=>{n.r(t),n.d(t,{BannerHistorySelect:()=>a});const a=()=>{const e=(0,n(4094).Y)(),{Select:t}=(0,n(680).y)().extend(...n(5746).I),{set:a,consent:o,groups:i,tcf:r,isGcm:s,gcmConsent:l,lazyLoadedDataForSecondView:c,activeAction:d,history:u,fetchHistory:g,visible:p,i18n:{historyLabel:h,historyItemLoadError:m,historySelectNone:v}}=e,[C,f]=(0,n(7936).J0)(),[y,b]=(0,n(7936).J0)({consent:o,groups:i,tcf:r,gcmConsent:l,lazyLoadedDataForSecondView:c}),k=e=>{let{buttonClicked:t,tcf:n,gcmConsent:o,...i}=e;a({...i,isTcf:!!n,tcf:null,gcmConsent:[]})};(0,n(7936).vJ)((()=>{const e={consent:[],groups:[],gcmConsent:[],lazyLoadedDataForSecondView:void 0};if(C){const{context:t}=C;k(t||e)}else k(e)}),[C]);const D=(0,n(7936).li)(!1);(0,n(7936).vJ)((()=>{c&&!D.current&&"history"===d&&p&&(D.current=!0,async function(){const e=await g();b({consent:o,groups:i,tcf:r,gcmConsent:l,lazyLoadedDataForSecondView:c}),a({history:e}),f(e[0])}())}),[c,d,p]),(0,n(7936).vJ)((()=>{p||(D.current=!1)}),[p]),(0,n(7936).vJ)((()=>()=>k(y)),[]);const S=null==C?void 0:C.uuid;return(0,n(6425).FD)(n(7936).FK,{children:[h," ",(0,n(6425).Y)(t,{disabled:!(null==u?void 0:u.length),value:(null==C?void 0:C.id)||-1,onChange:e=>{const t=+e.target.value;for(const e of u){const{id:n}=e;if(n===t){f(e);break}}},children:(null==u?void 0:u.length)>0?u.map((e=>{let{id:t,isDoNotTrack:a,isUnblock:o,isForwarded:i,created:r}=e;return(0,n(6425).FD)("option",{value:t,children:[new Date(r).toLocaleString(document.documentElement.lang),a?" (Do Not Track)":"",o?" (Content Blocker)":"",i?" (Consent Forwarding)":""]},t)})):(0,n(6425).Y)("option",{value:-1,children:v})}),(0,n(6425).FD)("div",{style:{opacity:.5,marginTop:5},children:["UUID: ",S||"-"]}),!(null==C?void 0:C.context)&&(0,n(6425).Y)("div",{style:{fontWeight:"bold",marginTop:5},children:m})]})}},3353:(e,t,n)=>{n.r(t),n.d(t,{BannerGroupList:()=>v});var a=n(6425),o=n(7936),i=n(4094),r=n(9694);const s=e=>{let{group:{id:t,isEssential:n},cookie:s}=e;const{id:l}=s,c=(0,i.Y)(),{consent:d,activeAction:u}=c,g=n||"history"===u,p=n||((null==d?void 0:d[t])||[]).some((e=>e===l)),h=(0,o.hb)((e=>c.updateCookieChecked(t,l,e)),[c,t,l]);return(0,a.Y)(r.Cookie,{cookie:s,propertyListProps:{isEssentialGroup:n},checked:p,disabled:g,onToggle:h})};var l=n(1801),c=n(4959),d=n(5548),u=n(180);const g=e=>{let{group:t}=e;const n=(0,i.Y)(),{name:o,description:r,items:g}=t,{group:{headlineFontSize:p},individualTexts:{headline:h,showMore:m,hideMore:v}}=n,C=(0,l.C)(t);return(0,a.FD)(d.Y,{legend:`${h}: ${o}`,headline:(0,a.FD)(c.S,{...C,fontSize:p,children:[o," (",g.length,")"]}),children:[(0,a.Y)("span",{children:r}),!!g&&(0,a.Y)(u.X,{showMore:m,hideMore:v,bullets:!0,groupLabel:o,children:g.map((e=>(0,a.Y)(s,{group:t,cookie:e},e.id)))})]})};var p=n(5453),h=n(8700);const m=e=>{let{children:t}=e;const{GroupList:n}=(0,h.o)().extend(...p.C);return(0,a.Y)(n,{children:t})},v=()=>{const{groups:e}=(0,i.Y)(),t=e.filter((e=>{let{items:t}=e;return t.length}));return(0,a.Y)(m,{children:t.map((e=>(0,a.Y)(g,{group:e},e.id)))})}},3362:(e,t,n)=>{n.r(t),n.d(t,{BannerSticky:()=>a});const a=()=>null},9694:(e,t,n)=>{n.r(t),n.d(t,{Cookie:()=>T});var a=n(6425),o=n(5453),i=n(7936);const r=/(\r\n|\r|\n|<br[ ]?\/>)/g;var s=n(4200),l=n(5746),c=n(8700),d=n(5922);const u=e=>{let{label:t,value:n,children:r,printValueAs:u,monospace:g}=e;const p=(0,c.o)(),{Link:h,CookieProperty:m}=p.extend(...l.I).extend(...o.C),v=(0,d.b)(),{i18n:{yes:C,no:f}}=v;let y="string"==typeof n&&n.startsWith("http")&&(0,s.g)(n)?(0,a.Y)(h,{href:n,target:"_blank",rel:"noopener noreferrer",children:n}):"string"==typeof n?"phone"===u?(0,a.Y)(h,{target:"_blank",href:`tel:${n.replace(/\s+/g,"")}`,children:n}):"mail"===u?(0,a.Y)(h,{target:"_blank",href:`mailto:${n}`,children:n}):(0,a.Y)("span",{dangerouslySetInnerHTML:{__html:n}}):n;return"boolean"===u&&(y=y?C:f),n||!1===n||"empty"===u?(0,a.FD)(i.FK,{children:[(0,a.FD)(m,{children:[t&&(0,a.FD)("strong",{children:[t,": "]}),(0,a.Y)("span",{role:"presentation",style:{fontFamily:g?"monospace":void 0},children:y})]}),(0,a.Y)(m,{children:!!r&&(0,a.Y)("div",{children:r})})]}):null};var g=n(5360),p=n(1477),h=n(1917);const m=e=>{let{mechanisms:t,...n}=e;const{screenReaderOnlyClass:o}=(0,c.o)(),r=(0,p.JY)(o),{iso3166OneAlpha2:s,predefinedDataProcessingInSafeCountriesLists:l,territorialLegalBasis:m,isDataProcessingInUnsafeCountries:v,i18n:{dataProcessingInThirdCountries:C,territorialLegalBasisArticles:{"dsg-switzerland":{dataProcessingInUnsafeCountries:f},"gdpr-eprivacy":{dataProcessingInUnsafeCountries:y}},safetyMechanisms:{label:b,eu:k,switzerland:D,adequacyDecision:S,contractualGuaranteeSccSubprocessors:w,standardContractualClauses:I,bindingCorporateRules:Y}}}=(0,d.b)(),{result:T,filter:L,isGdpr:P}=(0,h.F)({predefinedDataProcessingInSafeCountriesLists:l,territorialLegalBasis:m,isDataProcessingInUnsafeCountries:v,service:n}),M=Object.entries(T),O={A:S,"A-EU":`${S} (${k})`,"A-CH":`${S} (${D})`,B:I,C:w,D:m.length>1?"":P?y:f,"D-EU":y,"D-CH":f,E:Y},F=Object.keys(O).filter((e=>L((t=>t===e)).length>0)),x=t?t(F):F;return(0,a.FD)(i.FK,{children:[M.length>0&&(0,a.Y)(u,{label:C,value:(0,g.i)(M.map((e=>{let[t,n]=e;return(0,a.Y)("span",{dangerouslySetInnerHTML:{__html:r(n.map((e=>[e,O[e]])),s[t]??t)}},t)})),", ")}),x.length>0&&(0,a.Y)(u,{label:b,value:(0,g.i)(x.map((e=>(0,a.Y)("span",{dangerouslySetInnerHTML:{__html:O[e]?r([[e]],O[e]):e}},e))),", ")})]})};var v=n(180);const C=e=>{let{expandable:t,children:n,labelModifications:o={},groupLabel:r}=e;const{group:{detailsHideLessRelevant:s},i18n:{andSeparator:l,showLessRelevantDetails:c,hideLessRelevantDetails:g}}=(0,d.b)(),h=(0,i.li)(null),[m,C]=(0,i.J0)("");(0,i.vJ)((()=>{const{current:e}=h;if(e){const t=[...new Set([...e.querySelectorAll(":scope>div>strong")].map((e=>{const t=e.innerText.replace(/:?\s+$/,"");return o[t]||t})))];C((0,p.$D)(t,l))}}),[h.current,o]);const f=(0,i.hb)((e=>e.replace("%s",m)),[m]);return s&&t?(0,a.Y)("div",{"aria-hidden":!m,hidden:!m,children:(0,a.Y)(u,{value:(0,a.FD)(i.FK,{children:[(0,a.Y)("br",{}),(0,a.Y)(v.X,{showMore:f(c),hideMore:f(g),style:{fontStyle:"italic"},forceRender:!0,groupLabel:r,children:(0,a.FD)("div",{ref:h,children:[(0,a.Y)("br",{}),n]})})]})})}):n};var f=n(4766);const y=e=>{let{definitions:t,codeDynamics:n}=e;const{i18n:o}=(0,d.b)(),r=function(){const{i18n:{durationUnit:e}}=(0,d.b)();return(0,i.hb)(((t,n)=>(0,p.BP)(t,e.n1[n],e.nx[n])),[e])}(),s={http:{name:"HTTP Cookie",abbr:"HTTP",backgroundColor:"black"},local:{name:"Local Storage",abbr:"Local",backgroundColor:"#b3983c"},session:{name:"Session Storage",abbr:"Session",backgroundColor:"#3c99b3"},indexedDb:{name:"IndexedDB",abbr:"I-DB",backgroundColor:"#4ab33c"}};return null==t?void 0:t.map((e=>{let{children:t,type:i,name:l,host:c,duration:d,durationUnit:g,isSessionDuration:p,purpose:h}=e;var m;return(0,a.FD)(u,{label:o.technicalCookieName,monospace:!0,value:(0,f.t)(l,n),children:[(0,a.Y)(u,{label:o.type,value:(null==(m=s[i])?void 0:m.name)||i}),!!c&&(0,a.Y)(u,{label:o.host,value:c,monospace:!0}),(0,a.Y)(u,{label:o.duration,value:["local","indexedDb"].indexOf(i)>-1?o.noExpiration:p||"session"===i?"Session":r(d,g)}),t,(0,a.Y)(u,{label:o.purpose,value:h})]},`${i}-${l}-${c}`)}))};var b=n(5285),k=n(4349),D=n(680),S=n(4959);const w=e=>{let{type:t,isDisabled:n,isBold:i}=e;const{Cookie:r}=(0,D.y)().extend(...o.C),{activeAction:s,gcmConsent:l,updateGcmConsentTypeChecked:c,group:{descriptionFontSize:u},i18n:{gcm:{purposes:{[t]:g}}}}=(0,d.b)();return(0,a.Y)(r,{children:(0,a.Y)(S.S,{isChecked:l.indexOf(t)>-1,isDisabled:n||"history"===s,fontSize:u,onToggle:e=>c(t,e),children:(0,a.Y)("span",{style:{fontWeight:i?"bold":void 0},children:g})})})},I=e=>{let{cookie:{name:t,purpose:n,isProviderCurrentWebsite:o,provider:s,providerContact:l={},providerPrivacyPolicyUrl:c,providerLegalNoticeUrl:g,legalBasis:p,dataProcessingInCountries:v,dataProcessingInCountriesSpecialTreatments:f,isEmbeddingOnlyExternalResources:D,technicalDefinitions:S,codeDynamics:I,googleConsentModeConsentTypes:Y},isEssentialGroup:T,isDisabled:L}=e;const{i18n:P,iso3166OneAlpha2:M,websiteOperator:O,isGcm:F,designVersion:x}=(0,d.b)(),{deprecated:E,legalBasis:N}=P,{dataProcessingInUnsafeCountries:A,appropriateSafeguards:U}=function(e){let{dataProcessingInCountries:t,specialTreatments:n,tcf:a={internationalTransfers:!1,transferMechanisms:[]}}=e;const{designVersion:o,i18n:{safetyMechanisms:r,other:s},isDataProcessingInUnsafeCountries:l,dataProcessingInUnsafeCountriesSafeCountries:c,iso3166OneAlpha2:u}=(0,d.b)(),{internationalTransfers:g,transferMechanisms:p}=a;return{dataProcessingInUnsafeCountries:(0,i.Kr)((()=>l?(0,h.z)({dataProcessingInCountries:t,safeCountries:c,specialTreatments:n,isDisplay:!0}).map((e=>u[e]||e)):[]),[l,c,n,t,u]),appropriateSafeguards:(0,i.Kr)((()=>[...new Set([n.indexOf(b.ak.StandardContractualClauses)>-1&&r.standardContractualClauses,o>6&&g&&p.map((e=>{switch(e){case"SCCs":return r.standardContractualClauses;case"Adequacy decision":return r.adequacyDecision;case"BCRs":return r.bindingCorporateRules;case"Other":return s;default:return""}}))].flat().filter(Boolean))]),[n,g,p])}}({dataProcessingInCountries:v,specialTreatments:f}),{legalNotice:G,privacyPolicy:B,contactForm:$}=(0,k.s)(),H=(0,i.Kr)((()=>{if(o&&O){const{address:e,country:t,contactEmail:n,contactPhone:a}=O;return{provider:[e,M[t]||t].filter(Boolean).join(", "),contact:{email:n,phone:a,link:$},legalNoticeUrl:!1===G?"":G.url,privacyPolicyUrl:!1===B?"":B.url}}return{provider:s,contact:l,privacyPolicyUrl:c,legalNoticeUrl:g}}),[o,s,l,c,g,O,G,B,$]),Q=(0,i.Kr)((()=>Object.values(H.contact).filter(Boolean).length>0),[H.contact]),R=(0,i.Kr)((()=>{const e="legal-requirement"===p,t="legitimate-interest"===p||T;if(x<=11)return e?E.legalRequirement:t?P.legitimateInterest:P.consent;{const{consentPersonalData:n,consentStorage:a,legitimateInterestPersonalData:o,legitimateInterestStorage:i,legalRequirementPersonalData:r}=N;return[e?r:t?o:n,!D&&(e||t?i:a)].filter(Boolean).join(", ")}}),[x,p,T,N,D]);return(0,a.FD)(i.FK,{children:[!!n&&(0,a.Y)(u,{label:P.purpose,value:(W=n,"string"==typeof W?W.split(r).map(((e,t)=>e.match(r)?(0,i.n)("br",{key:t}):e)):W)}),(0,a.Y)(u,{label:P.legalBasis.label,value:R}),F&&Y.length>0&&(0,a.Y)(u,{label:P.gcm.dataProcessingInService,printValueAs:"empty",children:(0,a.Y)("div",{style:{display:"inline-block"},children:(0,a.Y)(u,{printValueAs:"empty",children:Y.map((e=>(0,a.Y)(w,{type:e,isDisabled:L},e)))})})}),(0,a.Y)(u,{label:P.provider,value:H.provider,children:Q&&(0,a.FD)(i.FK,{children:[(0,a.Y)(u,{label:P.providerContactPhone,value:H.contact.phone,printValueAs:"phone"}),(0,a.Y)(u,{label:P.providerContactEmail,value:H.contact.email,printValueAs:"mail"}),(0,a.Y)(u,{label:P.providerContactLink,value:H.contact.link})]})}),(0,a.Y)(u,{label:P.providerPrivacyPolicyUrl,value:H.privacyPolicyUrl}),(0,a.Y)(u,{label:P.providerLegalNoticeUrl,value:H.legalNoticeUrl}),x<10&&A.length>0&&(0,a.Y)(u,{label:E.dataProcessingInUnsafeCountries,value:A.join(", ")}),x<10&&U.length>0&&(0,a.Y)(u,{label:E.appropriateSafeguard,value:U.join(", ")}),(0,a.FD)(C,{expandable:x>9,labelModifications:{[P.technicalCookieName]:P.technicalCookieDefinitions},groupLabel:t,children:[x>9&&(0,a.Y)(m,{dataProcessingInCountries:v,dataProcessingInCountriesSpecialTreatments:f}),!D&&(0,a.Y)(y,{codeDynamics:I,definitions:S})]})]});var W};var Y=n(7140);const T=e=>{let{cookie:t,checked:n,disabled:i,onToggle:r,propertyListProps:s={}}=e;const{Cookie:l}=(0,c.o)().extend(...o.C),{name:u}=t,{group:{descriptionFontSize:g}}=(0,d.b)(),p=(0,Y.p)();return(0,a.FD)(l,{children:[(0,a.Y)(S.S,{isChecked:n,isDisabled:i,fontSize:g,onToggle:r,"aria-describedby":p,children:(0,a.Y)("strong",{children:u})}),(0,a.Y)("div",{id:p,children:(0,a.Y)(I,{cookie:t,...s,isDisabled:!n})})]})}},180:(e,t,n)=>{n.d(t,{X:()=>a});const a=e=>{let{onToggle:t,children:a,showMore:o,hideMore:i,groupLabel:r,bullets:s,forceRender:l,...c}=e;const{Link:d}=(0,n(8700).o)().extend(...n(5746).I),[u,g]=(0,n(7936).J0)(!1),p=(0,n(7140).p)();return(0,n(6425).FD)(n(7936).FK,{children:[s&&(0,n(6425).Y)("span",{"aria-hidden":!0,children:"  •  "}),(0,n(6425).Y)(d,{href:"#",onClick:e=>{const n=!u;g(n),null==t||t(n),e.preventDefault()},onKeyDown:e=>{"space"===e.code.toLowerCase()&&e.target.click()},...r?{"aria-label":`${u?i:o}: ${r}`}:{},...a?{"aria-expanded":u,"aria-controls":p,role:"button"}:{},...c,children:u?i:o}),a&&(0,n(6425).Y)("div",{hidden:!u,id:p,children:(u||l)&&a})]})}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.19/96d2c46387a32bb10b0ba2069a6d16fe/banner-lite-banner-lazy.lite.js.map
