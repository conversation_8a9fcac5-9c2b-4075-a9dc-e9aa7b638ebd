"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[79],{17312:(e,t,n)=>{n.d(t,{r:()=>h});var s=n(3713),o=n(53029),a=n(96431),i=n(18197),r=n(6099),l=n(92453),c=n(36086),d=n(41594);const h=e=>{let{languages:t=[],recordId:n,onClick:h,children:u,wrapperProps:p={},...m}=e;const[g,y]=(0,d.useState)(!1),f=t.filter((e=>{let{id:t}=e;return!1!==t})),x=t.filter((e=>{let{id:t}=e;return!1===t})),b={size:13,shape:"square",style:{display:"block",width:"auto",borderRadius:5}},v={flex:"none",style:{textAlign:"center",paddingRight:5,fontSize:11,cursor:"pointer"}},j=(0,d.useCallback)((async e=>{if(!g){y(!0);try{h(n,e)}finally{y(!1)}}}),[g,h,n]);return(0,s.jsx)(i.A,{spinning:g,...p,children:(0,s.jsxs)(r.A,{align:"middle",...m,children:[f.map((e=>{const{code:t,flag:a,id:i}=e;return i===n?null:(0,s.jsxs)(l.A,{...v,onClick:()=>j(e),children:[(0,s.jsx)(c.A,{src:a,...b,children:t.toUpperCase()}),(0,s.jsx)(o.A,{})]},t)})),x.length>0&&x.map((e=>{const{code:t,flag:n}=e;return(0,s.jsxs)(l.A,{...v,onClick:()=>j(e),children:[(0,s.jsx)(c.A,{src:n,...b,children:t.toUpperCase()}),(0,s.jsx)(a.A,{})]},t)})),u]})})}},388:(e,t,n)=>{n.d(t,{O:()=>a});var s=n(3713),o=n(12719);const a=e=>{let{children:t,wrapperAttributes:n={},...a}=e;const{modal:i,tag:r}=(0,o.WH)(a);return(0,s.jsxs)(s.Fragment,{children:[i,(0,s.jsx)("span",{...n,children:r})]})}},60111:(e,t,n)=>{n.d(t,{E:()=>g});var s=n(3713),o=n(4509),a=n(57344),i=n(2986),r=n(22969),l=n(68959),c=n(57333),d=n(41594);let h,u;const p=()=>{const{setActivatorNodeRef:e,listeners:t}=(0,d.useContext)(h);return(0,s.jsx)(c.Ay,{type:"text",size:"small",icon:(0,s.jsx)(o.A,{}),style:{cursor:"move"},ref:e,...t})},m=e=>{const{elementType:t="tr"}=(0,d.useContext)(u),{attributes:n,listeners:o,setNodeRef:a,setActivatorNodeRef:i,transform:c,transition:p,isDragging:m}=(0,r.gl)({id:e["data-row-key"]}),g={...e.style,transform:l.Ks.Translate.toString(c),transition:p,...m?{position:"relative",zIndex:9999}:{}},y=(0,d.useMemo)((()=>({setActivatorNodeRef:i,listeners:o})),[i,o]),f={...e,ref:a,style:g,...n};return(0,s.jsx)(h.Provider,{value:y,children:"li"===t?(0,s.jsx)("li",{...f}):"div"===t?(0,s.jsx)("div",{...f}):(0,s.jsx)("tr",{...f})})};function g(){return h||(h=(0,d.createContext)({}),u=(0,d.createContext)({})),(0,d.useRef)({SortableContext:function(e){let{children:t,onDragEnd:n,items:o,...l}=e;return(0,s.jsx)(u.Provider,{value:l,children:(0,s.jsx)(a.Mp,{modifiers:[i.FN],onDragEnd:n,accessibility:{container:document.body},children:(0,s.jsx)(r.gB,{items:o,strategy:r._G,children:t})})})},SortableRow:m,DragHandle:p}).current}},3820:(e,t,n)=>{n.d(t,{S:()=>p});var s=n(3713),o=n(19117),a=n(91386),i=n(41594),r=n(66399),l=n(78915),c=n(55221),d=n(84255),h=n(59726);const u=e=>{let{when:t,title:n}=e;const{__:o}=(0,h.s)(),[a,r=""]=(0,i.useMemo)((()=>n.split(".").map((e=>`${e.trim()}.`))),[n]);return(0,s.jsx)(d.Ay,{when:t,children:e=>{let{isActive:t,onConfirm:n,onCancel:i}=e;return(0,s.jsx)(l.A,{open:t,onCancel:i,onOk:n,centered:!0,okText:o("Leave"),cancelText:o("Cancel"),children:(0,s.jsx)(c.Ay,{status:"warning",title:a,subTitle:r})})}})};function p(e){let{isEdit:t,defaultValues:n,template:l,entityTemplateVersion:c,attributes:d,handleSave:h,i18n:p,initialHasChanges:m,trackFieldsDifferFromDefaultValues:g=[],unloadPromptWhen:y}=e;const{message:f}=o.A.useApp(),x={...n,...d||{}},[b]=a.A.useForm(),[v,j]=(0,i.useState)(!1),w=(0,i.useRef)(m||!1),A=!(!t||!l)&&c!==l.version,k=!!l&&(A||!t),C=[],T=(0,i.useCallback)((async e=>{j(!0);try{const t=await h(e);f.success(p.successMessage),(0,r.P)().then((()=>{b.resetFields(),"function"==typeof t&&t()})),w.current=!1}catch(e){f.error(e)}finally{j(!1)}}),[b,h]),I=(0,i.useCallback)((()=>{f.error(p.validationError)}),[b,p]),B=(0,s.jsx)(u,{when:e=>w.current&&(!y||y(e)),title:m&&p.unloadConfirmInitialActive?p.unloadConfirmInitialActive:p.unloadConfirm}),P=(0,i.useCallback)(((e,t)=>{if(g&&x){C.splice(0,C.length);for(const e of g)t[e]!==x[e]&&C.push(e)}w.current=!0}),[g,x]);return{defaultValues:x,template:l,isEdit:t,isTemplateUpdate:A,templateCheck:k,form:b,isBusy:v,setIsBusy:j,hasTrackedFieldDifferenceToDefaultValue:e=>C.indexOf(e)>-1,onFinish:T,onFinishFailed:I,prompt:B,onValuesChange:P}}},69810:(e,t,n)=>{n.d(t,{j:()=>a});var s=n(41594),o=n(59726);function a(e){let{predefinedDataProcessingInSafeCountriesLists:t,iso3166OneAlpha2:n}=e;const{_x:a,__:i}=(0,o.s)(),r=(0,s.useCallback)((e=>{const s=t[e].map((e=>n[e])).join(", ");return"ADEQUACY_EU"===e?i("Countries with an adequacy decision in EU: %s",s):"ADEQUACY_CH"===e?i("Countries with an adequacy decision in Switzerland: %s",s):"GDPR"===e?i("Countries where GDPR applies: %s",s):i("Countries where %s applies",e)}),[i,t,n]);return(0,s.useCallback)((e=>{const s=[],o=[],l=[],c={ADEQUACY_CH:r("ADEQUACY_CH"),ADEQUACY_EU:r("ADEQUACY_EU"),GDPR:r("GDPR"),SAFE:""},d=[],h=e.indexOf("gdpr-eprivacy")>-1,u=e.indexOf("dsg-switzerland")>-1,p=a("GDPR (EU/EEA)","legal-text"),m=a("DSG (Switzerland)","legal-text"),g=a("GDPR","legal-text"),y=a("Art. 49 (1) (a) GDPR (EU/EEA)","legal-text"),f=a("Art. 17 (1) (a) DSG (Switzerland)","legal-text"),x=a("Art. 49 (1) (a) GDPR","legal-text"),b=i("https://gdpr-text.com/read/article-49/"),v=i("https://www.fedlex.admin.ch/eli/cc/2022/491/en#art_17");if(u&&h){s.push(p,m),o.push(y,f),l.push(b,v),d.push(c.ADEQUACY_EU,c.ADEQUACY_CH);const e=t.GDPR.filter((e=>t.ADEQUACY_CH.includes(e)));-1===e.indexOf("CH")&&e.push("CH"),c.SAFE=e.map((e=>n[e])).join(", ")}else u?(s.push(m),o.push(f),l.push(v),d.push(c.ADEQUACY_CH),c.SAFE=c.ADEQUACY_CH):h&&(s.push(g),o.push(x),l.push(b),d.push(c.ADEQUACY_EU),c.SAFE=`${c.GDPR}\n\n${c.ADEQUACY_EU}`);return{legalBasis:s,dataProcessingInUnsafeCountriesArticles:o,dataProcessingInUnsafeCountriesArticlesLinks:l,isGdpr:h,isDsg:u,labelGdprEuEea:p,labelDsg:m,labelGdpr:g,safeCountriesList:c,adequacyCountriesLists:d}}),[a,i,r,r])}},53810:(e,t,n)=>{n.d(t,{Z:()=>o});var s=n(41594);function o(e,t){(0,s.useEffect)((()=>{const t=new AbortController,n=[()=>t.abort()];return(async()=>{try{const s=await e({abortController:t,aborted:()=>t.signal.aborted});"function"==typeof s&&n.push(s)}catch(e){if("AbortError"!==e.name)throw e}})(),()=>n.forEach((e=>e()))}),t)}},24513:(e,t,n)=>{function s(e){return/^\.?(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9])$/gm.test(e)}n.d(t,{j:()=>s})},84200:(e,t,n)=>{function s(e){return e.indexOf(".")>-1&&!!/^(?:(?:https?|ftp):\/\/)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:\/\S*)?$/.test(e)}n.d(t,{g:()=>s})},85360:(e,t,n)=>{n.d(t,{i:()=>a});var s=n(3713),o=n(41594);function a(e,t){const n=e.filter(Boolean);return 0===n.length?null:n.reduce(((e,a,i)=>e.length?[...e,(0,s.jsx)(o.Fragment,{children:"function"==typeof t?t(i,n.length):t},i),a]:[a]),[])}},66399:(e,t,n)=>{n.d(t,{P:()=>s});const s=e=>new Promise((t=>setTimeout((()=>t(e)),0)))},32150:(e,t,n)=>{n.d(t,{C:()=>a});var s=n(68789);const o={path:"/:objectType/multilingual/copy",namespace:"wp/v2",method:s.RouteHttpVerb.POST};async function a(e,t,n){const{root:a,nonce:i}=window.wpApiSettings,{translations:r}=await(0,s.commonRequest)({location:o,options:{restRoot:a,restNonce:i,restNamespace:"wp/v2",restQuery:{}},request:{id:t,targetLocale:n},params:{objectType:e}});return r[n]}},40164:(e,t,n)=>{n.d(t,{e:()=>o});var s=n(3713);const o=e=>{let{children:t,maxWidth:n="auto",style:o={}}=e;return(0,s.jsx)("div",{className:"rcb-config-content",style:{maxWidth:"fixed"===n?1300:n,...o},children:t})}},83753:(e,t,n)=>{n.r(t),n.d(t,{SettingsForm:()=>Ce});var s=n(3713),o=n(19117),a=n(18197),i=n(91386),r=n(5190),l=n(44497),c=n(57922),d=n(41594),h=n(27667),u=n(32150),p=n(59726);function m(){const{__:e}=(0,p.s)();return(0,d.useMemo)((()=>[{name:"legalNotice",label:e("Legal notice"),multiple:!1},{name:"privacyPolicy",label:e("Privacy policy"),multiple:!1},{name:"gtc",label:e("General terms and conditions"),multiple:!1},{name:"tos",label:e("Terms of use"),multiple:!1},{name:"cancellationPolicy",label:e("Cancellation policy"),multiple:!1},{name:"cookiePolicy",label:e("Cookie policy"),multiple:!1},{name:"dpa",label:e("Data processing agreement"),multiple:!1},{name:"disputeResolution",label:e("Dispute resolution"),multiple:!1},{name:"other",label:e("Other"),multiple:!0}]),[])}var g=n(3820),y=n(52113);const f=Symbol(),x=()=>(0,y.NV)(f);var b=n(24262),v=n(19393),j=n(6099),w=n(92453),A=n(9551),k=n(65824),C=n(6196),T=n(45854),I=n(64715),B=n(81533),P=n(39555),S=n(56702),D=n(14322),G=n(53029),L=n(53573),N=n(33210),E=n(96431),F=n(66399),R=n(97745),_=n(60111),O=n(17312);const M={labelCol:{span:0},wrapperCol:{span:24},style:{margin:0}},U=()=>{const{message:e}=o.A.useApp(),{_i:t,__:n,_x:a}=(0,p.s)(),{renderPageSelector:r,onHasPrivacyPolicyRCBSentenceChanged:l,onPageEditClick:c}=x(),{DragHandle:h,SortableContext:u,SortableRow:g}=(0,_.E)(),[y,f]=(0,d.useState)("unset"),[b,v]=(0,d.useState)(!1);(0,d.useEffect)((()=>{"unset"!==y&&(null==l||l("in-text"===y))}),[y]);const j=m();return(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.navLinks.length!==t.navLinks.length||e.isBannerLessConsent!==t.isBannerLessConsent,children:o=>{let{getFieldValue:l}=o;return(0,s.jsx)(i.A.List,{name:"navLinks",children:(o,d)=>{let{add:p,remove:m,move:x}=d;return(0,s.jsxs)("table",{className:"wp-list-table widefat fixed striped table-view-list",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{width:45,align:"right",children:" "}),(0,s.jsx)("td",{width:200,children:n("Type")}),(0,s.jsx)("td",{width:300,children:n("Link")}),(0,s.jsx)("td",{width:200,children:n("Link text")}),(0,s.jsx)("td",{width:70,align:"right",children:" "})]})}),(0,s.jsx)("tbody",{children:(0,s.jsxs)(u,{items:o.map((e=>{let{key:t}=e;return`${t}`})),onDragEnd:e=>{let{active:t,over:n}=e;const s=o.findIndex((e=>e.key===+t.id)),a=o.findIndex((e=>e.key===+(null==n?void 0:n.id)));x(s,a)},children:[0===o.length&&(0,s.jsx)("td",{colSpan:5,style:{textAlign:"center"},children:n("No links are displayed in the footer.")}),o.map((d=>(0,s.jsxs)(g,{"data-row-key":`${d.key}`,children:[(0,s.jsx)("td",{children:o.length>1&&(0,s.jsx)(h,{})}),(0,s.jsxs)("td",{children:[(0,s.jsx)(i.A.Item,{name:[d.name,"id"],noStyle:!0,children:(0,s.jsx)(A.A,{type:"hidden"})}),(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>{let{navLinks:n}=e,{navLinks:s}=t;return n.map((e=>{let{pageType:t}=e;return t})).join(",")!==s.map((e=>{let{pageType:t}=e;return t})).join(",")},children:e=>{let{getFieldValue:t}=e;const n=t("navLinks").map(((e,t)=>{let{pageType:n}=e;return t!==d.name&&n})).filter(Boolean);return(0,s.jsx)(i.A.Item,{name:[d.name,"pageType"],noStyle:!0,children:(0,s.jsx)(C.A,{children:j.map((e=>{let{label:t,multiple:o,name:a}=e;return(0,s.jsx)(C.A.Option,{value:a,disabled:!o&&n.indexOf(a)>-1,children:t},a)}))})})}}),(0,s.jsx)("div",{children:(0,s.jsx)(i.A.Item,{name:[d.name,"isExternalUrl"],noStyle:!0,valuePropName:"checked",children:(0,s.jsx)(T.A,{children:n("Use external URL")})})})]}),(0,s.jsx)("td",{children:(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>{let{navLinks:{[d.name]:n},cookiePolicyId:s}=e,{navLinks:{[d.name]:o},cookiePolicyId:a}=t;return(null==n?void 0:n.isExternalUrl)!==(null==o?void 0:o.isExternalUrl)||(null==n?void 0:n.pageType)!==(null==o?void 0:o.pageType)||(null==n?void 0:n.pageId)!==(null==o?void 0:o.pageId)||(null==n?void 0:n.isTargetBlank)!==(null==o?void 0:o.isTargetBlank)||s!==a},children:o=>{let{getFieldValue:l}=o;const h=l(["navLinks",d.name]);if(!h)return null;const{isExternalUrl:u,pageType:p,pageId:m,languages:g}=h,x=l("cookiePolicyId"),j=l("isBannerLessConsent");return(0,s.jsxs)(s.Fragment,{children:[u?(0,s.jsx)(i.A.Item,{...M,name:[d.name,"externalUrl"],rules:[{type:"url",message:n("Please enter a valid URL!")}],children:(0,s.jsx)(A.A,{placeholder:"https://example.com/..."})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A.Item,{name:[d.name,"pageId"],noStyle:!0,children:(0,s.jsx)(r,{id:"navTablePageId",multiple:!1,disabled:!1,onlyPages:!0,selectedPageContent:"privacyPolicy"===p?e=>{const t="string"==typeof e?e.toLowerCase().indexOf("real cookie banner")>-1?"in-text":"not-in-text":"unset";t!==y&&"show-suggestion"!==y&&(0,F.P)().then((()=>f(t)))}:void 0})}),(null==g?void 0:g.length)>0&&(0,s.jsx)("p",{className:"description",style:{fontSize:12},children:n("You need to select the page in your default language here. The translation will be output automatically.")})]}),!u&&(0,s.jsx)(I.A,{title:j?t(n("The cookie banner is hidden on all subpages due to the active option {{a}}Consent > Banner-less consent{{/a}}."),{a:(0,s.jsx)("a",{href:"#/settings/consent",style:{fontStyle:"italic"}})}):void 0,children:(0,s.jsx)("div",{children:(0,s.jsx)(i.A.Item,{name:[d.name,"hideCookieBanner"],noStyle:!0,valuePropName:"checked",children:(0,s.jsx)(T.A,{disabled:j,children:n("Hide cookie banner on this page")})})})}),"privacyPolicy"===p&&"unset"!==y&&("in-text"!==y||u?(0,s.jsxs)("div",{className:"notice notice-info inline below-h2 notice-alt",style:{margin:"10px 0"},children:[(0,s.jsxs)("p",{children:[n("You must mention in your privacy policy that you use Real Cookie Banner and what data it processes. The following text suggestion can be copied into your privacy policy."),(0,s.jsx)(T.A,{checked:b,onChange:e=>v(e.target.checked),style:{marginTop:12,fontSize:"inherit"},children:n("I will check the following sample text for a privacy policy regarding the use of Real Cookie Banners myself and adapt it for my specific use case. In doing so, I will particularly consider overlaps with other areas of my privacy policy.")})]}),b&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(A.A.TextArea,{rows:6,readOnly:!0,style:{marginBottom:12},onClick:t=>{(0,R.l)(t.target.value),e.success(n("Successfully copied privacy policy text to clipboard!"))},value:a('To manage the cookies and similar technologies used (tracking pixels, web beacons, etc.) and related consents, we use the consent tool "Real Cookie Banner". Details on how "Real Cookie Banner" works can be found at <a href="https://devowl.io/rcb/data-processing/" rel="noreferrer" target="_blank">https://devowl.io/rcb/data-processing/</a>.\n\nThe legal basis for the processing of personal data in this context are Art. 6 (1) (c) GDPR and Art. 6 (1) (f) GDPR. Our legitimate interest is the management of the cookies and similar technologies used and the related consents.\n\nThe provision of personal data is neither contractually required nor necessary for the conclusion of a contract. You are not obliged to provide the personal data. If you do not provide the personal data, we will not be able to manage your consents.',"legal-text")}),m>0&&(0,s.jsxs)("a",{onClick:()=>c(m),style:{marginBottom:12},className:"button button-small",children:[(0,s.jsx)(G.A,{})," ",n("Edit privacy policy")]})]})]}):(0,s.jsxs)("div",{style:{margin:"10px 0"},children:[(0,s.jsx)(D.A,{style:{color:"#52c41a"}})," ",n("You mentioned Real Cookie Banner in your privacy policy.")," • ",(0,s.jsx)("a",{onClick:()=>f("show-suggestion"),children:n("Show suggested text")})]})),"cookiePolicy"===p&&0===x&&(0,s.jsxs)("div",{style:{margin:"10px 0"},children:[(0,s.jsx)(L.A,{style:{color:"darkorange"}})," ",n("You have not yet activated the cookie policy feature. Real Cookie Banner offers you the possibility to create your own cookie policy. If you use your own cookie policy, you can ignore this notice."),(0,s.jsx)("br",{}),(0,s.jsx)(W,{})]}),(0,s.jsx)("div",{children:(0,s.jsx)(i.A.Item,{...M,name:[d.name,"isTargetBlank"],valuePropName:"checked",style:{display:!1!==l(["navLinks",d.name,"isTargetBlank"])?"none":void 0},children:(0,s.jsx)(T.A,{children:n("Open in new window")})})})]})}})}),(0,s.jsxs)("td",{children:[(0,s.jsx)(i.A.Item,{noStyle:!0,name:[d.name,"label"],children:(0,s.jsx)(A.A,{})}),(0,s.jsx)(O.r,{style:{marginTop:10,marginLeft:5},recordId:l(["navLinks",d.name,"id"]),languages:l(["navLinks",d.name,"languages"]),onClick:l(["navLinks",d.name,"languageOnClick"])})]}),(0,s.jsx)("td",{children:(0,s.jsx)("a",{className:"button button-small",onClick:()=>{m(d.name)},children:(0,s.jsx)(N.A,{})})})]},d.key)))]})}),(0,s.jsx)("tfoot",{children:(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:5,align:"right",children:(0,s.jsxs)("a",{className:"button button-primary alignright",onClick:()=>{p({pageType:"other",hideCookieBanner:!0,isTargetBlank:!0})},children:[(0,s.jsx)(E.A,{})," ",n("Add another link")]})})})})]})}})}})};var V=n(24325),z=n(67993),Y=n(388);const q=()=>{const{createCookiePolicy:e}=x(),t=m(),{getFieldValue:n,setFieldsValue:s}=i.A.useFormInstance();return async()=>{const{pageId:o,onNewNavLinks:a}=await e(),i=(0,S.G)(n("navLinks")),r=i.findIndex((e=>{let{pageType:t}=e;return"cookiePolicy"===t}));r>-1&&i.splice(r,1);const l=i.findIndex((e=>{let{pageType:t}=e;return"privacyPolicy"===t}));i.splice(l+1,0,{isExternalUrl:!1,isTargetBlank:!0,hideCookieBanner:!0,pageId:o,pageType:"cookiePolicy",label:t.find((e=>{let{name:t}=e;return"cookiePolicy"===t})).label}),s({cookiePolicyId:o,navLinks:i}),await(null==a?void 0:a(i))}},W=()=>{const{__:e}=(0,p.s)(),t=q();return(0,s.jsx)(b.A,{title:e("I am aware that the texts in the cookie policy are suggested formulations and that the manufacturer of Real Cookie Banner cannot take any liability for the topicality, correctness or completeness of the information. I will check the information on my own responsibility, added missing information and correct information that does not fit to my specific use case on my own responsibility."),cancelText:e("Cancel"),okText:e("Create page"),overlayStyle:{maxWidth:450},onConfirm:t,placement:"bottomLeft",children:(0,s.jsx)("button",{className:"button",type:"button",style:{margin:"2px 0"},children:e("Create page")})})},H=()=>{const{__:e,_x:t,_i:n}=(0,p.s)(),{isPro:o}=(0,V.J)(),{iso3166OneAlpha2:a,renderPageSelector:r,deactivateCookiePolicy:l,onCustomizeClick:c}=x(),h=(0,d.useRef)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isBannerActive!==t.isBannerActive,children:n=>{let{getFieldValue:o,setFieldsValue:a}=n;const r=o("isBannerActive");return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.A.Item,{label:e("Cookie Banner/Dialog"),children:[(0,s.jsx)(i.A.Item,{noStyle:!0,name:"isBannerActive",valuePropName:"checked",children:(0,s.jsx)(v.A,{style:{display:r?void 0:"none"}})}),!r&&(0,s.jsx)(b.A,{title:t("I am aware that the texts in the cookie banner and content blocker are suggested formulations and that the manufacturer of Real Cookie Banner cannot take any liability for the topicality, correctness or completeness of the information. I have checked the information on my own responsibility, added missing information and correct information that does not fit to my specific use case on my own responsibility.","legal-text"),cancelText:e("Cancel"),okText:e("Activate now!"),overlayStyle:{maxWidth:450},onCancel:()=>{a({isBannerActive:!1})},onConfirm:()=>{a({isBannerActive:!0})},placement:"bottomLeft",children:(0,s.jsx)(v.A,{checked:!1})}),(0,s.jsx)("p",{className:"description",style:{marginTop:5},children:e("You can enable and disable the cookie banner. We recommend to activate the cookie banner on your website after you have added all services.")})]}),(0,s.jsxs)(i.A.Item,{label:e("Content Blocker"),style:{display:r?void 0:"none"},children:[(0,s.jsx)(i.A.Item,{name:"isBlockerActive",valuePropName:"checked",noStyle:!0,children:(0,s.jsx)(v.A,{style:{marginTop:5}})}),(0,s.jsx)("p",{className:"description",style:{marginTop:5},children:e("This feature allows you to block content that would process personal data and set cookies, but for which you do not yet have the visitor's consent.")})]})]})}}),(0,s.jsxs)(i.A.Item,{label:e("Website operator details"),children:[(0,s.jsx)("p",{className:"description",style:{marginTop:7,marginBottom:12},children:e("You as a website operator should provide your address and at least one digital contact option to fulfill your information obligations for self-hosted services. If you have a company with subsidiaries, please disclose the company that is responsible for the data processing on their website.")}),(0,s.jsxs)(j.A,{gutter:[10,10],children:[(0,s.jsx)(w.A,{span:24,children:(0,s.jsx)(i.A.Item,{name:"operatorContactAddress",style:{margin:0},children:(0,s.jsx)(A.A,{addonBefore:e("Full address"),placeholder:e("e.g. Global Co., 90210 Broadway Blvd., Nashville, TN 37011-5678 (without country)")})})}),(0,s.jsx)(w.A,{span:12,children:(0,s.jsxs)(k.A.Compact,{className:"rcb-antd-select-addon",children:[(0,s.jsx)(A.A,{addonBefore:e("Country")}),(0,s.jsx)(i.A.Item,{name:"operatorCountry",noStyle:!0,children:(0,s.jsx)(C.A,{showSearch:!0,optionFilterProp:"children",children:Object.keys(a).map((e=>(0,s.jsx)(C.A.Option,{value:e,children:a[e]},e)))})})]})}),(0,s.jsx)(w.A,{span:12,children:(0,s.jsx)(i.A.Item,{name:"operatorContactPhone",style:{margin:0},children:(0,s.jsx)(A.A,{addonBefore:e("Phone")})})}),(0,s.jsx)(w.A,{span:12,children:(0,s.jsx)(i.A.Item,{name:"operatorContactEmail",rules:[{type:"email",message:e("Please provide a valid email!")}],style:{margin:0},children:(0,s.jsx)(A.A,{addonBefore:e("Email")})})}),(0,s.jsx)(w.A,{span:12,children:(0,s.jsxs)(k.A.Compact,{className:"rcb-antd-select-addon",children:[(0,s.jsx)(A.A,{addonBefore:e("Contact form")}),(0,s.jsx)(i.A.Item,{name:"operatorContactFormId",noStyle:!0,children:(0,s.jsx)(r,{id:"operatorContactFormId",disabled:!1,multiple:!1,onlyPages:!0})})]})})]})]}),(0,s.jsxs)(i.A.Item,{label:e("Legal basis to be applied"),children:[(0,s.jsx)(i.A.Item,{name:"territorialLegalBasis",noStyle:!0,rules:[{required:!0,message:e("Please choose at least one legal basis to apply!")}],children:(0,s.jsxs)(T.A.Group,{style:{marginTop:5},children:[(0,s.jsx)(T.A,{value:"gdpr-eprivacy",children:e("GDPR / ePrivacy Directive")}),(0,s.jsx)(T.A,{value:"dsg-switzerland",children:e("DSG (Switzerland)")})]})}),(0,s.jsx)("p",{className:"description",style:{marginBottom:0},children:e("Your website, and consequently the cookie banner, can be aligned to different legal bases. If you select multiple legal bases, suggestions in the settings will always match the stricter legal basis.")})]}),(0,s.jsx)(i.A.Item,{label:e("Cookie policy page"),shouldUpdate:(e,t)=>{let{cookiePolicyId:n}=e,{cookiePolicyId:s}=t;return n!==s},children:t=>{let{getFieldValue:o,setFieldsValue:a}=t;const d=o("cookiePolicyId");return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{style:{display:d>0?"block":"none",maxWidth:400},children:(0,s.jsxs)(k.A.Compact,{className:"rcb-antd-select-addon",children:[(0,s.jsx)(i.A.Item,{name:"cookiePolicyId",noStyle:!0,children:(0,s.jsx)(r,{id:"cookiePolicyId",disabled:!0,multiple:!1,onlyPages:!0,selectedPageUrl:e=>{h.current=e}})}),(0,s.jsx)(A.A,{addonAfter:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("a",{onClick:()=>c("rcb-cookie-policy-instruction"),children:e("Customize")})," • ",(0,s.jsx)("a",{onClick:()=>window.open(h.current,"_blank"),children:e("Open page")})," • ",(0,s.jsx)(b.A,{title:e("If you want to deactivate the cookie policy page, the page created for it will also be deleted."),cancelText:e("Cancel"),okText:e("Deactivate and delete page"),onConfirm:async()=>{const{onNewNavLinks:e}=await l(d),t=o("navLinks").filter((e=>{let{pageType:t}=e;return"cookiePolicy"!==t}));a({cookiePolicyId:0,navLinks:t}),await(null==e?void 0:e(t))},overlayStyle:{maxWidth:450},placement:"bottomLeft",children:(0,s.jsx)("a",{children:e("Deactivate")})})]})})]})}),!d&&(0,s.jsx)(W,{}),(0,s.jsx)("div",{style:{margin:"5px 0px 0px"},children:(0,s.jsx)(z.Y,{url:e("https://devowl.io/knowledge-base/cookie-policy-wordpress-website/")})}),(0,s.jsx)("p",{className:"description",style:{marginBottom:0},children:n(e("A cookie policy is a document that lists all the cookies and similar instruments used on a website and provides comprehensive information about each cookie. There is {{strong}}no obligation{{/strong}} to create a cookie policy under the GDPR or the ePrivacy Directive. But it can help you to present the handling of cookies and cookie-like technologies to your website visitors transparently and separately from your privacy policy."),{strong:(0,s.jsx)("strong",{})})})]})}}),(0,s.jsxs)(i.A.Item,{label:e("Footer links"),children:[(0,s.jsx)("p",{className:"description",style:{marginTop:7,marginBottom:12},children:e("According to the ePrivacy Directive, legally required pages such as the privacy policy may be accessible without the user having to give consent to services and their cookies. Therefore, it is recommended to avoid processing personal data and setting cookies on these pages and not to display a cookie banner, but add those links to the footer of the cookie banner. Content blockers block loading of scripts etc. even on pages where the cookie banner is not displayed.")}),(0,s.jsx)(U,{})]}),(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isBannerLessConsent!==t.isBannerLessConsent,children:t=>{let{getFieldValue:a}=t;const l=a("isBannerLessConsent");return(0,s.jsx)(i.A.Item,{label:e("Hide cookie banner on specific pages"),children:(0,s.jsx)(I.A,{title:l?n(e("The cookie banner is hidden on all subpages due to the active option {{a}}Consent > Banner-less consent{{/a}}."),{a:(0,s.jsx)("a",{href:"#/settings/consent",style:{fontStyle:"italic"}})}):void 0,children:(0,s.jsxs)(k.A.Compact,{className:"rcb-antd-select-addon",children:[(0,s.jsx)(A.A,{addonBefore:(0,s.jsxs)(s.Fragment,{children:[e("Hide on additional pages")," ",(0,s.jsx)(Y.O,{title:e("Want to hide cookie banner on more pages?"),testDrive:!0,feature:"hide-page-ids",description:e("Cookie banners are legally necessary, but distract your users from the essentials. For example, on landing pages for advertising campaigns, they lead to lower conversion rates. On these pages, it can be useful to avoid processing personal data and setting cookies to keep the conversion rate high. You can hide the cookie banner on these pages.")})]})}),(0,s.jsx)(i.A.Item,{name:"hidePageIds",noStyle:!0,children:(0,s.jsx)(r,{id:"hidePageIds",disabled:!o||l,multiple:!0})})]})})})}}),(0,s.jsxs)(i.A.Item,{label:e("Load services after consent via"),children:[(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isGcm!==t.isGcm,children:t=>{let{getFieldValue:a}=t;return(0,s.jsx)(i.A.Item,{name:"setCookiesViaManager",noStyle:!0,children:(0,s.jsx)(B.Ay.Group,{children:(0,s.jsxs)(k.A,{direction:"vertical",children:[(0,s.jsx)(B.Ay,{disabled:!o,value:"none",children:e("HTML/JavaScript Snippet")}),(0,s.jsx)(I.A,{title:o&&!a("isGcm")&&n(e("In order to be able to transfer consents to Google Tag Manager via Google Consent Mode, please activate this standard first under {{a}}Settings > Google Consent Mode{{/a}}."),{a:(0,s.jsx)("a",{href:"#/settings/gcm"})}),children:(0,s.jsxs)(B.Ay,{disabled:!o||!a("isGcm"),value:"googleTagManagerWithGcm",children:[e("Google Tag Manager with Google Consent Mode")," ",(0,s.jsx)(z.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-with-google-tag-manager-google-consent-mode/")})]})}),(0,s.jsxs)(B.Ay,{disabled:!o,value:"googleTagManager",children:[e("%s Event",P.iy)," ",(0,s.jsx)(z.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-with-google-tag-manager-or-matomo-tag-manager/")})]}),(0,s.jsxs)(B.Ay,{disabled:!o,value:"matomoTagManager",children:[e("%s Event",P.OF)," ",(0,s.jsx)(z.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-with-google-tag-manager-or-matomo-tag-manager/")})]})]})})})}}),(0,s.jsx)("div",{style:{margin:"5px 0px 0px"},children:(0,s.jsx)(Y.O,{title:e("Want to use a Tag Manager legally?"),testDrive:!0,feature:"set-cookies-via-manager",assetName:e("pro-modal/set-cookies-via-manager.png"),description:e("You can integrate services via Google Tag Manager or Matomo Tag Manager. At the same time, you can obtain and document consents via Real Cookie Banner in accordance with data protection regulations.")})}),(0,s.jsxs)("p",{className:"description",children:[(0,s.jsx)("strong",{children:e("You only need to change this option if you use Google Tag Manager or Matomo Tag Manager.")})," ",n(e("If you opt-in to or opt-out from services, you will normally execute JavaScript code to ensure that scripts are enabled/disabled and cookies are set/removed on the visitor's client. If you use Google Tag Manager or Matomo Tag Manager, you can map this behavior using tags. However, even if they are executed via the tag manager, the tags may only be fired if consent has been given for the respective service. Real Cookie Banner can automatically transmit the consent to the respective Tag Manager via events or Google Consent Mode (recommended)."),{strong:(0,s.jsx)("strong",{})})]})]})]})},$=()=>{const{__:e}=(0,p.s)(),{isTcf:t,activeTab:n,onStartTcfVendorConfiguration:o}=x();return(0,s.jsx)(i.A.Item,{className:"rcb-antd-form-sticky-submit",colon:!1,labelAlign:"left",label:(0,s.jsx)("a",{className:"button button-link",href:"#/settings/reset",children:e("Reset plugin and texts")}),style:{textAlign:"center"},children:(0,s.jsxs)(k.A,{direction:"horizontal",children:[t&&"tcf"===n&&(0,s.jsx)("button",{className:"button",onClick:()=>o(),style:{marginRight:10},children:e("Start vendor configuration")}),(0,s.jsx)("input",{type:"submit",className:"button button-primary",value:e("Save settings")})]})})};var J=n(27465),Q=n(8116),Z=n(43799);function K(e,t){const n=e.filter(Boolean);return n.length>1&&n.splice(n.length-1,0,"{{andSeparator}}"),n.join(", ").replace(/,\s+{{andSeparator}},\s+/g,t)}var X=n(85360),ee=n(69810);const{Panel:te}=J.A,ne=()=>{const{__:e,_i:t,_n:n}=(0,p.s)(),{isPro:o}=(0,V.J)(),{isTcf:a,consentsDeletedAt:r,iso3166OneAlpha2:l,ageNotice:c,ageNoticeCountryAgeMap:h,predefinedDataProcessingInSafeCountriesLists:u,onCustomizeClick:m,bannerlessConsentChecks:g,renderPageSelector:y}=x(),f=(0,ee.j)({predefinedDataProcessingInSafeCountriesLists:u,iso3166OneAlpha2:l}),v=q(),P=(0,d.useMemo)((()=>g.legalBasisConsentWithoutVisualContentBlocker.map((e=>{let{name:t}=e;return t}))),[g]),S=!c||c.indexOf("{{minAge}}")>-1;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>JSON.stringify(e.territorialLegalBasis)!==JSON.stringify(t.territorialLegalBasis),children:n=>{let{getFieldValue:a}=n;const r=a("territorialLegalBasis"),{legalBasis:l,labelGdpr:c,labelGdprEuEea:d,safeCountriesList:h,adequacyCountriesLists:u,dataProcessingInUnsafeCountriesArticles:p,dataProcessingInUnsafeCountriesArticlesLinks:g}=f(r);return(0,s.jsxs)(i.A.Item,{label:e("Consent for data processing in unsafe third countries"),children:[(0,s.jsx)(i.A.Item,{name:"isDataProcessingInUnsafeCountries",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{disabled:!o,value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{disabled:!o,value:!1,children:e("Disabled")})]})}),(0,s.jsx)("div",{style:{margin:"5px 0px 0px"},children:(0,s.jsx)(Y.O,{title:e("Do you need consent for data processing in unsafe countries?"),testDrive:!0,feature:"data-processing-in-unsafe-countries",assetName:e("pro-modal/data-processing-in-insecure-third-countries.png"),description:t(e("The GDPR offers a great level of data protection. For data processing in countries where the GDPR does not apply, safety mechanisms are required that guarantee an appropriate level of protection. This is usually an adequacy decision or standard contractual clauses. If none of the safety mechanisms are in place, you must inform your website visitors about special risks and their consequences and obtain special consent in accordance with {{aGdpr}}Art. 49 GDPR{{/aGdpr}}."),{aGdpr:(0,s.jsx)("a",{href:e("https://gdpr-text.com/read/article-49/"),target:"_blank",rel:"noreferrer"})})})}),(0,s.jsxs)("p",{className:"description",children:[t(e("Countries in which {{legalBasis/}} are not applicable are considered as unsafe third countries in which no personal data of your visitors may initially be transmitted or processed. For some countries, there is an {{adequacy}}{{u}}adequacy decision{{/u}}{{/adequacy}} that declares the country to be safe. You can also conclude {{aSCC}}standard contractual clauses{{/aSCC}} with service providers that contractually guarantee safe data processing. Sometimes, however, none of these safety mechanisms exist and it is not possible to waive the use of the service. In accordance with {{article/}}, it is possible to obtain the consent of website visitors for the one-off and multiple processing of personal data. Consent to the regular processing of data in unsafe third countries in this way is controversial. This means that at least some of the convenient services can still be used. Activate this option to flag the processing of personal data in unsafe countries and obtain the special consent from your visitors."),{legalBasis:(0,X.i)(l.map((e=>{const t=[c,d].indexOf(e)>-1?h.GDPR:"";return t?(0,s.jsx)(I.A,{title:t,children:(0,s.jsx)("u",{style:{textDecorationStyle:"dashed"},children:e})},e):(0,s.jsx)("span",{children:e},e)})),e(" or ")),article:(0,X.i)(p.map(((e,t)=>(0,s.jsx)("a",{href:g[t],target:"_blank",rel:"noreferrer",children:e},e))),e(" or ")),u:(0,s.jsx)("u",{style:{textDecorationStyle:"dashed"}}),adequacy:(0,s.jsx)(I.A,{title:(0,X.i)(u.map((e=>(0,s.jsx)("div",{children:e},e))),(0,s.jsx)("br",{}))}),aSCC:(0,s.jsx)("a",{href:e("https://commission.europa.eu/law/law-topic/data-protection/international-dimension-data-protection/standard-contractual-clauses-scc_en"),target:"_blank",rel:"noreferrer"})})," • ",(0,s.jsx)("a",{onClick:()=>m("rcb-banner-texts-data-processing-in-unsafe-countries"),children:e("Change text in customizer")})]})]})}}),(0,s.jsxs)(i.A.Item,{label:e("Age notice for consent"),children:[(0,s.jsx)(i.A.Item,{name:"isAgeNotice",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{value:!1,children:e("Disabled")})]})}),(0,s.jsxs)("p",{className:"description",children:[t(e("In accordance with {{a}}Art. 8 GDPR{{/a}}, consent to services that process personal data and/or set cookies can only be given from the age of 16 years (varying in some EU countries) or together with a legal guardian. If your website does not clearly and exclusively address adults (e.g. dating or porn sites), you as the website operator must inform children under the minimum age in simple language that they are not allowed to consent to non-essential services without their legal guardian."),{a:(0,s.jsx)("a",{href:e("https://gdpr-text.com/read/article-8/"),target:"_blank",rel:"noreferrer"})})," • ",(0,s.jsx)("a",{onClick:()=>m("rcb-banner-texts-age-notice"),children:e("Change text in customizer")})]}),(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>{let{isAgeNotice:n,operatorCountry:s,ageNoticeAgeLimit:o}=e,{isAgeNotice:a,operatorCountry:i,ageNoticeAgeLimit:r}=t;return n!==a||s!==i||o!==r},children:n=>{let{getFieldValue:o}=n;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(k.A.Compact,{className:"rcb-antd-select-addon",style:{display:o("isAgeNotice")&&S?void 0:"none"},children:[(0,s.jsx)(A.A,{addonBefore:e("Age limit")}),(0,s.jsx)(i.A.Item,{noStyle:!0,name:"ageNoticeAgeLimit",children:(0,s.jsx)(C.A,{showSearch:!0,optionFilterProp:"children",children:Object.keys(h).map((t=>{const n=o("operatorCountry"),a="INHERIT"===t?n&&h[n]?n:"GDPR":t;return(0,s.jsx)(C.A.Option,{value:t,children:(0,s.jsxs)(j.A,{children:[(0,s.jsx)(w.A,{flex:"auto",children:"INHERIT"===t?e("Determine age limit based on specified website operator country"):"GDPR"===t?e("GDPR standard"):l[t]}),(0,s.jsx)(w.A,{flex:"none",children:e("%d years",h[a])})]})},t)}))})})]}),o("isAgeNotice")&&S&&-1===["INHERIT","GDPR"].indexOf(o("ageNoticeAgeLimit"))&&(0,s.jsx)("div",{className:"notice notice-info inline below-h2 notice-alt",style:{margin:"10px 0 0"},children:(0,s.jsx)("p",{children:t(e("The age limit should only differ from the age limit in the country of the website operator if you are primarily targeting visitors from another country with a {{strong}}higher{{/strong}} age limit. Changing the age limit therefore only makes sense in a few exceptional cases."),{strong:(0,s.jsx)("strong",{})})})})]})}})]}),(0,s.jsxs)(i.A.Item,{label:e("Naming of all services in first view"),children:[(0,s.jsx)(i.A.Item,{name:"isListServicesNotice",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{value:!1,children:e("Disabled")})]})}),(0,s.jsxs)("p",{className:"description",children:[e("The visitor to your website should be adequately informed of what he or she is agreeing to when consenting to all services. Therefore, in the first view of the cookie banner, all services can be written out by name in a comma-separated list. Your website visitor can thus better assess whether he really wants to agree to all services without visiting the individual privacy settings.")," • ",(0,s.jsx)("a",{onClick:()=>m("rcb-banner-texts-list-services-notice"),children:e("Change text in customizer")})]})]}),(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isBannerLessConsent!==t.isBannerLessConsent||e.cookiePolicyId!==t.cookiePolicyId||e.isTcf!==t.isTcf,children:t=>{let{getFieldValue:a,setFieldsValue:r}=t;const l=a("isBannerLessConsent"),c=a("cookiePolicyId"),d=a("isTcf");return(0,s.jsxs)(i.A.Item,{label:e("Banner-less consent"),children:[(0,s.jsx)(i.A.Item,{name:"isBannerLessConsent",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{style:{display:l?void 0:"none"},children:[(0,s.jsx)(B.Ay.Button,{disabled:!o,value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{disabled:!o,value:!1,children:e("Disabled")})]})}),!l&&(0,s.jsxs)(B.Ay.Group,{value:!1,children:[(0,s.jsx)(b.A,{title:(0,s.jsxs)(k.A,{direction:"vertical",size:"small",children:[(0,s.jsx)("div",{children:e("Enabling the option means that the cookie banner will no longer be displayed on your website. Consent to certain services can only be given via visual content blockers or the legal link to the privacy settings (only a few website visitors use this). To comply with your information obligations, especially for essential services, you must have a cookie policy on your website or provide all the necessary information manually in your privacy policy. The document must be linked on every subpage of the website.")}),(0,s.jsx)("div",{children:c>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(D.A,{style:{color:"#52c41a"}})," ",e("Cookie policy page already created")]}):(0,s.jsx)(T.A,{defaultChecked:!0,children:e("Create cookie policy page with content automatically")})}),P.length>0&&(0,s.jsx)(Q.A,{type:"warning",message:n("The service %s does not have a visual content blocker, which is why website visitors without cookie banners cannot consent to it (except via the legal link to change the privacy settings). As a result, this service will normally no longer be embeded on your website.","The services %s do not have visual content blockers, which is why website visitors without cookie banners cannot consent to them (except via the legal link to change the privacy settings). As a result, these services will normally no longer be embeded on your website.",P.length,K(P,e(" and "))),showIcon:!0}),d&&(0,s.jsx)(Q.A,{type:"warning",message:e("Banner-less consent will preventing visitors from consenting to TCF vendor purposes, which will result in significantly reduced advertising revenue. We advise against using the Transparency & Consent Framework together with banner-less consent!"),showIcon:!0})]},"alerts"),cancelText:e("Cancel"),okText:e("Activate now!"),overlayStyle:{maxWidth:750},onCancel:()=>{r({isBannerLessConsent:!1})},onConfirm:async e=>{r({isBannerLessConsent:!0}),function(e,t,n){void 0===n&&(n=0);const s=[];let o=e.parentElement;let a=0;for(;null!==o;){const i=o.nodeType===Node.ELEMENT_NODE;if(0===a&&1===n&&i){const n=e.closest(t);return n?[n]:[]}if(i&&o.matches(t)&&s.push(o),o=o.parentElement,0!==n&&s.length>=n)break;a++}return s}(e.target,".rcb-antd-popconfirm",1)[0].querySelector("input").checked&&await v()},placement:"bottomLeft",children:(0,s.jsx)(B.Ay.Button,{disabled:!o,value:!0,onClick:e=>e.stopPropagation(),onChange:e=>e.stopPropagation(),children:e("Enabled")})}),(0,s.jsx)(B.Ay.Button,{disabled:!o,value:!1,children:e("Disabled")})]}),(0,s.jsx)("div",{style:{margin:"5px 0px 0px",display:o?"none":void 0},children:(0,s.jsx)(Y.O,{title:e("Do you want to avoid using a cookie banner?"),testDrive:!0,feature:"banner-less-consent",assetName:e("pro-modal/banner-less-consent.png"),description:e("Bannerless consent ensures a seamless, distraction-free experience for your visitors. By using essential services and visual content blockers, you can avoid intrusive cookie banners and at the same time allow users to easily manage their consent for specific content such as YouTube videos in accordance with the legal regulations.")})}),(0,s.jsx)("p",{className:"description",style:{marginTop:5},children:e("If you only use essential services and those with a visual content blocker, you can avoid using a cookie banner so that your website visitors are not distracted. Visual content blockers initially block all services that require consent. For example, if a visitor wants to watch a YouTube video, they can give their consent in the content blocker for these videos to always be loaded in future. To fulfill your information obligations, you should have a cookie policy on your website (created automatically) or provide all necessary information in your privacy policy (manually).")})]})}}),(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isBannerLessConsent!==t.isBannerLessConsent,children:t=>{let{getFieldValue:n}=t;return n("isBannerLessConsent")&&o&&(0,s.jsxs)(i.A.Item,{label:e("Show cookie banner on specific pages"),children:[(0,s.jsx)(i.A.Item,{name:"bannerLessConsentShowOnPageIds",noStyle:!0,children:(0,s.jsx)(y,{id:"bannerLessConsentShowOnPageIds",disabled:!1,multiple:!0,onlyPages:!1})}),(0,s.jsx)("p",{className:"description",children:e("The cookie banner is generally not displayed due to consent being obtained without a banner (banner-less). However, if non-essential services without visual content blockers are required on certain subpages, you can display the cookie banner when the visitor accesses one of these subpages for the first time. This is useful e.g. for using services like Google Ads Conversion Tracking or Google Analytics on specific landing pages.")})]})}}),(0,s.jsxs)(i.A.Item,{label:e("Handling of failed consent documentation"),style:{marginBottom:0},children:[(0,s.jsx)(i.A.Item,{name:"failedConsentDocumentationHandling",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{value:"essentials",children:e("Allow only essential services")}),(0,s.jsx)(B.Ay.Button,{value:"optimistic",children:e("Respect user consent")})]})}),(0,s.jsx)("p",{className:"description",children:e("It is possible that your website can still be accessed (e.g. due to a highly scalable page cache), but that consents given in the cookie banner can no longer be documented (e.g. server overload due to too much traffic). In this case, Real Cookie Banner will try to document the consent as soon as the server is avaible again in accordance with your legal obligations. In the meantime, there is a risk if the consent of your website visitor is respected and thus non-essential services are played out whose consent or legitimate interest you cannot prove. However, if you expect a temporary overload of your server, e.g. due to an event, it may be economically advantageous to take this risk.")})]}),(0,s.jsx)(J.A,{ghost:!0,children:(0,s.jsx)(te,{header:(0,s.jsx)("a",{children:e("Advanced settings (Do not track, behavior for bots, saving of IP addresses, duration of cookie consent, lifetime of consent documentations)")}),children:(0,s.jsxs)(Z.A,{style:{margin:5},children:[(0,s.jsxs)(i.A.Item,{label:e('Respect "Do Not Track"'),children:[(0,s.jsx)(i.A.Item,{name:"isRespectDoNotTrack",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{value:!1,children:e("Disabled")})]})}),(0,s.jsx)("div",{style:{margin:"5px 0px 0px"},children:(0,s.jsx)(z.Y,{url:e("https://devowl.io/data-protection/do-not-track/")})}),(0,s.jsxs)("p",{className:"description",children:[e('Users can set the "Do not track" HTTP header in their browser to indicate that they do not want to be tracked. If you enable this option, users will be able to access your website with this HTTP header without a cookie banner, if there are no non-essential services that are used based on legitimate interest. Only the non-rejectable services will be used in the case.')," ",a&&(0,s.jsx)("strong",{children:e("According to the TCF standard, all rights of objection are also exercised for these visitors, as the cookie banner is not displayed at all and thus no transparency about legitimate interest can be established.")})]})]}),(0,s.jsxs)(i.A.Item,{label:e("Automatically accept all services for bots"),children:[(0,s.jsx)(i.A.Item,{name:"isAcceptAllForBots",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{value:!1,children:e("Disabled")})]})}),(0,s.jsx)("p",{className:"description",children:t(e("Bots are not subject of the GDPR according to {{aGdpr}}Art. 1 GDPR{{/aGdpr}} and technically cannot give consent to the processing of personal data and the setting of cookies. As a result, they may consider your site as slow if a cookie banner permanently blocks all other content. Therefore, you should allow the bots to browse your site without consent, but still allow all services."),{aGdpr:(0,s.jsx)("a",{href:e("https://gdpr-text.com/read/article-1/"),target:"_blank",rel:"noreferrer"})})})]}),(0,s.jsxs)(i.A.Item,{label:e("Save IP address on consent"),children:[(0,s.jsx)(i.A.Item,{name:"isSaveIp",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{value:!1,children:e("Disabled")})]})}),(0,s.jsx)("p",{className:"description",children:t(e("Depending on the country of origin, it may make legal sense to store the IP address of the user who consents. On the other hand, you must inform the user in your privacy policy that you are storing this IP address together with the consent, as this is legally {{a}}personal data{{/a}}."),{a:(0,s.jsx)("a",{href:e("https://devowl.io/data-protection/personal-data-gdpr/"),target:"_blank",rel:"noreferrer"})})})]}),(0,s.jsxs)(i.A.Item,{label:e("Duration of cookie consent"),children:[(0,s.jsx)(i.A.Item,{name:"cookieDuration",wrapperCol:{span:8},style:{marginBottom:0},rules:[{type:"number",min:1,max:365,transform:e=>+e,message:e("Please use a number between %d and %d!",1,365)}],children:(0,s.jsx)(A.A,{addonAfter:e("days"),type:"number",max:365})}),(0,s.jsx)("p",{className:"description",children:e("The consent of a user must be limited in time. You can define how long the consent is valid. It can be valid for a maximum of 365 days because every 6-12 months you have to remind the user of the right to revoke consent (cookie banner is displayed again). If you change the behavior or content of the cookie banner meanwhile, visitors to your website will have to give their consent again, even if the time period has not yet expired.")}),(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.cookieDuration!==t.cookieDuration,children:t=>{let{getFieldValue:n}=t;return n("cookieDuration")<183&&(0,s.jsx)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:{margin:0},children:(0,s.jsx)("p",{children:e("You are not allowed to ask for consent again in some EU countries, such as Italy, for at least six months (unless the content of the cookie banner changes). In other countries, you must ask for consent again after 12 months at the latest. Therefore, consent should be valid for between 183 days and 365 days.")})})}})]}),(0,s.jsxs)(i.A.Item,{label:e("Delete documented consents after"),children:[(0,s.jsx)(i.A.Item,{name:"consentDuration",wrapperCol:{span:8},style:{marginBottom:0},rules:[{type:"number",min:1,max:120,transform:e=>+e,message:e("Please use a number between %d and %d!",1,120)}],children:(0,s.jsx)(A.A,{addonAfter:e("months"),type:"number"})}),r&&(0,s.jsx)("p",{className:"description",children:(0,s.jsx)("strong",{children:e("Consents was last cleaned up on %s.",new Date(r).toLocaleString(document.documentElement.lang))})}),(0,s.jsx)("p",{className:"description",children:t(e("Consents are automatically documented in order to be able to prove compliance with the legal requirements according to {{a}}Art. 5 GDPR{{/a}} and, in case of dispute, to prove how the consent was obtained. The absolute statute of limitations for data protection violations in most EU countries is 10 years. After that, you should delete consents in the interest of data economy."),{a:(0,s.jsx)("a",{href:e("https://gdpr-text.com/read/article-5/"),target:"_blank",rel:"noreferrer"})})})]})]})},"nav")})]})},se=()=>{const{message:e}=o.A.useApp(),{__:t,_x:n,_i:r}=(0,p.s)(),{isPro:l,isLicensed:c}=(0,V.J)(),{isCountryBypass:h,countryBypassDbDownloadTime:u,onCountryBypassUpdateDatabase:m,iso3166OneAlpha2:g,predefinedCountryBypassLists:y}=x(),[f,b]=(0,d.useState)(!1),v=(0,d.useCallback)((async()=>{b(!0);try{await m(),e.success(t("You successfully updated the IP-to-Country database."))}catch(t){throw e.error(t.responseJSON.message),t}finally{b(!1)}}),[m]),j=(0,d.useCallback)((e=>"GDPR"===e?t("Countries where GDPR applies"):t("Countries where %s applies",e)),[]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.A.Item,{label:t("Geo-restriction"),style:{marginBottom:10},children:[(0,s.jsx)(i.A.Item,{name:"isCountryBypass",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{disabled:!l||!c,value:!0,children:t("Enabled")}),(0,s.jsx)(B.Ay.Button,{disabled:!l||!c,value:!1,children:t("Disabled")})]})}),l&&!c&&(0,s.jsx)("div",{className:"notice notice-error inline below-h2 notice-alt",style:{margin:"10px 0"},children:(0,s.jsxs)("p",{children:[t("This functionality is only available with a valid license, as the IP-to-Country database must be downloaded regularly from our cloud service.")," ","•"," ",(0,s.jsx)("a",{href:`#/licensing?navigateAfterActivation=${encodeURIComponent(window.location.href)}`,children:t("Activate license")})]})}),(0,s.jsx)("div",{style:{margin:"5px 0px 0px"},children:(0,s.jsx)(Y.O,{title:t("Enable geo-restriction?"),testDrive:!0,feature:"country-bypass",assetName:t("pro-modal/geo-restriction.png"),description:t("Cookie banners must be displayed only to visitors from certain countries. For example, visitors from the EU. With geo-restriction you can specify exactly who sees a cookie banner and who doesn't, by country.")})}),(0,s.jsx)("p",{className:"description",children:t("With the geo-restrictions you can specify that the cookie banner should be displayed only for users from certain countries. In many countries (outside the EU) it is not necessary to display a cookie banner. The location of the user is determined by their IP address.")})]}),(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isCountryBypass!==t.isCountryBypass,children:e=>{let{getFieldValue:o}=e;const c=o("isCountryBypass");return l&&c&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A.Item,{wrapperCol:{offset:6},name:"countryBypassCheckboxTerms",valuePropName:"checked",style:{marginBottom:15},rules:[{type:"boolean",required:!0,transform:e=>e||void 0,message:t("Please confirm that you have read this!")}],children:(0,s.jsx)(T.A,{disabled:h,children:r(n("To determine the location of your website visitors, the MaxMind GeoLite2 database will be downloaded to your server and used from there. I have read and agree to the {{aMaxMindEndUserLicense}}MaxMind End User License Agreement{{/aMaxMindEndUserLicense}}. The download of the database is done via a devowl.io server and I agree to the {{aTerms}}terms and conditions{{/aTerms}} and {{aPrivacyPolicy}}privacy policy{{/aPrivacyPolicy}}.","legal-text"),{aMaxMindEndUserLicense:(0,s.jsx)("a",{href:t("https://www.maxmind.com/en/end-user-license-agreement"),target:"_blank",rel:"noreferrer"}),aTerms:(0,s.jsx)("a",{href:t("https://devowl.io/terms/"),target:"_blank",rel:"noreferrer"}),aPrivacyPolicy:(0,s.jsx)("a",{href:t("https://devowl.io/privacy-policy/"),target:"_blank",rel:"noreferrer"})})})}),(0,s.jsx)(i.A.Item,{wrapperCol:{offset:6},name:"countryBypassCheckboxAccuracy",valuePropName:"checked",style:{marginBottom:15},rules:[{type:"boolean",required:!0,transform:e=>e||void 0,message:t("Please confirm that you have read this!")}],children:(0,s.jsx)(T.A,{disabled:h,children:r(n("I understand that this type of location determination is not accurate with one hundred percent certainty. According to {{a}}MaxMind's data{{/a}}, 99.8%% accuracy is estimated without obligation.","legal-text"),{a:(0,s.jsx)("a",{href:t("https://support.maxmind.com/hc/en-us/articles/4407630607131-Geolocation-Accuracy"),target:"_blank",rel:"noreferrer"})})})}),(0,s.jsxs)(i.A.Item,{label:t("Show banner only to users from these countries"),children:[(0,s.jsx)(i.A.Item,{name:"countryBypassCountries",noStyle:!0,children:(0,s.jsxs)(C.A,{mode:"multiple",showSearch:!0,optionFilterProp:"children",children:[Object.keys(y).map((e=>{const t=j(e);return t&&(0,s.jsx)(C.A.Option,{value:e,children:t},e)})),Object.keys(g).map((e=>(0,s.jsx)(C.A.Option,{value:e,children:g[e]},e)))]})}),(0,s.jsx)("p",{className:"description",children:t("Determines for users from which countries you want to display a cookie banner. It is recommended to display cookie banners at least in the countries where the GDPR and CCPA apply. The list of countries can be extended at any time by the accession of new countries (Last update: October 2023).")}),Object.keys(y).map((e=>{const t=j(e);return t&&(0,s.jsxs)("p",{className:"description",children:[(0,s.jsxs)("strong",{children:[t,": "]}),y[e].map((e=>g[e])).join(", ")]},e)}))]}),(0,s.jsxs)(i.A.Item,{label:t("Implicit consent for users from third countries"),style:{marginBottom:10},children:[(0,s.jsx)(i.A.Item,{name:"countryBypassType",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{value:"all",children:t("Accept all")}),(0,s.jsx)(B.Ay.Button,{value:"essentials",children:t("Accept only essentials")})]})}),(0,s.jsx)("p",{className:"description",children:t("Set what consent should be implied when a visitor is not shown a cookie banner.")})]}),h&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(i.A.Item,{label:t("IP-to-Country database"),children:[(0,s.jsx)(a.A,{spinning:f,children:(0,s.jsx)("button",{type:"button",className:"button",onClick:v,children:t("Update now")})}),u?(0,s.jsx)("p",{style:{margin:"10px 0 0 0"},children:(0,s.jsx)("strong",{children:t("You have downloaded the IP-to-Country database the last time on %s",new Date(u).toLocaleString(document.documentElement.lang))})}):(0,s.jsx)("div",{className:"notice notice-error inline below-h2 notice-alt",style:{margin:"10px 0 0 0"},children:(0,s.jsx)("p",{children:t("Something seems to have gone wrong when downloading the IP-to-Country database. Please check if your server has a firewall configured and is blocking the download accordingly, or contact our support!")})}),(0,s.jsx)("p",{className:"description",children:t("The IP-to-Country database contains a huge set of IP ranges where we can look up where the current user is coming from based on a specific IP address.")})]})})]})}})]})};var oe=n(39795);const ae=()=>{const{message:e}=o.A.useApp(),{__:t,_x:n,_i:r}=(0,p.s)(),{isPro:l,isLicensed:c}=(0,V.J)(),{iso3166OneAlpha2:h,isTcf:u,tcfAcceptedTime:m,tcfGvlDownloadTime:g,onTcfUpdateDatabase:y}=x(),[f,b]=(0,d.useState)(!1),v=(0,d.useCallback)((async()=>{b(!0);try{await y(),e.success(t("You successfully updated the GVL."))}catch(t){throw e.error(t.responseJSON.message),t}finally{b(!1)}}),[y]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.A.Item,{label:t("Transparency & Consent Framework (TCF)"),style:{marginBottom:10},children:[(0,s.jsx)(i.A.Item,{name:"isTcf",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{disabled:!l||!c,value:!0,children:t("Enabled")}),(0,s.jsx)(B.Ay.Button,{disabled:!l||!c,value:!1,children:t("Disabled")})]})}),l&&!c&&(0,s.jsx)("div",{className:"notice notice-error inline below-h2 notice-alt",style:{margin:"10px 0"},children:(0,s.jsxs)("p",{children:[t("This functionality is only available with a valid license, as the TCF Global Vendor List (GVL) must be downloaded regularly from our cloud service.")," ","•"," ",(0,s.jsx)("a",{href:`#/licensing?navigateAfterActivation=${encodeURIComponent(window.location.href)}`,children:t("Activate license")})]})}),(0,s.jsxs)("div",{style:{margin:"5px 0px 0px"},children:[(0,s.jsx)(z.Y,{url:t("https://devowl.io/knowledge-base/real-cookie-banner-tcf-setup/")}),(0,s.jsx)(Y.O,{title:t("Enable TCF compatibility?"),testDrive:!0,feature:"tcf",assetName:t("pro-modal/tcf-compatibility.png"),description:t("TCF (Transparency & Consent Framework) is an industry standard for obtaining consent that is recognized in the EU. You must obtain consent in this form to fully use services such as Google Adsense and to increase your advertising revenue.")})]}),(0,s.jsx)("p",{className:"description",children:r(t("The {{a}}Transparency & Consent Framework (TCF){{/a}} is a standard of the European digital marketing industry association {{aIab}}IAB Europe{{/aIab}}. It allows users to consent to data collection and processing in accordance with applicable EU law and to share it with third-party services (e.g. advertising networks) in a standardized format. Some services require TCF standard consent to be used on your website. We recommend that you only enable TCF compatibility if you know you need it, as it limits the functionality of Real Cookie Banner."),{a:(0,s.jsx)("a",{href:t("https://iabeurope.eu/transparency-consent-framework/"),target:"_blank",rel:"noreferrer"}),aIab:(0,s.jsx)("a",{href:t("https://iabeurope.eu/"),target:"_blank",rel:"noreferrer"})})})]}),(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isTcf!==t.isTcf||e.isBannerLessConsent!==t.isBannerLessConsent,children:e=>{let{getFieldValue:o}=e;const c=o("isTcf"),d=o("isBannerLessConsent");return l&&c&&(0,s.jsxs)(s.Fragment,{children:[m&&(0,s.jsx)(i.A.Item,{wrapperCol:{offset:6},style:{marginBottom:10},children:(0,s.jsx)("p",{style:{margin:0},children:(0,s.jsx)("strong",{children:t("You last agreed to the following on %s",new Date(m).toLocaleString(document.documentElement.lang))})})}),(0,s.jsx)(i.A.Item,{wrapperCol:{offset:6},hidden:!d,children:(0,s.jsx)(oe.q,{notices:[{message:d&&(0,X.i)([r(t("You have decided to obtain consent without cookie banners (banner-less consent) on your website by activating {{a}}Banner-less consent{{/a}}. This means that your website visitors cannot consent to the purposes of TCF vendors in the cookie banner, as no cookie banner is displayed. As a result, most advertising partners will not be able to display any or only very limited advertising, which {{strong}}will generate significantly less revenue{{/strong}}."),{a:(0,s.jsx)("a",{href:"#/settings/consent"}),strong:(0,s.jsx)("strong",{})}),r(t("According to the {{a}}TCF Policy Appendix B D. a.{{/a}}, you must also ensure that the privacy settings on each subpage are easily accessible (e.g. {{aLinks}}place legal links in the footer{{/aLinks}}) so that you do not violate the terms of the standard."),{a:(0,s.jsx)("a",{href:t("https://iabeurope.eu/iab-europe-transparency-consent-framework-policies/#headline-2055-18959"),target:"_blank",rel:"noreferrer"}),aLinks:(0,s.jsx)("a",{href:"#/consent/legal",target:"_blank",rel:"noreferrer"})}),(0,s.jsx)("strong",{children:t("We advise against using the Transparency & Consent Framework together with banner-less consent!")},"against")],(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("br",{}),(0,s.jsx)("br",{})]})),severity:"warning"}]})}),(0,s.jsx)(i.A.Item,{wrapperCol:{offset:6},name:"tcfCheckboxTerms",valuePropName:"checked",style:{marginBottom:15},rules:[{type:"boolean",required:!0,transform:e=>e||void 0,message:t("Please confirm that you have read the terms!")}],children:(0,s.jsx)(T.A,{disabled:u,children:r(n("I have read, agree to, and will fully comply with the {{aPolicies}}IAB Europe Transparency & Consent Framework Policies{{/aPolicies}} and {{aTerms}}Terms and Conditions for the IAB Europe Transparency & Consent Framework{{/aTerms}}.","legal-text"),{aPolicies:(0,s.jsx)("a",{href:t("https://iabeurope.eu/iab-europe-transparency-consent-framework-policies/"),target:"_blank",rel:"noreferrer"}),aTerms:(0,s.jsx)("a",{href:t("https://iabeurope.eu/iab-europe-transparency-consent-framework-terms-conditions/"),target:"_blank",rel:"noreferrer"})})})}),(0,s.jsx)(i.A.Item,{wrapperCol:{offset:6},name:"tcfCheckboxVisually",valuePropName:"checked",rules:[{type:"boolean",required:!0,transform:e=>e||void 0,message:t("Please confirm that you agree to the visual changes!")}],children:(0,s.jsx)(T.A,{disabled:u,children:n("I know that enabling TCF mode will change my consent management (cookie banner) visually as well as functionally. Some options of Real Cookie Banner may be limited to be compliant with the guidelines of the TCF standard.","legal-text")})}),(0,s.jsxs)(i.A.Item,{label:t("Country of the website operator"),required:!0,children:[(0,s.jsx)(i.A.Item,{noStyle:!0,name:"operatorCountry",rules:[{required:!0,transform:e=>e||void 0,message:t("Please choose the country of origin of your company!")}],children:(0,s.jsx)(C.A,{showSearch:!0,optionFilterProp:"children",children:Object.keys(h).map((e=>(0,s.jsx)(C.A.Option,{value:e,children:h[e]},e)))})}),(0,s.jsx)("p",{className:"description",children:t("The country whose law applies to the operation of your website. Commonly, this corresponds to the country in which your business is established.")})]}),u&&(0,s.jsxs)(i.A.Item,{label:t("Global Vendor List (GVL)"),children:[(0,s.jsx)(a.A,{spinning:f,children:(0,s.jsx)("button",{type:"button",className:"button",onClick:v,children:t("Update now")})}),g?(0,s.jsx)("p",{style:{margin:"10px 0 0 0"},children:(0,s.jsx)("strong",{children:t("You have downloaded the GVL the last time on %s",new Date(g).toLocaleString(document.documentElement.lang))})}):(0,s.jsx)("div",{className:"notice notice-error inline below-h2 notice-alt",style:{margin:"10px 0 0 0"},children:(0,s.jsx)("p",{children:t("Something seems to have gone wrong when downloading the GVL. Please check if your server has a firewall configured and is blocking the download accordingly, or contact our support!")})}),(0,s.jsx)("p",{className:"description",children:r(t("The {{a}}Global Vendor List (GVL){{/a}} contains all vendors registered with IAB Europe that can receive consents via TCF standard. This list is downloaded to your database so that technical and legal data about the vendors is known and you can select from it. The GVL will be automatically updated every week on Thursdays at 5:00 PM (CET) (published once a week by IAB Europe). {{strong}}In case of any changes in the texts or information of the activated vendors, Real Cookie Banner will automatically obtain new consent from all your visitors.{{/strong}}"),{strong:(0,s.jsx)("strong",{}),a:(0,s.jsx)("a",{href:t("https://iabeurope.eu/vendor-list/"),rel:"noreferrer",target:"_blank"})})})]})]})}})]})},ie=()=>{const{__:e,_i:t}=(0,p.s)(),{isPro:n}=(0,V.J)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.A.Item,{label:e("Google Consent Mode"),style:{marginBottom:10},children:[(0,s.jsx)(i.A.Item,{name:"isGcm",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{disabled:!n,value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{disabled:!n,value:!1,children:e("Disabled")})]})}),(0,s.jsxs)("div",{style:{margin:"5px 0px 0px"},children:[(0,s.jsx)(z.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-google-consent-mode-setup/")}),(0,s.jsx)(Y.O,{title:e("Enable Google Consent Mode?"),testDrive:!0,feature:"gcm",assetName:e("pro-modal/gcm.png"),description:e("Google Consent Mode is a standard from Google for the standardized signaling of consent purposes. This is particularly important in order to be able to collect and evaluate data collected in Google services more comprehensively.")})]}),(0,s.jsx)("p",{className:"description",children:e("Google Consent Mode provides standardized consent for Google services to Google. This allows you e.g. to instruct Google not to set cookies for certain services, but still to collect basic data about your website visitors using other technologies. According to Google, only aggregated and anonymized data will be collected.")})]}),(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isGcm!==t.isGcm||e.setCookiesViaManager!==t.setCookiesViaManager,children:o=>{let{getFieldValue:a,setFieldsValue:r}=o;const l=a("isGcm"),c=a("setCookiesViaManager");return(0,s.jsxs)(s.Fragment,{children:[!l&&new Date<=new Date("2024-05-30")&&(0,s.jsx)(i.A.Item,{wrapperCol:{offset:6},style:{marginBottom:10},children:(0,s.jsx)("div",{className:"notice notice-info inline below-h2 notice-alt",style:{margin:0},children:(0,s.jsx)("p",{children:t(e("As of March 2024, Google Consent Mode will be required as Google's response to the {{aDma}}Digital Markets Act (DMA){{/aDma}} in order to continue collecting complete data in e.g. Google Analytics, Remarketing for Google Ads or Google AdSense."),{aDma:(0,s.jsx)("a",{href:e("https://eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX:32019R1150"),target:"_blank",rel:"noreferrer"})})})})}),n&&l&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.A.Item,{label:e("Google Tag Manager integration"),required:!0,children:[(0,s.jsxs)(B.Ay.Group,{value:"googleTagManagerWithGcm"===c,onChange:e=>{e.target.value&&"googleTagManager"===c||r({setCookiesViaManager:e.target.value?"googleTagManagerWithGcm":"none"})},children:["googleTagManager"===c?(0,s.jsx)(b.A,{title:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{style:{marginTop:0},children:e("By activating this Google Tag Manager integration, consents will be transferred to Google Tag Manager via Google Consent Mode from now on. Please note that you have previously transferred consents to Google Tag Manager via Google Tag Manager Events. The Google Tag Manager events will no longer be transmitted after changing this setting.")}),(0,s.jsx)("p",{children:t(e("You must therefore {{a}}reconfigure the settings in your Google Tag Manager for Google Consent Mode{{/a}}!"),{a:(0,s.jsx)("a",{href:e("https://devowl.io/knowledge-base/real-cookie-banner-with-google-tag-manager-google-consent-mode/"),target:"_blank",rel:"noreferrer"})})})]}),cancelText:e("Cancel"),okText:e("Activate now!"),overlayStyle:{maxWidth:450},onConfirm:()=>{r({setCookiesViaManager:"googleTagManagerWithGcm"})},placement:"bottomLeft",children:(0,s.jsx)(B.Ay.Button,{value:!0,children:e("Enabled")})}):(0,s.jsx)(B.Ay.Button,{value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{value:!1,children:e("Disabled")})]}),(0,s.jsx)("div",{style:{margin:"5px 0px 0px"},children:(0,s.jsx)(z.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-with-google-tag-manager-google-consent-mode/")})}),(0,s.jsx)("p",{className:"description",children:e('Do you use Google Tag Manager? If yes, please activate this option to transfer consents for all services that you obtain in your cookie banner as "Additional consent" via Google Consent Mode to the consent management of Google Tag Manager.')})]}),(0,s.jsxs)(i.A.Item,{label:e("Show recommendations for using Google services without consent"),required:!0,children:[(0,s.jsx)(i.A.Item,{noStyle:!0,name:"isGcmShowRecommandationsWithoutConsent",rules:[{type:"boolean",required:!0,message:e("Please choose an option!")}],children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{value:!1,children:e("Disabled")})]})}),(0,s.jsx)("div",{style:{margin:"5px 0px 0px"},children:(0,s.jsx)(z.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-google-consent-mode-setup/#data-protection-criticism-of-google-consent-mode")})}),(0,s.jsx)("p",{className:"description",children:t(e("At its core, Google Consent Mode wants to collect more data about website visitors for you, for which Google services should also be loaded before consent has been given. In this case, Google for example prevents the setting of cookies. However, the IP address of your website operator is already transmitted to Google. If the IP address is regarded as personal data (usually common legal interpretation), this requires a legal basis in accordance with {{aGdpr}}Art. 6 GDPR{{/aGdpr}}. Apart from consent, only legitimate interest comes into question, which must be viewed critically in this case in terms of data protection law. First and foremost, you as the website operator and not Google are liable for possible data protection violations of this kind. By activating this option, Real Cookie Banner will show you as website operator, recommendations for the configuration of services which, in our legal opinion, lead to data protection violations, but which are in the interest of using Google Consent Mode. Google Consent Mode can also be used if the services are only loaded with consent."),{aGdpr:(0,s.jsx)("a",{href:e("https://gdpr-text.com/read/article-6/"),target:"_blank",rel:"noreferrer"})})})]}),(0,s.jsxs)(i.A.Item,{label:e("Collect additional data via URL parameters"),children:[(0,s.jsx)(i.A.Item,{noStyle:!0,name:"isGcmCollectAdditionalDataViaUrlParameters",children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{value:!1,children:e("Disabled")})]})}),(0,s.jsx)("div",{style:{margin:"5px 0px 0px"},children:(0,s.jsx)(z.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-google-consent-mode-setup/#collect-additional-data-via-url-parameters")})}),(0,s.jsx)("p",{className:"description",children:t(e("If consent is not given for the {{code}}ad_storage{{/code}} and/or {{code}}analytics_storage{{/code}} consnt types (purposes) of Google Consent Mode, this option can be used to transfer data that is typically stored in cookies as GET parameters in internal links. This is particularly relevant if you place advertisements in Google Ads and conversion tracking should also work without consent. Please note that data can only be collected if you download the respective Google services before obtaining consent. As client IDs can be transmitted, among other things, which can be regarded as personal data, we consider this data transmission to be questionable in terms of data protection law."),{code:(0,s.jsx)("code",{})})})]}),(0,s.jsxs)(i.A.Item,{label:e("Redact ads data without consent"),children:[(0,s.jsx)(i.A.Item,{noStyle:!0,name:"isGcmRedactAdsDataWithoutConsent",children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{value:!1,children:e("Disabled")})]})}),(0,s.jsx)("div",{style:{margin:"5px 0px 0px"},children:(0,s.jsx)(z.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-google-consent-mode-setup/#redact-ads-data-without-consent")})}),(0,s.jsx)("p",{className:"description",children:t(e("If consent is not given for the {{code}}ad_storage{{/code}} consent type (purpose) of Google Consent Mode, no new cookies will be set for advertising purposes, except to fight fraud and spam. In addition, this option allows measurements for clicks on advertising to be routed through the domain {{code}}googleadsyndication.com{{/code}} instead of {{code}}doubleclick.net{{/code}} or {{code}}google.com{{/code}}, on which no cookies are set for advertising, and identifiers for clicks and page URLs are removed from advertising links. This enables more data protection-friendly conversion tracking with the simultaneous loss of detailed information on the attribution of target achievement."),{code:(0,s.jsx)("code",{})})})]}),(0,s.jsxs)(i.A.Item,{label:e("Naming of requested consent types in first view"),children:[(0,s.jsx)(i.A.Item,{noStyle:!0,name:"isGcmListPurposes",children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{value:!0,children:e("Enabled")}),(0,s.jsx)(B.Ay.Button,{value:!1,children:e("Disabled")})]})}),(0,s.jsx)("div",{style:{margin:"5px 0px 0px"},children:(0,s.jsx)(z.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-google-consent-mode-setup/#naming-of-requested-consent-types-in-first-view")})}),(0,s.jsx)("p",{className:"description",children:e("The visitor to your website should be adequately informed as to which consent types (purposes) they are consenting to in accordance with Google Consent Mode. For this reason, all consent types should be listed by name in the first view of the cookie banner. This makes it easier for your website visitor to assess whether they really want to agree to all consents without visiting the individual privacy settings. ")})]})]})]})}})]})};var re=n(28101),le=n(53810);const ce=()=>{const{message:e}=o.A.useApp(),{__:t,_i:n}=(0,p.s)(),{isPro:a}=(0,V.J)(),{isTcf:r,multisiteEndpoints:{current:l,forwardTo:c},fetchMultisiteEndpoints:h}=x(),[u,m]=(0,d.useState)(!1);(0,le.Z)((async()=>{m(!0);try{await h()}catch(t){throw e.error(t.responseJSON.message),t}finally{m(!1)}}),[]);const g=(0,d.useCallback)((n=>((0,R.l)(n.target.parentElement.previousElementSibling.value),e.success(t("Successfully copied to the clipboard!")),n.preventDefault(),!1)),[]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.A.Item,{label:t("Consent Forwarding"),children:[(0,s.jsx)(i.A.Item,{name:"isConsentForwarding",noStyle:!0,children:(0,s.jsxs)(B.Ay.Group,{children:[(0,s.jsx)(B.Ay.Button,{disabled:!a,value:!0,children:t("Enabled")}),(0,s.jsx)(B.Ay.Button,{disabled:!a,value:!1,children:t("Disabled")})]})}),(0,s.jsxs)("div",{style:{margin:"5px 0px 0px"},children:[(0,s.jsx)(z.Y,{url:t("https://devowl.io/knowledge-base/cookie-banner-consent-forwarding-setup/")}),(0,s.jsx)(Y.O,{title:t("One consent for all your websites?"),testDrive:!0,feature:"consent-forwarding",assetName:t("pro-modal/consent-forwarding.png"),description:t("In WordPress multisites or multiple instances of WordPress running for one organization, you only need to obtain consent once. Consents are automatically forwarded, so your visitor only needs to accept services and cookies once, instead of on each site again.")})]}),(0,s.jsx)("p",{className:"description",children:t("You can ask the user for consent on one site, and the consent applies to other sites in a WordPress multisite installation or other standalone WordPress installations. This is useful if you run multiple WordPress sites for one organization. For example, you have a company page, landing page, and blog, and you want to show the user as few cookie banners as possible.")})]}),(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isConsentForwarding!==t.isConsentForwarding,children:e=>{let{getFieldValue:o}=e;const d=o("isConsentForwarding");return a&&d&&(0,s.jsxs)(s.Fragment,{children:[r&&(0,s.jsx)(i.A.Item,{wrapperCol:{offset:6},children:(0,s.jsx)("div",{className:"notice notice-info inline below-h2 notice-alt",style:{margin:0},children:(0,s.jsx)("p",{children:t("Please note that in all sites that are provided with consent forwarding, you must use exactly the same TCF vendors with the same settings (vendor restrictions). If there are any discrepancies, the consent in the TCF standardized consent (TC string) will overwrite each other according to its specification.")})})}),(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>JSON.stringify(e.forwardTo)!==JSON.stringify(t.forwardTo)||JSON.stringify(e.crossDomains)!==JSON.stringify(t.crossDomains),children:e=>{let{getFieldValue:o}=e;return(JSON.stringify(o("forwardTo"))+JSON.stringify(o("crossDomains"))).indexOf("http://")>-1&&(0,s.jsx)(i.A.Item,{wrapperCol:{offset:6},children:(0,s.jsx)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:{margin:0},children:(0,s.jsx)("p",{children:n(t("One of your configured endpoints is using an insecure protocol (not HTTPS). For security reasons, most modern browsers reject and block the use of cross-domain cookies on unsecured pages. {{a}}Learn more.{{/a}}"),{a:(0,s.jsx)("a",{href:t("https://www.chromestatus.com/feature/5633521622188032"),rel:"noreferrer",target:"_blank"})})})})})}}),(0,s.jsxs)(i.A.Item,{label:t("Forward to"),children:[(0,s.jsx)(i.A.Item,{name:"forwardTo",noStyle:!0,children:(0,s.jsx)(C.A,{mode:"multiple",notFoundContent:(0,s.jsx)(re.A,{description:t("No additional sites endpoints found.")}),filterOption:!1,loading:u,children:Object.keys(c).map((e=>(0,s.jsx)(C.A.Option,{value:e,children:c[e]},e)))})}),(0,s.jsx)("p",{className:"description",children:t("Select all sites to which the consent of the user on the current site should be forwarded.")})]}),(0,s.jsxs)(i.A.Item,{label:t("External 'Forward To' endpoints"),children:[(0,s.jsx)(i.A.Item,{name:"crossDomains",noStyle:!0,children:(0,s.jsx)(A.A.TextArea,{autoSize:{minRows:3}})}),(0,s.jsx)("p",{className:"description",children:t("This option is only required if you want to forward consent to another WordPress installation. Please enter one URL endpoint of the Real Cookie Banner WP REST API per line. Below you find a list of all available endpoints of this WordPress installation.")}),Object.keys(l).map((e=>(0,s.jsxs)("p",{children:[(0,s.jsx)("label",{children:l[e]}),(0,s.jsx)(A.A,{value:e,readOnly:!0,addonAfter:(0,s.jsx)("button",{className:"button-link alignright",onClick:g,children:t("Copy to clipboard")})})]},e)))]})]})}})]})};var de=n(19991);const he=()=>{const{__:e}=(0,p.s)(),{onResetAll:t,onResetTexts:n,resetTextsLanguages:o}=x(),[a,r]=(0,d.useState)(!1),l=(0,d.useMemo)((()=>o.filter((e=>e.isRequired)).map((e=>e.code))),[o]),[c,h]=(0,d.useState)(l);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.A.Item,{label:e("Reset texts"),children:[(0,s.jsxs)("p",{className:"description",style:{marginTop:6},children:[e("You can reset all texts in your cookie banner to their default values. This includes texts in the customizer (customize banner), service groups, services (if created from the service template), content blockers (if created from the content blocker template) and the cookie policy (if created)."),o.length>1&&(0,s.jsxs)(s.Fragment,{children:[" ",e("You can select multiple languages for which you want to reset the texts.")]})]}),1!==o.length&&(0,s.jsxs)(k.A,{direction:"vertical",children:[(0,s.jsxs)(k.A,{direction:"horizontal",children:[(0,s.jsx)("a",{href:"#",onClick:e=>{e.preventDefault(),h(o.map((e=>e.code)))},children:e("Select all")}),(0,s.jsx)(de.A,{type:"vertical"}),(0,s.jsx)("a",{href:"#",onClick:e=>{e.preventDefault(),h(l)},children:e("Deselect all")})]}),(0,s.jsx)(T.A.Group,{options:o.map((e=>{let{code:t,name:n,isDisabled:o,isRequired:a,notice:i}=e;return{label:i?(0,s.jsxs)(I.A,{title:i,children:[n," ",(0,s.jsx)(L.A,{})]}):n,value:t,disabled:o||a}})),value:c,onChange:e=>h(e)})]}),(0,s.jsx)("div",{style:{marginTop:10},children:(0,s.jsx)(b.A,{title:e("Are you sure?"),cancelText:e("Cancel"),okText:e("Reset texts"),onConfirm:()=>n(c),placement:"topLeft",children:(0,s.jsx)("input",{type:"button",className:"button",value:e("Reset")})})})]}),(0,s.jsxs)(i.A.Item,{label:e("Reset all"),children:[(0,s.jsx)("p",{className:"description",style:{marginTop:6},children:e("You can reset all settings of Real Cookie Banner to the default values. This will also irreversibly delete any services and content blockers that have been created, as well as design settings. Documentation of consent may be kept to fulfill the legal disclosure requirement.")}),(0,s.jsx)(T.A,{checked:a,onChange:e=>r(e.target.checked),children:e("Delete documented consent")}),(0,s.jsx)("div",{style:{marginTop:10},children:(0,s.jsx)(b.A,{title:e("Are you sure?"),cancelText:e("Cancel"),okText:e("Reset all"),onOpenChange:e=>!e&&r(!1),onConfirm:()=>t(a),placement:"topLeft",children:(0,s.jsx)("input",{type:"button",className:"button",value:e("Reset")})})})]})]})};var ue=n(68789),pe=n(76576),me=n(36069),ge=n(71951),ye=n(30617),fe=n(42090),xe=n(98036),be=n(12975),ve=n(636),je=n(68588),we=n(40164),Ae=n(89657);const ke={labelCol:{span:6},wrapperCol:{span:16}},Ce=(0,c.PA)((()=>{const{message:e}=o.A.useApp(),t=(0,h.g)().tab||"",n=(0,h.Zp)(),{tcfIntegrationItem:c,navigateAfterTcfActivation:x}=(0,pe.f)(),{link:b}=(0,me.E)(),{optionStore:v,checklistStore:j,tcfStore:w}=(0,ge.g)(),A=m().map((e=>{let{multiple:t,name:n}=e;return!t&&n})).filter(Boolean),k=(0,d.useMemo)((()=>{var e;return!1===(null==(e=j.items.filter((e=>{let{id:t}=e;return"save-settings"===t}))[0])?void 0:e.checked)}),[j]),{fetchedBannerLinks:C,busySettings:T,areSettingsFetched:I,isBannerActive:B,isBlockerActive:P,hidePageIds:S,setCookiesViaManager:D,operatorCountry:G,operatorContactAddress:L,operatorContactEmail:N,operatorContactPhone:E,operatorContactFormId:F,cookiePolicyId:R,territorialLegalBasis:_,isAcceptAllForBots:O,isRespectDoNotTrack:M,isBannerLessConsent:U,bannerLessConsentShowOnPageIds:V,cookieDuration:z,failedConsentDocumentationHandling:Y,isSaveIp:q,isDataProcessingInUnsafeCountries:W,isAgeNotice:J,ageNoticeAgeLimit:Q,isListServicesNotice:Z,isConsentForwarding:K,forwardTo:X,crossDomains:ee,affiliateLink:te,affiliateLabelBehind:oe,affiliateLabelDescription:re,isCountryBypass:le,countryBypassCountries:de,countryBypassType:Ce,isTcf:Te,tcfAcceptedTime:Ie,tcfGvlDownloadTime:Be,isGcm:Pe,isGcmShowRecommandationsWithoutConsent:Se,isGcmCollectAdditionalDataViaUrlParameters:De,isGcmRedactAdsDataWithoutConsent:Ge,isGcmListPurposes:Le,consentDuration:Ne,consentsDeletedAt:Ee,bannerlessConsentChecks:Fe,countryBypassDbDownloadTime:Re,others:{customizeValuesBanner:{texts:{ageNoticeBanner:_e}},iso3166OneAlpha2:Oe,ageNoticeCountryAgeMap:Me,predefinedCountryBypassLists:Ue,frontend:{predefinedDataProcessingInSafeCountriesLists:Ve},customizeBannerUrl:ze,adminUrl:Ye,resetUrl:qe,resetTexts:{dry:We,url:He}}}=v,{prompt:$e,form:Je,isBusy:Qe,defaultValues:Ze,onFinish:Ke,onFinishFailed:Xe,onValuesChange:et,contextValue:tt}=function(e){const{__:t}=(0,p.s)(),{isTcf:n,isGcm:s,isCountryBypass:o}=e,a=m(),i={isBannerActive:!1,isBlockerActive:!1,operatorContactAddress:"",operatorContactEmail:"",operatorContactFormId:0,operatorContactPhone:"",cookiePolicyId:0,territorialLegalBasis:["gdpr-eprivacy"],hidePageIds:[],navLinks:[],setCookiesViaManager:"none",isAcceptAllForBots:!0,isRespectDoNotTrack:!1,cookieDuration:365,isDataProcessingInUnsafeCountries:!1,isAgeNotice:!0,isBannerLessConsent:!1,bannerLessConsentShowOnPageIds:[],ageNoticeAgeLimit:"INHERIT",isListServicesNotice:!0,failedConsentDocumentationHandling:"essentials",isSaveIp:!1,consentDuration:120,isCountryBypass:o,countryBypassCheckboxTerms:!!o,countryBypassCheckboxAccuracy:!!o,countryBypassCountries:[],countryBypassType:"all",isTcf:n,operatorCountry:"",tcfCheckboxTerms:!!n,tcfCheckboxVisually:!!n,isGcm:s,isGcmShowRecommandationsWithoutConsent:!1,isGcmCollectAdditionalDataViaUrlParameters:!1,isGcmRedactAdsDataWithoutConsent:!0,isGcmListPurposes:!0,isConsentForwarding:!1,forwardTo:[],crossDomains:"",affiliateLink:"",affiliateLabelBehind:"",affiliateLabelDescription:""},r=(0,g.S)({...e,defaultValues:i,i18n:{successMessage:t("Successfully updated settings!"),validationError:t("The settings could not be saved due to missing/invalid form values."),unloadConfirm:t("You have unsaved changes. If you leave this page, your changes will be discarded.")}}),{form:l}=r;let c;const h=(0,d.useCallback)(((e,t)=>{r.onValuesChange(e,t);const{navLinks:n,isGcm:s}=e;if((null==n?void 0:n.length)>0)for(const e in n){const t=n[e];(null==t?void 0:t.pageType)&&"other"!==t.pageType&&!Object.hasOwn(t,"label")&&setTimeout((()=>r.form.setFieldValue(["navLinks",+e,"label"],a.find((t=>{let{name:s}=t;return s===n[e].pageType})).label)),50)}!1===s&&(c=l.getFieldValue("setCookiesViaManager"),setTimeout((()=>{r.form.setFieldsValue({setCookiesViaManager:"none"})}),50)),!0===s&&"googleTagManagerWithGcm"===c&&(c=void 0,setTimeout((()=>{r.form.setFieldsValue({setCookiesViaManager:"googleTagManagerWithGcm"})}),50))}),[r.onValuesChange,r.form]),u=(0,d.useCallback)((async e=>{r.onFinish(e),e.isTcf||l.setFieldsValue({tcfCheckboxTerms:!1,tcfCheckboxVisually:!1}),e.isCountryBypass||l.setFieldsValue({countryBypassCheckboxTerms:!1,countryBypassCheckboxAccuracy:!1})}),[r.onFinish,l]);return{...r,onFinish:u,onValuesChange:h,defaultValues:i,contextValue:{isCountryBypass:o,isTcf:n,isGcm:s}}}({isCountryBypass:le,isTcf:Te,isGcm:Pe,unloadPromptWhen:e=>{let{nextLocation:{pathname:t}}=e;return!t.startsWith("/settings")},handleSave:async e=>{if(await v.updateSettings({...rt,...e},e.navLinks),k){const e=["","consent","country-bypass"],s=e.indexOf(t);s>-1&&e[s+1]&&n(`/settings/${e[s+1]}`)}x&&v.tcfGvlDownloadTime&&(window.location.href=x)}}),nt=(0,d.useCallback)(((e,t)=>{Object.hasOwn(e,"operatorCountry")&&"CH"===t.operatorCountry&&Je.setFieldValue("territorialLegalBasis",["gdpr-eprivacy","dsg-switzerland"]),et(e,t)}),[et]);(0,d.useEffect)((()=>{v.fetchBannerLinks()}),[]);const st=(0,d.useCallback)((e=>{let{selectedPageContent:t,selectedPageUrl:n,onlyPages:o,...a}=e;return(0,s.jsx)(je.D,{...a,postType:o?["page"]:void 0,forceDefaultLanguage:!0,applyTitleRenderOnSelectOption:!0,titleRender:(e,s)=>{const{id:o,title:{rendered:a},content:{rendered:i},link:r}=e||{id:0,title:{rendered:void 0},content:{rendered:""}};return t&&(s.indexOf(o)>-1?t(i):0===s.length&&t(void 0)),n&&n(s.indexOf(o)>-1?r:void 0),a},perPage:100})}),[]),ot=(0,d.useCallback)((t=>t.map(((n,s)=>{let{data:{id:o,title:{rendered:a},meta:{pageType:i,isExternalUrl:r,pageId:c,externalUrl:d,hideCookieBanner:h,isTargetBlank:p},multilingual:m}}=n;return{id:o,pageType:A.indexOf(i)>-1&&t.some(((e,t)=>{let{data:{meta:{pageType:n}}}=e;return t<s&&i===n}))?"other":i,isExternalUrl:r,pageId:c,externalUrl:d,hideCookieBanner:h,isTargetBlank:p,label:a,languages:(0,l.toJS)(m),languageOnClick:async(t,n)=>{let{code:s,id:o}=n;try{!1===o&&await(0,u.C)("rcb-banner-links",t,s);const e=new URL(window.location.href);e.searchParams.set("lang",s),window.location.href=e.toString()}catch(t){var a;if(!(null==(a=t.responseJSON)?void 0:a.message))throw t;e.error(t.responseJSON.message)}}}}))),[]),[at,it]=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,y.gm)(f,...t)}({...tt,onPageEditClick:e=>window.open(`${Ye}post.php?post=${e}&action=edit`,"_blank"),activeTab:t,resetTextsLanguages:We,onResetTexts:e=>{window.location.href=`${He}&${e.map((e=>`reset-lang[]=${e}`)).join("&")}`},onResetAll:e=>{window.location.href=`${qe}&${e?"&reset-consents":""}`},onStartTcfVendorConfiguration:()=>{window.location.href=b},renderPageSelector:st,onHasPrivacyPolicyRCBSentenceChanged:()=>{j.probablyFetchByChangedItem("privacy-policy-mention-usage")},consentsDeletedAt:Ee,iso3166OneAlpha2:Oe,ageNotice:_e,ageNoticeCountryAgeMap:Me,predefinedCountryBypassLists:Ue,predefinedDataProcessingInSafeCountriesLists:Ve,onCustomizeClick:e=>window.open(`${ze.replace(/autofocus\[panel]=[\w-]+/,`autofocus[control]=${e}`)}&return=${encodeURIComponent(window.location.href)}`,"_blank"),countryBypassDbDownloadTime:Re,tcfAcceptedTime:Ie,tcfGvlDownloadTime:Be,multisiteEndpoints:{current:{},forwardTo:{}},bannerlessConsentChecks:Fe},{onCountryBypassUpdateDatabase:()=>v.updateCountryBypassDatabase(),onTcfUpdateDatabase:()=>w.updateGvl(),fetchMultisiteEndpoints:async e=>{let{set:t}=e;const[n,s]=await Promise.all([(0,fe.E)({location:be.z,params:{filter:"onlyCurrent"}}),(0,fe.E)({location:be.z,params:{filter:"notCurrent"}})]);t({multisiteEndpoints:{current:n,forwardTo:s}})},createCookiePolicy:async()=>{const{id:e}=await(0,fe.E)({location:xe.A});return{pageId:e,onNewNavLinks:e=>v.updateSettings({},e)}},deactivateCookiePolicy:async(e,t)=>(await(0,fe.E)({location:{path:`/pages/${t}`,method:ue.RouteHttpVerb.DELETE,namespace:"wp/v2"},request:{force:!0}}),{onNewNavLinks:e=>v.updateSettings({},e)})},{inherit:["isCountryBypass","isGcm","isTcf","tcfGvlDownloadTime","countryBypassDbDownloadTime","activeTab"]});if(!C||!I)return(0,s.jsx)(a.A,{spinning:!0,style:ve.oO});const rt={...Ze,isBannerActive:B,isBlockerActive:P,hidePageIds:[...S],setCookiesViaManager:D,operatorCountry:G,operatorContactAddress:L,operatorContactEmail:N,operatorContactPhone:E,operatorContactFormId:F,cookiePolicyId:R,territorialLegalBasis:_,isAcceptAllForBots:O,isRespectDoNotTrack:M,isBannerLessConsent:U,bannerLessConsentShowOnPageIds:[...V],cookieDuration:z,failedConsentDocumentationHandling:Y,isSaveIp:q,isDataProcessingInUnsafeCountries:W,isAgeNotice:J,ageNoticeAgeLimit:Q,isListServicesNotice:Z,isGcmShowRecommandationsWithoutConsent:Se,isGcmCollectAdditionalDataViaUrlParameters:De,isGcmRedactAdsDataWithoutConsent:Ge,isGcmListPurposes:Le,isConsentForwarding:K,forwardTo:[...X],crossDomains:ee,affiliateLink:te,affiliateLabelBehind:oe,affiliateLabelDescription:re,countryBypassCountries:[...de],countryBypassType:Ce,consentDuration:Ne,navLinks:ot(v.bannerLinks.sortedBannerLinks)};return(0,s.jsx)(at,{value:it,children:(0,s.jsxs)(a.A,{spinning:T||Qe,children:[$e,(0,s.jsx)(i.A,{name:"settings",initialValues:rt,form:Je,...ke,onFinish:Ke,onValuesChange:nt,onFinishFailed:Xe,scrollToFirstError:{behavior:"smooth",block:"center"},labelWrap:!0,children:(0,s.jsx)(i.A.Item,{noStyle:!0,labelCol:{span:0},wrapperCol:{span:24},children:(0,s.jsx)(r.A,{activeKey:t,onTabClick:e=>{n(`/settings/${e}`)},items:[{key:"",label:(0,ye.__)("General"),children:(0,s.jsxs)(we.e,{maxWidth:"fixed",children:[(0,s.jsx)(H,{}),(0,s.jsx)($,{})]})},{key:"consent",label:(0,ye.__)("Consent"),children:(0,s.jsxs)(we.e,{maxWidth:"fixed",children:[(0,s.jsx)(ne,{}),(0,s.jsx)($,{})]})},{key:"country-bypass",label:(0,ye.__)("Geo-restriction"),children:(0,s.jsxs)(we.e,{maxWidth:"fixed",children:[(0,s.jsx)(se,{}),(0,s.jsx)($,{})]})},{key:"tcf",label:(0,ye.__)("Transparency & Consent Framework (TCF)"),children:(0,s.jsxs)(we.e,{maxWidth:"fixed",children:[(0,s.jsx)(ae,{}),(0,s.jsx)($,{})]})},{key:"gcm",label:(0,ye.__)("Google Consent Mode"),children:(0,s.jsxs)(we.e,{maxWidth:"fixed",children:[(0,s.jsx)(ie,{}),(0,s.jsx)($,{})]})},{key:"multisite",label:(0,ye.__)("Consent Forwarding"),children:(0,s.jsxs)(we.e,{maxWidth:"fixed",children:[(0,s.jsx)(ce,{}),(0,s.jsx)($,{})]})},!1,"reset"===t&&{key:"reset",label:(0,ye.__)("Reset"),children:(0,s.jsx)(he,{})}].filter(Boolean)})})}),c&&(0,s.jsx)(Ae.b,{always:!0,identifier:"tcf-integration",title:(0,ye.__)("Consent required according to TCF"),sprintfArgs:[c]})]})})}))},68588:(e,t,n)=>{n.d(t,{D:()=>g});var s=n(3713),o=n(6196),a=n(18197),i=n(51192),r=n(41594),l=n(24513),c=n(84200);function d(e){var t;return(0,c.g)(e)&&!(0,l.j)(e)?null==(t=(new DOMParser).parseFromString(`<a href="${e}"></a>`,"text/html").querySelector("a"))?void 0:t.href:(new DOMParser).parseFromString(e,"text/html").documentElement.textContent}var h=n(68789),u=n(79521),p=n(30617),m=n(42090);const g=e=>{let{postType:t,postStatus:n=["draft","publish","private"],perPage:l=10,value:c,multiple:g,disabled:y,forceDefaultLanguage:f,onChange:x,titleRender:b=(e=>null==e?void 0:e.title.rendered),applyTitleRenderOnSelectOption:v,filter:j=(()=>!0)}=e;const[w,A]=(0,r.useState)(!1),[k,C]=(0,r.useState)(c),[T,I]=(0,r.useState)(!1),[B,P]=(0,r.useState)([]),S=(0,r.useCallback)((async(e,s)=>{void 0===s&&(s=!0),I(!0);const{defaultLanguage:o,currentLanguage:a}=(0,u.j)(),i=(await(0,m.E)({location:{path:"/search",method:h.RouteHttpVerb.GET,namespace:"wp/v2"},request:{status:e.include?["draft","publish","private"]:n,...t?{subtype:t.join(",")}:{},...e},params:{_dataLocale:f?o:a,_embed:"self",_rcbExtendSearchResult:!0}})).map((e=>{let{_embedded:{self:[t]}}=e;return{content:{rendered:"",raw:""},...t}}));return s&&P(i),I(!1),i}),[]);!function(e,t,n,s){const[o,a]=(0,r.useState)(e);(0,r.useEffect)((()=>{const n=setTimeout((()=>{a(e)}),t);return null==s||s(e),()=>{clearTimeout(n)}}),[e]),(0,r.useEffect)((()=>{var e;!1!==(e=o)&&S({search:e,per_page:e.length?50:l})}),[o])}(w,""===w?0:800,0,(e=>{!1!==e&&I(!0),P([])})),(0,r.useEffect)((()=>{const e=e=>(void 0===e&&(e=!0),"number"==typeof k&&k>0||Array.isArray(k)&&k.length>0?S({include:Array.isArray(k)?k:[k]},e):Promise.resolve([])),t=async()=>{if("visible"===document.visibilityState){const[t]=await e(!1);t&&P((e=>e.map((e=>e.id===t.id?t:e))))}};return e(),document.addEventListener("visibilitychange",t),()=>{document.removeEventListener("visibilitychange",t)}}),[]),(0,r.useEffect)((()=>{JSON.stringify(k)!==JSON.stringify(c)&&("number"==typeof c&&c>0||Array.isArray(c)&&c.length>0)&&(C(c),S({include:Array.isArray(c)?c:[c]}))}),[c,k]);const D=Array.isArray(k)?k:[k].filter(Boolean),G=`— ${(0,p.__)("Select")} —`;return(0,s.jsxs)(o.A,{mode:g?"multiple":void 0,disabled:y,showSearch:!0,value:k,placeholder:(0,p.__)("Search..."),notFoundContent:T?(0,s.jsx)(a.A,{size:"small"}):null,onClick:()=>A(""),onSearch:A,onChange:e=>{const t=Array.isArray(e)?e.map(Number):+e;C(t),null==x||x(t)},filterOption:!1,loading:T,labelRender:e=>{let{label:t}=e;return(0,r.isValidElement)(t)?t.props["data-label"]:t},children:[!g&&!T&&(0,s.jsx)(o.A.Option,{value:0,children:d(v&&b(void 0,D)||G)}),B.map((e=>{const n=d(b(e,D)),a=e.type_singular&&((null==t?void 0:t.length)>1||!t)?e.type_singular:void 0;return(0,s.jsx)(o.A.Option,{value:e.id,style:{display:j(e)?void 0:"none"},children:(0,s.jsxs)(i.A,{justify:"space-between",align:"center","data-label":`${n}${a?` (${a})`:""}`,children:[(0,s.jsx)("span",{children:n}),a&&(0,s.jsx)("span",{style:{opacity:.7,paddingLeft:5,paddingRight:5},children:a})]})},e.id)}))]})}},76576:(e,t,n)=>{n.d(t,{f:()=>a});var s=n(27667),o=n(68789);function a(){return o.qs.parse((0,s.zy)().search)}},36069:(e,t,n)=>{n.d(t,{E:()=>r});var s=n(41594),o=n(27667),a=n(8140),i=n(71951);const r=()=>{const{tcfStore:e}=(0,i.g)(),t=+(0,o.g)().vendorConfiguration,n=isNaN(+t)?0:+t,r=!!t,l=e.vendorConfigurations.entries.get(n)||new a.p(e.vendorConfigurations,{id:0}),c=(0,s.useCallback)((e=>{let{key:t}=e;return`#/cookies/tcf-vendors/edit/${t}`}),[l]);return{vendorConfiguration:l,id:n,queried:r,fetched:0!==l.key,link:"#/cookies/tcf-vendors",editLink:c,addLink:"#/cookies/tcf-vendors/new"}}},52066:(e,t,n)=>{n.d(t,{A:()=>l});var s=n(2464),o=n(41594);const a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"};var i=n(4679),r=function(e,t){return o.createElement(i.A,(0,s.A)({},e,{ref:t,icon:a}))};const l=o.forwardRef(r)},53573:(e,t,n)=>{n.d(t,{A:()=>l});var s=n(2464),o=n(41594);const a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var i=n(4679),r=function(e,t){return o.createElement(i.A,(0,s.A)({},e,{ref:t,icon:a}))};const l=o.forwardRef(r)}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/e87c852cf9dc9bbb10243da87d5fdd59/chunk-config-tab-settings.lite.js.map
