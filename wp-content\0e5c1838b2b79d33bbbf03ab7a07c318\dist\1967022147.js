"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[40],{968:(e,t,n)=>{function i(e,t,n){void 0===n&&(n=0);const i=[];let o=e.parentElement;const r=void 0!==t;let a=0;for(;null!==o;){const s=o.nodeType===Node.ELEMENT_NODE;if(0===a&&1===n&&s&&r){const n=e.closest(t);return n?[n]:[]}if((!r||s&&o.matches(t))&&i.push(o),o=o.parentElement,0!==n&&i.length>=n)break;a++}return i}n.d(t,{M:()=>i})},1877:(e,t,n)=>{n.r(t),n.d(t,{WebsiteBanner:()=>He});var i=n(6425),o=n(7936),r=n(7177),a=n(5285),s=n(72),c=n(998),l=n(8664),d=n(6399),u=n(151),h=n(1685),p=n.n(h),m=n(7114),g=n(5914);const v=[Symbol("extendBannerContentStylesheet"),(e,t)=>{let{boolIf:n,boolSwitch:i,boolOr:o,computed:r,boolNot:a,jsx:s,variable:c}=e,{dimsOverlay:l,dimsHeader:d,dimsFooter:u,dimsRightSidebar:h,boolLargeOrMobile:p,isMobile:m,isBanner:v,design:f,bodyDesign:y,headerDesign:b,layout:w,decision:C,mobile:x,texts:k,activeAction:O,footerDesign:D,individualLayout:P,individualPrivacyOpen:S,footerBorderStyle:A,headerBorderStyle:Y}=t;const I=r([b.logo,b.logoRetina,b.logoFitDim,b.logoRetinaFitDim,b.logoMaxHeight],(e=>{let[t,n,i,o,r]=e;const a=n&&!(null==t?void 0:t.endsWith(".svg"))&&window.devicePixelRatio>1?o:i;return(null==a?void 0:a[0])>0?{width:(0,g.dD)(a[0]),height:(0,g.dD)(a[1])}:{width:"auto",height:(0,g.dD)(r)}})),B=n({when:v,then:{when:[S,a(P.inheritBannerMaxWidth)],then:P.bannerMaxWidth(),or:w.bannerMaxWidth()}}),N=p(b.borderWidth,n),[E]=s("div",{classNames:"header-container",position:"sticky",zIndex:9,top:0,background:n(b.inheritBg,f.bg(),b.bg()),padding:p(b.padding,n),paddingBottom:`calc(${N} + ${p(b.padding,n,2)})`,...Y,pseudos:{":has(>div:empty)":{display:"none"},":has(>div:empty)+div":Y,":after":{content:"''",display:"block",position:"absolute",left:"0px",right:"0px",bottom:"0px",background:b.borderColor(),height:N},">div":{transition:"width 500ms, max-width 500ms",maxWidth:B,margin:"auto",display:"flex",alignItems:"center",position:"relative",textAlign:n(b.inheritTextAlign,f.textAlign("val"),b.textAlign("val")),justifyContent:n(b.inheritTextAlign,i([[f.textAlign("is-center"),"center"],[f.textAlign("is-right"),"flex-end"]]),i([[b.textAlign("is-center"),"center"],[b.textAlign("is-right"),"flex-end"]])),flexDirection:n({when:[b.logo("is-filled"),k.headline("is-filled")],then:i([[b.logoPosition("is-left"),"row"],[b.logoPosition("is-right"),"row-reverse"]],"column")})},">div>img":{margin:p(b.logoMargin,n),width:I.width(),height:I.height()}}}),$=i([[[O("is-filled"),C.showCloseIcon()],"51px"]],"0px"),T=l[1].height(),L=c(`calc(${T} - ${n(v,"0px","20px")} - ${$})`),R=c(`calc(100px + ${h[1].height()} + ${d[1].height()} + ${u[1].height()})`),[F]=s("div",{classNames:"content",position:"relative",overflow:"auto",maxHeight:n({when:m,then:{when:S,then:`calc(${T} - ${$})`,or:`calc(min(${T}, ${x.maxHeight()}) - ${$})`},or:{when:o([S,a(w.maxHeightEnabled)]),then:L(),or:`min(max(${w.maxHeight()}, ${R()}), ${L()})`}}),..."Win32"===navigator.platform?{overflow:CSS.supports("overflow","overlay")?"overlay":"scroll",scrollbarWidth:"thin",scrollbarColor:`${y.teachingsFontColor()} transparent`,pseudos:{"::-webkit-scrollbar":{width:"11px"},"::-webkit-scrollbar-track":{background:"transparent"},"::-webkit-scrollbar-thumb":{background:y.teachingsFontColor(),borderRadius:w.dialogBorderRadius(),border:`3px solid ${f.bg()}`}}}:{}}),W=p(D.borderWidth,n),[M]=s("div",{classNames:"footer-container",fontWeight:D.fontWeight(),color:D.fontColor(),position:"sticky",bottom:"0px",zIndex:1,padding:p(D.padding,n),paddingTop:`calc(${W} + ${p(D.padding,n,0)})`,background:n(D.inheritBg,f.bg(),D.bg()),fontSize:p(D.fontSize,n),textAlign:n(D.inheritTextAlign,f.textAlign("val"),D.textAlign()),...A,pseudos:{":after":{content:"''",display:"block",position:"absolute",left:"0px",right:"0px",top:"0px",background:D.borderColor(),height:W},">div":{transition:"width 500ms, max-width 500ms",maxWidth:B,margin:"auto",lineHeight:"1.8"},":has(>div:empty)":{display:"none"}}});return{HeaderContainer:E,Content:F,FooterContainer:M}}];var f=n(9081);const y=[Symbol("extendBannerBodyStylesheet"),(e,t)=>{let{boolIf:n,boolNot:i,boolOr:o,boolSwitch:r,jsx:a,rule:s}=e,{scaleVertical:c,dimsContent:l,dimsHeader:[,d],dimsFooter:[,u],activeAction:h,boolLargeOrMobile:p,bodyDesign:m,isBanner:g,isDialog:v,isMobile:f,isMobileWidth:y,layout:b,individualLayout:w,individualPrivacyOpen:C,design:x,footerBorderStyle:k}=t;const{fontColor:O}=x,{padding:D}=m,[,{scrollbar:P,scrolledBottom:S}]=l,A=o([g,C]),Y="300px",I=n(g,Y,`calc(${b.dialogMaxWidth()} - ${p(D,n,1)} - ${p(D,n,3)} - (${p(x.borderWidth,n)} * 2))`),B=i(y),N=n(v,`${p(x.borderWidth,n)} solid ${x.borderColor()}`),[E]=a("div",{classNames:"body-container",background:x.bg(),lineHeight:1.4,paddingRight:p(m.padding,n,1),paddingLeft:p(m.padding,n,3),borderLeft:N,borderRight:N,pseudos:{":has(+div>div:empty)":k,">div":{transition:"width 500ms, max-width 500ms",margin:"auto",maxWidth:n({when:g,then:{when:[C,i(w.inheritBannerMaxWidth)],then:w.bannerMaxWidth(),or:b.bannerMaxWidth()}})},">div:after":{content:"''",display:"block",clear:"both"}}}),$=n(m.acceptAllOneRowLayout,"0 0 calc(50% - 5px)","1 1 100%"),T=n(m.acceptAllOneRowLayout,"5px"),L=new Array(4).fill(null).map(((e,t)=>s({order:t}))),[R]=a("div",{classNames:"tb-right",position:n(o([i(C),y]),"sticky"),margin:n({when:[B,v,C,i(w.inheritDialogMaxWidth)],then:"0 0 10px 10px"}),background:x.bg(),maxWidth:"100%",width:n(B,I,"auto"),float:n(B,n({when:A,then:"right"})),paddingTop:n(y,"10px",n({when:A,then:p(D,n,0),or:"10px"})),paddingBottom:n(C,n(y,"5px","10px"),p(D,n,2)),zIndex:1,display:"flex",flexWrap:"wrap",transition:"box-shadow ease-in-out .1s",boxShadow:n({when:[P(),i(S)],then:`0 -15px 15px -15px rgba(${O("r")} ${O("g")} ${O("b")} / 30%)`}),bottom:n(i(C),u.height()),top:n(C,d.height()),pseudos:{">a":{marginBottom:n(f,`calc(10px * ${c()})`,"10px")},[`>${L[0][0]}`]:{flex:$,marginRight:T},[`>${L[1][0]}`]:{flex:$,marginLeft:T}}}),F=[B,g,i(h["is-history"])],[W]=a("div",{classNames:"tb-left",float:n({when:F,then:"left"}),width:n({when:F,then:`calc(100% - ${Y})`}),paddingRight:n({when:[g,B],then:"20px"}),paddingTop:n(C,"10px",p(m.padding,n,0)),paddingBottom:n({when:[B,i(C)],then:{when:o([g,C]),then:p(m.padding,n,2),or:"0px"},or:"10px"}),pseudos:{" img":{maxWidth:"100%",height:"auto"}}}),[M]=a("div",{position:"sticky",bottom:`calc(${u.height(!0,"0px")} - 1px)`,height:"0px",margin:"auto",transition:"box-shadow ease-in-out .1s",boxShadow:n({when:[P(),i(S)],then:`0 15px 15px 15px rgba(${x.fontColor("r")} ${x.fontColor("g")} ${x.fontColor("b")} / 20%)`}),display:r([[C,"block"],[[g,i(y)],"block"]],"none")});return{Container:E,RightSide:R,LeftSide:W,BeforeFooter:M,buttonOrderClasses:L}}];var b=n(4094);function w(){const{individualPrivacyOpen:e,onSave:t,updateGroupChecked:n,updateCookieChecked:i,groups:r,activeAction:a,onClose:s,set:c}=(0,b.Y)(),l=e?"ind_all":"main_all",u=e?"ind_essential":"main_essential",h=e?"ind_close_icon":"main_close_icon",p=e?"ind_custom":"main_custom",m={buttonClickedAll:l,buttonClickedEssentials:u,buttonClickedCloseIcon:h,buttonClickedCustom:p,acceptAll:(0,o.hb)((async()=>{await(0,d.P)(),c((e=>{let{updateGroupChecked:t}=e;r.forEach((e=>t(e.id,!0)))})),await(0,d.P)(),t(!1,l)}),[l]),acceptEssentials:(0,o.hb)((e=>{void 0===e&&(e=!1),r.forEach((e=>{let{isEssential:t,id:o,items:r}=e;if(t)n(o,!0);else for(const{legalBasis:e,id:t}of r)i(o,t,"legitimate-interest"===e)})),t(!1,!0===e?h:u)}),[u]),acceptIndividual:(0,o.hb)((()=>t(!1,p)),[p]),openIndividualPrivacy:(0,o.hb)((()=>c({individualPrivacyOpen:!0})),[c])};return{...m,closeIcon:(0,o.hb)((()=>{a?s():m.acceptEssentials(!0)}),[a,s,m.acceptEssentials])}}var C=n(680),x=n(6812);const k=e=>{let{children:t}=e;const n=[...t].sort((()=>Math.random()-.5));return(0,i.Y)(i.FK,{children:n})},O=()=>{const e=(0,b.Y)(),{isConsentRecord:t,activeAction:n,bodyDesign:{acceptEssentialsUseAcceptAll:r,acceptAllOneRowLayout:a},decision:{showGroups:s,groupsFirstView:c,saveButton:l,acceptAll:d,acceptEssentials:u,buttonOrder:h},texts:{acceptAll:p,acceptEssentials:m,acceptIndividual:g},saveButton:{type:v,useAcceptAll:f},individualTexts:{save:O},individualPrivacyOpen:D,didGroupFirstChange:P,productionNotice:S,buttonClicked:A="",fetchLazyLoadedDataForSecondView:Y}=e,{a11yIds:{firstButton:I}}=(0,C.y)(),{buttonOrderClasses:B}=(0,C.y)().extend(...y),N=r&&d===u,E=f&&d===v,{all:$,essential:T,individual:L,save:R}=(0,o.Kr)((()=>{const e=h.split(","),t=e.reduce(((t,n)=>(t[n]=e.indexOf(n),t)),{}),n=e.reduce(((t,n)=>(t[e.indexOf(n)]=n,t)),{});return a&&(t[n[0]]=1,t[n[1]]=0),t}),[h,a]),F=!1,{buttonClickedAll:W,buttonClickedEssentials:M,buttonClickedCustom:H,acceptAll:J,acceptEssentials:_,acceptIndividual:U,openIndividualPrivacy:V}=w(),G="change"===n&&!t,z=!D&&F,K=D||F,q=!D,j=(0,o.li)();return(0,i.FD)(k,{children:[z?(0,i.Y)(x.$,{onClick:U,busyOnClick:G,className:B[$][1],type:"acceptAll",framed:A===H,id:I,children:O}):(0,i.Y)(x.$,{onClick:J,busyOnClick:G,className:B[$][1],type:"acceptAll",framed:A===W,id:I,children:p}),(0,i.Y)(x.$,{onClick:()=>_(),busyOnClick:G,className:B[T][1],type:N?"acceptAll":"acceptEssentials",framed:A===M,children:m}),K&&(0,i.Y)(x.$,{onClick:U,busyOnClick:G,className:B[R][1],type:E?"acceptAll":"save",framed:A===H,children:O}),q&&(0,i.Y)(x.$,{onClick:V,onMouseEnter:()=>{Y&&(j.current=setTimeout(Y,500))},onMouseLeave:()=>clearTimeout(j.current),busyOnClick:G,className:B[L][1],type:"acceptIndividual",framed:A.startsWith("ind_"),children:g}),S]})};var D=n(5922),P=n(1477),S=n(5750),A=n(5548),Y=n(5453),I=n(1801);n(4959);const B=e=>{let{group:t}=e;const{name:n}=t,r=(0,b.Y)(),{decision:{groupsFirstView:a},design:{fontSize:s}}=r;return(0,I.C)(t),(0,i.Y)(o.FK,{children:(0,i.FD)("span",{children:[(0,i.Y)("i",{}),(0,i.Y)("span",{children:n})]})})},N=()=>{const{texts:{headline:e}}=(0,b.Y)(),{DottedGroupList:t,screenReaderOnlyClass:n}=(0,C.y)().extend(...Y.C),{groups:o,decision:{showGroups:r}}=(0,b.Y)();return r?(0,i.FD)(t,{children:[(0,i.Y)("legend",{className:n,children:e}),o.filter((e=>{let{items:t}=e;return!!t.length})).map((e=>(0,i.Y)(B,{group:e},e.id)))]}):null},E=(0,c.g)(Promise.resolve(S.X),"BodyDescription"),$=(0,c.g)(Promise.all([n.e(261),n.e(452),n.e(671),n.e(4)]).then(n.bind(n,3353)).then((e=>{let{BannerGroupList:t}=e;return t}))),T=(0,c.g)(Promise.all([n.e(261),n.e(452),n.e(671),n.e(4)]).then(n.bind(n,9558)).then((e=>{let{BannerHistorySelect:t}=e;return t}))),L={margin:"20px 0 10px 0"};var R=n(3963),F=n(6546),W=n(8700);const M=()=>{const{FooterLanguageSwitcherSelect:e}=(0,W.o)().extend(...F.h),{footerDesign:{languageSwitcher:t},languageSwitcher:n,onLanguageSwitch:r}=(0,b.Y)(),a=(0,o.Kr)((()=>n.find((e=>{let{current:t}=e;return t}))),[n]),s="flags"===t&&!!(null==a?void 0:a.flag);return(0,i.FD)(e,{"data-flag":s,children:[s&&(0,i.Y)("span",{style:{backgroundImage:`url(${a.flag})`}}),(0,i.Y)("select",{value:null==a?void 0:a.locale,"aria-label":null==a?void 0:a.name,onChange:e=>{null==r||r(n.find((t=>{let{locale:n}=t;return n===e.target.value})))},children:n.map((e=>{let{locale:t,name:n}=e;return(0,i.Y)("option",{value:t,children:n},t)}))})]})},H=(0,o.Rf)(((e,t)=>{const{FooterContainer:n}=(0,C.y)().extend(...v),r=(0,b.Y)(),{isTcf:a,layout:{type:s},footerDesign:{languageSwitcher:c},individualPrivacyOpen:l,onClose:d,i18n:{tcf:u},isConsentRecord:h,languageSwitcher:p,set:m}=r,g=(0,o.hb)((e=>{d(),e.preventDefault()}),[d]),{rows:f,render:y}=(0,R.D)({onClose:h?g:void 0,putPoweredByLinkInRow:"banner"===s?0:1,row1:[!1],row1End:[(null==p?void 0:p.length)>0&&c&&"disabled"!==c&&(0,i.Y)(M,{},"languageSwitcher")]});return(0,i.Y)(n,{ref:t,children:(0,i.Y)("div",{children:y(f)})})}));var J=n(9620);function _(e,t){const n=e.map((e=>(0,J.q)(e))),i=()=>n.map((e=>{let[t]=e;return t.matches})),[r,a]=(0,o.J0)(i);return(0,o.Nf)((()=>{if(t)return()=>{};const e=()=>a(i);return n.forEach((t=>{let[,n]=t;return n(e)})),()=>n.forEach((t=>{let[,,n]=t;return n(e)}))}),[t]),r}var U=n(9815),V=n(6545);const G=()=>{var e;const{headerDesign:{fontColor:t,fontSize:n},texts:{acceptEssentials:o},activeAction:r,pageRequestUuid4:a,i18n:{close:s,closeWithoutSaving:c},buttonClicked:l=""}=(0,b.Y)(),{buttonClickedCloseIcon:d,closeIcon:u}=w(),[h]=_([`(max-width: ${U.X}px)`]);return(0,i.Y)(V.U,{width:n,color:t,tooltipText:r?"change"===r?c:s:o,tooltipAlways:h,framed:l===d,renderInContainer:null==(e=document.getElementById(a))?void 0:e.querySelector("dialog"),onClick:u})},z=(0,o.Rf)(((e,t)=>{let{className:n}=e;const o=(0,C.y)(),{a11yIds:r,HeaderContainer:a,hasCloseIcon:s,HeaderTitle:c}=o.extend(...v).extend(...F.h),{headerDesign:{logo:l,logoRetina:d,logoAlt:u},decision:{showCloseIcon:h},texts:{headline:p},activeAction:m,individualPrivacyOpen:g,individualTexts:f,i18n:{headerTitlePrivacyPolicyHistory:y}}=(0,b.Y)(),w=d&&!(null==l?void 0:l.endsWith(".svg"))&&window.devicePixelRatio>1?d:l,x=!!h||!!m,k=g?"history"===m?y:f.headline:p;return(0,i.Y)(a,{ref:t,className:n,children:(0,i.FD)("div",{children:[!!w&&(0,i.Y)("img",{"aria-hidden":!0,alt:u||"",src:w}),!!k&&(0,i.Y)(c,{id:r.headline,className:x?s:void 0,children:k}),x&&(0,i.Y)(G,{})]})})})),K=(0,c.g)(Promise.resolve(z),"BannerHeader"),q=(0,c.g)(Promise.resolve((e=>{let{leftSideContainerRef:t,rightSideContainerRef:n}=e;const{a11yIds:r,Container:a,RightSide:s,LeftSide:c,BeforeFooter:l}=(0,C.y)().extend(...y),d=(0,b.Y)(),{tcf:u,isGcm:h,individualPrivacyOpen:p,activeAction:m,individualTexts:{postamble:g},i18n:{nonStandard:v,nonStandardDesc:w},groups:x,designVersion:k}=d,S=function(e){const{groups:t}=(0,D.b)();let n=t.map((e=>{let{items:t}=e;return[...t]})).flat();return n}(),Y=(0,P.bM)({services:S,disableListServicesNotice:k>9&&p}),I=h&&(0,f.h)(S).length>0,B=(0,o.Kr)((()=>!1),[u,I]),R=(0,i.FD)(c,{ref:t,children:[(0,i.Y)(E,{id:r.description,...x.length>0?Y:{},children:"history"===m&&(0,i.Y)(T,{})}),x.length>0&&(0,i.FD)(o.FK,{children:[p?(0,i.FD)(o.FK,{children:[B&&(0,i.Y)(A.Y,{headline:v,style:L,borderless:!0,children:w}),(0,i.Y)($,{}),[!1,!1].filter(Boolean).sort((()=>k<10?1:-1))]}):(0,i.Y)(N,{}),!!g&&p&&(0,i.Y)(E,{teachings:[g],isPostamble:!0})]})]},"leftSide"),F="history"===m?(0,i.Y)("div",{ref:n}):(0,i.Y)(s,{ref:n,children:(0,i.Y)(O,{})},"rightSide");return(0,i.FD)(a,{children:[(0,i.Y)("div",{children:p?[F,R]:[R,F]}),(0,i.Y)(l,{})]})})),"BannerBody"),j=(0,c.g)(Promise.resolve(H),"BannerFooter");var Q=n(968),X=n(5151);const Z='[href^="#consent-"]';function ee(){window.location.hash.startsWith("#consent-")&&(window.location.hash="")}var te=n(8639);function ne(e,t){const n=(0,o.li)(0),i=(0,o.li)(0),[r,a]=(0,o.J0)(e),[s,c]=(0,o.J0)(void 0),[l,d]=(0,o.J0)(t),[u,h]=(0,o.J0)(void 0);return(0,o.vJ)((()=>{n.current>0&&("none"===e?a(e):(a("none"),c(e))),n.current++}),[e]),(0,o.vJ)((()=>{i.current>0&&(0===t?d(t):(d(0),h(t),a("none"),c(e))),i.current++}),[t]),(0,o.vJ)((()=>{void 0!==s&&(a(s),c(void 0))}),[s]),(0,o.vJ)((()=>{void 0!==u&&(d(u),h(void 0))}),[u]),[r,l]}function ie(e){let{isVisible:t,animationIn:n,animationOut:i,animationInDuration:o,animationOutDuration:r,animationInDelay:a,animationOutDelay:s}=e;return t?{animation:n,duration:o,delay:a}:{animation:i,duration:r,delay:s}}const oe=e=>{let{animateOnMount:t=!0,isVisible:n=!0,animationIn:r="fadeIn",animationOut:a="fadeOut",animationInDelay:s=0,animationOutDelay:c=0,animationInDuration:l=1e3,animationOutDuration:d=1e3,className:u="",style:h={},children:p}=e;const[{animation:m,duration:g,delay:v},f]=(0,o.J0)(t?ie({isVisible:n,animationIn:r,animationOut:a,animationInDelay:s,animationOutDelay:c,animationInDuration:l,animationOutDuration:d}):{animation:"",delay:void 0,duration:0});(0,o.vJ)((()=>{f(ie({isVisible:n,animationIn:r,animationOut:a,animationInDelay:s,animationOutDelay:c,animationInDuration:l,animationOutDuration:d}))}),[n,r,a,s,c,l,d]);const y=`animate__animated animate__${m} ${u}`,b=m?{}:{opacity:n?1:0,transition:`opacity ${v}ms`};return(0,i.Y)("div",{className:y,style:{animationDelay:`${v}ms`,animationDuration:`${g}ms`,pointerEvents:n?"all":"none",...h,...b},children:p})},re=(0,c.g)(Promise.resolve((()=>{const{Content:e,hideOnMobileClass:t,dimsContent:n,dimsOverlay:r,dimsHeader:a,dimsFooter:s,dimsRightSidebar:c,A11ySkipToLink:l,a11yIds:{firstButton:u}}=(0,C.y)().extend(...m.R).extend(...v),{decision:{acceptAll:h,acceptEssentials:g,showCloseIcon:f},mobile:y,individualPrivacyOpen:w,bodyDesign:{acceptEssentialsUseAcceptAll:x},activeAction:k,pageRequestUuid4:O,i18n:{skipToConsentChoices:D}}=(0,b.Y)(),P=(0,o.li)(),S=x&&h===g?h:g,A=!y.hideHeader||k||w||"hide"===S&&f?"":t,Y=(0,o.li)();Y.current=Y.current||{};const I=(0,o.hb)((()=>[document.querySelector(`#${O} div[class*="animate__"]`)]),[O]),B=(0,o.hb)(((e,t)=>{let[n,,i]=e;t?n(t,I()):i()}),[I]),N=(0,o.hb)((e=>B(a,e)),[B]),E=(0,o.hb)((e=>B(s,e)),[B]),$=(0,o.hb)((e=>B(c,e)),[B]);return(0,o.vJ)((()=>{const e=I(),t=[n[0](P.current),r[0](document.querySelector(`#${O}`),e)];return()=>t.forEach((e=>e()))}),[]),(0,o.vJ)((()=>{p().mutate((()=>(0,d.P)().then((()=>P.current.scrollTop=0))))}),[w]),(0,i.FD)(e,{ref:P,children:[(0,i.Y)(l,{href:`#${u}`,children:D}),(0,i.Y)(K,{ref:N,className:A}),(0,i.Y)(q,{rightSideContainerRef:$}),(0,i.Y)(j,{ref:E})]})})),"BannerContent"),ae=(0,c.g)(Promise.all([n.e(261),n.e(452),n.e(671),n.e(4)]).then(n.bind(n,3362)).then((e=>{let{BannerSticky:t}=e;return t}))),se=(e,t)=>{const{dataset:n,style:i}=document.body;void 0===n.rcbPreviousOverflow&&(n.rcbPreviousOverflow=i.overflow),i.overflow=e&&t?"hidden":n.rcbPreviousOverflow,document.body.parentElement.style.overflow=i.overflow},ce=(0,c.g)(Promise.resolve((()=>{const e=(0,b.Y)(),{recorder:t,visible:n,activeAction:r,isConsentGiven:a,skipOverlay:c,pageRequestUuid4:h,individualPrivacyOpen:m,fetchLazyLoadedDataForSecondView:g,onClose:v,layout:{overlay:f,animationInDuration:y,animationOutDuration:w},sticky:C,keepVariablesInTexts:x}=e,k=(0,o.li)(),O=(0,o.li)(),D=(0,o.li)(!1),[P,S]=function(e){let{animationIn:t,animationInOnlyMobile:n,animationOut:i,animationOutOnlyMobile:r}=e;const[a]=_([`(max-width: ${U.X}px)`],!0);let s=n?a?t:"none":t,c=r?a?i:"none":i;return(0,o.Kr)((()=>{const e=window.navigator.userAgent.toLowerCase();return 4===["firefox","gecko","mobile","android"].map((t=>e.indexOf(t)>-1)).filter(Boolean).length}),[])&&(s="none",c="none"),[s,c]}(e.layout),[A,Y]=ne(P,y),[I,B]=ne("none"===S?"fadeOut":S,"none"===S?0:w),[N,E]=(0,l.F)(["BannerContent","BannerHeader","BannerBody","BannerFooter","BodyDescription"],(e=>{(0,d.P)().then(e)}),(()=>k.current.style.removeProperty("display"))),$=(0,W.o)(),{a11yIds:{firstButton:T},inner:L,Dialog:R,Overlay:F,individualPrivacyOpen:M,computedMobileUpdate:H}=$.extend(...u.Z);(0,o.Kr)((()=>{M.update(m),m&&(null==g||g())}),[m]),(0,o.vJ)((()=>{H()}),[]),(0,o.vJ)((()=>()=>{se(!1,f)}),[f]),function(){const{openBanner:e,openHistory:t,revokeConsent:n}=(0,b.Y)();(0,o.vJ)((()=>{const i=(i,o,r)=>{if(e)switch(i){case"change":e(r);break;case"history":t(r);break;case"revoke":n(o,r)}},o=t=>{if(!e)return;const n=t.target;(0,Q.M)(n,Z).concat((0,X.B)(n,Z)?[n]:[]).forEach((e=>{i(e.getAttribute("href").slice(9),e.getAttribute("data-success-message"),t)})),(0,X.B)(n,".rcb-sc-link")&&i(n.getAttribute("href").slice(1),n.getAttribute("data-success-message"),t)},r=()=>{const{hash:e}=window.location;e.startsWith("#consent-")&&i(e.substring(9),void 0,void 0)};return r(),window.addEventListener("hashchange",r),document.addEventListener("click",o,!0),()=>{window.removeEventListener("hashchange",r),document.removeEventListener("click",o,!0)}}),[e,t,n])}(),(0,o.vJ)((()=>{n&&t&&p().mutate((()=>{t.restart()}))}),[n,t]),(0,o.vJ)((()=>{const e=k.current,t=O.current||document.getElementById(h),i=function(e){r?v():(this.querySelector(`a[href="#${T}"]`).focus(),e.preventDefault())};if(n?(D.current=!0,(null==e?void 0:e.isConnected)&&(e.open&&(null==e.close||e.close.call(e)),p().mutate((()=>{var t;null==(t=e[f&&!x?"showModal":"show"])||t.call(e)})),e.addEventListener("cancel",i))):e&&(null==e.close||e.close.call(e)),t){const e=0,i=n?"none"===P?e:y:"none"===S?e:w,o=i>0,r=e=>{o&&(t.style.transition=`background ${i}ms`),t.style.display=e?"block":"none",se(e,f)};n?p().mutate((()=>{r(!0)})):D.current&&(setTimeout((()=>p().mutate((()=>r(!1)))),i),ee())}return()=>{null==e||e.removeEventListener("keyup",i)}}),[n,f,r,v]),(0,o.vJ)((()=>{n&&p().mutate((()=>k.current.focus({preventScroll:!0})))}),[n,m]),(0,o.vJ)((()=>{const e=e=>{let{detail:{triggeredByOtherTab:t}}=e;t&&v()};return document.addEventListener(s.r,e),()=>{document.removeEventListener(s.r,e)}}),[v]);const J=[];if(a&&C.enabled&&J.push((0,i.Y)(ae,{},"sticky")),n||D.current){const e=(0,i.Y)(R,{className:"wp-exclude-emoji "+(m?"second-layer":""),ref:k,style:{display:"none"},"data-nosnippet":!0,"data-lenis-prevent":!0,children:(0,i.Y)(N,{value:E,children:(0,i.Y)(oe,{animationIn:A,animationInDuration:Y,animationOut:I,animationOutDuration:B,isVisible:n,className:L,children:(0,i.Y)(re,{})})})},"dialog");J.push(c?e:(0,i.Y)(F,{id:h,className:$.className,ref:O,children:e},"overlay"))}return(0,i.Y)(o.FK,{children:J})})));var le=n(2655),de=n(5974);const ue=e=>{e&&(e.preventDefault(),e.stopPropagation())},he=(e,t)=>Object.assign(e,{activeAction:t,individualPrivacyOpen:!0,refreshSiteAfterSave:"change"===t&&2e3,visible:!0});var pe=n(9408),me=n(9179);const ge=e=>{let{children:t}=e;return(0,i.Y)(o.FK,{children:t})},ve=e=>{let{promise:t,children:n,suspenseProbs:r}=e;const a=(0,o.Kr)((()=>(0,c.g)((t||Promise.resolve()).then((()=>ge)),void 0,r)),[t]);return(0,i.Y)(a,{children:n})};var fe=n(5973),ye=n(5780),be=n(2767),we=n(2834),Ce=n(7246),xe=n(3354);const ke=async()=>{const e=[];document.dispatchEvent(new CustomEvent("RCB/PreDecision/Promises",{detail:{promises:e}}));try{const t=await Promise.all(e);for(const e of t)if(e)return e}catch(e){}return!1};var Oe=n(9589);const De=async e=>{let{supportsCookiesName:t}=e;return!(0,Oe.s)(t)&&"essentials"},Pe=async e=>{let{revisionHash:t,getUserDecision:n}=e;const i=n();if(!1===i)return!1;const{revision:o}=i;return t===o&&"consent"};var Se=n(5790),Ae=n(491);function Ye(){const{userAgent:e}=navigator;return!!e&&!/chrome-lighthouse/i.test(e)&&!(0,Ae.W)()&&(0,Se.S1)(e)}const Ie=(e,t,n)=>(void 0===t&&(t=1e4),void 0===n&&(n=!0),async i=>{let{getUserDecision:o}=i;if(Ye()||!n)return!1;if(o())return"consent";try{const{predecision:n}=await(r=e(),a=t,new Promise(((e,t)=>{r.then(e,t);const n=new Error("Timed out");setTimeout(t,a,n)})));return n}catch(e){return!1}var r,a});var Be=n(3114),Ne=n(7519),Ee=n(9521),$e=n(7406);const Te={path:"/consent/dynamic-predecision",method:$e.X.POST,obfuscatePath:"keep-last-part"};function Le(e){(0,o.vJ)((()=>{if((0,Ee.j)().customizeIdsBanner)return;const{restNamespace:t,restRoot:i,restQuery:o,restNonce:r,restPathObfuscateOffset:a,others:{isPreventPreDecision:s,isInvalidateImplicitUserConsent:c,hasDynamicPreDecisions:l,frontend:{isRespectDoNotTrack:u,isAcceptAllForBots:h}}}=(0,Ne.b)(),{onSave:p,suspense:m}=e;var g,v,f,y;!async function(e,t){const{gateways:i,args:o,onIsDoNotTrack:r,onShowCookieBanner:a,isInvalidateImplicitUserConsent:s}=t;let c=!0;const l=e instanceof Ce.U?e.getOptions():e,d={...l,getUserDecision:()=>{const e=(0,pe.y)(l.decisionCookieName);if(e){var t;if(e.revision!==l.revisionHash)return!1;if(s&&(null==(t=e.buttonClicked)?void 0:t.startsWith("implicit_")))return!1}return e}};for(const e of i){const t=await e(d,...o);if(!1!==t){c=!1;const e=e=>Promise.all([n.e(261),n.e(452),n.e(671),n.e(4)]).then(n.bind(n,93)).then((t=>{let{apply:n}=t;return n({type:e,...l})}));"all"===t?e("all"):"essentials"===t?e("essentials"):"dnt"===t?r((()=>e("essentials"))):"consent"===t&&e("consent");break}}c&&(a(),document.dispatchEvent(new CustomEvent("RCB/Banner/Show/Interactive")),await(0,we.G)(),document.dispatchEvent(new CustomEvent(xe.Z,{detail:{}})))}((0,Be.C)(),{gateways:[async()=>(await m.tcf,!1),ke,De,Pe,(f=["login-action-"],"force-cookie-banner",async()=>{const{className:e}=document.body;return!(e&&e.indexOf("force-cookie-banner")>-1)&&f.filter((t=>e.indexOf(t)>-1)).length>0&&"consent"}),(v=!!h&&"all",async e=>{let{getUserDecision:t}=e;return await(0,d.P)(),!(!1!==t()||!v)&&!!Ye()&&v}),(y=u,void 0===y&&(y=!0),async e=>{let{getUserDecision:t,groups:n}=e;const i=n.find((e=>{let{isEssential:t}=e;return t}));if(!1!==t()||!y)return!1;for(const e of n)if(e!==i)for(const{legalBasis:t}of e.items)if("legitimate-interest"===t)return!1;return!!function(){try{const e=window;if((e.doNotTrack||e.navigator.doNotTrack||e.navigator.msDoNotTrack||"msTrackingProtectionEnabled"in e.external)&&("1"==e.doNotTrack||"yes"==e.navigator.doNotTrack||"1"==e.navigator.doNotTrack||"1"==e.navigator.msDoNotTrack||e.external.msTrackingProtectionEnabled()))return!0}catch(e){}return!1}()&&"dnt"}),Ie((async()=>{const{clientWidth:e,clientHeight:n}=document.documentElement;return(0,fe.h)({location:Te,options:{restNamespace:t,restRoot:i,restQuery:o,restNonce:r,restPathObfuscateOffset:a},sendRestNonce:!1,sendReferer:!0,request:{viewPortWidth:e,viewPortHeight:n,referer:window.location.href,tcfStringImplicitEssentials:void 0}})}),1e4,l),(g=s,async()=>!!g&&(Ye()?"all":"consent"))],args:[e],isInvalidateImplicitUserConsent:c,onIsDoNotTrack:()=>{p(!0,"none")},onShowCookieBanner:()=>{e.set({visible:!0})}})}),[])}var Re=n(7513);const Fe={path:"/consent",method:$e.X.GET,obfuscatePath:"keep-last-part"},We={path:"/revision/second-view",method:$e.X.GET,obfuscatePath:"keep-last-part"},Me=(0,c.g)(Promise.resolve((()=>{const{pageRequestUuid4:e}=(0,b.Y)(),t=(0,te.N)();t.specify(e);const[n,o]=(0,W.d)(t);return(0,i.Y)(n,{value:o,children:(0,i.Y)(ce,{})})}))),He=e=>{let{poweredLink:t}=e;const{frontend:n,customizeValuesBanner:s,pageRequestUuid4:c,iso3166OneAlpha2:l,bannerDesignVersion:u,bannerI18n:h,isPro:p,isLicensed:m,isDevLicense:g,affiliate:v,isCurrentlyInTranslationEditorPreview:f}=(0,Ee.j)(),{restNamespace:y,restRoot:w,restQuery:C,restNonce:x,restPathObfuscateOffset:k}=(0,Ne.b)(),{decisionCookieName:O}=n,D=(0,Be.C)(),P=D.getUserDecision(!0),S=!1===P?void 0:P.buttonClicked,A=function(e,t){const n=localStorage.getItem(e);if(n)return JSON.parse(n);const i=r.A.get(e);return(null==t?void 0:t.startsWith("implicit"))?"implicit_all"===t?Object.values(a.um):[]:JSON.parse(i||"[]")}(D.getOption("gcmCookieName"),S),Y=(F=n.isTcf,W=n.tcf,M=n.tcfMetadata,D.getOptions(),H=async()=>{},J=[F,W,M,S],(0,o.Kr)((()=>(0,d.P)().then(H)),J)),[I,B]=function(e,t){const i=window.rcbLazyPromise;let o,r;if(i)[r,o]=i;else{let e=!1;r=!1===t?Promise.resolve({}):new Promise((t=>{o=async()=>{e||(e=!0,t(await(0,fe.h)({location:We,options:{restNamespace:y,restRoot:w,restQuery:C,restNonce:x,restPathObfuscateOffset:k},params:{revisionHash:n.revisionHash},sendRestNonce:!1})))}}))}return[r,o]}(0,n.hasLazyData),N=document.getElementById(c),E=(0,o.Kr)((()=>new ye.v(N)),[]),$=(0,le.u)(),T={onClose:e=>{Object.assign(e,{visible:!1,refreshSiteAfterSave:!1})},openHistory:(e,t)=>{he(e,"history"),ue(t)},openBanner:(e,t)=>{he(e,"change"),ue(t)},revokeConsent:(e,t,n)=>{let{onPersistConsent:i,onApplyConsent:o,isTcf:r,tcf:a,isGcm:s,groups:c}=e;i({consent:(0,de.w)(c,!0),gcmConsent:s?[]:void 0,buttonClicked:"shortcode_revoke",tcfString:void 0}).then((()=>o())).then((()=>{t&&alert(t),ee(),setTimeout((()=>window.location.reload()),2e3)})),ue(n)},onSave:(e,t,n)=>{const{refreshSiteAfterSave:i}=e,o=(0,d.P)().then((async()=>{const{onPersistConsent:i,onApplyConsent:o,activeAction:r,consent:a,tcf:s,isTcf:c,isGcm:l,gcmConsent:d,recorder:u}=e;return i({consent:a,gcmConsent:void 0,markAsDoNotTrack:t,buttonClicked:n,tcfString:void 0,recorderJsonString:u?JSON.stringify(u.createReplay()):void 0,uiView:"change"===r?"change":"revoke"!==r?"initial":void 0}).then((()=>o()))}));i?o.then((()=>{ee(),setTimeout((()=>window.location.reload()),i||2e3)})):Object.assign(e,{visible:!1})},updateCookieChecked:(e,t,n,i)=>{const{consent:o,isGcm:r,groups:a,updateGcmConsentTypeChecked:s}=e;o[t]||(o[t]=[]);const c=o[t],l=c.indexOf(n);i&&-1===l?c.push(n):!i&&l>-1&&c.splice(l,1),c.length||delete o[t]},updateGroupChecked:(e,t,n)=>{const{groups:i,updateCookieChecked:o}=e;for(const e of i.find((e=>{let{id:n}=e;return n===t})).items)o(t,e.id,n)}},[L,R]=(0,b.d)({...s,...n,blocker:void 0,recorder:E,productionNotice:(0,i.Y)(be.A,{isPro:p,isLicensed:m,isDevLicense:g,i18n:h}),pageRequestUuid4:c,iso3166OneAlpha2:l,gcmConsent:A,tcf:void 0,tcfFilterBy:"legInt",poweredLink:t,visible:!1,skipOverlay:!0,previewCheckboxActiveState:!1,individualPrivacyOpen:!1,designVersion:u,i18n:h,keepVariablesInTexts:f,affiliate:v,consent:{...!1===P?{}:P.consent,...(0,Be.C)().getDefaultDecision(!1===P)},onPersistConsent:Re.x,onApplyConsent:()=>(0,Be.C)().applyCookies({type:"consent"}),didGroupFirstChange:!1,fetchLazyLoadedDataForSecondView:B,suspense:{tcf:Y,lazyLoadedDataForSecondView:I}},{...$,...T,fetchHistory:async()=>{const e=[];try{e.push(...await(0,fe.h)({location:Fe,options:{restNamespace:y,restRoot:w,restQuery:C,restNonce:x,restPathObfuscateOffset:k},cookieValueAsParam:[O],sendRestNonce:!1}))}catch(e){}for(const{createdClientTime:t}of D.getConsentQueue())e.unshift({created:new Date(t).toISOString(),isDoNotTrack:!1,isForwarded:!1,isUnblock:!1,context:void 0,id:new Date(t).getTime(),uuid:void 0});return e},onLanguageSwitch:(e,t)=>{let{url:n}=t;window.location.href=n}},{deps:[Y]});var F,W,M,H,J;Le(R),function(e,t){(0,o.vJ)((()=>{const n=()=>{const n=(0,pe.y)(t);n&&e.set({consent:n.consent,isConsentGiven:!0})};return document.addEventListener(me.T,n),()=>{document.removeEventListener(me.T,n)}}),[])}(R,O);const _=(e=>{const t=(0,o.li)(!1),n=(0,o.li)(null),i=(0,o.li)(new Promise((e=>{})));return(0,o.vJ)((()=>t.current?()=>{}:(n.current=e,e.then((o=>{n.current!==e||t.current||(t.current=!0,i.current=Promise.resolve(o))})).catch((()=>{})),()=>{n.current=null})),[e]),i.current})(Y);return(0,i.Y)(L,{value:R,children:(0,i.Y)(ve,{promise:_,children:(0,i.Y)(Me,{})})})}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/78d0f47e9c5c2aaa54a0af34bf6a3188/banner-lite-banner-ui.lite.js.map
