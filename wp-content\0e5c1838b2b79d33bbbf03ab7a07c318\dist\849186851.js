"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[4],{93:(e,t,n)=>{n.r(t),n.d(t,{apply:()=>k});var o=n(2834),a=n(6399),i=n(1714),r=n(9408);const s="Google Tag Manager",c="Matomo Tag Manager",l="gtm",d="mtm";function u(e,t){let n,o,a,{presetId:i,isGcm:r}=t,u=!1,g="";const p={events:!0,executeCodeWhenNoTagManagerConsentIsGiven:!0};let h=e||"none";switch("googleTagManagerWithGcm"!==h||r||(h="googleTagManager"),h){case"googleTagManager":case"googleTagManagerWithGcm":a=l,n="dataLayer",g=s,p.events="googleTagManagerWithGcm"!==h;break;case"matomoTagManager":a=d,n="_mtm",g=c;break;default:p.events=!1,p.executeCodeWhenNoTagManagerConsentIsGiven=!1}return n&&(o=()=>(window[n]=window[n]||[],window[n])),a&&i===a&&(u=!0,p.events=!1,p.executeCodeWhenNoTagManagerConsentIsGiven=!1),{getDataLayer:o,useManager:h,serviceIsManager:u,managerLabel:g,expectedManagerPresetId:a,features:p}}function g(e){let t,{decisionCookieName:n,setCookiesViaManager:o,isGcm:a,groups:i,type:s}=e;const{useManager:c}=u(o,{isGcm:a,presetId:""}),l=i.find((e=>{let{isEssential:t}=e;return t})),d={[l.id]:l.items.map((e=>{let{id:t}=e;return t}))};if("consent"===s){const e=(0,r.y)(n);!1!==e?t=e.consent:(console.warn("Something went wrong while reading the cookie, fallback to essentials only..."),t=d)}return"essentials"===s&&(t=d),{isManagerActive:"none"!==c,selectedGroups:t,iterateServices:async function(e){const n=i.map((e=>e.items.map((t=>[e,t])))).flat();n.sort(((e,t)=>e[1].executePriority-t[1].executePriority));for(const[i,c]of n){var r;const n="all"===s||(null==(r=t[i.id])?void 0:r.indexOf(c.id))>-1,l=u(o,{presetId:c.presetId,isGcm:a});await e(i,c,n,l)}}}}var p=n(7400),h=n(7177),m=n(4766),v=n(729),C=n(6145);var f=n(72),y=n(9179),b=n(6336);async function k(e){const t=[];await g(e).iterateServices((async(e,n,o)=>{o&&t.push({group:e,service:n})})),document.dispatchEvent(new CustomEvent(f.r,{detail:{services:t,triggeredByOtherTab:e.triggeredByOtherTab}})),await(0,o.G)();const{dataLayer:n,isManagerOptOut:r,services:s,ready:c}=await async function(e){const t=[],{isManagerActive:n,iterateServices:o}=g(e),{skipOptIn:a}=e;const r=[];return await o((async(e,o,s,c)=>{let{getDataLayer:l,serviceIsManager:d}=c;const{codeDynamics:u,codeOptIn:g,executeCodeOptInWhenNoTagManagerConsentIsGiven:h}=o;if(s){const s=n&&h,c="function"==typeof a&&a(o);s||c||!g||r.push((0,i.l)(g,u));const l={group:e,service:o};document.dispatchEvent(new CustomEvent(p.D,{detail:l})),t.push(l)}})),{isManagerOptOut:!1,dataLayer:undefined,services:t,ready:Promise.all(r)}}(e),{ready:l}=await async function(e,t,n){const o=[],{isManagerActive:a,iterateServices:r}=g(e);return t?(r(((e,n,o,a)=>{let{tagManagerOptInEventName:i}=n,{features:r}=a;o&&i&&r.events&&t.push({event:i})})),setTimeout((()=>r(((e,n,o,a)=>{let{tagManagerOptOutEventName:i}=n,{features:r}=a;!o&&i&&r.events&&t.push({event:i})}))),1e3)):a&&n&&await r((async(e,t,n)=>{let{codeDynamics:a,codeOptIn:r,executeCodeOptInWhenNoTagManagerConsentIsGiven:s}=t;n&&s&&o.push((0,i.l)(r,a))})),{ready:Promise.all(o)}}(e,n,r),d=Promise.all([c,l]);await(0,a.P)(),document.dispatchEvent(new CustomEvent(y.T,{detail:{services:s,ready:d}}));const{deleteHttpCookies:u,services:k,ready:D}=await async function(e,t){const n=[],{isManagerActive:o,iterateServices:a}=g(e),r=[],s=[];return await a((async(e,a,c)=>{const{id:l,codeDynamics:d,codeOptOut:u,deleteTechnicalDefinitionsAfterOptOut:g,isEmbeddingOnlyExternalResources:p,technicalDefinitions:f,executeCodeOptOutWhenNoTagManagerConsentIsGiven:y}=a;if(!c){const c=o&&y;(c&&t||!c)&&r.push((0,i.l)(u,d)),g&&!p&&(function(e,t){for(const{type:n,name:o}of e){const e=(0,m.t)(o,t);if("*"===e)continue;const a=new RegExp((0,v.Z)(e),"g");switch(n){case"http":for(const e of Object.keys(h.A.get()))a.test(e)&&h.A.remove(e);break;case"local":case"session":try{const e="local"===n?window.localStorage:window.sessionStorage;if(e)for(const t of Object.keys(e))if(a.test(t)){try{e.setItem(t,null)}catch(e){}let n=0;for(;e.getItem(t)&&n<100;)n++,e.removeItem(t)}}catch(e){continue}}}}(f,d),f.some((e=>{let{type:t}=e;return"http"===t}))&&s.push(l));const b={group:e,service:a};document.dispatchEvent(new CustomEvent(C.G,{detail:b})),n.push(b)}})),{services:n,ready:Promise.all(r),deleteHttpCookies:s}}(e,r);document.dispatchEvent(new CustomEvent(b.a,{detail:{services:k,deleteHttpCookies:u,ready:Promise.all([d,D])}}))}},6264:(e,t,n)=>{async function o(e,t){e.createdClientTime=(new Date).toISOString();const o=t.getConsentQueue();o.push(e),t.setConsentQueue(o);try{await t.getOption("persistConsent")(e,!0),t.setConsentQueue(t.getConsentQueue().filter((t=>{let{createdClientTime:n}=t;return e.createdClientTime!==n})))}catch(o){const{groups:a,decisionCookieName:i,tcfCookieName:r,gcmCookieName:s,failedConsentDocumentationHandling:c,revisionHash:l}=t.getOptions(),d="optimistic"===c,{decision:u,createdClientTime:g,tcfString:p,gcmConsent:h,buttonClicked:m}=e,v={consent:d?"all"===u?a.reduce(((e,t)=>(e[t.id]=t.items.map((e=>{let{id:t}=e;return t})),e)),{}):"essentials"===u?(0,n(5974).w)(a,!1):u:(0,n(5974).w)(a,!1),previousUuids:[],revision:l,uuid:g,created:new Date(g),buttonClicked:m};localStorage.setItem(i,JSON.stringify(v)),p&&localStorage.setItem(r,d?p:""),h&&localStorage.setItem(s,d?JSON.stringify(h):"[]")}}n.d(t,{persistWithQueueFallback:()=>o})},7724:(e,t,n)=>{function o(e,t){void 0===t&&(t=!1);const{decisionCookieName:a,tcfCookieName:i,gcmCookieName:r}=e.getOptions(),s=()=>{localStorage.removeItem(a),localStorage.removeItem(i),localStorage.removeItem(r),localStorage.removeItem(e.getConsentQueueName())},c=document.querySelector('a[href*="rcb-clear-current-cookie=1"]');if(null==c||c.addEventListener("click",s),e.isConsentQueueLocked()){const t=t=>{t.key!==e.getConsentQueueName(!0)||t.newValue||o(e)};return window.addEventListener("storage",t),()=>{window.removeEventListener("storage",t),null==c||c.removeEventListener("click",s)}}{let o,i=0;const r=async()=>{e.isConsentQueueLocked(!0);const t=e.getConsentQueue();let c=15e3;if(t.length>0){i++;try{const o=t.shift(),r=0===t.length||!n(7177).A.get(a),l=await e.getOption("persistConsent")(o,r),d=n(7177).A.get(a);d&&-1===d.indexOf(l)&&n(7177).A.set(a,d.replace(/^(.*?:.*?):/gm,`$1,${l}:`)),e.setConsentQueue(t),0===t.length&&s(),i=0,c=1500}catch(e){c=15*i*1e3}}o=setTimeout(r,c)};return e.isConsentQueueLocked(!0),o=setTimeout(r,t?0:15e3),()=>{e.isConsentQueueLocked(!1),clearTimeout(o),null==c||c.removeEventListener("click",s)}}}n.d(t,{retryPersistFromQueue:()=>o})},9558:(e,t,n)=>{n.r(t),n.d(t,{BannerHistorySelect:()=>o});const o=()=>{const e=(0,n(4094).Y)(),{Select:t}=(0,n(680).y)().extend(...n(5746).I),{set:o,consent:a,groups:i,tcf:r,isGcm:s,gcmConsent:c,lazyLoadedDataForSecondView:l,activeAction:d,history:u,fetchHistory:g,visible:p,i18n:{historyLabel:h,historyItemLoadError:m,historySelectNone:v}}=e,[C,f]=(0,n(7936).J0)(),[y,b]=(0,n(7936).J0)({consent:a,groups:i,tcf:r,gcmConsent:c,lazyLoadedDataForSecondView:l}),k=e=>{let{buttonClicked:t,tcf:n,gcmConsent:a,...i}=e;o({...i,isTcf:!!n,tcf:null,gcmConsent:[]})};(0,n(7936).vJ)((()=>{const e={consent:[],groups:[],gcmConsent:[],lazyLoadedDataForSecondView:void 0};if(C){const{context:t}=C;k(t||e)}else k(e)}),[C]);const D=(0,n(7936).li)(!1);(0,n(7936).vJ)((()=>{l&&!D.current&&"history"===d&&p&&(D.current=!0,async function(){const e=await g();b({consent:a,groups:i,tcf:r,gcmConsent:c,lazyLoadedDataForSecondView:l}),o({history:e}),f(e[0])}())}),[l,d,p]),(0,n(7936).vJ)((()=>{p||(D.current=!1)}),[p]),(0,n(7936).vJ)((()=>()=>k(y)),[]);const S=null==C?void 0:C.uuid;return(0,n(6425).FD)(n(7936).FK,{children:[h," ",(0,n(6425).Y)(t,{disabled:!(null==u?void 0:u.length),value:(null==C?void 0:C.id)||-1,onChange:e=>{const t=+e.target.value;for(const e of u){const{id:n}=e;if(n===t){f(e);break}}},children:(null==u?void 0:u.length)>0?u.map((e=>{let{id:t,isDoNotTrack:o,isUnblock:a,isForwarded:i,created:r}=e;return(0,n(6425).FD)("option",{value:t,children:[new Date(r).toLocaleString(document.documentElement.lang),o?" (Do Not Track)":"",a?" (Content Blocker)":"",i?" (Consent Forwarding)":""]},t)})):(0,n(6425).Y)("option",{value:-1,children:v})}),(0,n(6425).FD)("div",{style:{opacity:.5,marginTop:5},children:["UUID: ",S||"-"]}),!(null==C?void 0:C.context)&&(0,n(6425).Y)("div",{style:{fontWeight:"bold",marginTop:5},children:m})]})}},3353:(e,t,n)=>{n.r(t),n.d(t,{BannerGroupList:()=>v});var o=n(6425),a=n(7936),i=n(4094),r=n(9694);const s=e=>{let{group:{id:t,isEssential:n},cookie:s}=e;const{id:c}=s,l=(0,i.Y)(),{consent:d,activeAction:u}=l,g=n||"history"===u,p=n||((null==d?void 0:d[t])||[]).some((e=>e===c)),h=(0,a.hb)((e=>l.updateCookieChecked(t,c,e)),[l,t,c]);return(0,o.Y)(r.Cookie,{cookie:s,propertyListProps:{isEssentialGroup:n},checked:p,disabled:g,onToggle:h})};var c=n(1801),l=n(4959),d=n(5548),u=n(180);const g=e=>{let{group:t}=e;const n=(0,i.Y)(),{name:a,description:r,items:g}=t,{group:{headlineFontSize:p},individualTexts:{headline:h,showMore:m,hideMore:v}}=n,C=(0,c.C)(t);return(0,o.FD)(d.Y,{legend:`${h}: ${a}`,headline:(0,o.FD)(l.S,{...C,fontSize:p,children:[a," (",g.length,")"]}),children:[(0,o.Y)("span",{children:r}),!!g&&(0,o.Y)(u.X,{showMore:m,hideMore:v,bullets:!0,children:g.map((e=>(0,o.Y)(s,{group:t,cookie:e},e.id)))})]})};var p=n(5453),h=n(8700);const m=e=>{let{children:t}=e;const{GroupList:n}=(0,h.o)().extend(...p.C);return(0,o.Y)(n,{children:t})},v=()=>{const{groups:e}=(0,i.Y)(),t=e.filter((e=>{let{items:t}=e;return t.length}));return(0,o.Y)(m,{children:t.map((e=>(0,o.Y)(g,{group:e},e.id)))})}},3362:(e,t,n)=>{n.r(t),n.d(t,{BannerSticky:()=>o});const o=()=>null},9694:(e,t,n)=>{n.r(t),n.d(t,{Cookie:()=>Y});var o=n(6425),a=n(5453),i=n(7936);const r=/(\r\n|\r|\n|<br[ ]?\/>)/g;var s=n(4200),c=n(5746),l=n(8700),d=n(5922);const u=e=>{let{label:t,value:n,children:r,printValueAs:u,monospace:g}=e;const p=(0,l.o)(),{Link:h,CookieProperty:m}=p.extend(...c.I).extend(...a.C),v=(0,d.b)(),{i18n:{yes:C,no:f}}=v;let y="string"==typeof n&&n.startsWith("http")&&(0,s.g)(n)?(0,o.Y)(h,{href:n,target:"_blank",rel:"noopener noreferrer",children:n}):"string"==typeof n?"phone"===u?(0,o.Y)(h,{target:"_blank",href:`tel:${n.replace(/\s+/g,"")}`,children:n}):"mail"===u?(0,o.Y)(h,{target:"_blank",href:`mailto:${n}`,children:n}):(0,o.Y)("span",{dangerouslySetInnerHTML:{__html:n}}):n;return"boolean"===u&&(y=y?C:f),n||!1===n||"empty"===u?(0,o.FD)(i.FK,{children:[(0,o.FD)(m,{children:[t&&(0,o.FD)("strong",{children:[t,": "]}),(0,o.Y)("span",{role:"presentation",style:{fontFamily:g?"monospace":void 0},children:y})]}),(0,o.Y)(m,{children:!!r&&(0,o.Y)("div",{children:r})})]}):null};var g=n(5360),p=n(1477),h=n(1917);const m=e=>{let{mechanisms:t,...n}=e;const{screenReaderOnlyClass:a}=(0,l.o)(),r=(0,p.JY)(a),{iso3166OneAlpha2:s,predefinedDataProcessingInSafeCountriesLists:c,territorialLegalBasis:m,isDataProcessingInUnsafeCountries:v,i18n:{dataProcessingInThirdCountries:C,territorialLegalBasisArticles:{"dsg-switzerland":{dataProcessingInUnsafeCountries:f},"gdpr-eprivacy":{dataProcessingInUnsafeCountries:y}},safetyMechanisms:{label:b,eu:k,switzerland:D,adequacyDecision:S,contractualGuaranteeSccSubprocessors:w,standardContractualClauses:I,bindingCorporateRules:Y}}}=(0,d.b)(),{result:T,filter:P,isGdpr:M}=(0,h.F)({predefinedDataProcessingInSafeCountriesLists:c,territorialLegalBasis:m,isDataProcessingInUnsafeCountries:v,service:n}),O=Object.entries(T),L={A:S,"A-EU":`${S} (${k})`,"A-CH":`${S} (${D})`,B:I,C:w,D:m.length>1?"":M?y:f,"D-EU":y,"D-CH":f,E:Y},F=Object.keys(L).filter((e=>P((t=>t===e)).length>0)),x=t?t(F):F;return(0,o.FD)(i.FK,{children:[O.length>0&&(0,o.Y)(u,{label:C,value:(0,g.i)(O.map((e=>{let[t,n]=e;return(0,o.Y)("span",{dangerouslySetInnerHTML:{__html:r(n.map((e=>[e,L[e]])),s[t]??t)}},t)})),", ")}),x.length>0&&(0,o.Y)(u,{label:b,value:(0,g.i)(x.map((e=>(0,o.Y)("span",{dangerouslySetInnerHTML:{__html:L[e]?r([[e]],L[e]):e}},e))),", ")})]})};var v=n(180);const C=e=>{let{expandable:t,children:n,labelModifications:a={}}=e;const{group:{detailsHideLessRelevant:r},i18n:{andSeparator:s,showLessRelevantDetails:c,hideLessRelevantDetails:l}}=(0,d.b)(),g=(0,i.li)(null),[h,m]=(0,i.J0)("");(0,i.vJ)((()=>{const{current:e}=g;if(e){const t=[...new Set([...e.querySelectorAll(":scope>div>strong")].map((e=>{const t=e.innerText.replace(/:?\s+$/,"");return a[t]||t})))];m((0,p.$D)(t,s))}}),[g.current,a]);const C=(0,i.hb)((e=>e.replace("%s",h)),[h]);return r&&t?(0,o.Y)("div",{"aria-hidden":!h,hidden:!h,children:(0,o.Y)(u,{value:(0,o.FD)(i.FK,{children:[(0,o.Y)("br",{}),(0,o.Y)(v.X,{showMore:C(c),hideMore:C(l),style:{fontStyle:"italic"},forceRender:!0,children:(0,o.FD)("div",{ref:g,children:[(0,o.Y)("br",{}),n]})})]})})}):n};var f=n(4766);const y=e=>{let{definitions:t,codeDynamics:n}=e;const{i18n:a}=(0,d.b)(),r=function(){const{i18n:{durationUnit:e}}=(0,d.b)();return(0,i.hb)(((t,n)=>(0,p.BP)(t,e.n1[n],e.nx[n])),[e])}(),s={http:{name:"HTTP Cookie",abbr:"HTTP",backgroundColor:"black"},local:{name:"Local Storage",abbr:"Local",backgroundColor:"#b3983c"},session:{name:"Session Storage",abbr:"Session",backgroundColor:"#3c99b3"},indexedDb:{name:"IndexedDB",abbr:"I-DB",backgroundColor:"#4ab33c"}};return null==t?void 0:t.map((e=>{let{children:t,type:i,name:c,host:l,duration:d,durationUnit:g,isSessionDuration:p,purpose:h}=e;var m;return(0,o.FD)(u,{label:a.technicalCookieName,monospace:!0,value:(0,f.t)(c,n),children:[(0,o.Y)(u,{label:a.type,value:(null==(m=s[i])?void 0:m.name)||i}),!!l&&(0,o.Y)(u,{label:a.host,value:l,monospace:!0}),(0,o.Y)(u,{label:a.duration,value:["local","indexedDb"].indexOf(i)>-1?a.noExpiration:p||"session"===i?"Session":r(d,g)}),t,(0,o.Y)(u,{label:a.purpose,value:h})]},`${i}-${c}-${l}`)}))};var b=n(5285),k=n(4349),D=n(680),S=n(4959);const w=e=>{let{type:t,isDisabled:n,isBold:i}=e;const{Cookie:r}=(0,D.y)().extend(...a.C),{activeAction:s,gcmConsent:c,updateGcmConsentTypeChecked:l,group:{descriptionFontSize:u},i18n:{gcm:{purposes:{[t]:g}}}}=(0,d.b)();return(0,o.Y)(r,{children:(0,o.Y)(S.S,{isChecked:c.indexOf(t)>-1,isDisabled:n||"history"===s,fontSize:u,onToggle:e=>l(t,e),children:(0,o.Y)("span",{style:{fontWeight:i?"bold":void 0},children:g})})})},I=e=>{let{cookie:{purpose:t,isProviderCurrentWebsite:n,provider:a,providerContact:s={},providerPrivacyPolicyUrl:c,providerLegalNoticeUrl:l,legalBasis:g,dataProcessingInCountries:p,dataProcessingInCountriesSpecialTreatments:v,isEmbeddingOnlyExternalResources:f,technicalDefinitions:D,codeDynamics:S,googleConsentModeConsentTypes:I},isEssentialGroup:Y,isDisabled:T}=e;const{i18n:P,iso3166OneAlpha2:M,websiteOperator:O,isGcm:L,designVersion:F}=(0,d.b)(),{deprecated:x,legalBasis:E}=P,{dataProcessingInUnsafeCountries:N,appropriateSafeguards:A}=function(e){let{dataProcessingInCountries:t,specialTreatments:n,tcf:o={internationalTransfers:!1,transferMechanisms:[]}}=e;const{designVersion:a,i18n:{safetyMechanisms:r,other:s},isDataProcessingInUnsafeCountries:c,dataProcessingInUnsafeCountriesSafeCountries:l,iso3166OneAlpha2:u}=(0,d.b)(),{internationalTransfers:g,transferMechanisms:p}=o;return{dataProcessingInUnsafeCountries:(0,i.Kr)((()=>c?(0,h.z)({dataProcessingInCountries:t,safeCountries:l,specialTreatments:n,isDisplay:!0}).map((e=>u[e]||e)):[]),[c,l,n,t,u]),appropriateSafeguards:(0,i.Kr)((()=>[...new Set([n.indexOf(b.ak.StandardContractualClauses)>-1&&r.standardContractualClauses,a>6&&g&&p.map((e=>{switch(e){case"SCCs":return r.standardContractualClauses;case"Adequacy decision":return r.adequacyDecision;case"BCRs":return r.bindingCorporateRules;case"Other":return s;default:return""}}))].flat().filter(Boolean))]),[n,g,p])}}({dataProcessingInCountries:p,specialTreatments:v}),{legalNotice:U,privacyPolicy:G,contactForm:B}=(0,k.s)(),H=(0,i.Kr)((()=>{if(n&&O){const{address:e,country:t,contactEmail:n,contactPhone:o}=O;return{provider:[e,M[t]||t].filter(Boolean).join(", "),contact:{email:n,phone:o,link:B},legalNoticeUrl:!1===U?"":U.url,privacyPolicyUrl:!1===G?"":G.url}}return{provider:a,contact:s,privacyPolicyUrl:c,legalNoticeUrl:l}}),[n,a,s,c,l,O,U,G,B]),$=(0,i.Kr)((()=>Object.values(H.contact).filter(Boolean).length>0),[H.contact]),Q=(0,i.Kr)((()=>{const e="legal-requirement"===g,t="legitimate-interest"===g||Y;if(F<=11)return e?x.legalRequirement:t?P.legitimateInterest:P.consent;{const{consentPersonalData:n,consentStorage:o,legitimateInterestPersonalData:a,legitimateInterestStorage:i,legalRequirementPersonalData:r}=E;return[e?r:t?a:n,!f&&(e||t?i:o)].filter(Boolean).join(", ")}}),[F,g,Y,E,f]);return(0,o.FD)(i.FK,{children:[!!t&&(0,o.Y)(u,{label:P.purpose,value:(R=t,"string"==typeof R?R.split(r).map(((e,t)=>e.match(r)?(0,i.n)("br",{key:t}):e)):R)}),(0,o.Y)(u,{label:P.legalBasis.label,value:Q}),L&&I.length>0&&(0,o.Y)(u,{label:P.gcm.dataProcessingInService,printValueAs:"empty",children:(0,o.Y)("div",{style:{display:"inline-block"},children:(0,o.Y)(u,{printValueAs:"empty",children:I.map((e=>(0,o.Y)(w,{type:e,isDisabled:T},e)))})})}),(0,o.Y)(u,{label:P.provider,value:H.provider,children:$&&(0,o.FD)(i.FK,{children:[(0,o.Y)(u,{label:P.providerContactPhone,value:H.contact.phone,printValueAs:"phone"}),(0,o.Y)(u,{label:P.providerContactEmail,value:H.contact.email,printValueAs:"mail"}),(0,o.Y)(u,{label:P.providerContactLink,value:H.contact.link})]})}),(0,o.Y)(u,{label:P.providerPrivacyPolicyUrl,value:H.privacyPolicyUrl}),(0,o.Y)(u,{label:P.providerLegalNoticeUrl,value:H.legalNoticeUrl}),F<10&&N.length>0&&(0,o.Y)(u,{label:x.dataProcessingInUnsafeCountries,value:N.join(", ")}),F<10&&A.length>0&&(0,o.Y)(u,{label:x.appropriateSafeguard,value:A.join(", ")}),(0,o.FD)(C,{expandable:F>9,labelModifications:{[P.technicalCookieName]:P.technicalCookieDefinitions},children:[F>9&&(0,o.Y)(m,{dataProcessingInCountries:p,dataProcessingInCountriesSpecialTreatments:v}),!f&&(0,o.Y)(y,{codeDynamics:S,definitions:D})]})]});var R},Y=e=>{let{cookie:t,checked:n,disabled:i,onToggle:r,propertyListProps:s={}}=e;const{Cookie:c}=(0,l.o)().extend(...a.C),{name:u}=t,{group:{descriptionFontSize:g}}=(0,d.b)();return(0,o.FD)(c,{children:[(0,o.Y)(S.S,{isChecked:n,isDisabled:i,fontSize:g,onToggle:r,children:(0,o.Y)("strong",{children:u})}),(0,o.Y)(I,{cookie:t,...s,isDisabled:!n})]})}},180:(e,t,n)=>{n.d(t,{X:()=>o});const o=e=>{let{onToggle:t,children:o,showMore:a,hideMore:i,bullets:r,forceRender:s,...c}=e;const{Link:l}=(0,n(8700).o)().extend(...n(5746).I),[d,u]=(0,n(7936).J0)(!1),g=(0,n(7140).p)();return(0,n(6425).FD)(n(7936).FK,{children:[r&&(0,n(6425).Y)(n(7936).FK,{children:"  •  "}),(0,n(6425).Y)(l,{href:"#",onClick:e=>{const n=!d;u(n),null==t||t(n),e.preventDefault()},...o?{"aria-expanded":d,"aria-controls":g}:{},...c,children:d?i:a}),o&&(0,n(6425).Y)("div",{hidden:!d,id:g,children:(d||s)&&o})]})}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/ac83cd529bbe1624e8ef09a9bdd9181d/banner-lite-banner-lazy.lite.js.map
