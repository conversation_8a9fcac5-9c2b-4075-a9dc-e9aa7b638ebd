"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[763],{96596:(e,t,n)=>{n.d(t,{m:()=>_});var o=n(3713),r=n(91502),s=n(6099),i=n(92453),a=n(78915),d=n(9551),l=n(28101),c=n(24262),u=n(19991),h=n(64715),v=n(73491),m=n(75792),g=n(41594),f=n(85360),p=n(27465),y=n(18197),x=n(65824),C=n(81533),w=n(45854),b=n(59726),j=n(36305);const k=e=>{const t=(0,j.l)(),{view:n,rows:r}=t,{__:s,_n:i}=(0,b.s)(),{name:a,description:d,provider:l,lists:u,hasRealTimeApi:h,updatedAt:m,createContentBlockerForVendorId:f,onCreateOrEditContentBlocker:p,onCreate:y}=e,[k,A]=(0,g.useState)((()=>{var e;return null==(e=u.filter((e=>{let{isRecommended:t,name:n}=e;return t&&n}))[0])?void 0:e.name})),T=(0,g.useMemo)((()=>"vendors"===n?r.filter((e=>{let{vendor:{id:t},blocker:n}=e;return t===f&&!1!==n}))[0]:void 0),[n,r,f]),[S,I]=(0,g.useState)(!!p&&!T),{lang:F}=document.documentElement,[N]=u.filter((e=>{let{name:t}=e;return t===k}));return(0,o.jsxs)(x.A,{direction:"vertical",children:[(0,o.jsxs)("p",{className:"description",children:[d,(0,o.jsx)("br",{}),(0,o.jsx)("br",{}),s("Only advertising partners to whom your website visits have consented can participate in the auction. For advertising partners who require consent in accordance with the TCF standard, the respective TCF vendors must be configured."),(0,o.jsx)("br",{}),(0,o.jsx)("br",{}),s("You can automatically import these lists of TCF vendor configurations:")]}),(0,o.jsx)(C.Ay.Group,{value:k,onChange:e=>A(e.target.value),children:(0,o.jsx)(x.A,{direction:"vertical",children:u.map((e=>{let{name:t,description:n,isRecommended:r,vendorIds:{length:a}}=e;return(0,o.jsxs)(C.Ay,{value:t,children:[(0,o.jsxs)("div",{children:[t," (",i("%d TCF vendor","%d TCF vendors",a,a),")"," ",r&&(0,o.jsx)(v.A,{color:"blue",children:s("Recommended")})]}),(0,o.jsx)("p",{className:"description",children:n})]},t)}))})}),(0,o.jsx)("p",{className:"description",children:(0,o.jsxs)("i",{children:[s("Last updated: %s",new Date(m).toLocaleDateString(F))," • ",!h&&s("%s does not offer an API for real-time updates.",l)]})}),p&&!T&&(0,o.jsx)(w.A,{checked:S,onChange:e=>I(e.target.checked),children:s('Create content blocker for %s (blocks scripts until consent for purpose "Store and/or access information on a device" is given for this TCF vendors)',a)}),(0,o.jsx)("div",{style:{textAlign:"right"},children:N&&(0,o.jsx)(c.A,{title:(0,o.jsxs)(o.Fragment,{children:[s("After activating the TCF vendors, I will check the information for each activated TCF vendor in the TCF vendor configuration myself and correct any information that does not match my use case."),S&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("br",{}),(0,o.jsx)("br",{}),s("In addition, I'll checked the information in the content blocker  myself for correctness and completeness and add missing information or corrected information that does not fit my use case. I am aware that the manufacturer of Real Cookie Banner cannot take any liability in this respect.")]})]}),overlayInnerStyle:{maxWidth:300},placement:"bottomLeft",onConfirm:async()=>{await y({adNetwork:e,list:N}),S&&!T&&setTimeout((()=>{p()}),0)},okText:s("Create"),cancelText:s("Cancel"),children:(0,o.jsx)("a",{className:"button button-primary",children:s("Create TCF vendor configurations")})})})]})},{Panel:A}=p.A,T=e=>{let{onCreate:t}=e;const n=(0,j.l)(),{adNetworks:r,view:s}=n,i=("vendors"===s?n.defaultCreateAdNetworkIdentifier:void 0)||r[0].identifier;return 0===n.rows.length?(0,o.jsx)(y.A,{spinning:!0}):(0,o.jsx)(p.A,{accordion:!0,defaultActiveKey:i,children:r.map((e=>{const{name:n,identifier:r,logo:s}=e;return(0,o.jsx)(A,{header:n,extra:(0,o.jsx)("img",{src:s,style:{height:"1em"}}),children:(0,o.jsx)(k,{...e,onCreate:t})},r)}))})};var S=n(97276);function I(){const{__:e}=(0,b.s)(),[t,n]=(0,g.useState)(),[r,s]=(0,g.useState)(),i=(0,g.useCallback)((e=>{let{total:t,title:o}=e,r=0;const i=new AbortController;let a;const d=new Promise((e=>a=e));return n(0),s({total:t,title:o,abortController:i}),[()=>{r++,r>=t?(n(void 0),s(void 0),a()):n(r)},i.signal,d]}),[]);return[i,"number"==typeof t&&r?(0,o.jsx)(l.A,{style:{clear:"both"},description:(0,o.jsx)(o.Fragment,{children:(0,o.jsx)("div",{children:r.title})}),image:(0,o.jsx)(S.A,{type:"circle",width:100,percent:+(t/r.total*100).toFixed(0),format:e=>(0,o.jsxs)(o.Fragment,{children:[e," %",(0,o.jsx)("br",{}),(0,o.jsxs)("span",{style:{fontSize:10},children:[t," / ",r.total]})]})}),children:(0,o.jsx)("button",{className:"button button-primary",onClick:()=>{r.abortController.abort(),n(void 0),s(void 0)},children:e("Cancel")})}):void 0]}var F=n(17312);const{Column:N}=r.A,_=()=>{const{__:e,_i:t}=(0,b.s)(),n=(0,j.l)(),{busy:p,vendorCount:y,view:x,rows:C}=n,[w,k]=(0,g.useState)({vendor:""}),[A,S]=(0,g.useState)([]),[_,B]=I(),[V,P]=(0,g.useState)((()=>"vendors"===x&&!!n.defaultCreateAdNetworkIdentifier)),R=(0,g.useMemo)((()=>n.adNetworks.map((e=>({network:e,vendorIds:e.lists.map((e=>{let{vendorIds:t}=e;return t})).flat()})))),[n.adNetworks]),O=(0,g.useMemo)((()=>[...C].sort(((e,t)=>{let{vendor:n}=e,{vendor:o}=t;if(!n||!o)return-1;const{name:r}=n,{name:s}=o;return r<s?-1:r>s?1:0})).filter((e=>{let{vendor:t}=e;const{vendor:n}=w;if(t&&n){const e=n.toLowerCase();return t.id.toString()===e||t.name.toLowerCase().indexOf(e)>-1}return!0}))),[C,C.length,w]),z=(0,g.useCallback)((e=>O.filter((t=>{let{configuration:n,vendor:o}=t;return e.indexOf("boolean"==typeof n?o.id:n.id)>-1&&!0!==n}))),[O]),E=e("Create TCF vendor configuration");return B||(y||"vendors"===x?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(s.A,{justify:"end",gutter:10,children:["vendors"===x&&(0,o.jsxs)(i.A,{children:[(0,o.jsx)(a.A,{open:V,title:e("Add vendors from ad networks"),onCancel:()=>P(!1),footer:null,width:800,children:(0,o.jsx)(T,{onCreate:async t=>{let{list:{vendorIds:o}}=t;P(!1);const r=z(o).map((e=>{let{vendor:t}=e;return t})),[s,i,a]=_({title:e("Creating vendors..."),total:r.length});await n.onBulkCreate(r,s,i,a)}})}),(0,o.jsx)("a",{onClick:()=>P(!0),className:"button right",style:{marginBottom:10},children:t(e("Add vendors from ad networks (e.g. {{img/}} Google Adsense)"),{img:(0,o.jsx)("img",{style:{height:"1em"},src:"https://assets.devowl.io/in-app/wp-real-cookie-banner/logos/google-a-dsense.svg"})})})]}),(0,o.jsx)(i.A,{style:{width:400},children:(0,o.jsx)(d.A.Search,{autoFocus:!0,style:{maxWidth:400},placeholder:e("Search vendor by name or ID..."),onChange:e=>k((t=>({...t,vendor:e.target.value})))})}),"vendor-configurations"===x&&(0,o.jsx)(i.A,{children:(0,o.jsx)("a",{onClick:n.onSwitchToVendorView,className:"button button-primary right",style:{marginBottom:10},children:E})})]}),(0,o.jsx)("div",{style:{textAlign:"right",marginBottom:15}}),(0,o.jsxs)(r.A,{pagination:{pageSize:50,showTotal:(e,t)=>`${t[0]}-${t[1]} / ${e}`,showSizeChanger:!1},locale:{emptyText:(0,o.jsx)(l.A,{description:e("No data")})},loading:p,dataSource:O,rowKey:e=>{let{vendor:t,configuration:n}=e;return"boolean"==typeof n?t.id:n.id},size:"small",bordered:!0,rowSelection:{type:"checkbox",getCheckboxProps:e=>{let{configuration:t}=e;return{disabled:"vendors"===x&&!0===t}},selectedRowKeys:A,onChange:(e,t,n)=>{let{type:o}=n;S("all"===o?A.length>0?[]:O.map((e=>{let{configuration:t,vendor:n}=e;return"boolean"==typeof t?t?0:n.id:t.id})).filter(Boolean):e)}},children:[(0,o.jsx)(N,{title:(0,f.i)([...A.length>0?[(0,o.jsx)("span",{children:e("%d selected",A.length)},"bulk"),"vendors"===x&&(0,o.jsx)(c.A,{title:(0,o.jsxs)(o.Fragment,{children:[e("Please note that selecting more vendors than you actually use may lead to ineffective consent. Therefore, only activate the vendors that you actually actively work with!"),(0,o.jsx)("br",{}),(0,o.jsx)("br",{}),e("After activating the TCF vendors, I will check the information for each activated TCF vendor in the TCF vendor configuration myself and correct any information that does not match my use case.")]}),overlayInnerStyle:{maxWidth:300},placement:"bottomLeft",onConfirm:()=>{const[t,o,r]=_({title:e("Creating vendors..."),total:A.length});n.onBulkCreate(z(A).map((e=>{let{vendor:t}=e;return t})),t,o,r),S([])},okText:e("Create"),cancelText:e("Cancel"),children:(0,o.jsx)("a",{children:e("Create all selected vendors")})},"bulk-create"),"vendor-configurations"===x&&(0,o.jsx)(c.A,{title:e("Are you sure you want to delete this vendors?"),placement:"bottomLeft",onConfirm:()=>{const[t,o,r]=_({title:e("Deleting vendors..."),total:A.length});n.onBulkDelete(z(A).map((e=>{let{configuration:t}=e;return t})),t,o,r),S([])},okText:e("Delete all"),cancelText:e("Cancel"),children:(0,o.jsx)("a",{children:e("Delete all")})},"bulk-delete")]:[],(0,o.jsx)("span",{children:e("Vendor")},"title")],(0,o.jsx)(u.A,{type:"vertical"})),dataIndex:"vendor",render:(t,n)=>{const{vendor:r,configuration:s}=n;return(0,o.jsxs)("span",{children:[r?(0,o.jsxs)(o.Fragment,{children:[r.name," ",(0,o.jsxs)("span",{style:{opacity:.5},children:["#",r.id]})," "]}):"boolean"!=typeof s&&(0,o.jsxs)(h.A,{title:e("This vendor is no longer available and/or has been removed from the list of available vendors by the GVL. For this vendor, you can no longer request a consent from your visitors."),children:[(0,o.jsxs)("span",{style:{opacity:.5},children:["#",s.vendorId]})," ",(0,o.jsx)(v.A,{color:"error",children:e("Abandoned")})]}),"vendors"===x&&!0===s&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(v.A,{children:e("Already created")}),!1!==n.blocker&&(0,o.jsx)(v.A,{children:e("Content Blocker")})]})]})}},"vendor"),(0,o.jsx)(N,{title:e("Used in ad networks"),dataIndex:"adNetworks",render:(e,t)=>{let{vendor:n}=t;if(!n)return null;const{id:r}=n,s=R.map((e=>{let{network:t,vendorIds:n}=e;return n.indexOf(r)>-1?t:void 0})).filter(Boolean);return s.map((e=>{let{name:t,logo:n}=e;return(0,o.jsxs)("span",{style:{paddingRight:10},children:[(0,o.jsx)("img",{src:n,style:{height:"1em"}})," ",t]},t)}))}},"adNetworks"),(0,o.jsx)(N,{title:e("Privacy policy"),dataIndex:"privacyPolicy",render:(e,t)=>{let{vendor:n}=t;if(!(null==n?void 0:n.urls))return null;const{urls:[{privacy:r}]}=n;return r&&(0,o.jsx)("a",{href:r,target:"_blank",rel:"noreferrer",children:new URL(r).origin})}},"privacyPolicy"),(0,o.jsx)(N,{title:e("Actions"),dataIndex:"actions",render:(t,r)=>{const{configuration:s,vendor:i}=r;return(0,f.i)(["vendor-configurations"===x&&"boolean"!=typeof s&&r.languages&&(0,o.jsx)(F.r,{recordId:r.configuration.id,languages:r.languages,onClick:r.languageOnClick,wrapperProps:{wrapperClassName:"alignleft"}},"multilingual"),"vendors"===x&&!0!==s&&(0,o.jsx)("a",{onClick:()=>n.onCreate(i),children:e("Create")},"create"),"vendor-configurations"===x&&"boolean"!=typeof s&&i&&(0,o.jsx)("a",{onClick:()=>n.onCreateOrEditContentBlocker(s,i),children:s.blocker?e("Edit Content Blocker"):e("Create Content Blocker")},"contentBlocker"),"vendor-configurations"===x&&"boolean"!=typeof s&&i&&(0,o.jsx)("a",{onClick:()=>n.onEdit(s,i),children:e("Edit")},"edit"),"vendor-configurations"===x&&"boolean"!=typeof s&&(0,o.jsx)(c.A,{title:e("Are you sure you want to delete this vendor?"),placement:"bottomRight",onConfirm:()=>n.onDelete(s),okText:e("Delete"),cancelText:e("Cancel"),overlayStyle:{maxWidth:350},children:(0,o.jsx)("a",{children:e("Delete")})},"delete")],(0,o.jsx)(u.A,{type:"vertical"}))}},"actions")]})]}):p?(0,o.jsx)(m.A,{loading:!0,active:!0,paragraph:{rows:1}}):(0,o.jsx)(l.A,{description:e("You have not yet created a TCF vendor configuration."),children:(0,o.jsx)("a",{className:"button button-primary",onClick:n.onSwitchToVendorView,children:E})}))}},36305:(e,t,n)=>{n.d(t,{g:()=>i,l:()=>s});var o=n(52113);const r=Symbol(),s=()=>(0,o.NV)(r),i=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,o.gm)(r,...t)}},57759:(e,t,n)=>{n.r(t),n.d(t,{TcfLayout:()=>c});var o=n(3713),r=n(18197),s=n(57922),i=n(41594),a=n(27667),d=n(71951),l=n(89657);const c=(0,s.PA)((()=>{const{tcfStore:e}=(0,d.g)(),{purposes:t}=e;return(0,i.useEffect)((()=>{e.fetchDeclarations()}),[]),0===t.size?(0,o.jsx)(r.A,{style:{margin:"auto",marginTop:15}}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(a.sv,{}),(0,o.jsx)(l.b,{identifier:"tcf-vendor"})]})}))},55117:(e,t,n)=>{n.r(t),n.d(t,{TcfVendorConfigurationList:()=>y});var o=n(3713),r=n(19117),s=n(44497),i=n(57922),a=n(41594),d=n(32150),l=n(36305),c=n(96596),u=n(70884),h=n(53603),v=n(36920),m=n(36069),g=n(44227),f=n(71951),p=n(40164);const y=(0,i.PA)((()=>{const{message:e}=r.A.useApp(),[t,n]=(0,a.useState)(!1),{addLink:i,editLink:y}=(0,m.E)(),{addLink:x,editLink:C}=(0,v.t)(),{tcfStore:w,optionStore:b}=(0,f.g)(),{vendorConfigurations:j,vendorConfigurationCount:k}=w,{busy:A,entries:T}=j,S=(0,h.m)("tcf-vendor"),I=(0,u.useTcfVendorAdNetworks)();(0,a.useEffect)((()=>{k>0&&!t&&(w.fetchVendorConfigurations(),n(!0))}),[k,t]);const[F,N]=(0,l.g)({busy:A,vendorCount:k,adNetworks:I,rows:Array.from(T.values()).map((t=>{var n;const{key:o,restrictivePurposes:r,data:{status:s,meta:{vendorId:i},blocker:a,multilingual:l}}=t;return{busy:!1,configuration:{id:o,vendorId:i,blocker:a,restrictivePurposes:r,status:s},vendor:null==(n=t.vendorModel)?void 0:n.data,languages:l,languageOnClick:async(t,n)=>{let{code:o,id:r}=n;try{const e=!1===r?(await(0,d.C)("rcb-tcf-vendor-conf",t,o)).id:r,n=new URL(window.location.href);n.hash=`#/cookies/tcf-vendors/edit/${e}`,n.searchParams.set("lang",o),window.location.href=n.toString()}catch(t){var s;if(!(null==(s=t.responseJSON)?void 0:s.message))throw t;e.error(t.responseJSON.message)}}}})),view:"vendor-configurations",onSwitchToVendorView:()=>window.location.hash=i,onEdit:e=>window.location.hash=y(T.get(e.id)),onDelete:async t=>{try{await T.get(t.id).delete({force:!0})}catch(t){e.error(t.responseJSON.message)}},onCreateOrEditContentBlocker:(e,t)=>{let{id:n,blocker:o}=e,{name:r}=t;return window.location.hash=o?C(new g.g(void 0,{id:o})):`${x}?force=scratch&attributes=${JSON.stringify({name:r,tcfVendors:[n],criteria:"tcfVendors"})}`},onBulkDelete:async(t,n,o,r)=>{for(const{id:s}of t)T.get(s).delete({force:!0},{allowBatchRequest:{onQueueItemFinished:n,waitForPromise:r},settings:{signal:o}}).catch((t=>e.error(t.responseJSON.message)));await r,(0,s.runInAction)((()=>{for(const{id:e}of t)T.delete(e)})),b.fetchCurrentRevision()}},{},{inherit:["busy","vendorCount","rows"]});return(0,o.jsxs)(p.e,{children:[(0,o.jsx)(F,{value:N,children:(0,o.jsx)(c.m,{})}),(0,o.jsx)("p",{className:"description",style:{maxWidth:800,margin:"30px auto 0",textAlign:"center"},children:S})]})}))},70884:(e,t,n)=>{n.r(t),n.d(t,{TcfVendorSelector:()=>x,useTcfVendorAdNetworks:()=>y});var o=n(3713),r=n(19117),s=n(44497),i=n(57922),a=n(41594),d=n(27667),l=n(36305),c=n(96596),u=n(36962),h=n(76576),v=n(36069),m=n(8140),g=n(71951),f=n(30617),p=n(40164);function y(){return[{identifier:"google-adsense",name:"Google AdSense",description:(0,f.__)("%s displays advertisements from various advertising partners (ad servers) on your website by integrating just one script. A complex bidding system decides which advertising partner receives the advertising slot in the end.","Google AdSense"),provider:"Google",logo:"https://assets.devowl.io/in-app/wp-real-cookie-banner/logos/google-a-dsense.svg",hasRealTimeApi:!1,updatedAt:"2025-01-10T14:31:24.791Z",createContentBlockerForVendorId:755,onCreateOrEditContentBlocker:()=>{window.location.href=`#/blocker/new?force=google-adsense-tcf&navigateAfterCreation=${encodeURIComponent("#/cookies/tcf-vendors")}`},lists:[{name:(0,f.__)("Google Advertising Products only"),description:(0,f.__)("Only Google can display ads booked e.g. via Google Ads. This means that you obtain less consent from your website visitors, but will not generate the optimum revenue."),vendorIds:[755]},{name:(0,f.__)("Commonly used ad partners"),description:(0,f.__)("Consent is requested for all advertising partners who display a particularly large amount of advertising via Adsense (list provided by Google). This increases your revenue and at the same time you obtain consent from a manageable number of TCF vendors."),vendorIds:"1,4,9,10,11,12,13,15,16,22,23,24,25,27,28,34,37,39,42,44,50,59,60,68,69,71,72,73,76,77,81,82,84,85,91,93,95,97,98,109,110,115,122,126,129,130,132,136,139,140,147,156,161,163,168,192,195,213,226,228,241,243,246,253,264,273,275,278,281,284,293,294,304,308,312,315,317,328,345,373,381,384,388,394,397,402,409,415,416,447,452,468,506,512,544,559,587,606,631,657,667,734,755,758,759,762,767,772,793,806,827,832,853,929,1050,1296,1301,1334".split(",").map(Number),isRecommended:!0},{name:(0,f.__)("All ad partners using %s","Google Adsense"),description:(0,f.__)("Consent is obtained for all advertising partners who display advertising via %s. This leads to maximum revenue, but data protectionists could question the effectiveness of the consent due to the excessive number of vendors.","Google Adsense"),vendorIds:"1,2,4,8,9,10,11,12,13,14,15,16,20,22,23,24,25,26,27,28,29,30,31,33,34,37,39,42,44,44,46,47,48,50,53,55,56,58,59,60,61,62,66,68,69,71,72,73,75,76,77,80,81,82,83,84,85,90,91,92,93,94,95,97,97,98,100,101,104,108,109,110,111,115,115,120,122,122,126,127,129,130,130,131,132,133,134,136,138,139,140,141,142,143,147,147,148,150,151,153,154,155,156,157,159,160,161,163,168,173,174,178,184,185,192,193,194,195,196,199,203,203,205,206,211,213,215,216,217,224,226,227,228,231,232,235,237,238,239,241,242,243,244,246,248,249,251,252,253,254,255,259,263,264,270,273,274,275,276,278,279,280,281,282,284,285,293,293,294,297,298,301,304,308,311,312,314,315,316,317,318,319,321,323,325,326,328,329,331,333,336,337,343,345,347,350,350,354,358,361,368,371,373,374,377,378,380,381,382,384,387,388,394,397,402,409,410,412,413,415,416,418,422,424,427,430,434,435,436,438,440,444,447,448,450,452,454,459,468,469,471,473,475,479,486,488,490,491,493,495,496,497,498,501,502,506,507,508,509,511,512,516,517,519,524,527,528,531,534,535,536,539,541,544,546,549,550,551,553,554,556,559,561,568,569,570,571,572,573,580,581,584,587,591,591,596,597,598,601,606,606,610,612,615,617,618,620,621,624,625,626,628,631,639,644,646,647,648,652,653,655,656,657,658,662,663,665,666,667,671,673,674,676,677,681,682,684,685,687,690,697,699,702,706,707,708,709,712,713,714,715,716,717,718,719,721,723,724,725,726,727,728,729,730,731,732,733,734,736,736,737,738,740,742,742,744,745,746,748,749,750,751,754,755,756,758,759,762,765,766,767,768,769,770,771,772,773,774,775,776,778,779,780,781,783,784,786,788,790,793,794,795,796,797,798,799,800,801,803,804,806,806,808,810,811,812,814,815,816,819,819,820,821,822,825,827,828,831,832,833,834,835,835,838,839,840,844,845,848,849,850,851,853,854,855,856,857,858,860,861,862,864,865,867,869,870,870,871,872,874,875,876,877,878,880,881,881,882,883,884,885,888,889,891,891,893,894,896,898,900,902,903,907,910,911,911,915,919,920,922,925,927,929,930,930,931,934,935,936,937,938,941,943,944,946,950,950,951,952,953,954,955,956,957,958,959,961,962,963,964,965,966,967,968,970,972,973,975,976,978,982,987,990,991,993,994,995,996,997,998,999,1001,1002,1003,1004,1005,1006,1009,1013,1014,1015,1016,1017,1019,1020,1021,1022,1024,1025,1026,1027,1028,1029,1029,1030,1031,1036,1037,1038,1039,1040,1041,1043,1044,1045,1047,1048,1049,1050,1051,1055,1057,1058,1059,1060,1060,1061,1062,1063,1064,1067,1068,1069,1070,1071,1072,1073,1075,1076,1078,1079,1080,1081,1082,1083,1084,1085,1087,1090,1094,1097,1098,1100,1101,1103,1104,1106,1107,1108,1110,1111,1112,1113,1116,1116,1119,1120,1121,1122,1124,1126,1127,1129,1130,1132,1133,1134,1136,1137,1138,1139,1140,1141,1142,1144,1146,1147,1149,1151,1153,1154,1155,1155,1156,1157,1159,1160,1162,1162,1163,1164,1165,1167,1168,1169,1170,1171,1172,1173,1174,1174,1175,1176,1177,1178,1180,1181,1182,1183,1184,1185,1187,1188,1189,1190,1193,1195,1195,1196,1198,1199,1200,1201,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1216,1217,1218,1219,1220,1221,1222,1222,1223,1224,1225,1226,1226,1227,1228,1229,1230,1231,1232,1234,1234,1235,1236,1238,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1255,1256,1257,1258,1259,1260,1261,1262,1263,1263,1264,1266,1267,1268,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1278,1279,1280,1280,1281,1282,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1296,1297,1297,1298,1299,1300,1301,1301,1302,1303,1304,1305,1306,1307,1308,1310,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1325,1326,1327,1328,1329,1330,1330,1332,1333,1334,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1348,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376".split(",").map(Number)}]},{identifier:"the-moneytizer",name:"The Moneytizer",description:(0,f.__)("%s displays advertisements from various advertising partners (ad servers) on your website by integrating just one script. A complex bidding system decides which advertising partner receives the advertising slot in the end.","The Moneytizer"),provider:"The Moneytizer",logo:"https://assets.devowl.io/in-app/wp-real-cookie-banner/logos/the-moneyt-izer.png",hasRealTimeApi:!1,updatedAt:"2025-01-21T14:31:24.791Z",createContentBlockerForVendorId:1265,onCreateOrEditContentBlocker:()=>{window.location.href=`#/blocker/new?force=the-moneytizer-tcf&navigateAfterCreation=${encodeURIComponent("#/cookies/tcf-vendors")}`},lists:[{name:(0,f.__)("All ad partners using %s","The Moneytizer"),description:(0,f.__)("Consent is obtained for all advertising partners who display advertising via %s. This leads to maximum revenue, but data protectionists could question the effectiveness of the consent due to the excessive number of vendors.","The Moneytizer"),vendorIds:"2,10,11,12,13,16,21,23,24,25,28,31,32,36,39,40,42,45,50,52,58,61,62,68,69,71,72,73,76,79,80,81,87,90,91,95,97,102,108,111,114,128,129,131,132,138,142,142,148,151,156,157,161,164,165,183,210,231,238,241,244,253,254,259,264,276,284,285,301,316,358,380,382,410,423,434,436,469,484,511,561,565,606,610,617,639,655,666,687,724,737,755,776,779,780,781,793,799,816,842,918,922,924,990,1028,1043,1083,1111,1132,1134,1135,1148,1165,1265,1288".split(",").map(Number)}]}]}const x=(0,i.PA)((()=>{const{message:e}=r.A.useApp(),{tcfStore:t,optionStore:n}=(0,g.g)(),{busyVendors:i,fetchedAllVendorConfigurations:f,vendorConfigurations:x,vendors:C}=t,[w,b]=(0,a.useState)(),{link:j}=(0,v.E)(),{adNetwork:k}=(0,h.f)(),A=y(),T=(0,d.Zp)();(0,a.useEffect)((()=>{t.fetchedAllVendorConfigurations||t.fetchVendorConfigurations(),t.fetchVendors()}),[]);const[S,I]=(0,l.g)({busy:i||0===C.size||!f,vendorCount:C.size,rows:Array.from(C.values()).map((e=>{const{vendorConfiguration:t}=e;return{busy:!1,configuration:!!t,vendor:e.data,blocker:!!t&&t.data.blocker}})),view:"vendors",defaultCreateAdNetworkIdentifier:k,adNetworks:A,onCreate:e=>{let{id:t}=e;return b(C.get(t.toString()))},onBulkCreate:async(t,o,r,i)=>{const a=[],d=[];for(const{id:n}of t){const t=new m.p(x,{status:"publish",meta:{dataProcessingInCountries:"[]",dataProcessingInCountriesSpecialTreatments:"[]",restrictivePurposes:"[]",vendorId:n}});a.push(t),d.push(t.persist(void 0,{allowBatchRequest:{onQueueItemFinished:o,waitForPromise:i},settings:{signal:r}}).catch((t=>e.error(t.responseJSON.message))))}await Promise.allSettled(d),(0,s.runInAction)((()=>{for(const e of a)e.key&&e.collection.entries.set(e.key,e)})),await n.fetchCurrentRevision(),setTimeout((()=>T(j.slice(1))),0)}},{},{inherit:["busy","vendorCount","rows"]});return void 0===w?(0,o.jsx)(p.e,{children:(0,o.jsx)(S,{value:I,children:(0,o.jsx)(c.m,{})})}):(0,o.jsx)(p.e,{maxWidth:"fixed",children:(0,o.jsx)(u.TcfVendorConfigurationForm,{vendor:w})})}))},96077:(e,t,n)=>{n.d(t,{A:()=>d});var o=n(2464),r=n(41594);const s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"};var i=n(4679),a=function(e,t){return r.createElement(i.A,(0,o.A)({},e,{ref:t,icon:s}))};const d=r.forwardRef(a)}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/b00278f4d1ff6e3e11e7baf09183d60a/chunk-config-tab-tcf.lite.js.map
