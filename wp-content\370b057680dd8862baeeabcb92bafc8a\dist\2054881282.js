/*! For license information please see 134.lite.js.LICENSE.txt */
(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[134],{55531:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var o=n(2464),r=n(41594);const l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M793 242H366v-74c0-6.7-7.7-10.4-12.9-6.3l-142 112a8 8 0 000 12.6l142 112c5.2 4.1 12.9.4 12.9-6.3v-74h415v470H175c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h618c35.3 0 64-28.7 64-64V306c0-35.3-28.7-64-64-64z"}}]},name:"rollback",theme:"outlined"};var i=n(4679),a=function(e,t){return r.createElement(i.A,(0,o.A)({},e,{ref:t,icon:l}))};const c=r.forwardRef(a)},53573:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var o=n(2464),r=n(41594);const l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var i=n(4679),a=function(e,t){return r.createElement(i.A,(0,o.A)({},e,{ref:t,icon:l}))};const c=r.forwardRef(a)},74072:(e,t,n)=>{"use strict";n.d(t,{A:()=>me});var o=n(41594),r=n(53029),l=n(65924),i=n.n(l),a=n(87458),c=n(51963),s=n(78294),d=n(74188),u=n(15220),p=n(2620),f=n(78255),g=n(81739);const m={border:0,background:"transparent",padding:0,lineHeight:"inherit",display:"inline-flex"},b=o.forwardRef(((e,t)=>{const{style:n,noStyle:r,disabled:l,tabIndex:i=0}=e,a=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["style","noStyle","disabled","tabIndex"]);let c={};return r||(c=Object.assign({},m)),l&&(c.pointerEvents="none"),c=Object.assign(Object.assign({},c),n),o.createElement("div",Object.assign({role:"button",tabIndex:i,ref:t},a,{onKeyDown:e=>{const{keyCode:t}=e;t===g.A.ENTER&&e.preventDefault()},onKeyUp:t=>{const{keyCode:n}=t,{onClick:o}=e;n===g.A.ENTER&&o&&o()},style:c}))}));var y=n(80840),v=n(22122),h=n(64715),x=n(2464);const O={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"};var E=n(4679),w=function(e,t){return o.createElement(E.A,(0,x.A)({},e,{ref:t,icon:O}))};const C=o.forwardRef(w);var S=n(79045),k=n(77381),j=n(11981),A=n(52146),R=n(42677),$=n(78052);const D=e=>{const t={};return[1,2,3,4,5].forEach((n=>{t[`\n      h${n}&,\n      div&-h${n},\n      div&-h${n} > textarea,\n      h${n}\n    `]=((e,t,n,o)=>{const{titleMarginBottom:r,fontWeightStrong:l}=o;return{marginBottom:r,color:n,fontWeight:l,fontSize:e,lineHeight:t}})(e[`fontSizeHeading${n}`],e[`lineHeightHeading${n}`],e.colorTextHeading,e)})),t},I=e=>{const{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},(0,j.Y)(e)),{textDecoration:e.linkDecoration,"&:active, &:hover":{textDecoration:e.linkHoverDecoration},[`&[disabled], &${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},T=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:R.bK[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),H=e=>{const{componentCls:t,paddingSM:n}=e,o=n;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(o).mul(-1).equal(),marginBottom:`calc(1em - ${(0,$.zA)(o)})`},[`${t}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorTextDescription,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},z=e=>({[`${e.componentCls}-copy-success`]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},[`${e.componentCls}-copy-icon-only`]:{marginInlineStart:0}}),M=e=>{const{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${t}-secondary`]:{color:e.colorTextDescription},[`&${t}-success`]:{color:e.colorSuccess},[`&${t}-warning`]:{color:e.colorWarning},[`&${t}-danger`]:{color:e.colorError,"a&:active, a&:focus":{color:e.colorErrorActive},"a&:hover":{color:e.colorErrorHover}},[`&${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},D(e)),{[`\n      & + h1${t},\n      & + h2${t},\n      & + h3${t},\n      & + h4${t},\n      & + h5${t}\n      `]:{marginTop:n},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:n}}}),T(e)),I(e)),{[`\n        ${t}-expand,\n        ${t}-collapse,\n        ${t}-edit,\n        ${t}-copy\n      `]:Object.assign(Object.assign({},(0,j.Y)(e)),{marginInlineStart:e.marginXXS})}),H(e)),z(e)),{"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),{"&-rtl":{direction:"rtl"}})}},P=(0,A.OF)("Typography",(e=>[M(e)]),(()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"}))),B=e=>{const{prefixCls:t,"aria-label":n,className:r,style:l,direction:a,maxLength:c,autoSize:s=!0,value:d,onSave:u,onCancel:p,onEnd:f,component:m,enterIcon:b=o.createElement(C,null)}=e,y=o.useRef(null),v=o.useRef(!1),h=o.useRef(),[x,O]=o.useState(d);o.useEffect((()=>{O(d)}),[d]),o.useEffect((()=>{if(y.current&&y.current.resizableTextArea){const{textArea:e}=y.current.resizableTextArea;e.focus();const{length:t}=e.value;e.setSelectionRange(t,t)}}),[]);const E=()=>{u(x.trim())},w=m?`${t}-${m}`:"",[j,A,R]=P(t),$=i()(t,`${t}-edit-content`,{[`${t}-rtl`]:"rtl"===a},r,w,A,R);return j(o.createElement("div",{className:$,style:l},o.createElement(k.A,{ref:y,maxLength:c,value:x,onChange:e=>{let{target:t}=e;O(t.value.replace(/[\n\r]/g,""))},onKeyDown:e=>{let{keyCode:t}=e;v.current||(h.current=t)},onKeyUp:e=>{let{keyCode:t,ctrlKey:n,altKey:o,metaKey:r,shiftKey:l}=e;h.current!==t||v.current||n||o||r||l||(t===g.A.ENTER?(E(),null==f||f()):t===g.A.ESC&&p())},onCompositionStart:()=>{v.current=!0},onCompositionEnd:()=>{v.current=!1},onBlur:()=>{E()},"aria-label":n,rows:1,autoSize:s}),null!==b?(0,S.Ob)(b,{className:`${t}-edit-content-confirm`}):null))};var N=n(77676),W=n.n(N),L=n(52733);const F=e=>{let{copyConfig:t,children:n}=e;const[r,l]=o.useState(!1),[i,a]=o.useState(!1),c=o.useRef(null),s=()=>{c.current&&clearTimeout(c.current)},d={};return t.format&&(d.format=t.format),o.useEffect((()=>s),[]),{copied:r,copyLoading:i,onClick:(0,L._q)((e=>{return o=void 0,r=void 0,u=function*(){var o;null==e||e.preventDefault(),null==e||e.stopPropagation(),a(!0);try{const r="function"==typeof t.text?yield t.text():t.text;W()(r||String(n)||"",d),a(!1),l(!0),s(),c.current=setTimeout((()=>{l(!1)}),3e3),null===(o=t.onCopy)||void 0===o||o.call(t,e)}catch(e){throw a(!1),e}},new((i=void 0)||(i=Promise))((function(e,t){function n(e){try{a(u.next(e))}catch(e){t(e)}}function l(e){try{a(u.throw(e))}catch(e){t(e)}}function a(t){var o;t.done?e(t.value):(o=t.value,o instanceof i?o:new i((function(e){e(o)}))).then(n,l)}a((u=u.apply(o,r||[])).next())}));var o,r,i,u}))}};function K(e,t){return o.useMemo((()=>{const n=!!e;return[n,Object.assign(Object.assign({},t),n&&"object"==typeof e?e:null)]}),[e])}const U=o.forwardRef(((e,t)=>{const{prefixCls:n,component:r="article",className:l,rootClassName:a,setContentRef:c,children:s,direction:d,style:u}=e,f=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:g,direction:m,typography:b}=o.useContext(y.QO),v=null!=d?d:m;let h=t;c&&(h=(0,p.K4)(t,c));const x=g("typography",n),[O,E,w]=P(x),C=i()(x,null==b?void 0:b.className,{[`${x}-rtl`]:"rtl"===v},l,a,E,w),S=Object.assign(Object.assign({},null==b?void 0:b.style),u);return O(o.createElement(r,Object.assign({className:C,style:S,ref:h},f),s))})),V=U;var q=n(41415);const X={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};var _=function(e,t){return o.createElement(E.A,(0,x.A)({},e,{ref:t,icon:X}))};const Q=o.forwardRef(_);var Y=n(9066);function G(e){return!1===e?[!1,!1]:Array.isArray(e)?e:[e]}function J(e,t,n){return!0===e||void 0===e?t:e||n&&t}const Z=e=>{const{prefixCls:t,copied:n,locale:r,iconOnly:l,tooltips:a,icon:c,loading:s,tabIndex:d,onCopy:u}=e,p=G(a),f=G(c),{copied:g,copy:m}=null!=r?r:{},y=n?J(p[1],g):J(p[0],m),v="string"==typeof y?y:n?g:m;return o.createElement(h.A,{key:"copy",title:y},o.createElement(b,{className:i()(`${t}-copy`,{[`${t}-copy-success`]:n,[`${t}-copy-icon-only`]:l}),onClick:u,"aria-label":v,tabIndex:d},n?J(f[1],o.createElement(q.A,null),!0):J(f[0],s?o.createElement(Y.A,null):o.createElement(Q,null),!0)))};var ee=n(18539);const te=o.forwardRef(((e,t)=>{let{style:n,children:r}=e;const l=o.useRef(null);return o.useImperativeHandle(t,(()=>({isExceed:()=>{const e=l.current;return e.scrollHeight>e.clientHeight},getHeight:()=>l.current.clientHeight}))),o.createElement("span",{"aria-hidden":!0,ref:l,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},n)},r)}));function ne(e){const t=typeof e;return"string"===t||"number"===t}function oe(e,t){let n=0;const o=[];for(let r=0;r<e.length;r+=1){if(n===t)return o;const l=e[r],i=n+(ne(l)?String(l).length:1);if(i>t){const e=t-n;return o.push(String(l).slice(0,e)),o}o.push(l),n=i}return e}const re={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function le(e){const{enableMeasure:t,width:n,text:r,children:l,rows:i,expanded:a,miscDeps:d,onEllipsis:u}=e,p=o.useMemo((()=>(0,c.A)(r)),[r]),f=o.useMemo((()=>function(e){let t=0;return e.forEach((e=>{ne(e)?t+=String(e).length:t+=1})),t}(p)),[r]),g=o.useMemo((()=>l(p,!1)),[r]),[m,b]=o.useState(null),y=o.useRef(null),v=o.useRef(null),h=o.useRef(null),x=o.useRef(null),[O,E]=o.useState(!1),[w,C]=o.useState(0),[S,k]=o.useState(0);(0,s.A)((()=>{C(t&&n&&f?1:0)}),[n,r,i,t,p]),(0,s.A)((()=>{var e,t,n,o;if(1===w){const r=!!(null===(e=v.current)||void 0===e?void 0:e.isExceed());C(r?2:3),b(r?[0,f]:null),E(r);const l=(null===(t=v.current)||void 0===t?void 0:t.getHeight())||0,a=(1===i?0:(null===(n=h.current)||void 0===n?void 0:n.getHeight())||0)+((null===(o=x.current)||void 0===o?void 0:o.getHeight())||0),c=Math.max(l,a);k(c+1),u(r)}}),[w]);const j=m?Math.ceil((m[0]+m[1])/2):0;(0,s.A)((()=>{var e;const[t,n]=m||[0,0];if(t!==n){const o=((null===(e=y.current)||void 0===e?void 0:e.getHeight())||0)>S;let r=j;n-t==1&&(r=o?t:n),b(o?[t,r]:[r,n])}}),[m,j]);const A=o.useMemo((()=>{if(2!==w||!m||m[0]!==m[1]){const e=l(p,!1);return 3!==w&&0!==w?o.createElement("span",{style:Object.assign(Object.assign({},re),{WebkitLineClamp:i})},e):e}return l(a?p:oe(p,m[0]),O)}),[a,w,m,p].concat((0,ee.A)(d))),R={width:n,margin:0,padding:0};return o.createElement(o.Fragment,null,A,1===w&&o.createElement(o.Fragment,null,o.createElement(te,{style:Object.assign(Object.assign(Object.assign({},R),re),{WebkitLineClamp:i}),ref:v},g),o.createElement(te,{style:Object.assign(Object.assign(Object.assign({},R),re),{WebkitLineClamp:i-1}),ref:h},g),o.createElement(te,{style:Object.assign(Object.assign(Object.assign({},R),re),{WebkitLineClamp:1}),ref:x},l([],!0))),2===w&&m&&m[0]!==m[1]&&o.createElement(te,{style:Object.assign(Object.assign({},R),{top:400}),ref:y},l(oe(p,j),!0)))}const ie=e=>{let{enableEllipsis:t,isEllipsis:n,children:r,tooltipProps:l}=e;return(null==l?void 0:l.title)&&t?o.createElement(h.A,Object.assign({open:!!n&&void 0},l),r):r};const ae=o.forwardRef(((e,t)=>{var n,l,g;const{prefixCls:m,className:x,style:O,type:E,disabled:w,children:C,ellipsis:S,editable:k,copyable:j,component:A,title:R}=e,$=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:D,direction:I}=o.useContext(y.QO),[T]=(0,v.A)("Text"),H=o.useRef(null),z=o.useRef(null),M=D("typography",m),P=(0,u.A)($,["mark","code","delete","underline","strong","keyboard","italic"]),[N,W]=K(k),[L,U]=(0,d.A)(!1,{value:W.editing}),{triggerType:q=["icon"]}=W,X=e=>{var t;e&&(null===(t=W.onStart)||void 0===t||t.call(W)),U(e)},_=(e=>{const t=(0,o.useRef)();return(0,o.useEffect)((()=>{t.current=e})),t.current})(L);((e,t)=>{const n=o.useRef(!1);o.useEffect((()=>{n.current?e():n.current=!0}),t)})((()=>{var e;!L&&_&&(null===(e=z.current)||void 0===e||e.focus())}),[L]);const Q=e=>{null==e||e.preventDefault(),X(!0)},[Y,G]=K(j),{copied:J,copyLoading:ee,onClick:te}=F({copyConfig:G,children:C}),[ne,oe]=o.useState(!1),[re,ae]=o.useState(!1),[ce,se]=o.useState(!1),[de,ue]=o.useState(!1),[pe,fe]=o.useState(!0),[ge,me]=K(S,{expandable:!1,symbol:e=>e?null==T?void 0:T.collapse:null==T?void 0:T.expand}),[be,ye]=(0,d.A)(me.defaultExpanded||!1,{value:me.expanded}),ve=ge&&(!be||"collapsible"===me.expandable),{rows:he=1}=me,xe=o.useMemo((()=>ve&&(void 0!==me.suffix||me.onEllipsis||me.expandable||N||Y)),[ve,me,N,Y]);(0,s.A)((()=>{ge&&!xe&&(oe((0,f.F)("webkitLineClamp")),ae((0,f.F)("textOverflow")))}),[xe,ge]);const[Oe,Ee]=o.useState(ve),we=o.useMemo((()=>!xe&&(1===he?re:ne)),[xe,re,ne]);(0,s.A)((()=>{Ee(we&&ve)}),[we,ve]);const Ce=ve&&(Oe?de:ce),Se=ve&&1===he&&Oe,ke=ve&&he>1&&Oe,[je,Ae]=o.useState(0),Re=e=>{var t;se(e),ce!==e&&(null===(t=me.onEllipsis)||void 0===t||t.call(me,e))};o.useEffect((()=>{const e=H.current;if(ge&&Oe&&e){const t=ke?e.offsetHeight<e.scrollHeight:e.offsetWidth<e.scrollWidth;de!==t&&ue(t)}}),[ge,Oe,C,ke,pe,je]),o.useEffect((()=>{const e=H.current;if("undefined"==typeof IntersectionObserver||!e||!Oe||!ve)return;const t=new IntersectionObserver((()=>{fe(!!e.offsetParent)}));return t.observe(e),()=>{t.disconnect()}}),[Oe,ve]);let $e={};$e=!0===me.tooltip?{title:null!==(n=W.text)&&void 0!==n?n:C}:o.isValidElement(me.tooltip)?{title:me.tooltip}:"object"==typeof me.tooltip?Object.assign({title:null!==(l=W.text)&&void 0!==l?l:C},me.tooltip):{title:me.tooltip};const De=o.useMemo((()=>{const e=e=>["string","number"].includes(typeof e);if(ge&&!Oe)return e(W.text)?W.text:e(C)?C:e(R)?R:e($e.title)?$e.title:void 0}),[ge,Oe,R,$e.title,Ce]);if(L)return o.createElement(B,{value:null!==(g=W.text)&&void 0!==g?g:"string"==typeof C?C:"",onSave:e=>{var t;null===(t=W.onChange)||void 0===t||t.call(W,e),X(!1)},onCancel:()=>{var e;null===(e=W.onCancel)||void 0===e||e.call(W),X(!1)},onEnd:W.onEnd,prefixCls:M,className:x,style:O,direction:I,component:A,maxLength:W.maxLength,autoSize:W.autoSize,enterIcon:W.enterIcon});const Ie=()=>{const{expandable:e,symbol:t}=me;return e?be&&"collapsible"!==e?null:o.createElement("a",{key:"expand",className:`${M}-${be?"collapse":"expand"}`,onClick:e=>((e,t)=>{var n;ye(t.expanded),null===(n=me.onExpand)||void 0===n||n.call(me,e,t)})(e,{expanded:!be}),"aria-label":be?T.collapse:null==T?void 0:T.expand},"function"==typeof t?t(be):t):null},Te=()=>{if(!N)return;const{icon:e,tooltip:t,tabIndex:n}=W,l=(0,c.A)(t)[0]||(null==T?void 0:T.edit),i="string"==typeof l?l:"";return q.includes("icon")?o.createElement(h.A,{key:"edit",title:!1===t?"":l},o.createElement(b,{ref:z,className:`${M}-edit`,onClick:Q,"aria-label":i,tabIndex:n},e||o.createElement(r.A,{role:"button"}))):null},He=e=>[e&&Ie(),Te(),Y?o.createElement(Z,Object.assign({key:"copy"},G,{prefixCls:M,copied:J,locale:T,onCopy:te,loading:ee,iconOnly:null==C})):null];return o.createElement(a.A,{onResize:e=>{let{offsetWidth:t}=e;Ae(t)},disabled:!ve},(n=>o.createElement(ie,{tooltipProps:$e,enableEllipsis:ve,isEllipsis:Ce},o.createElement(V,Object.assign({className:i()({[`${M}-${E}`]:E,[`${M}-disabled`]:w,[`${M}-ellipsis`]:ge,[`${M}-ellipsis-single-line`]:Se,[`${M}-ellipsis-multiple-line`]:ke},x),prefixCls:m,style:Object.assign(Object.assign({},O),{WebkitLineClamp:ke?he:void 0}),component:A,ref:(0,p.K4)(n,H,t),direction:I,onClick:q.includes("text")?Q:void 0,"aria-label":null==De?void 0:De.toString(),title:R},P),o.createElement(le,{enableMeasure:ve&&!Oe,text:C,rows:he,width:je,onEllipsis:Re,expanded:be,miscDeps:[J,be,ee,N,Y]},((t,n)=>function(e,t){let{mark:n,code:r,underline:l,delete:i,strong:a,keyboard:c,italic:s}=e,d=t;function u(e,t){t&&(d=o.createElement(e,{},d))}return u("strong",a),u("u",l),u("del",i),u("code",r),u("mark",n),u("kbd",c),u("i",s),d}(e,o.createElement(o.Fragment,null,t.length>0&&n&&!be&&De?o.createElement("span",{key:"show-content","aria-hidden":!0},t):t,(e=>[e&&!be&&o.createElement("span",{"aria-hidden":!0,key:"ellipsis"},"..."),me.suffix,He(e)])(n)))))))))}));const ce=o.forwardRef(((e,t)=>{var{ellipsis:n,rel:r}=e,l=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["ellipsis","rel"]);const i=Object.assign(Object.assign({},l),{rel:void 0===r&&"_blank"===l.target?"noopener noreferrer":r});return delete i.navigate,o.createElement(ae,Object.assign({},i,{ref:t,ellipsis:!!n,component:"a"}))})),se=o.forwardRef(((e,t)=>o.createElement(ae,Object.assign({ref:t},e,{component:"div"}))));const de=(e,t)=>{var{ellipsis:n}=e,r=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["ellipsis"]);const l=o.useMemo((()=>n&&"object"==typeof n?(0,u.A)(n,["expandable","rows"]):n),[n]);return o.createElement(ae,Object.assign({ref:t},r,{ellipsis:l,component:"span"}))},ue=o.forwardRef(de);const pe=[1,2,3,4,5],fe=o.forwardRef(((e,t)=>{const{level:n=1}=e,r=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["level"]);let l;return l=pe.includes(n)?`h${n}`:"h1",o.createElement(ae,Object.assign({ref:t},r,{component:l}))})),ge=V;ge.Text=ue,ge.Link=ce,ge.Title=fe,ge.Paragraph=se;const me=ge},77676:(e,t,n)=>{"use strict";var o=n(13178),r={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var n,l,i,a,c,s,d=!1;t||(t={}),n=t.debug||!1;try{if(i=o(),a=document.createRange(),c=document.getSelection(),(s=document.createElement("span")).textContent=e,s.ariaHidden="true",s.style.all="unset",s.style.position="fixed",s.style.top=0,s.style.clip="rect(0, 0, 0, 0)",s.style.whiteSpace="pre",s.style.webkitUserSelect="text",s.style.MozUserSelect="text",s.style.msUserSelect="text",s.style.userSelect="text",s.addEventListener("copy",(function(o){if(o.stopPropagation(),t.format)if(o.preventDefault(),void 0===o.clipboardData){n&&console.warn("unable to use e.clipboardData"),n&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var l=r[t.format]||r.default;window.clipboardData.setData(l,e)}else o.clipboardData.clearData(),o.clipboardData.setData(t.format,e);t.onCopy&&(o.preventDefault(),t.onCopy(o.clipboardData))})),document.body.appendChild(s),a.selectNodeContents(s),c.addRange(a),!document.execCommand("copy"))throw new Error("copy command was unsuccessful");d=!0}catch(o){n&&console.error("unable to copy using execCommand: ",o),n&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),d=!0}catch(o){n&&console.error("unable to copy using clipboardData: ",o),n&&console.error("falling back to prompt"),l=function(e){var t=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return e.replace(/#{\s*key\s*}/g,t)}("message"in t?t.message:"Copy to clipboard: #{key}, Enter"),window.prompt(l,e)}}finally{c&&("function"==typeof c.removeRange?c.removeRange(a):c.removeAllRanges()),s&&document.body.removeChild(s),i()}return d}},13178:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],o=0;o<e.rangeCount;o++)n.push(e.getRangeAt(o));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach((function(t){e.addRange(t)})),t&&t.focus()}}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.19/f32f4b0db39d7318f153cec2a3e0a429/134.lite.js.map
