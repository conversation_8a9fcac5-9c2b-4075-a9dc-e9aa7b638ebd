"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[4],{18505:(e,t,n)=>{n.d(t,{apply:()=>k});var s=n(22834),a=n(66399),r=n(84200);function o(e){var t;return(0,r.g)(e)&&!/^\.?(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9])$/gm.test(e)?null==(t=(new DOMParser).parseFromString(`<a href="${e}"></a>`,"text/html").querySelector("a"))?void 0:t.href:(new DOMParser).parseFromString(e,"text/html").documentElement.textContent}var i=n(34766);function l(e,t,s){return void 0===s&&(s=document.body),new Promise((r=>{e?(0,a.P)().then((()=>Promise.all([n.e(343),n.e(4)]).then(n.t.bind(n,61104,23)).then((n=>{let{default:a}=n;return a(s,(0,i.t)(e,t),{done:r,error:e=>{console.error(e)},beforeWriteToken:e=>{const{attrs:t,booleanAttrs:n,src:s,href:a,content:r,tagName:i}=e;let l=s;if(null==n?void 0:n["skip-write"])return!1;for(const e in t)if(t[e]=o(t[e]),"unique-write-name"===e&&document.querySelector(`[unique-write-name="${t[e]}"]`))return!1;var c;return"script"===i&&""===(null==r?void 0:r.trim())&&"undefined"===l&&(l=null==(c=Object.entries(t).find((e=>{let[t,n]=e;try{if(-1===["id","src","type"].indexOf(t)){const{pathname:e}=new URL(n,window.location.href);if(e.indexOf(".js")>-1||n.startsWith("http"))return n}}catch(e){}})))?void 0:c[1]),l&&(e.src=o(l)),a&&(e.href=o(a)),e}})})))):r()}))}var c=n(50724);const d="Google Tag Manager",u="Matomo Tag Manager",p="gtm",g="mtm";function h(e,t){let n,s,a,{presetId:r,isGcm:o}=t,i=!1,l="";const c={events:!0,executeCodeWhenNoTagManagerConsentIsGiven:!0};let h=e||"none";switch("googleTagManagerWithGcm"!==h||o||(h="googleTagManager"),h){case"googleTagManager":case"googleTagManagerWithGcm":a=p,n="dataLayer",l=d,c.events="googleTagManagerWithGcm"!==h;break;case"matomoTagManager":a=g,n="_mtm",l=u;break;default:c.events=!1,c.executeCodeWhenNoTagManagerConsentIsGiven=!1}return n&&(s=()=>(window[n]=window[n]||[],window[n])),a&&r===a&&(i=!0,c.events=!1,c.executeCodeWhenNoTagManagerConsentIsGiven=!1),{getDataLayer:s,useManager:h,serviceIsManager:i,managerLabel:l,expectedManagerPresetId:a,features:c}}function m(e){let t,{decisionCookieName:n,setCookiesViaManager:s,isGcm:a,groups:r,type:o}=e;const{useManager:i}=h(s,{isGcm:a,presetId:""}),l=r.find((e=>{let{isEssential:t}=e;return t})),d={[l.id]:l.items.map((e=>{let{id:t}=e;return t}))};if("consent"===o){const e=(0,c.y)(n);!1!==e?t=e.consent:(console.warn("Something went wrong while reading the cookie, fallback to essentials only..."),t=d)}return"essentials"===o&&(t=d),{isManagerActive:"none"!==i,selectedGroups:t,iterateServices:async function(e){const n=r.map((e=>e.items.map((t=>[e,t])))).flat();n.sort(((e,t)=>e[1].executePriority-t[1].executePriority));for(const[r,l]of n){var i;const n="all"===o||(null==(i=t[r.id])?void 0:i.indexOf(l.id))>-1,c=h(s,{presetId:l.presetId,isGcm:a});await e(r,l,n,c)}}}}const f="RCB/OptIn";var v=n(57177);function x(e){return`^${(t=e.replace(/\*/g,"PLEACE_REPLACE_ME_AGAIN"),t.replace(new RegExp("[.\\\\+*?\\[\\^\\]$(){}=!<>|:\\-]","g"),"\\$&")).replace(/PLEACE_REPLACE_ME_AGAIN/g,"(.*)")}$`;var t}const C="RCB/OptOut";var b=n(80072);const y="RCB/OptIn/All",j="RCB/OptOut/All";async function k(e){const t=[];await m(e).iterateServices((async(e,n,s)=>{s&&t.push({group:e,service:n})})),document.dispatchEvent(new CustomEvent(b.r,{detail:{services:t,triggeredByOtherTab:e.triggeredByOtherTab}})),await(0,s.G)();const{dataLayer:n,isManagerOptOut:r,services:o,ready:c}=await async function(e){const t=[],{isManagerActive:n,iterateServices:s}=m(e),{skipOptIn:a}=e;const r=[];return await s((async(e,s,o,i)=>{let{getDataLayer:c,serviceIsManager:d}=i;const{codeDynamics:u,codeOptIn:p,executeCodeOptInWhenNoTagManagerConsentIsGiven:g}=s;if(o){const o=n&&g,i="function"==typeof a&&a(s);o||i||!p||r.push(l(p,u));const c={group:e,service:s};document.dispatchEvent(new CustomEvent(f,{detail:c})),t.push(c)}})),{isManagerOptOut:!1,dataLayer:undefined,services:t,ready:Promise.all(r)}}(e),{ready:d}=await async function(e,t,n){const s=[],{isManagerActive:a,iterateServices:r}=m(e);return t?(r(((e,n,s,a)=>{let{tagManagerOptInEventName:r}=n,{features:o}=a;s&&r&&o.events&&t.push({event:r})})),setTimeout((()=>r(((e,n,s,a)=>{let{tagManagerOptOutEventName:r}=n,{features:o}=a;!s&&r&&o.events&&t.push({event:r})}))),1e3)):a&&n&&await r((async(e,t,n)=>{let{codeDynamics:a,codeOptIn:r,executeCodeOptInWhenNoTagManagerConsentIsGiven:o}=t;n&&o&&s.push(l(r,a))})),{ready:Promise.all(s)}}(e,n,r),u=Promise.all([c,d]);await(0,a.P)(),document.dispatchEvent(new CustomEvent(y,{detail:{services:o,ready:u}}));const{deleteHttpCookies:p,services:g,ready:h}=await async function(e,t){const n=[],{isManagerActive:s,iterateServices:a}=m(e),r=[],o=[];return await a((async(e,a,c)=>{const{id:d,codeDynamics:u,codeOptOut:p,deleteTechnicalDefinitionsAfterOptOut:g,isEmbeddingOnlyExternalResources:h,technicalDefinitions:m,executeCodeOptOutWhenNoTagManagerConsentIsGiven:f}=a;if(!c){const c=s&&f;(c&&t||!c)&&r.push(l(p,u)),g&&!h&&(function(e,t){for(const{type:n,name:s}of e){const e=(0,i.t)(s,t);if("*"===e)continue;const a=new RegExp(x(e),"g");switch(n){case"http":for(const e of Object.keys(v.A.get()))a.test(e)&&v.A.remove(e);break;case"local":case"session":try{const e="local"===n?window.localStorage:window.sessionStorage;if(e)for(const t of Object.keys(e))if(a.test(t)){try{e.setItem(t,null)}catch(e){}let n=0;for(;e.getItem(t)&&n<100;)n++,e.removeItem(t)}}catch(e){continue}}}}(m,u),m.some((e=>{let{type:t}=e;return"http"===t}))&&o.push(d));const b={group:e,service:a};document.dispatchEvent(new CustomEvent(C,{detail:b})),n.push(b)}})),{services:n,ready:Promise.all(r),deleteHttpCookies:o}}(e,r);document.dispatchEvent(new CustomEvent(j,{detail:{services:g,deleteHttpCookies:p,ready:Promise.all([u,h])}}))}},86264:(e,t,n)=>{n.d(t,{persistWithQueueFallback:()=>a});var s=n(5974);async function a(e,t){e.createdClientTime=(new Date).toISOString();const n=t.getConsentQueue();n.push(e),t.setConsentQueue(n);try{await t.getOption("persistConsent")(e,!0),t.setConsentQueue(t.getConsentQueue().filter((t=>{let{createdClientTime:n}=t;return e.createdClientTime!==n})))}catch(n){const{groups:a,decisionCookieName:r,tcfCookieName:o,gcmCookieName:i,failedConsentDocumentationHandling:l,revisionHash:c}=t.getOptions(),d="optimistic"===l,{decision:u,createdClientTime:p,tcfString:g,gcmConsent:h,buttonClicked:m}=e,f={consent:d?"all"===u?a.reduce(((e,t)=>(e[t.id]=t.items.map((e=>{let{id:t}=e;return t})),e)),{}):"essentials"===u?(0,s.w)(a,!1):u:(0,s.w)(a,!1),previousUuids:[],revision:c,uuid:p,created:new Date(p),buttonClicked:m};localStorage.setItem(r,JSON.stringify(f)),g&&localStorage.setItem(o,d?g:""),h&&localStorage.setItem(i,d?JSON.stringify(h):"[]")}}},17724:(e,t,n)=>{n.d(t,{retryPersistFromQueue:()=>a});var s=n(57177);function a(e,t){void 0===t&&(t=!1);const{decisionCookieName:n,tcfCookieName:r,gcmCookieName:o}=e.getOptions(),i=()=>{localStorage.removeItem(n),localStorage.removeItem(r),localStorage.removeItem(o),localStorage.removeItem(e.getConsentQueueName())},l=document.querySelector('a[href*="rcb-clear-current-cookie=1"]');if(null==l||l.addEventListener("click",i),e.isConsentQueueLocked()){const t=t=>{t.key!==e.getConsentQueueName(!0)||t.newValue||a(e)};return window.addEventListener("storage",t),()=>{window.removeEventListener("storage",t),null==l||l.removeEventListener("click",i)}}{let a,r=0;const o=async()=>{e.isConsentQueueLocked(!0);const t=e.getConsentQueue();let l=15e3;if(t.length>0){r++;try{const a=t.shift(),o=0===t.length||!s.A.get(n),c=await e.getOption("persistConsent")(a,o),d=s.A.get(n);d&&-1===d.indexOf(c)&&s.A.set(n,d.replace(/^(.*?:.*?):/gm,`$1,${c}:`)),e.setConsentQueue(t),0===t.length&&i(),r=0,l=1500}catch(e){l=15*r*1e3}}a=setTimeout(o,l)};return e.isConsentQueueLocked(!0),a=setTimeout(o,t?0:15e3),()=>{e.isConsentQueueLocked(!1),clearTimeout(a),null==l||l.removeEventListener("click",i)}}}},34766:(e,t,n)=>{n.d(t,{t:()=>a});const s=/{{([A-Za-z0-9_]+)}}/gm;function a(e,t){return e.replace(s,((e,n)=>Object.prototype.hasOwnProperty.call(t,n)?t[n]:e))}},69558:(e,t,n)=>{n.r(t),n.d(t,{BannerHistorySelect:()=>d});var s=n(3713),a=n(41594),r=n(16215),o=n(20089),i=n(45746),l=n(84094),c=n(30680);const d=()=>{const e=(0,l.Y)(),{Select:t}=(0,c.y)().extend(...i.I),{set:n,consent:d,groups:u,tcf:p,isGcm:g,gcmConsent:h,lazyLoadedDataForSecondView:m,activeAction:f,history:v,fetchHistory:x,visible:C,i18n:{historyLabel:b,historyItemLoadError:y,historySelectNone:j}}=e,[k,S]=(0,a.useState)(),[E,I]=(0,a.useState)({consent:d,groups:u,tcf:p,gcmConsent:h,lazyLoadedDataForSecondView:m}),w=e=>{let{buttonClicked:t,tcf:s,gcmConsent:a,...i}=e;const l={isTcf:!!s,tcf:null,gcmConsent:[]};s&&(Object.assign(l,{tcf:"gvl"in s?s:(0,r.T)(s)}),(null==t?void 0:t.startsWith("implicit_"))&&(0,o.o)(l.tcf.model,t)),n({...i,...l})};(0,a.useEffect)((()=>{const e={consent:[],groups:[],gcmConsent:[],lazyLoadedDataForSecondView:void 0};if(k){const{context:t}=k;w(t||e)}else w(e)}),[k]);const P=(0,a.useRef)(!1);(0,a.useEffect)((()=>{m&&!P.current&&"history"===f&&C&&(P.current=!0,async function(){const e=await x();I({consent:d,groups:u,tcf:p,gcmConsent:h,lazyLoadedDataForSecondView:m}),n({history:e}),S(e[0])}())}),[m,f,C]),(0,a.useEffect)((()=>{C||(P.current=!1)}),[C]),(0,a.useEffect)((()=>()=>w(E)),[]);const M=null==k?void 0:k.uuid;return(0,s.jsxs)(a.Fragment,{children:[b," ",(0,s.jsx)(t,{disabled:!(null==v?void 0:v.length),value:(null==k?void 0:k.id)||-1,onChange:e=>{const t=+e.target.value;for(const e of v){const{id:n}=e;if(n===t){S(e);break}}},children:(null==v?void 0:v.length)>0?v.map((e=>{let{id:t,isDoNotTrack:n,isUnblock:a,isForwarded:r,created:o}=e;return(0,s.jsxs)("option",{value:t,children:[new Date(o).toLocaleString(document.documentElement.lang),n?" (Do Not Track)":"",a?" (Content Blocker)":"",r?" (Consent Forwarding)":""]},t)})):(0,s.jsx)("option",{value:-1,children:j})}),(0,s.jsxs)("div",{style:{opacity:.5,marginTop:5},children:["UUID: ",M||"-"]}),!(null==k?void 0:k.context)&&(0,s.jsx)("div",{style:{fontWeight:"bold",marginTop:5},children:y})]})}},53602:(e,t,n)=>{n.r(t),n.d(t,{BannerGroupList:()=>P});var s=n(3713),a=n(41594),r=n(84094),o=n(45453),i=n(55924),l=n(59112),c=n(84832),d=n(81784),u=n(43181),p=n(9463),g=n(41520),h=n(64349),m=n(30680),f=n(4959);const v=e=>{let{type:t,isDisabled:n,isBold:a}=e;const{Cookie:r}=(0,m.y)().extend(...o.C),{activeAction:i,gcmConsent:l,updateGcmConsentTypeChecked:c,group:{descriptionFontSize:d},i18n:{gcm:{purposes:{[t]:u}}}}=(0,g.b)();return(0,s.jsx)(r,{children:(0,s.jsx)(f.S,{isChecked:l.indexOf(t)>-1,isDisabled:n||"history"===i,fontSize:d,onToggle:e=>c(t,e),children:(0,s.jsx)("span",{style:{fontWeight:a?"bold":void 0},children:u})})})},x=e=>{let{cookie:{name:t,purpose:n,isProviderCurrentWebsite:r,provider:o,providerContact:m={},providerPrivacyPolicyUrl:f,providerLegalNoticeUrl:x,legalBasis:C,dataProcessingInCountries:b,dataProcessingInCountriesSpecialTreatments:y,isEmbeddingOnlyExternalResources:j,technicalDefinitions:k,codeDynamics:S,googleConsentModeConsentTypes:E},isEssentialGroup:I,isDisabled:w}=e;const{i18n:P,iso3166OneAlpha2:M,websiteOperator:O,isGcm:T,designVersion:D}=(0,g.b)(),{deprecated:L,legalBasis:A}=P,{dataProcessingInUnsafeCountries:F,appropriateSafeguards:B}=(0,p.Q)({dataProcessingInCountries:b,specialTreatments:y}),{legalNotice:N,privacyPolicy:R,contactForm:$}=(0,h.s)(),z=(0,a.useMemo)((()=>{if(r&&O){const{address:e,country:t,contactEmail:n,contactPhone:s}=O;return{provider:[e,M[t]||t].filter(Boolean).join(", "),contact:{email:n,phone:s,link:$},legalNoticeUrl:!1===N?"":N.url,privacyPolicyUrl:!1===R?"":R.url}}return{provider:o,contact:m,privacyPolicyUrl:f,legalNoticeUrl:x}}),[r,o,m,f,x,O,N,R,$]),U=(0,a.useMemo)((()=>Object.values(z.contact).filter(Boolean).length>0),[z.contact]),G=(0,a.useMemo)((()=>{const e="legal-requirement"===C,t="legitimate-interest"===C||I;if(D<=11)return e?L.legalRequirement:t?P.legitimateInterest:P.consent;{const{consentPersonalData:n,consentStorage:s,legitimateInterestPersonalData:a,legitimateInterestStorage:r,legalRequirementPersonalData:o}=A;return[e?o:t?a:n,!j&&(e||t?r:s)].filter(Boolean).join(", ")}}),[D,C,I,A,j]);return(0,s.jsxs)(a.Fragment,{children:[!!n&&(0,s.jsx)(l.E,{label:P.purpose,value:(0,i.g)(n)}),(0,s.jsx)(l.E,{label:P.legalBasis.label,value:G}),T&&E.length>0&&(0,s.jsx)(l.E,{label:P.gcm.dataProcessingInService,printValueAs:"empty",children:(0,s.jsx)("div",{style:{display:"inline-block"},children:(0,s.jsx)(l.E,{printValueAs:"empty",children:E.map((e=>(0,s.jsx)(v,{type:e,isDisabled:w},e)))})})}),(0,s.jsx)(l.E,{label:P.provider,value:z.provider,children:U&&(0,s.jsxs)(a.Fragment,{children:[(0,s.jsx)(l.E,{label:P.providerContactPhone,value:z.contact.phone,printValueAs:"phone"}),(0,s.jsx)(l.E,{label:P.providerContactEmail,value:z.contact.email,printValueAs:"mail"}),(0,s.jsx)(l.E,{label:P.providerContactLink,value:z.contact.link})]})}),(0,s.jsx)(l.E,{label:P.providerPrivacyPolicyUrl,value:z.privacyPolicyUrl}),(0,s.jsx)(l.E,{label:P.providerLegalNoticeUrl,value:z.legalNoticeUrl}),D<10&&F.length>0&&(0,s.jsx)(l.E,{label:L.dataProcessingInUnsafeCountries,value:F.join(", ")}),D<10&&B.length>0&&(0,s.jsx)(l.E,{label:L.appropriateSafeguard,value:B.join(", ")}),(0,s.jsxs)(d.m,{expandable:D>9,labelModifications:{[P.technicalCookieName]:P.technicalCookieDefinitions},groupLabel:t,children:[D>9&&(0,s.jsx)(c.Q,{dataProcessingInCountries:b,dataProcessingInCountriesSpecialTreatments:y}),!j&&(0,s.jsx)(u.f,{codeDynamics:S,definitions:k})]})]})};var C=n(8700),b=n(17140);const y=e=>{let{cookie:t,checked:n,disabled:a,onToggle:r,propertyListProps:i={}}=e;const{Cookie:l}=(0,C.o)().extend(...o.C),{name:c}=t,{group:{descriptionFontSize:d}}=(0,g.b)(),u=(0,b.p)();return(0,s.jsxs)(l,{children:[(0,s.jsx)(f.S,{isChecked:n,isDisabled:a,fontSize:d,onToggle:r,"aria-describedby":u,children:(0,s.jsx)("strong",{children:c})}),(0,s.jsx)("div",{id:u,children:(0,s.jsx)(x,{cookie:t,...i,isDisabled:!n})})]})},j=e=>{let{group:{id:t,isEssential:n},cookie:o}=e;const{id:i}=o,l=(0,r.Y)(),{consent:c,activeAction:d}=l,u=n||"history"===d,p=n||((null==c?void 0:c[t])||[]).some((e=>e===i)),g=(0,a.useCallback)((e=>l.updateCookieChecked(t,i,e)),[l,t,i]);return(0,s.jsx)(y,{cookie:o,propertyListProps:{isEssentialGroup:n},checked:p,disabled:u,onToggle:g})};var k=n(11801),S=n(55548),E=n(50180);const I=e=>{let{group:t}=e;const n=(0,r.Y)(),{name:a,description:o,items:i}=t,{group:{headlineFontSize:l},individualTexts:{headline:c,showMore:d,hideMore:u}}=n,p=(0,k.C)(t);return(0,s.jsxs)(S.Y,{legend:`${c}: ${a}`,headline:(0,s.jsxs)(f.S,{...p,fontSize:l,children:[a," (",i.length,")"]}),children:[(0,s.jsx)("span",{children:o}),!!i&&(0,s.jsx)(E.X,{showMore:d,hideMore:u,bullets:!0,groupLabel:a,children:i.map((e=>(0,s.jsx)(j,{group:t,cookie:e},e.id)))})]})};var w=n(4528);const P=()=>{const{groups:e}=(0,r.Y)(),t=e.filter((e=>{let{items:t}=e;return t.length}));return(0,s.jsx)(w._,{children:t.map((e=>(0,s.jsx)(I,{group:e},e.id)))})}},63362:(e,t,n)=>{n.r(t),n.d(t,{BannerSticky:()=>s});const s=()=>null},14685:(e,t,n)=>{n.r(t),n.d(t,{BannerTcfGroupList:()=>$});var s=n(3713),a=n(41594),r=n(81257),o=n(43204),i=n(20658),l=n(81116),c=n(55924),d=n(85360),u=n(45746),p=n(45453),g=n(84094),h=n(30680),m=n(35010),f=n(96875),v=n(4959),x=n(59112),C=n(50180),b=n(53024),y=n(9463),j=n(84832),k=n(81784),S=n(43181);function E(e,t,n){return e?e[n?"specialPurposes":"purposes"][`${t}`]||e.stdRetention:void 0}const I=e=>{let{id:t}=e;var n,r;const{Cookie:o}=(0,h.y)().extend(...p.C),[i,l]=(0,a.useState)(!1),c=(0,g.Y)(),d=(0,m.V)(),{designVersion:u,tcfFilterBy:f,lazyLoadedDataForSecondView:I,tcf:{gvl:P,model:M,original:{vendorConfigurations:O}}}=c,{vendors:{[t]:T},purposes:D,specialPurposes:L,features:A,specialFeatures:F,dataCategories:B}=P,{name:N,["consent"===f?"purposes":"legIntPurposes"]:R,flexiblePurposes:$,specialPurposes:z,features:U,specialFeatures:G,dataDeclaration:V,usesCookies:_,cookieMaxAgeSeconds:Q,cookieRefresh:W,usesNonCookieAccess:H,dataRetention:Y}=T,{dataProcessingInCountries:q,dataProcessingInCountriesSpecialTreatments:Z}=(0,a.useMemo)((()=>Object.values(O).filter((e=>{let{vendorId:n}=e;return n===t}))[0]),[t]),X=(0,a.useMemo)((()=>[...R,...$.filter((e=>-1===R.indexOf(e)))].filter((e=>(0,b.n)(M,e,"legInt"===f,T)))),[t,f]),{group:{descriptionFontSize:J},i18n:{tcf:{declarations:K,dataRetentionPeriod:ee,...te},safetyMechanisms:{adequacyDecision:ne,bindingCorporateRules:se,standardContractualClauses:ae},other:re,...oe},activeAction:ie}=c,{deprecated:le}=oe,{urls:ce,additionalInformation:de,deviceStorageDisclosure:ue}=(null==I||null==(n=I.tcf)?void 0:n.vendors[t])||{urls:[],additionalInformation:{},deviceStorageDisclosure:{}},pe=null==de?void 0:de.legalAddress,ge=!!(null==de?void 0:de.internationalTransfers),he=(null==de?void 0:de.transferMechanisms)||[],me=M["consent"===f?"vendorConsents":"vendorLegitimateInterests"],fe="history"===ie,ve=me.has(t),[xe,Ce]=(0,a.useState)(ve);(0,a.useEffect)((()=>{Ce(ve)}),[ve]);const be=(0,a.useCallback)((e=>{try{me[e?"set":"unset"](t),Ce(e)}catch(e){}}),[t,me,Ce]),{dataProcessingInUnsafeCountries:ye,appropriateSafeguards:je}=(0,y.Q)({dataProcessingInCountries:q,specialTreatments:Z,tcf:{internationalTransfers:ge,transferMechanisms:he}}),{privacy:ke,legIntClaim:Se}=(null==ce?void 0:ce[0])||{langId:"",privacy:"",legIntClaim:""},Ee=(0,a.useCallback)(((e,t)=>(void 0===t&&(t=!1),e.map((e=>`${(t?L:D)[e].name}${Y?` (${ee}: ${d(E(Y,e,!1),"d")})`:""}`)).join(", "))),[D,L,Y]);return(0,s.jsxs)(o,{children:[(0,s.jsx)(v.S,{isChecked:xe,isDisabled:fe,fontSize:J,onToggle:be,after:(0,s.jsx)(C.X,{onToggle:l,showMore:oe.showMore,hideMore:oe.hideMore,groupLabel:N,bullets:!0}),children:(0,s.jsx)("strong",{children:N})}),i&&(0,s.jsxs)(a.Fragment,{children:[!!pe&&(0,s.jsx)(x.E,{label:oe.provider,value:pe.split(";").join(", ")}),(0,s.jsx)(x.E,{label:oe.providerPrivacyPolicyUrl,value:ke}),"legInt"===f&&(0,s.jsx)(x.E,{label:te.legIntClaim,value:Se}),u<10&&ye.length>0&&(0,s.jsx)(x.E,{label:le.dataProcessingInUnsafeCountries,value:ye.join(", ")}),u<10&&je.length>0&&(0,s.jsx)(x.E,{label:le.appropriateSafeguard,value:je.join(", ")}),X.length>0&&(0,s.jsx)(x.E,{label:K.purposes.title,value:Ee(X)}),z.length>0&&(0,s.jsx)(x.E,{label:K.specialPurposes.title,value:Ee(z,!0)}),U.length>0&&(0,s.jsx)(x.E,{label:K.features.title,value:U.map((e=>A[e].name)).join(", ")}),G.length>0&&(0,s.jsx)(x.E,{label:K.specialFeatures.title,value:G.map((e=>F[e].name)).join(", ")}),(null==V?void 0:V.length)>0&&(0,s.jsx)(x.E,{label:K.dataCategories.title,value:V.map((e=>B[e].name)).join(", ")}),(0,s.jsx)(x.E,{label:oe.usesCookies,value:_,printValueAs:"boolean"}),_&&(0,s.jsx)(x.E,{label:oe.duration,value:Q<=0?"Session":d(Q,"s")}),(0,s.jsx)(x.E,{label:oe.cookieRefresh,value:W,printValueAs:"boolean"}),(0,s.jsx)(x.E,{label:oe.usesNonCookieAccess,value:H,printValueAs:"boolean"}),(0,s.jsxs)(k.m,{expandable:u>9,labelModifications:{[oe.technicalCookieName]:oe.technicalCookieDefinitions},groupLabel:N,children:[u>9&&(0,s.jsx)(j.Q,{dataProcessingInCountries:q,dataProcessingInCountriesSpecialTreatments:Z,mechanisms:e=>{const t=[...e],n=he.map((e=>e.toLowerCase()));return n.indexOf("adequacy decision")>-1&&t.push(ne),n.indexOf("sccs")>-1&&-1===e.indexOf("B")&&t.push(ae),n.indexOf("bcrs")>-1&&-1===e.indexOf("E")&&t.push(se),n.indexOf("other")>-1&&t.push(re),t}}),(null==ue||null==(r=ue.disclosures)?void 0:r.length)>0&&(0,s.jsx)(S.f,{codeDynamics:{},definitions:ue.disclosures.map((e=>{let{type:t,identifier:n,domain:a,domains:r,maxAgeSeconds:o,cookieRefresh:i,purposes:l}=e;return{type:w(t),name:n,host:r?r.join(","):a||"n/a",isSessionDuration:null!==o&&o<=0,duration:o,durationUnit:"s",purpose:(null==l?void 0:l.length)?l.map((e=>{var t;return null==(t=D[e])?void 0:t.name})).filter(Boolean).join(", "):void 0,children:(0,s.jsx)(x.E,{label:oe.cookieRefresh,value:i,printValueAs:"boolean"})}}))})]})]})]})};function w(e){switch(e){case"cookie":return"HTTP Cookie";case"web":return"LocalStorage, Session Storage, IndexDB";case"app":return"App";default:return e}}const P=e=>{let{declaration:t,id:n}=e;const{Link:r,Cookie:o}=(0,h.y)().extend(...u.I).extend(...p.C),[i,b]=(0,a.useState)(!1),y=(0,m.V)(),{tcf:{gvl:j,model:k},tcfFilterBy:S,group:{descriptionFontSize:I},activeAction:w,i18n:{purpose:P,showMore:M,hideMore:O,tcf:{example:T,vendors:D,vendorsCount:L,dataRetentionPeriod:A}}}=(0,g.Y)(),{[t]:{[n.toString()]:F}}=j,{name:B,description:N,illustrations:R}=F,$=["specialPurposes","features","dataCategories"].indexOf(t)>-1,z="history"===w||$,U=$?void 0:k["specialFeatures"===t?"specialFeatureOptins":"legInt"===S?"purposeLegitimateInterests":"purposeConsents"],G=$||!!(null==U?void 0:U.has(n)),[V,_]=(0,a.useState)(G);(0,a.useEffect)((()=>{_(G)}),[G]);const Q=(0,a.useCallback)((e=>{try{U[e?"set":"unset"](n),_(e)}catch(e){}}),[n,U,_]),W=(0,a.useMemo)((()=>(0,l.L)(j,k,n,t,"legInt"===S,!0)),[j,k,n,t,S]);return(0,s.jsxs)(o,{children:[(0,s.jsxs)(v.S,{hideCheckbox:-1===["purposes","specialFeatures"].indexOf(t),isChecked:V,isDisabled:z,fontSize:I,onToggle:Q,after:(0,s.jsx)(C.X,{onToggle:b,showMore:M,hideMore:O,groupLabel:B,bullets:!0}),children:[(0,s.jsx)("strong",{children:B})," (",(0,f.BP)(W.length,...L),")"]}),i&&(0,s.jsxs)(a.Fragment,{children:[(0,s.jsx)(x.E,{label:P,value:(0,c.g)(N),children:(null==R?void 0:R.length)>0&&R.map(((e,t)=>(0,s.jsx)(x.E,{label:`${T} #${t+1}`,value:e},e)))}),(0,s.jsx)(x.E,{label:D,value:(0,d.i)(W.map((e=>{let{id:a,name:o,urls:i,dataRetention:l}=e;return(0,s.jsxs)(r,{href:(null==i?void 0:i[0].privacy)||"about:blank",target:"_blank",rel:"noreferrer",children:[o,["purposes","specialPurposes"].indexOf(t)>-1&&l?` (${A}: ${y(E(l,n,!1),"d")})`:""]},a)})),", ")})]})]})};var M=n(98927),O=n(55548);const T=e=>{let{declaration:t}=e;const n=(0,g.Y)(),{i18n:{tcf:{declarations:{[t]:{title:r,desc:o}}}}}=n,i=function(e){const t=(0,g.Y)(),{tcf:{gvl:n,model:s},tcfFilterBy:r}=t,{[e]:o}=n;return(0,a.useMemo)((()=>(0,M.i)(e,n,s,r,!0)),[o,n,s,e,r])}(t);return i.length>0?(0,s.jsxs)(O.Y,{headline:r,children:[o,i.map((e=>{let{id:n}=e;return(0,s.jsx)(P,{declaration:t,id:n},n)}))]}):null};var D=n(54989);const L=()=>{const e=(0,g.Y)(),{tcfFilterBy:t,i18n:{tcf:{vendors:n,filterNoVendors:a}}}=e,r=(0,D.E)(t),o=r.length>0;return(0,s.jsxs)(O.Y,{headline:n,children:[r.map((e=>{let{id:t}=e;return(0,s.jsx)(I,{id:t},t)})),!o&&a]})};var A=n(8700),F=n(17140);const B=e=>{let{legend:t,active:n,onChange:a,items:r}=e;const{ButtonGroup:o,buttonGroupItem:i,screenReaderOnlyClass:l,Label:c}=(0,A.o)().extend(...u.I),d=(0,F.p)(),p=e=>{const{value:t}=e.target;a(t)};return(0,s.jsxs)(o,{children:[(0,s.jsx)("legend",{className:l,children:t}),r.map((e=>{let{key:t,value:a}=e;return(0,s.jsxs)(c,{children:[(0,s.jsx)("input",{name:d,type:"radio",value:t,checked:n===t,className:i,"aria-label":a,onChange:p}),(0,s.jsx)("span",{"aria-hidden":!0,children:a})]},t)}))]})};var N=n(4528);const R=["purposes","specialPurposes","features","specialFeatures","dataCategories"],$=()=>{const e=(0,g.Y)(),{tcfFilterBy:t,suspense:n,i18n:{legitimateInterest:l,consent:c,tcf:{filterText:d,standard:u,standardDesc:p}},set:h}=e;return(0,s.jsxs)(a.Fragment,{children:[(0,s.jsxs)(O.Y,{headline:u,style:i.r,borderless:!0,children:[p,(0,s.jsxs)("div",{style:i.r,children:[(0,s.jsx)("span",{"aria-hidden":!0,children:d})," ",(0,s.jsx)(B,{legend:d,active:t,onChange:e=>h({tcfFilterBy:e}),items:[{key:"legInt",value:l},{key:"consent",value:c}]})]})]}),(0,s.jsx)(r.k,{promise:n.lazyLoadedDataForSecondView,suspenseProbs:{fallback:(0,s.jsx)(o.t,{})},children:(0,s.jsxs)(N._,{children:[(0,s.jsx)(L,{}),R.map((e=>(0,s.jsx)(T,{declaration:e},e)))]})})]})}},59112:(e,t,n)=>{n.d(t,{E:()=>d});var s=n(3713),a=n(41594),r=n(84200),o=n(45746),i=n(45453),l=n(8700),c=n(41520);const d=e=>{let{label:t,value:n,children:d,printValueAs:u,monospace:p}=e;const g=(0,l.o)(),{Link:h,CookieProperty:m}=g.extend(...o.I).extend(...i.C),f=(0,c.b)(),{i18n:{yes:v,no:x}}=f;let C="string"==typeof n&&n.startsWith("http")&&(0,r.g)(n)?(0,s.jsx)(h,{href:n,target:"_blank",rel:"noopener noreferrer",children:n}):"string"==typeof n?"phone"===u?(0,s.jsx)(h,{target:"_blank",href:`tel:${n.replace(/\s+/g,"")}`,children:n}):"mail"===u?(0,s.jsx)(h,{target:"_blank",href:`mailto:${n}`,children:n}):(0,s.jsx)("span",{dangerouslySetInnerHTML:{__html:n}}):n;return"boolean"===u&&(C=C?v:x),n||!1===n||"empty"===u?(0,s.jsxs)(a.Fragment,{children:[(0,s.jsxs)(m,{children:[t&&(0,s.jsxs)("strong",{children:[t,": "]}),(0,s.jsx)("span",{role:"presentation",style:{fontFamily:p?"monospace":void 0},children:C})]}),(0,s.jsx)(m,{children:!!d&&(0,s.jsx)("div",{children:d})})]}):null}},84832:(e,t,n)=>{n.d(t,{Q:()=>u});var s=n(3713),a=n(41594),r=n(85360),o=n(59112),i=n(8700),l=n(96875),c=n(41520),d=n(21917);const u=e=>{let{mechanisms:t,...n}=e;const{screenReaderOnlyClass:u}=(0,i.o)(),p=(0,l.JY)(u),{iso3166OneAlpha2:g,predefinedDataProcessingInSafeCountriesLists:h,territorialLegalBasis:m,isDataProcessingInUnsafeCountries:f,i18n:{dataProcessingInThirdCountries:v,territorialLegalBasisArticles:{"dsg-switzerland":{dataProcessingInUnsafeCountries:x},"gdpr-eprivacy":{dataProcessingInUnsafeCountries:C}},safetyMechanisms:{label:b,eu:y,switzerland:j,adequacyDecision:k,contractualGuaranteeSccSubprocessors:S,standardContractualClauses:E,bindingCorporateRules:I}}}=(0,c.b)(),{result:w,filter:P,isGdpr:M}=(0,d.F)({predefinedDataProcessingInSafeCountriesLists:h,territorialLegalBasis:m,isDataProcessingInUnsafeCountries:f,service:n}),O=Object.entries(w),T={A:k,"A-EU":`${k} (${y})`,"A-CH":`${k} (${j})`,B:E,C:S,D:m.length>1?"":M?C:x,"D-EU":C,"D-CH":x,E:I},D=Object.keys(T).filter((e=>P((t=>t===e)).length>0)),L=t?t(D):D;return(0,s.jsxs)(a.Fragment,{children:[O.length>0&&(0,s.jsx)(o.E,{label:v,value:(0,r.i)(O.map((e=>{let[t,n]=e;return(0,s.jsx)("span",{dangerouslySetInnerHTML:{__html:p(n.map((e=>[e,T[e]])),g[t]??t)}},t)})),", ")}),L.length>0&&(0,s.jsx)(o.E,{label:b,value:(0,r.i)(L.map((e=>(0,s.jsx)("span",{dangerouslySetInnerHTML:{__html:T[e]?p([[e]],T[e]):e}},e))),", ")})]})}},81784:(e,t,n)=>{n.d(t,{m:()=>c});var s=n(3713),a=n(41594),r=n(59112),o=n(96875),i=n(41520),l=n(50180);const c=e=>{let{expandable:t,children:n,labelModifications:c={},groupLabel:d}=e;const{group:{detailsHideLessRelevant:u},i18n:{andSeparator:p,showLessRelevantDetails:g,hideLessRelevantDetails:h}}=(0,i.b)(),m=(0,a.useRef)(null),[f,v]=(0,a.useState)("");(0,a.useEffect)((()=>{const{current:e}=m;if(e){const t=[...new Set([...e.querySelectorAll(":scope>div>strong")].map((e=>{const t=e.innerText.replace(/:?\s+$/,"");return c[t]||t})))];v((0,o.$D)(t,p))}}),[m.current,c]);const x=(0,a.useCallback)((e=>e.replace("%s",f)),[f]);return u&&t?(0,s.jsx)("div",{"aria-hidden":!f,hidden:!f,children:(0,s.jsx)(r.E,{value:(0,s.jsxs)(a.Fragment,{children:[(0,s.jsx)("br",{}),(0,s.jsx)(l.X,{showMore:x(g),hideMore:x(h),style:{fontStyle:"italic"},forceRender:!0,groupLabel:d,children:(0,s.jsxs)("div",{ref:m,children:[(0,s.jsx)("br",{}),n]})})]})})}):n}},43181:(e,t,n)=>{n.d(t,{f:()=>l});var s=n(3713),a=n(34766),r=n(59112),o=n(35010),i=n(41520);const l=e=>{let{definitions:t,codeDynamics:n}=e;const{i18n:l}=(0,i.b)(),c=(0,o.V)(),d={http:{name:"HTTP Cookie",abbr:"HTTP",backgroundColor:"black"},local:{name:"Local Storage",abbr:"Local",backgroundColor:"#b3983c"},session:{name:"Session Storage",abbr:"Session",backgroundColor:"#3c99b3"},indexedDb:{name:"IndexedDB",abbr:"I-DB",backgroundColor:"#4ab33c"}};return null==t?void 0:t.map((e=>{let{children:t,type:o,name:i,host:u,duration:p,durationUnit:g,isSessionDuration:h,purpose:m}=e;var f;return(0,s.jsxs)(r.E,{label:l.technicalCookieName,monospace:!0,value:(0,a.t)(i,n),children:[(0,s.jsx)(r.E,{label:l.type,value:(null==(f=d[o])?void 0:f.name)||o}),!!u&&(0,s.jsx)(r.E,{label:l.host,value:u,monospace:!0}),(0,s.jsx)(r.E,{label:l.duration,value:["local","indexedDb"].indexOf(o)>-1?l.noExpiration:h||"session"===o?"Session":c(p,g)}),t,(0,s.jsx)(r.E,{label:l.purpose,value:m})]},`${o}-${i}-${u}`)}))}},4528:(e,t,n)=>{n.d(t,{_:()=>o});var s=n(3713),a=n(45453),r=n(8700);const o=e=>{let{children:t}=e;const{GroupList:n}=(0,r.o)().extend(...a.C);return(0,s.jsx)(n,{children:t})}},50180:(e,t,n)=>{n.d(t,{X:()=>l});var s=n(3713),a=n(41594),r=n(45746),o=n(8700),i=n(17140);const l=e=>{let{onToggle:t,children:n,showMore:l,hideMore:c,groupLabel:d,bullets:u,forceRender:p,...g}=e;const{Link:h}=(0,o.o)().extend(...r.I),[m,f]=(0,a.useState)(!1),v=(0,i.p)();return(0,s.jsxs)(a.Fragment,{children:[u&&(0,s.jsx)("span",{"aria-hidden":!0,children:"  •  "}),(0,s.jsx)(h,{href:"#",onClick:e=>{const n=!m;f(n),null==t||t(n),e.preventDefault()},onKeyDown:e=>{"space"===e.code.toLowerCase()&&e.target.click()},...d?{"aria-label":`${m?c:l}: ${d}`}:{},...n?{"aria-expanded":m,"aria-controls":v,role:"button"}:{},...g,children:m?c:l}),n&&(0,s.jsx)("div",{hidden:!m,id:v,children:(m||p)&&n})]})}},9463:(e,t,n)=>{n.d(t,{Q:()=>i});var s=n(41594),a=n(55285),r=n(21917),o=n(41520);function i(e){let{dataProcessingInCountries:t,specialTreatments:n,tcf:i={internationalTransfers:!1,transferMechanisms:[]}}=e;const{designVersion:l,i18n:{safetyMechanisms:c,other:d},isDataProcessingInUnsafeCountries:u,dataProcessingInUnsafeCountriesSafeCountries:p,iso3166OneAlpha2:g}=(0,o.b)(),{internationalTransfers:h,transferMechanisms:m}=i;return{dataProcessingInUnsafeCountries:(0,s.useMemo)((()=>u?(0,r.z)({dataProcessingInCountries:t,safeCountries:p,specialTreatments:n,isDisplay:!0}).map((e=>g[e]||e)):[]),[u,p,n,t,g]),appropriateSafeguards:(0,s.useMemo)((()=>[...new Set([n.indexOf(a.ak.StandardContractualClauses)>-1&&c.standardContractualClauses,l>6&&h&&m.map((e=>{switch(e){case"SCCs":return c.standardContractualClauses;case"Adequacy decision":return c.adequacyDecision;case"BCRs":return c.bindingCorporateRules;case"Other":return d;default:return""}}))].flat().filter(Boolean))]),[n,h,m])}}},35010:(e,t,n)=>{n.d(t,{V:()=>o});var s=n(41594),a=n(96875),r=n(41520);function o(){const{i18n:{durationUnit:e}}=(0,r.b)();return(0,s.useCallback)(((t,n)=>(0,a.BP)(t,e.n1[n],e.nx[n])),[e])}},84200:(e,t,n)=>{function s(e){return e.indexOf(".")>-1&&!!/^(?:(?:https?|ftp):\/\/)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:\/\S*)?$/.test(e)}n.d(t,{g:()=>s})},55924:(e,t,n)=>{n.d(t,{g:()=>r});var s=n(41594);const a=/(\r\n|\r|\n|<br[ ]?\/>)/g,r=e=>"string"==typeof e?e.split(a).map(((e,t)=>e.match(a)?(0,s.createElement)("br",{key:t}):e)):e}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.19/f5d8ff744d68d9e921ff8dc27a7dffd3/banner-lazy.lite.js.map
