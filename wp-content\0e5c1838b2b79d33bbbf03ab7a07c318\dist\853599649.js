"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[462],{66732:(e,t,s)=>{s.d(t,{X:()=>r});var n=s(12719),i=s(59726);function r(){const{__:e}=(0,i.s)();return(0,n.WH)({title:e("Want a better integrated visual content blocker for your website?"),testDrive:!0,feature:"visual-content-blocker",assetName:e("pro-modal/visual-content-blocker.webp"),description:e("Instead of a lot of text, you can offer your visitor a more pleasant way to view blocked content. For example, you can replace your video embeds with a privacy-compliant dummy player with thumbnail, or an embedded map with a preview map.")})}},67748:(e,t,s)=>{s.r(t),s.d(t,{BlockerEditForm:()=>je});var n=s(3713),i=s(75792),r=s(18197),a=s(91386),o=s(78915),l=s(24985),c=s(57922),d=s(41594),u=s(27667),h=s(27114),p=s(20931),m=s(42396),v=s(92454),f=s(14676),x=s(74865),b=s(9551),y=s(59726);const g=()=>{const{__:e}=(0,y.s)();return(0,n.jsxs)(a.A.Item,{label:e("Description"),children:[(0,n.jsx)(a.A.Item,{name:"description",noStyle:!0,children:(0,n.jsx)(b.A.TextArea,{autoSize:{minRows:3}})}),(0,n.jsx)("p",{className:"description",children:e("You can give your visitors further explanations why a content has been blocked or, for example, how they can contact you alternatively instead of agreeing to load the contact form. The description is displayed only in visual content blockers.")})]})};var j=s(18137);const k=()=>{const{__:e}=(0,y.s)(),[t,s]=(0,d.useState)();return(0,n.jsxs)(a.A.Item,{label:e("Name"),required:!0,children:[(0,n.jsx)(a.A.Item,{name:"name",noStyle:!0,rules:[{required:!0,message:e("Please provide a name!")}],children:(0,n.jsx)(b.A,{})}),(0,n.jsxs)("p",{className:"description",ref:s,children:[(0,n.jsx)(j.E,{form:"blocker",valueName:"name",widthOfRef:t,renderDiff:e=>(0,n.jsx)(b.A,{value:e,readOnly:!0})}),e('Each content blocker should have a descriptive name that is understandable to a non-professional user, e.g. "Google Maps".')]})]})};var w=s(81533);const A=()=>{const{__:e}=(0,y.s)();return(0,n.jsxs)(a.A.Item,{label:e("Status"),required:!0,children:[(0,n.jsx)(a.A.Item,{name:"status",noStyle:!0,rules:[{required:!0,message:e("Please choose an option!")}],children:(0,n.jsxs)(w.Ay.Group,{children:[(0,n.jsx)(w.Ay.Button,{value:"publish",children:e("Enabled")}),(0,n.jsx)(w.Ay.Button,{value:"private",children:e("Disabled")}),(0,n.jsx)(w.Ay.Button,{value:"draft",children:e("Draft")})]})}),(0,n.jsx)("p",{className:"description",children:e('Content Blockers with the status "Draft" or "Disabled" are not visible to the public. In addition, a draft will be highlighted in the content blocker table so that you do not forget to configure it.')})]})};var C=s(67993),T=s(38994);const S=()=>{const{__:e}=(0,y.s)();return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(T.r,{offset:m.MX.labelCol.span,children:[e("General content blocker configuration")," ",(0,n.jsx)(C.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-create-individual-content-blocker/")})]}),(0,n.jsx)(k,{}),(0,n.jsx)(A,{}),(0,n.jsx)(g,{})]})},V=()=>{const{__:e}=(0,y.s)(),{isTcf:t}=(0,p.j)();return(0,n.jsxs)(a.A.Item,{label:e("Block by"),required:!0,style:{display:t?void 0:"none"},children:[(0,n.jsx)(a.A.Item,{name:"criteria",noStyle:!0,rules:[{required:!0,message:e("Please choose an option!")}],children:(0,n.jsxs)(w.Ay.Group,{children:[(0,n.jsx)(w.Ay.Button,{value:"services",children:e("Services")}),(0,n.jsx)(w.Ay.Button,{value:"tcfVendors",children:e("TCF Vendors")})]})}),(0,n.jsx)("p",{className:"description",children:e("You can block content through non-standard services or TCF vendors. If you want to block it through TCF vendors, then the visual content blocker cannot be displayed because TCF is usually used to obtain consent for ad networks. Moreover, after the initial consents in the cookie banner, users will probably never consent to the ad.")})]})};var _=s(32041);const I=()=>{const{__:e,_i:t}=(0,y.s)(),{openDialog:s}=(0,_.g)(),[i,r]=(0,d.useState)();return(0,n.jsxs)(a.A.Item,{label:e("URLs / Elements to block"),required:!0,children:[(0,n.jsx)(a.A.Item,{name:"rules",noStyle:!0,rules:[{required:!0,message:e("Please provide at least one URL/element!")}],children:(0,n.jsx)(b.A.TextArea,{autoSize:{minRows:3,maxRows:15}})}),(0,n.jsxs)("p",{className:"description",ref:r,children:[(0,n.jsx)(j.E,{form:"blocker",valueName:"rules",widthOfRef:i,difference:(e,t)=>{const s=e.split("\n"),n=t.split("\n").filter((e=>!s.includes(e)));return n.length>0?n:void 0},apply:(e,t,s)=>t({rules:s.split("\n").concat(e).join("\n")}),newValueText:e("Missing entries:"),renderDiff:(e,t)=>(0,n.jsx)(b.A.TextArea,{value:t.join("\n"),readOnly:!0,autoSize:{minRows:3,maxRows:15}})}),t(e("Enter one rule per line to replace content with a content blocker. You can block all available URLs or HTML tags on your website including videos, iframes, scripts, inline scripts and stylesheets. Please use an asterisk ({{code}}*{{/code}}) as a wildcard (placeholder)."),{code:(0,n.jsx)("code",{})})," • ",!!s&&(0,n.jsx)("button",{type:"button",className:"button-link",onClick:s,children:e("Can't handle it? Let a Cookie Expert help you!")}),(0,n.jsx)("br",{}),(0,n.jsx)("br",{}),t(e('{{strong}}Pro tip:{{/strong}} Look up all available syntaxes like {{code}}div[class*="my-embed"]{{/code}} in our knowledge base to block content perfectly.'),{strong:(0,n.jsx)("strong",{}),code:(0,n.jsx)("code",{})})," ",(0,n.jsx)(C.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-create-individual-content-blocker/")})]})]})},F=e=>{let{dropdown:t,children:s}=e;const{__:i,_i:r}=(0,y.s)();return(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.criteria!==t.criteria,children:e=>{let{getFieldValue:o}=e;return"services"===o("criteria")&&(0,n.jsxs)(a.A.Item,{label:i("Connected services"),required:!0,children:[(0,n.jsx)(a.A.Item,{name:"services",noStyle:!0,rules:[{type:"array",required:!0,message:i("Please provide at least one service!")}],children:t}),(0,n.jsx)("p",{className:"description",children:r(i("A content blocker is displayed until the user has agreed to {{strong}}all{{/strong}} necessary services that would be used by loading the content. You must define all services that are loaded as soon as the user wants to see the blocked content."),{strong:(0,n.jsx)("strong",{})})}),s]})}})};var P=s(45854),D=s(6099),N=s(92453);const B=e=>{let{dropdownVendors:t,children:s}=e;const{__:i,_i:r}=(0,y.s)(),{tcfPurposes:o=[]}=(0,p.j)();return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.criteria!==t.criteria,children:e=>{let{getFieldValue:l}=e;return"tcfVendors"===l("criteria")&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(a.A.Item,{label:i("Connected TCF Vendors"),required:!0,children:[(0,n.jsx)(a.A.Item,{name:"tcfVendors",noStyle:!0,rules:[{type:"array",required:!0,message:i("Please provide at least one vendor!")}],children:t}),(0,n.jsx)("p",{className:"description",children:r(i("A content blocker is displayed until the user has agreed to {{strong}}all{{/strong}} necessary TCF vendors and purposes that would be used by loading the content. You must define all TCF vendors and purposes that are loaded based on legitimate interest or consent as soon as the user wants to see the blocked content."),{strong:(0,n.jsx)("strong",{})})}),s]}),(0,n.jsxs)(a.A.Item,{label:i("Required TCF purposes"),required:!0,children:[(0,n.jsx)(a.A.Item,{noStyle:!0,name:"tcfPurposes",rules:[{type:"array",required:!0,message:i("Please provide at least one purpose!")}],children:(0,n.jsx)(P.A.Group,{style:{marginTop:6},children:(0,n.jsx)(D.A,{children:o.map((e=>{let{name:t,id:s}=e;return(0,n.jsx)(N.A,{span:12,children:(0,n.jsx)(P.A,{value:s,children:t})},`purpose-${s}`)}))})})}),(0,n.jsx)("p",{className:"description",style:{marginTop:10},children:i('Defined which purposes of all connected TCF vendors must be allowed (by consent or legitimate interest) for the content blocker to unblock the content. At a minimum, "Store and/or access information on a device" should be given if cookies or similar information are read/written. In terms of data economy according to the GDPR, you should specify further purposes if, for example, only personalized advertising is displayed and therefore unblocking without consent/legitimate interest to this purpose has no practical use.')})]})]})}})})};var E=s(39795);const M=e=>{let{servicesFieldProps:t,tcfVendorsFieldProps:s}=e;const{__:i}=(0,y.s)(),{template:r}=(0,p.j)(),o=[{message:null==r?void 0:r.ruleNotice,severity:"info"}].filter(Boolean).filter((e=>{let{message:t}=e;return t}));return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(T.r,{offset:m.MX.labelCol.span,children:i("Technical Definition")}),o.length>0&&(0,n.jsx)(a.A.Item,{wrapperCol:{offset:m.sN.labelCol.span,span:m.sN.wrapperCol.span},style:{marginBottom:0},children:(0,n.jsx)(E.q,{notices:o})}),(0,n.jsx)(I,{}),(0,n.jsx)(V,{}),(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.criteria!==t.criteria,children:e=>{let{getFieldValue:i}=e;switch(i("criteria")){case"services":return(0,n.jsx)(F,{...t});case"tcfVendors":return(0,n.jsx)(B,{...s});default:return null}}})]})},U=()=>{const{__:e,_x:t}=(0,y.s)(),{templateCheck:s,isTemplateUpdate:i}=(0,p.j)();return!(!s&&!i)&&(0,n.jsx)(a.A.Item,{name:"templateCheck",valuePropName:"checked",required:!0,rules:[{type:"boolean",required:!0,transform:e=>e||void 0,message:e("Please confirm that you have checked the content of the content blocker.")}],children:(0,n.jsxs)(P.A,{children:[t("I have checked the information in the content blocker template myself for correctness and completeness and have added missing information or corrected information that does not fit my use case. I am aware that the manufacturer of Real Cookie Banner cannot take any liability in this respect.","legal-text")," ",(0,n.jsx)(C.Y,{url:e("https://devowl.io/knowledge-base/is-real-cookie-banner-legally-compliant/")})]})})};var R=s(19393);const q=()=>{const{__:e}=(0,y.s)();return(0,n.jsx)(a.A.Item,{wrapperCol:{offset:m.MX.labelCol.span},children:(0,n.jsxs)("span",{children:[(0,n.jsx)(a.A.Item,{name:"isVisual",valuePropName:"checked",noStyle:!0,children:(0,n.jsx)(R.A,{})}),(0,n.jsxs)("span",{children:["  ",e("Show the visual content blocker, if possible")]})]})})};var O=s(24325);const L=()=>{const{__:e}=(0,y.s)(),{isPro:t}=(0,O.J)();return t&&(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isVisual!==t.isVisual||e.visualType!==t.visualType,children:t=>{let{getFieldValue:s}=t;const i=s("isVisual"),r=s("visualType");return!!i&&"default"!==r&&(0,n.jsx)(a.A.Item,{wrapperCol:{offset:m.MX.labelCol.span},children:(0,n.jsxs)("span",{children:[(0,n.jsx)(a.A.Item,{name:"isVisualDarkMode",valuePropName:"checked",noStyle:!0,children:(0,n.jsx)(R.A,{})}),"  ",e("Enable dark mode"),(0,n.jsx)("p",{className:"description",children:e("As soon as no image is found for the content blocker, a default image is automatically used. You can also specify whether the image should be displayed light or dark.")})]})})}})},$=()=>{const{__:e}=(0,y.s)();return(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isVisual!==t.isVisual,children:t=>{let{getFieldValue:s}=t;return!!s("isVisual")&&(0,n.jsx)(a.A.Item,{wrapperCol:{offset:m.MX.labelCol.span},children:(0,n.jsxs)("span",{children:[(0,n.jsx)(a.A.Item,{name:"shouldForceToShowVisual",valuePropName:"checked",noStyle:!0,children:(0,n.jsx)(R.A,{})}),"  ",e("Force visual content blocker for hidden elements"),(0,n.jsx)("p",{className:"description",children:e("In rare cases, visual content blockers are not displayed because the main element of the blocked content is not visible either. Enable this option if this is the case and you want to force to display a content blocker for non-visible elements.")})]})})}})};var G=s(7643);const J=()=>{const{__:e}=(0,y.s)(),{isPro:t}=(0,O.J)();return t&&(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isVisual!==t.isVisual||e.visualType!==t.visualType,children:t=>{let{getFieldValue:s}=t;const i=s("isVisual"),r=s("visualType");return!!i&&"default"!==r&&(0,n.jsxs)(a.A.Item,{label:e("Blur image"),children:[(0,n.jsx)(a.A.Item,{name:"visualBlur",noStyle:!0,children:(0,n.jsx)(G.A,{min:0,max:20,marks:{0:e("Disabled"),5:"5%",10:"10%",15:"15%",20:"20%"},tooltip:{open:!1},style:{marginLeft:40}})}),(0,n.jsx)("p",{className:"description",children:e("You can apply an additional blur to the background image. This can be useful e.g. for blocked contact forms to show it only schematically.")})]})}})};var Y=s(43799),z=s(66732);const H=()=>{const{__:e}=(0,y.s)(),{isPro:t}=(0,O.J)(),{modal:s,tag:i}=(0,z.X)(),r=(0,d.useMemo)((()=>({map:e("Map"),"audio-player":e("Audio player"),"video-player":e("Video player"),"feed-text":e("Feed (text)"),"feed-video":e("Feed (image/video)"),generic:e("None of these")})),[e]);return(0,n.jsxs)(n.Fragment,{children:[s,(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isVisualDarkMode!==t.isVisualDarkMode||e.isVisual!==t.isVisual||e.visualType!==t.visualType,children:s=>{let{getFieldValue:o}=s;const l=o("isVisual"),c=o("visualType");return!!l&&"default"!==c&&(0,n.jsx)(a.A.Item,{label:e("Content type"),required:!0,extra:i,children:(0,n.jsx)(a.A.Item,{name:"visualContentType",rules:[{required:!0,message:e("Please choose an option!")}],children:(0,n.jsx)(w.Ay.Group,{size:"large",buttonStyle:"solid",className:"rcb-antd-radio-button-card",children:Object.keys(r).map((e=>{const s=r[e];return(0,n.jsx)(w.Ay.Button,{value:e,disabled:!t,children:(0,n.jsx)(Y.A,{style:{width:240},bordered:!1,size:"small",cover:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("img",{style:{display:o("isVisualDarkMode")?"none":void 0},src:`https://assets.devowl.io/in-app/wp-real-cookie-banner/visual-content-blocker-preview/${e}-light.png`}),(0,n.jsx)("img",{style:{display:o("isVisualDarkMode")?void 0:"none"},src:`https://assets.devowl.io/in-app/wp-real-cookie-banner/visual-content-blocker-preview/${e}-dark.png`})]}),children:(0,n.jsx)(Y.A.Meta,{title:s})})},e)}))})})})}})]})};var X=s(24262);const W=()=>{const{__:e}=(0,y.s)(),{isPro:t}=(0,O.J)();return t&&(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.visualDownloadThumbnail!==t.visualDownloadThumbnail||e.isVisual!==t.isVisual||e.visualType!==t.visualType,children:t=>{let{getFieldValue:s,setFieldsValue:i}=t;const r=s("visualDownloadThumbnail"),o=s("isVisual"),l=s("visualType");return!!o&&"default"!==l&&(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(a.A.Item,{label:e("Automatic preview image"),children:[(0,n.jsx)(a.A.Item,{noStyle:!0,name:"visualDownloadThumbnail",required:!0,rules:[{required:!0,type:"boolean",message:e("Please choose an option!")}],children:(0,n.jsxs)(w.Ay.Group,{style:{display:r?void 0:"none"},children:[(0,n.jsx)(w.Ay.Button,{value:!0,children:e("Download preview image and serve locally")}),(0,n.jsx)(w.Ay.Button,{value:!1,children:e("Image from media library")})]})}),!r&&(0,n.jsxs)(w.Ay.Group,{value:r,children:[(0,n.jsx)(X.A,{title:e("I confirm that I have the required rights to embedded content and its thumbnails can be copied to my servers without e.g. copyright infringement."),cancelText:e("Cancel"),okText:e("Activate now"),overlayStyle:{maxWidth:450},onCancel:()=>i({visualDownloadThumbnail:!1}),onConfirm:()=>i({visualDownloadThumbnail:!0}),placement:"bottomLeft",children:(0,n.jsx)(w.Ay.Button,{value:!0,children:e("Download preview image and serve locally")})}),(0,n.jsx)(w.Ay.Button,{value:!1,onClick:()=>i({visualDownloadThumbnail:!1}),children:e("Image from media library")})]}),(0,n.jsx)("p",{className:"description",children:e("If you block an external URL, this option will try to download an image for this URL using different mechanisms (oEmbed, OpenGraph, ...), saves it locally and use it as background image.")})]})})}})},Z=()=>{const{__:e}=(0,y.s)(),{isPro:t}=(0,O.J)();return t&&(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isVisual!==t.isVisual||e.visualType!==t.visualType,children:t=>{let{getFieldValue:s}=t;const i=s("isVisual"),r=s("visualType");return!!i&&"default"!==r&&(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.visualContentType!==t.visualContentType,children:t=>{let{getFieldValue:s}=t;const i=s("visualContentType"),r=s("visualType");return(0,n.jsxs)(a.A.Item,{label:e("Button text"),style:{display:["audio-player","video-player"].indexOf(i)>-1||"hero"!==r?"none":void 0},children:[(0,n.jsx)(a.A.Item,{name:"visualHeroButtonText",noStyle:!0,children:(0,n.jsx)(b.A,{})}),(0,n.jsx)("p",{className:"description",children:e("If you specify a button text, a button with this text will be centered in the image and only when you click on this button the content blocker will be opened. Otherwise, clicking on the image itself will open the Content Blocker.")})]})}})}})},K=e=>{let{children:t}=e;const{__:s}=(0,y.s)(),{isPro:i}=(0,O.J)();return i&&(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.visualDownloadThumbnail!==t.visualDownloadThumbnail||e.isVisual!==t.isVisual||e.visualType!==t.visualType||e.visualContentType!==t.visualContentType,children:e=>{let{getFieldValue:i}=e;const r=i("isVisual"),o=i("visualType"),l=i("visualContentType"),c=i("visualDownloadThumbnail");return!!r&&"default"!==o&&(0,n.jsx)(a.A.Item,{label:s(c||"generic"===l?"Fallback preview image":"Preview image"),name:"visualMediaThumbnail",valuePropName:"attachmentId",style:{display:void 0===c?"none":void 0},children:t})}})},Q=()=>{const{__:e}=(0,y.s)(),{isPro:t}=(0,O.J)(),{modal:s,tag:i}=(0,z.X)(),r=(0,d.useMemo)((()=>({default:{title:e("Textbox"),description:e("Plain text with button")},wrapped:{title:e("Wrapped"),description:e("Image surrounding the content blocker")},hero:{title:e("Hero"),description:e("Image with content blocker on click")}})),[e]);return(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isVisual!==t.isVisual,children:o=>{let{getFieldValue:l}=o;return!!l("isVisual")&&(0,n.jsx)(a.A.Item,{wrapperCol:{offset:m.MX.labelCol.span},style:{paddingBottom:10},children:(0,n.jsx)("span",{children:(0,n.jsx)(a.A.Item,{name:"visualType",noStyle:!0,rules:t?[]:[{required:!0,type:"enum",enum:["default"],message:e("This type of visual content blocker is available only in the PRO version of the plugin. Please choose a textbox content blocker!")}],children:(0,n.jsx)(w.Ay.Group,{size:"large",buttonStyle:"solid",className:"rcb-antd-radio-button-card",style:{marginBottom:10},children:Object.keys(r).map((e=>{const{description:t,title:a}=r[e];return(0,n.jsx)(w.Ay.Button,{value:e,children:(0,n.jsx)(Y.A,{style:{width:300},bordered:!1,cover:(0,n.jsx)("img",{style:{height:168.75},src:`https://assets.devowl.io/in-app/wp-real-cookie-banner/visual-content-blocker-preview/${e}.png`}),children:(0,n.jsx)(Y.A.Meta,{title:(0,n.jsxs)(n.Fragment,{children:[s,a," ","default"!==e&&i]}),description:t})})},e)}))})})})})}})},ee=e=>{let{visualMediaThumbnailPicker:t}=e;const{__:s}=(0,y.s)();return(0,n.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.criteria!==t.criteria,children:e=>{let{getFieldValue:i}=e;return"services"===i("criteria")&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(T.r,{offset:m.MX.labelCol.span,description:s("For each content blocker it can be defined if it should be visually visible. This means that if the user has not agreed to the respective services, a box with a button is displayed to adjust the privacy settings so that the actual content can be loaded. The design of the box is copied from the cookie banner."),children:s("Visual")}),(0,n.jsx)(q,{}),(0,n.jsx)(Q,{}),(0,n.jsx)(H,{}),(0,n.jsx)(L,{}),(0,n.jsx)(W,{}),(0,n.jsx)(K,{children:t}),(0,n.jsx)(J,{}),(0,n.jsx)(Z,{}),(0,n.jsx)($,{})]})}})};var te=s(93842);const se=e=>{let{technicalProps:t,visualProps:s}=e;const{__:i}=(0,y.s)(),r=(0,d.useRef)(),o=(0,d.useRef)();return(0,n.jsxs)("div",{ref:r,children:[(0,n.jsx)(x.g,{containerRef:r,resetButton:o,resetButtonEvent:te.U,form:"blocker"}),(0,n.jsx)(f.o,{form:"blocker"}),(0,n.jsx)(S,{}),(0,n.jsx)(M,{...t}),(0,n.jsx)(ee,{...s}),(0,n.jsx)(v.h,{type:"blocker"}),(0,n.jsxs)(a.A.Item,{className:"rcb-antd-form-sticky-submit",colon:!1,labelAlign:"left",label:(0,n.jsx)(te.T,{anchorRef:o}),children:[(0,n.jsx)(U,{}),(0,n.jsx)("div",{style:{textAlign:"center",margin:"10px 0"},children:(0,n.jsx)("input",{type:"submit",className:"button button-primary",value:i("Save")})})]})]})};var ne=s(60971),ie=s(75432),re=s(71951),ae=s(30617),oe=s(67120);const le=(0,c.PA)((e=>{let{nonExistingServices:t,onCreated:s}=e;const[i,r]=(0,d.useState)(),a=(0,re.g)().cookieStore.groups.sortedGroups.map((e=>{let{data:{id:t,name:s}}=e;return{id:t,name:s}})),[l,c]=(0,d.useState)([]),u=(t||[]).filter((e=>{let{identifier:t}=e;return-1===l.indexOf(t)})),h=null==t?void 0:t.map((e=>{const{identifier:t,version:d}=e;return(0,n.jsx)(o.A,{open:i===t,title:(0,ae.__)("Add service"),width:"calc(100% - 50px)",bodyStyle:{paddingBottom:0},footer:null,onCancel:()=>r(void 0),children:(0,n.jsx)(oe.CookieEditForm,{overwriteAttributes:(0,ie.r)(e,{groups:a}),navigateAfterCreation:!1,scrollToTop:!1,template:{identifier:t,version:d},onCreated:e=>{r(void 0),c([...l,i]),s(e)}})},t)}));return(0,n.jsxs)(n.Fragment,{children:[h,0===u.length?null:(0,n.jsxs)("div",{className:"notice notice-warning below-h2 notice-alt",children:[(0,n.jsx)("p",{children:(0,ae.__)("Some services from the template could not be found. Please select (or create if not already exist) the following services:",u.join(", "))}),(0,n.jsx)("ul",{style:{margin:"0 0 10px"},children:u.map((e=>{let{identifier:t,name:s,subHeadline:i}=e;return(0,n.jsxs)("li",{children:[(0,n.jsxs)("strong",{children:[s,i?` (${i})`:""]})," • ",(0,n.jsx)("a",{onClick:e=>{r(t),e.preventDefault()},children:(0,ae.__)("Create now")})]},t)}))})]})]})}));var ce=s(36962);const de=(0,c.PA)((e=>{let{nonExistingTcfVendors:t,onCreated:s}=e;const[i,a]=(0,d.useState)(),{tcfStore:l}=(0,re.g)(),{vendorListVersion:c,vendors:u}=l,[h,p]=(0,d.useState)([]),m=(t||[]).filter((e=>{let{vendorId:t}=e;return-1===h.indexOf(t)}));if((0,d.useEffect)((()=>{t.length>0&&!c&&l.fetchVendors()}),[t,c]),!c&&t.length>0)return(0,n.jsx)(r.A,{spinning:!0});const v=null==t?void 0:t.map((e=>{let{vendorId:t}=e;return(0,n.jsx)(o.A,{open:i===t,title:(0,ae.__)("Add TCF vendor configuration"),width:"calc(100% - 50px)",bodyStyle:{paddingBottom:0},footer:null,onCancel:()=>a(void 0),children:(0,n.jsx)(ce.TcfVendorConfigurationForm,{navigateAfterCreation:!1,scrollToTop:!1,vendor:u.get(`${t}`),onCreated:e=>{a(void 0),p([...h,i]),s(e)}})},t)}));return(0,n.jsxs)(n.Fragment,{children:[v,0===m.length?null:(0,n.jsxs)("div",{className:"notice notice-warning below-h2 notice-alt",children:[(0,n.jsx)("p",{children:(0,ae.__)("Some TCF vendors from the template could not be found. Please select (or create if not already exist) the following TCF vendors:",m.join(", "))}),(0,n.jsx)("ul",{style:{margin:"0 0 10px"},children:m.map((e=>{let{vendorId:t,createAdNetwork:s}=e;return(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:u.get(`${t}`).data.name})," • ",(0,n.jsx)("a",{onClick:e=>{s?window.location.href=`#/cookies/tcf-vendors/new?adNetwork=${encodeURIComponent(s)}`:a(t),e.preventDefault()},children:(0,ae.__)("Create now")})]},t)}))})]})]})}));var ue=s(36920),he=s(44227),pe=s(43244),me=s(68789),ve=s(79521),fe=s(42090);let xe=!1;const be=e=>{let{attachmentId:t,title:s,allowedTypes:i,render:a,onChange:o}=e;const l=t||void 0,{data:c,error:u,fetching:h}=function(e){const[t,s]=(0,d.useState)(!1),[n,i]=(0,d.useState)(),[r,a]=(0,d.useState)(),o=(0,d.useCallback)((async e=>{s(!0);const{currentLanguage:t}=(0,ve.j)();try{const s=await(0,fe.E)({location:{path:"/media/:id",method:me.RouteHttpVerb.GET,namespace:"wp/v2"},params:{id:e,_dataLocale:t}});i(s),a(void 0)}catch(e){i(void 0),a(e)}finally{s(!1)}}),[]);return(0,d.useEffect)((()=>{e?o(e):(i(void 0),a(void 0))}),[e]),{fetching:t,data:n,fetch:o,error:r}}(l),p=null==c?void 0:c.source_url;(0,d.useEffect)((()=>{var e;"rest_post_invalid_id"===(null==u||null==(e=u.responseJSON)?void 0:e.code)&&o(void 0,void 0)}),[u]);const m=function(){const{addFilter:e}=pe.hooks,{MediaUpload:t}=pe.mediaUtils;return xe||(xe=!0,e("editor.MediaUpload","core/edit-post/components/media-upload/replace-media-upload",(()=>t))),t}();return(0,n.jsx)(r.A,{spinning:h,children:(0,n.jsx)(m,{onSelect:e=>{null==o||o(null==e?void 0:e.id,e)},title:s,allowedTypes:i,value:l,render:e=>{let{open:t}=e;return a({open:t,reset:()=>o(void 0,void 0),attachmentId:l,url:p})}})})};var ye=s(68588),ge=s(40164);const je=(0,c.PA)((e=>{let{template:t,overwriteAttributes:s,navigateAfterCreation:c=!0,comingFromServiceCreation:v=!1}=e;var f,x;const{blocker:b,id:y,queried:g,fetched:j,link:k}=(0,ue.t)(),w=(0,u.Zp)(),[A,C]=(0,d.useState)(!1),{cookieStore:T,tcfStore:S,optionStore:{isTcf:V,others:{isPro:_}}}=(0,re.g)(),I=(null==b?void 0:b.templateModel)||T.templatesBlocker.get(null==t?void 0:t.identifier),{declarations:F}=S,{prompt:P,form:D,isBusy:N,defaultValues:B,nonExistingServices:E,nonExistingTcfVendors:M,onFinish:U,onFinishFailed:R,onValuesChange:q,contextValue:O}=(0,h.z)({attributes:s,isTcf:V,isEdit:j,entityTemplateVersion:null==b||null==(x=b.data)||null==(f=x.meta)?void 0:f.presetVersion,template:null==I?void 0:I.use,initialHasChanges:v,handleSave:async e=>{try{var t,s,n;const{name:i,status:r,description:a,succeessorDeletionCheck:o,...l}=e,d={...l,visualMediaThumbnail:l.visualMediaThumbnail||0,criteria:l.criteria,tcfVendors:(null==(t=l.tcfVendors)?void 0:t.join(","))||"",tcfPurposes:(null==(s=l.tcfPurposes)?void 0:s.join(","))||"",services:(null==(n=l.services)?void 0:n.join(","))||"",isVisual:"services"===l.criteria&&l.isVisual,presetId:null==I?void 0:I.data.identifier,presetVersion:null==I?void 0:I.data.version};if(delete d.templateCheck,g)b.setName(i),b.setStatus(r),b.setDescription(a),b.setMeta(d),await b.patch();else{const e=new he.g(J,{title:{raw:i},content:{raw:a,protected:!1},status:r,meta:d});if(await e.persist(),o){const e=I.data.consumerData.successorOf.map((e=>{let{id:t}=e;return t})),t=[...T.blockers.entries.values()],s=e.map((e=>t.find((t=>{let{key:s}=t;return s===e}))||new he.g(T.blockers,{id:e})));await Promise.allSettled(s.map((e=>e.delete())))}}return()=>c&&("string"==typeof c?window.location.href=c:w(k.slice(1)))}catch(e){throw e.responseJSON.message}}}),L=g&&!j||I&&!I.use,[$,G]=(0,p.k)({...O,tcfPurposes:Object.values((null==F?void 0:F.purposes)||[])},{},{deps:[L]}),{blockers:J,essentialGroup:Y}=T,z=j?{name:b.data.title.raw,status:b.data.status,description:b.data.content.raw,criteria:b.data.meta.criteria,rules:b.data.meta.rules,tcfVendors:b.tcfVendors,tcfPurposes:b.tcfPurposes,services:b.services,isVisual:b.data.meta.isVisual,visualType:_?b.data.meta.visualType:"default",visualMediaThumbnail:b.data.meta.visualMediaThumbnail,visualContentType:b.data.meta.visualContentType,isVisualDarkMode:b.data.meta.isVisualDarkMode,visualBlur:b.data.meta.visualBlur,visualDownloadThumbnail:b.data.meta.visualDownloadThumbnail,visualHeroButtonText:b.data.meta.visualHeroButtonText,shouldForceToShowVisual:b.data.meta.shouldForceToShowVisual,templateCheck:void 0,succeessorDeletionCheck:void 0}:B,H=(0,d.useCallback)((e=>{D.setFieldsValue({services:[...D.getFieldValue("services"),e.key]})}),[D]),X=(0,d.useCallback)((e=>{D.setFieldsValue({tcfVendors:[...D.getFieldValue("tcfVendors"),e.key]})}),[D]);return(0,d.useEffect)((()=>{g&&!j&&J.getSingle({params:{id:y,context:"edit"}})}),[g,j]),(0,d.useEffect)((()=>{!I||I.use||I.busy||I.fetchUse()}),[I]),(0,d.useEffect)((()=>{(0,ne.V)(0),Y||T.fetchGroups()}),[]),(0,d.useEffect)((()=>{!F&&V&&S.fetchDeclarations()}),[V,F]),L?(0,n.jsx)(ge.e,{maxWidth:"fixed",children:(0,n.jsx)(i.A,{active:!0,paragraph:{rows:8}})}):(0,n.jsx)(ge.e,{maxWidth:"fixed",children:(0,n.jsx)($,{value:G,children:(0,n.jsxs)(r.A,{spinning:N||(null==I?void 0:I.busy)||!1,children:[P,(0,n.jsx)(a.A,{name:`blocker-${y}`,form:D,...m.MX,initialValues:z,onFinish:U,onFinishFailed:R,onValuesChange:q,scrollToFirstError:{behavior:"smooth",block:"center"},labelWrap:!0,children:(0,n.jsx)(se,{technicalProps:{servicesFieldProps:{dropdown:(0,n.jsx)(ye.D,{postType:["rcb-cookie"],multiple:!0,filter:e=>e["rcb-cookie-group"][0]!==(null==Y?void 0:Y.key)}),children:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("button",{type:"button",className:"button",onClick:()=>C(!0),children:(0,ae.__)("Create new service")}),(0,n.jsx)(o.A,{open:A,title:(0,ae.__)("Add service"),width:"calc(100% - 50px)",bodyStyle:{paddingBottom:0},footer:null,onCancel:()=>C(!1),children:(0,n.jsx)(oe.CookieEditForm,{navigateAfterCreation:!1,scrollToTop:!1,onCreated:e=>{C(!1),H(e)}})},y),(0,n.jsx)(le,{nonExistingServices:E,onCreated:H})]})},tcfVendorsFieldProps:{dropdownVendors:(0,n.jsx)(ye.D,{postType:["rcb-tcf-vendor-conf"],multiple:!0,titleRender:e=>{let{vendor:{name:t}}=e;return t}}),children:(0,n.jsx)(de,{nonExistingTcfVendors:M,onCreated:X})}},visualProps:{visualMediaThumbnailPicker:(0,n.jsx)(be,{title:(0,ae.__)("Select preview image"),allowedTypes:["image"],render:e=>{let{open:t,reset:s,attachmentId:i,url:r}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("p",{style:{marginTop:0},children:[(0,n.jsx)("a",{className:"button",onClick:t,children:i?(0,ae.__)("Replace image"):(0,ae.__)("Select from media library")})," ",i&&(0,n.jsx)("a",{className:"button",onClick:s,children:(0,ae.__)("Remove image")})]}),r&&(0,n.jsx)(l.A,{width:272,src:r})]})}})}})})]})})})}))},22923:(e,t,s)=>{s.r(t),s.d(t,{BlockerList:()=>I});var n=s(3713),i=s(19117),r=s(57922),a=s(41594),o=s(32150),l=s(52113);const c=Symbol(),d=()=>(0,l.NV)(c);var u=s(34650),h=s(75792),p=s(28101),m=s(52066),v=s(24262),f=s(18197),x=s(36086),b=s(73491),y=s(64715),g=s(55924),j=s(59726),k=s(24325),w=s(17312);const A=e=>{let{busy:t,attributes:{id:s,name:i,description:r,status:o,criteria:l,services:c,tcfVendors:h,rules:p,isVisual:A,presetId:C},avatarUrl:T,isUpdateAvailable:S,languages:V,languageOnClick:_}=e;const{__:I}=(0,j.s)(),{isLicensed:F}=(0,k.J)(),{onEdit:P,onDelete:D}=d(),[N,B]=(0,a.useState)(!1),E=(0,a.useMemo)((()=>{if(r){const e=document.createElement("div");return e.innerHTML=r,e.textContent}return""}),[r]);return(0,n.jsx)(u.A.Item,{itemID:s.toString(),actions:[(0,n.jsx)("a",{onClick:()=>P(s),children:I(S?"Edit and update":"Edit")},"edit"),(0,n.jsx)(v.A,{title:I("Are you sure that you want to delete this content blocker?"),placement:"bottomRight",onConfirm:()=>D(s),okText:I("Delete"),cancelText:I("Cancel"),overlayStyle:{maxWidth:350},children:(0,n.jsx)("a",{children:I("Delete")})},"delete"),(null==V?void 0:V.length)&&(0,n.jsx)(w.r,{recordId:s,languages:V,onClick:_},"languages")].filter(Boolean),children:(0,n.jsx)(f.A,{spinning:t,children:(0,n.jsx)(u.A.Item.Meta,{avatar:T?(0,n.jsx)(x.A,{size:"large",src:T,shape:"square"}):(0,n.jsx)(x.A,{size:"large",shape:"circle",children:i.toUpperCase()[0]}),title:(0,n.jsxs)("span",{children:[i," ","draft"===o?(0,n.jsx)(b.A,{color:"orange",children:I("Draft")}):"private"===o?(0,n.jsx)(b.A,{color:"red",children:I("Disabled")}):null,"services"===l&&0===c.length&&(0,n.jsxs)(b.A,{color:"red",children:[I("No connected services defined")," ",(0,n.jsx)(m.A,{})," ",I("Disabled")]}),"tcfVendors"===l&&0===h.length&&(0,n.jsxs)(b.A,{color:"red",children:[I("No connected TCF Vendors defined")," ",(0,n.jsx)(m.A,{})," ",I("Disabled")]}),!!C&&(0,n.jsx)(b.A,{children:I("Created from template")}),!!C&&!T&&(0,n.jsx)(y.A,{title:I(F?"There is no longer a content blocker template for this service. Probably the service has been discontinued. Please look for alternatives!":"This content blocker was created from a template. As you do not have a license activated at the moment, updates that are potentially available cannot be downloaded."),children:(0,n.jsx)(b.A,{color:"red",children:I(F?"No longer supported":"Possibly outdated")})}),!!S&&(0,n.jsx)(y.A,{title:I("The blocker template has been updated to provide current legal and technical information."),children:(0,n.jsx)(b.A,{color:"green",children:I("Update available")})})]}),description:(0,n.jsxs)("div",{children:[!!E&&(0,n.jsx)("div",{children:(0,g.g)(E)}),(0,n.jsxs)("div",{style:{paddingTop:5},children:[I("URLs / Elements to block"),":"," ",p.slice(0,N?p.length:5).map(((e,t)=>(0,n.jsx)(b.A,{children:e},`${e}-${t}`))),p.length>5&&!N&&(0,n.jsx)(b.A,{onClick:()=>B(!0),children:I("Show all")})]}),(0,n.jsxs)("div",{style:{paddingTop:5},children:[I("Visual Content Blocker"),":"," ",(0,n.jsx)(b.A,{children:I(A?"Yes, if possible":"No")})]})]})})})})},C=()=>{const{__:e}=(0,j.s)(),{busy:t,serviceCount:s,contentBlockerCount:i,rows:r,onCreate:o}=d(),l=(0,a.useMemo)((()=>{const e=[];for(let t=0;t<i;t++)e.push({key:t});return e}),[i]),c=e("Add content blocker");return i?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"wp-clearfix",children:(0,n.jsx)("a",{onClick:o,className:"button button-primary right",style:{marginBottom:10},children:c})}),t?(0,n.jsx)(u.A,{dataSource:l,renderItem:()=>(0,n.jsx)(u.A.Item,{children:(0,n.jsx)(h.A,{loading:!0,active:!0,paragraph:{rows:1}})})}):(0,n.jsx)(u.A,{children:r.map((e=>(0,a.createElement)(A,{...e,key:e.attributes.id.toString()})))})]}):(0,n.jsx)(p.A,{description:e(s>0?"You have not yet created a content blocker.":"Because a content blocker must be associated with a service, you must create a service first."),children:(0,n.jsx)("a",{className:"button button-primary",onClick:o,children:c})})};var T=s(53603),S=s(36920),V=s(71951),_=s(40164);const I=(0,r.PA)((()=>{const{message:e}=i.A.useApp(),{addLink:t,editLink:s}=(0,S.t)(),{cookieStore:r}=(0,V.g)(),{blockers:d,cookiesCount:u,blockersCount:h}=r,{busy:p,sortedBlockers:m,entries:v}=d,f=(0,T.m)("blocker");(0,a.useEffect)((()=>{r.fetchBlockers(),r.fetchGroups()}),[]);const[x,b]=function(){for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,l.gm)(c,...t)}({busy:p,serviceCount:u,contentBlockerCount:h,rows:m.map((t=>{const{key:s,busy:n,data:i,rules:r,services:a,tcfVendors:l,templateModel:c,isUpdateAvailable:d}=t,{title:{raw:u},content:{raw:h},status:p,meta:{criteria:m,presetId:v,isVisual:f}}=i;return{busy:n,attributes:{id:s,criteria:m,description:h,name:u,isVisual:f,rules:r,services:a,status:p,tcfVendors:l,presetId:v},avatarUrl:null==c?void 0:c.data.logoUrl,isUpdateAvailable:d,languages:i.multilingual,languageOnClick:async(t,s)=>{let{code:n,id:i}=s;try{const e=!1===i?(await(0,o.C)("rcb-blocker",t,n)).id:i,s=new URL(window.location.href);s.hash=`#/blocker/edit/${e}`,s.searchParams.set("lang",n),window.location.href=s.toString()}catch(t){var r;if(!(null==(r=t.responseJSON)?void 0:r.message))throw t;e.error(t.responseJSON.message)}}}}))},{onDelete:(e,t)=>v.get(t).delete({force:!0}),onEdit:(e,t)=>{window.location.href=s(v.get(t))},onCreate:()=>{window.location.href=t}},{inherit:["busy","contentBlockerCount","serviceCount","rows"]});return(0,n.jsxs)(_.e,{children:[(0,n.jsx)(x,{value:b,children:(0,n.jsx)(C,{})}),(0,n.jsx)("p",{className:"description",style:{maxWidth:800,margin:"30px auto 0",textAlign:"center"},children:f})]})}))},88663:(e,t,s)=>{s.r(t),s.d(t,{BlockerTemplateCenter:()=>h});var n=s(3713),i=s(57922),r=s(41594),a=s(93859),o=s(62789),l=s(67748),c=s(76576),d=s(71951),u=s(40164);const h=(0,i.PA)((()=>{const{cookieStore:e,optionStore:{isTcf:t}}=(0,d.g)(),[s,i]=(0,r.useState)(!1),[h,p]=(0,r.useState)(),{force:m,comingFromServiceCreation:v,attributes:f,navigateAfterCreation:x=!0}=(0,c.f)(),b=(0,r.useCallback)((async()=>{s||(i(!0),await e.fetchTemplatesBlocker());const t=Array.from(e.templatesBlocker.values()).map((e=>{let{data:t}=e;return t}));return t.sort(((e,t)=>e.headline.localeCompare(t.headline))),t}),[s]),[y,g]=(0,a.m)({type:"content-blocker",quickLinks:["blocker-individual","service-scanner","cookie-experts"],enableLocalFilter:!0,syncTemplates:()=>e.fetchTemplatesBlocker({storage:"redownload"}),fetchTemplates:b,fetchUse:async t=>(await b(),e.templatesBlocker.get(t).fetchUse()),initialSelection:m,onSelect:(e,s)=>{(null==e?void 0:e.tcfVendorIds.length)>0&&!t?window.location.href=`#/settings/tcf?tcfIntegrationItem=${encodeURIComponent(e.name)}&navigateAfterTcfActivation=${encodeURIComponent(`#/blocker/new?force=${e.identifier}`)}`:p({identifier:null==e?void 0:e.identifier,version:null==e?void 0:e.version,overwriteAttributes:s&&f?JSON.parse(f):void 0})}},{});return void 0===h?(0,n.jsx)(u.e,{children:(0,n.jsx)(y,{value:g,children:(0,n.jsx)(o.q,{})})}):(0,n.jsx)(u.e,{maxWidth:"fixed",children:(0,n.jsx)(l.BlockerEditForm,{comingFromServiceCreation:"1"===v,template:h.identifier?{identifier:h.identifier,version:h.version}:void 0,overwriteAttributes:h.overwriteAttributes,navigateAfterCreation:x})})}))},68588:(e,t,s)=>{s.d(t,{D:()=>v});var n=s(3713),i=s(6196),r=s(18197),a=s(51192),o=s(41594),l=s(24513),c=s(84200);function d(e){var t;return(0,c.g)(e)&&!(0,l.j)(e)?null==(t=(new DOMParser).parseFromString(`<a href="${e}"></a>`,"text/html").querySelector("a"))?void 0:t.href:(new DOMParser).parseFromString(e,"text/html").documentElement.textContent}var u=s(68789),h=s(79521),p=s(30617),m=s(42090);const v=e=>{let{postType:t,postStatus:s=["draft","publish","private"],perPage:l=10,value:c,multiple:v,disabled:f,forceDefaultLanguage:x,onChange:b,titleRender:y=(e=>null==e?void 0:e.title.rendered),applyTitleRenderOnSelectOption:g,filter:j=(()=>!0)}=e;const[k,w]=(0,o.useState)(!1),[A,C]=(0,o.useState)(c),[T,S]=(0,o.useState)(!1),[V,_]=(0,o.useState)([]),I=(0,o.useCallback)((async(e,n)=>{void 0===n&&(n=!0),S(!0);const{defaultLanguage:i,currentLanguage:r}=(0,h.j)(),a=(await(0,m.E)({location:{path:"/search",method:u.RouteHttpVerb.GET,namespace:"wp/v2"},request:{status:e.include?["draft","publish","private"]:s,...t?{subtype:t.join(",")}:{},...e},params:{_dataLocale:x?i:r,_embed:"self",_rcbExtendSearchResult:!0}})).map((e=>{let{_embedded:{self:[t]}}=e;return{content:{rendered:"",raw:""},...t}}));return n&&_(a),S(!1),a}),[]);!function(e,t,s,n){const[i,r]=(0,o.useState)(e);(0,o.useEffect)((()=>{const s=setTimeout((()=>{r(e)}),t);return null==n||n(e),()=>{clearTimeout(s)}}),[e]),(0,o.useEffect)((()=>{var e;!1!==(e=i)&&I({search:e,per_page:e.length?50:l})}),[i])}(k,""===k?0:800,0,(e=>{!1!==e&&S(!0),_([])})),(0,o.useEffect)((()=>{const e=e=>(void 0===e&&(e=!0),"number"==typeof A&&A>0||Array.isArray(A)&&A.length>0?I({include:Array.isArray(A)?A:[A]},e):Promise.resolve([])),t=async()=>{if("visible"===document.visibilityState){const[t]=await e(!1);t&&_((e=>e.map((e=>e.id===t.id?t:e))))}};return e(),document.addEventListener("visibilitychange",t),()=>{document.removeEventListener("visibilitychange",t)}}),[]),(0,o.useEffect)((()=>{JSON.stringify(A)!==JSON.stringify(c)&&("number"==typeof c&&c>0||Array.isArray(c)&&c.length>0)&&(C(c),I({include:Array.isArray(c)?c:[c]}))}),[c,A]);const F=Array.isArray(A)?A:[A].filter(Boolean),P=`— ${(0,p.__)("Select")} —`;return(0,n.jsxs)(i.A,{mode:v?"multiple":void 0,disabled:f,showSearch:!0,value:A,placeholder:(0,p.__)("Search..."),notFoundContent:T?(0,n.jsx)(r.A,{size:"small"}):null,onClick:()=>w(""),onSearch:w,onChange:e=>{const t=Array.isArray(e)?e.map(Number):+e;C(t),null==b||b(t)},filterOption:!1,loading:T,labelRender:e=>{let{label:t}=e;return(0,o.isValidElement)(t)?t.props["data-label"]:t},children:[!v&&!T&&(0,n.jsx)(i.A.Option,{value:0,children:d(g&&y(void 0,F)||P)}),V.map((e=>{const s=d(y(e,F)),r=e.type_singular&&((null==t?void 0:t.length)>1||!t)?e.type_singular:void 0;return(0,n.jsx)(i.A.Option,{value:e.id,style:{display:j(e)?void 0:"none"},children:(0,n.jsxs)(a.A,{justify:"space-between",align:"center","data-label":`${s}${r?` (${r})`:""}`,children:[(0,n.jsx)("span",{children:s}),r&&(0,n.jsx)("span",{style:{opacity:.7,paddingLeft:5,paddingRight:5},children:r})]})},e.id)}))]})}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/59cefc7b671ba4beae7e52d3a4d90b4f/chunk-config-tab-blocker.lite.js.map
