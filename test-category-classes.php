<?php
/**
 * Test script to verify that event category classes are being added correctly
 * This script should be run from the WordPress root directory
 */

// Load WordPress
require_once('wp-config.php');

// Get a sample event with categories
$events = get_posts([
    'post_type' => 'tribe_events',
    'posts_per_page' => 5,
    'meta_query' => [
        [
            'key' => '_EventStartDate',
            'value' => date('Y-m-d H:i:s'),
            'compare' => '>='
        ]
    ]
]);

echo "<h2>Testing Event Category Classes</h2>\n";

foreach ($events as $event) {
    echo "<h3>Event: " . esc_html($event->post_title) . " (ID: {$event->ID})</h3>\n";
    
    // Get categories for this event
    $categories = get_the_terms($event->ID, 'tribe_events_cat');
    
    if ($categories && !is_wp_error($categories)) {
        echo "<p><strong>Categories:</strong> ";
        foreach ($categories as $category) {
            echo esc_html($category->name) . " (slug: {$category->slug}) ";
        }
        echo "</p>\n";
        
        // Get post classes
        $classes = get_post_class([], $event->ID);
        
        echo "<p><strong>Post Classes:</strong></p>\n";
        echo "<ul>\n";
        foreach ($classes as $class) {
            if (strpos($class, 'cat_') === 0 || strpos($class, 'tribe_events_cat-') === 0) {
                echo "<li style='color: green; font-weight: bold;'>" . esc_html($class) . "</li>\n";
            } else {
                echo "<li>" . esc_html($class) . "</li>\n";
            }
        }
        echo "</ul>\n";
        
        // Check if both formats are present
        $has_old_format = false;
        $has_new_format = false;
        
        foreach ($categories as $category) {
            $old_class = 'cat_' . sanitize_html_class($category->slug);
            $new_class = 'tribe_events_cat-' . sanitize_html_class($category->slug);
            
            if (in_array($old_class, $classes)) {
                $has_old_format = true;
            }
            if (in_array($new_class, $classes)) {
                $has_new_format = true;
            }
        }
        
        echo "<p><strong>Status:</strong> ";
        if ($has_old_format && $has_new_format) {
            echo "<span style='color: green;'>✓ Both old and new class formats are present</span>";
        } elseif ($has_old_format) {
            echo "<span style='color: orange;'>⚠ Only old format (cat_) is present</span>";
        } elseif ($has_new_format) {
            echo "<span style='color: blue;'>ℹ Only new format (tribe_events_cat-) is present</span>";
        } else {
            echo "<span style='color: red;'>✗ No category classes found</span>";
        }
        echo "</p>\n";
        
    } else {
        echo "<p><em>No categories assigned to this event</em></p>\n";
    }
    
    echo "<hr>\n";
}

echo "<h3>Summary</h3>\n";
echo "<p>If the fix is working correctly, you should see both 'cat_' and 'tribe_events_cat-' class formats for each event category.</p>\n";
echo "<p>The JavaScript category color selector looks for the 'tribe_events_cat-' format to filter events.</p>\n";
