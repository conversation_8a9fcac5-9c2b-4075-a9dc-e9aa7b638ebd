"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[55],{96789:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(2464),i=n(41594);const o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 394c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8H708V152c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v166H400V152c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v166H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h168v236H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h168v166c0 4.4 3.6 8 8 8h64c4.4 0 8-3.6 8-8V706h228v166c0 4.4 3.6 8 8 8h64c4.4 0 8-3.6 8-8V706h164c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8H708V394h164zM628 630H400V394h228v236z"}}]},name:"number",theme:"outlined"};var a=n(4679),s=function(e,t){return i.createElement(a.A,(0,r.A)({},e,{ref:t,icon:o}))};const l=i.forwardRef(s)},76151:(e,t,n)=>{n.r(t),n.d(t,{default:()=>O});var r=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(e){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){return e.parentNode&&e.parentNode.removeChild(e)})),this.tags=[],this.ctr=0},e}(),i=n(65878),o=n(86407),a=n(29670),s=n(42819),l=n(78948),d=function(e,t,n){for(var r=0,o=0;r=o,o=(0,i.se)(),38===r&&12===o&&(t[n]=1),!(0,i.Sh)(o);)(0,i.K2)();return(0,i.di)(e,i.G1)},u=new WeakMap,c=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||u.get(n))&&!r){u.set(e,!0);for(var a=[],s=function(e,t){return(0,i.VF)(function(e,t){var n=-1,r=44;do{switch((0,i.Sh)(r)){case 0:38===r&&12===(0,i.se)()&&(t[n]=1),e[n]+=d(i.G1-1,t,n);break;case 2:e[n]+=(0,i.Tb)(r);break;case 4:if(44===r){e[++n]=58===(0,i.se)()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=(0,o.HT)(r)}}while(r=(0,i.K2)());return e}((0,i.c4)(e),t))}(t,a),l=n.props,c=0,f=0;c<s.length;c++)for(var h=0;h<l.length;h++,f++)e.props[f]=a[c]?s[c].replace(/&\f/g,l[h]):l[h]+" "+s[c]}}},f=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function h(e,t){switch((0,o.tW)(e,t)){case 5103:return a.j+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return a.j+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return a.j+e+a.vd+e+a.MS+e+e;case 6828:case 4268:return a.j+e+a.MS+e+e;case 6165:return a.j+e+a.MS+"flex-"+e+e;case 5187:return a.j+e+(0,o.HC)(e,/(\w+).+(:[^]+)/,a.j+"box-$1$2"+a.MS+"flex-$1$2")+e;case 5443:return a.j+e+a.MS+"flex-item-"+(0,o.HC)(e,/flex-|-self/,"")+e;case 4675:return a.j+e+a.MS+"flex-line-pack"+(0,o.HC)(e,/align-content|flex-|-self/,"")+e;case 5548:return a.j+e+a.MS+(0,o.HC)(e,"shrink","negative")+e;case 5292:return a.j+e+a.MS+(0,o.HC)(e,"basis","preferred-size")+e;case 6060:return a.j+"box-"+(0,o.HC)(e,"-grow","")+a.j+e+a.MS+(0,o.HC)(e,"grow","positive")+e;case 4554:return a.j+(0,o.HC)(e,/([^-])(transform)/g,"$1"+a.j+"$2")+e;case 6187:return(0,o.HC)((0,o.HC)((0,o.HC)(e,/(zoom-|grab)/,a.j+"$1"),/(image-set)/,a.j+"$1"),e,"")+e;case 5495:case 3959:return(0,o.HC)(e,/(image-set\([^]*)/,a.j+"$1$`$1");case 4968:return(0,o.HC)((0,o.HC)(e,/(.+:)(flex-)?(.*)/,a.j+"box-pack:$3"+a.MS+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+a.j+e+e;case 4095:case 3583:case 4068:case 2532:return(0,o.HC)(e,/(.+)-inline(.+)/,a.j+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if((0,o.b2)(e)-1-t>6)switch((0,o.wN)(e,t+1)){case 109:if(45!==(0,o.wN)(e,t+4))break;case 102:return(0,o.HC)(e,/(.+:)(.+)-([^]+)/,"$1"+a.j+"$2-$3$1"+a.vd+(108==(0,o.wN)(e,t+3)?"$3":"$2-$3"))+e;case 115:return~(0,o.K5)(e,"stretch")?h((0,o.HC)(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==(0,o.wN)(e,t+1))break;case 6444:switch((0,o.wN)(e,(0,o.b2)(e)-3-(~(0,o.K5)(e,"!important")&&10))){case 107:return(0,o.HC)(e,":",":"+a.j)+e;case 101:return(0,o.HC)(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+a.j+(45===(0,o.wN)(e,14)?"inline-":"")+"box$3$1"+a.j+"$2$3$1"+a.MS+"$2box$3")+e}break;case 5936:switch((0,o.wN)(e,t+11)){case 114:return a.j+e+a.MS+(0,o.HC)(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return a.j+e+a.MS+(0,o.HC)(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return a.j+e+a.MS+(0,o.HC)(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return a.j+e+a.MS+e+e}return e}var p=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case a.LU:e.return=h(e.value,e.length);break;case a.Sv:return(0,s.l)([(0,i.C)(e,{value:(0,o.HC)(e.value,"@","@"+a.j)})],r);case a.XZ:if(e.length)return(0,o.kg)(e.props,(function(t){switch((0,o.YW)(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return(0,s.l)([(0,i.C)(e,{props:[(0,o.HC)(t,/:(read-\w+)/,":"+a.vd+"$1")]})],r);case"::placeholder":return(0,s.l)([(0,i.C)(e,{props:[(0,o.HC)(t,/:(plac\w+)/,":"+a.j+"input-$1")]}),(0,i.C)(e,{props:[(0,o.HC)(t,/:(plac\w+)/,":"+a.vd+"$1")]}),(0,i.C)(e,{props:[(0,o.HC)(t,/:(plac\w+)/,a.MS+"input-$1")]})],r)}return""}))}}],g={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function v(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}var m=/[A-Z]|^ms/g,b=/_EMO_([^_]+?)_([^]*?)_EMO_/g,y=function(e){return 45===e.charCodeAt(1)},w=function(e){return null!=e&&"boolean"!=typeof e},S=v((function(e){return y(e)?e:e.replace(m,"-$&").toLowerCase()})),k=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(b,(function(e,t,n){return N={name:t,styles:n,next:N},t}))}return 1===g[e]||y(e)||"number"!=typeof t||0===t?t:t+"px"};function x(e,t,n){if(null==n)return"";if(void 0!==n.__emotion_styles)return n;switch(typeof n){case"boolean":return"";case"object":if(1===n.anim)return N={name:n.name,styles:n.styles,next:N},n.name;if(void 0!==n.styles){var r=n.next;if(void 0!==r)for(;void 0!==r;)N={name:r.name,styles:r.styles,next:N},r=r.next;return n.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var i=0;i<n.length;i++)r+=x(e,t,n[i])+";";else for(var o in n){var a=n[o];if("object"!=typeof a)null!=t&&void 0!==t[a]?r+=o+"{"+t[a]+"}":w(a)&&(r+=S(o)+":"+k(o,a)+";");else if(!Array.isArray(a)||"string"!=typeof a[0]||null!=t&&void 0!==t[a[0]]){var s=x(e,t,a);switch(o){case"animation":case"animationName":r+=S(o)+":"+s+";";break;default:r+=o+"{"+s+"}"}}else for(var l=0;l<a.length;l++)w(a[l])&&(r+=S(o)+":"+k(o,a[l])+";")}return r}(e,t,n);case"function":if(void 0!==e){var i=N,o=n(e);return N=i,x(e,t,o)}}if(null==t)return n;var a=t[n];return void 0!==a?a:n}var N,C=/label:\s*([^\s;\n{]+)\s*(;|$)/g,j=function(e,t,n){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var r=!0,i="";N=void 0;var o=e[0];null==o||void 0===o.raw?(r=!1,i+=x(n,t,o)):i+=o[0];for(var a=1;a<e.length;a++)i+=x(n,t,e[a]),r&&(i+=o[a]);C.lastIndex=0;for(var s,l="";null!==(s=C.exec(i));)l+="-"+s[1];var d=function(e){for(var t,n=0,r=0,i=e.length;i>=4;++r,i-=4)t=1540483477*(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&n)+(59797*(n>>>16)<<16);switch(i){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=1540483477*(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=1540483477*(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)}(i)+l;return{name:d,styles:i,next:N}};function E(e,t,n){var r="";return n.split(" ").forEach((function(n){void 0!==e[n]?t.push(e[n]+";"):r+=n+" "})),r}function A(e,t){if(void 0===e.inserted[t.name])return e.insert("",t,e.sheet,!0)}function $(e,t,n){var r=[],i=E(e,r,n);return r.length<2?n:i+t(r)}var O=function(e){var t=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var i,a,d=e.stylisPlugins||p,u={},h=[];i=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)u[t[n]]=!0;h.push(e)}));var g,v,m,b,y=[c,f],w=[s.A,(b=function(e){g.insert(e)},function(e){e.root||(e=e.return)&&b(e)})],S=(v=y.concat(d,w),m=(0,o.FK)(v),function(e,t,n,r){for(var i="",o=0;o<m;o++)i+=v[o](e,t,n,r)||"";return i});a=function(e,t,n,r){var i;g=n,i=e?e+"{"+t.styles+"}":t.styles,(0,s.l)((0,l.wE)(i),S),r&&(k.inserted[t.name]=!0)};var k={key:t,sheet:new r({key:t,container:i,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:u,registered:{},insert:a};return k.sheet.hydrate(h),k}(e);t.sheet.speedy=function(e){this.isSpeedy=e},t.compat=!0;var n=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=j(n,t.registered,void 0);return function(e,t,n){!function(e,t,n){var r=e.key+"-"+t.name;void 0===e.registered[r]&&(e.registered[r]=t.styles)}(e,t);var r=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var i=t;do{e.insert(t===i?"."+r:"",i,e.sheet,!0),i=i.next}while(void 0!==i)}}(t,i),t.key+"-"+i.name};return{css:n,cx:function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return $(t.registered,n,L(r))},injectGlobal:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=j(n,t.registered);A(t,i)},keyframes:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=j(n,t.registered),o="animation-"+i.name;return A(t,{name:i.name,styles:"@keyframes "+o+"{"+i.styles+"}"}),o},hydrate:function(e){e.forEach((function(e){t.inserted[e]=!0}))},flush:function(){t.registered={},t.inserted={},t.sheet.flush()},sheet:t.sheet,cache:t,getRegisteredStyles:E.bind(null,t.registered),merge:$.bind(null,t.registered,n)}},L=function e(t){for(var n="",r=0;r<t.length;r++){var i=t[r];if(null!=i){var o=void 0;switch(typeof i){case"boolean":break;case"object":if(Array.isArray(i))o=e(i);else for(var a in o="",i)i[a]&&a&&(o&&(o+=" "),o+=a);break;default:o=i}o&&(n&&(n+=" "),n+=o)}}return n}},61298:(e,t,n)=>{n.d(t,{A:()=>ve});var r=n(41594),i=n(60531),o=n(2464);const a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"};var s=n(4679),l=function(e,t){return r.createElement(s.A,(0,o.A)({},e,{ref:t,icon:a}))};const d=r.forwardRef(l);var u=n(65924),c=n.n(u),f=n(21483),h=n(81188),p=n(61129),g=n(4105),v=n(78493),m=n(48253);function b(){return"function"==typeof BigInt}function y(e){return!e&&0!==e&&!Number.isNaN(e)||!String(e).trim()}function w(e){var t=e.trim(),n=t.startsWith("-");n&&(t=t.slice(1)),(t=t.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,"")).startsWith(".")&&(t="0".concat(t));var r=t||"0",i=r.split("."),o=i[0]||"0",a=i[1]||"0";"0"===o&&"0"===a&&(n=!1);var s=n?"-":"";return{negative:n,negativeStr:s,trimStr:r,integerStr:o,decimalStr:a,fullStr:"".concat(s).concat(r)}}function S(e){var t=String(e);return!Number.isNaN(Number(t))&&t.includes("e")}function k(e){var t=String(e);if(S(e)){var n=Number(t.slice(t.indexOf("e-")+2)),r=t.match(/\.(\d+)/);return null!=r&&r[1]&&(n+=r[1].length),n}return t.includes(".")&&N(t)?t.length-t.indexOf(".")-1:0}function x(e){var t=String(e);if(S(e)){if(e>Number.MAX_SAFE_INTEGER)return String(b()?BigInt(e).toString():Number.MAX_SAFE_INTEGER);if(e<Number.MIN_SAFE_INTEGER)return String(b()?BigInt(e).toString():Number.MIN_SAFE_INTEGER);t=e.toFixed(k(t))}return w(t).fullStr}function N(e){return"number"==typeof e?!Number.isNaN(e):!!e&&(/^\s*-?\d+(\.\d+)?\s*$/.test(e)||/^\s*-?\d+\.\s*$/.test(e)||/^\s*-?\.\d+\s*$/.test(e))}var C=function(){function e(t){if((0,v.A)(this,e),(0,f.A)(this,"origin",""),(0,f.A)(this,"negative",void 0),(0,f.A)(this,"integer",void 0),(0,f.A)(this,"decimal",void 0),(0,f.A)(this,"decimalLen",void 0),(0,f.A)(this,"empty",void 0),(0,f.A)(this,"nan",void 0),y(t))this.empty=!0;else if(this.origin=String(t),"-"===t||Number.isNaN(t))this.nan=!0;else{var n=t;if(S(n)&&(n=Number(n)),N(n="string"==typeof n?n:x(n))){var r=w(n);this.negative=r.negative;var i=r.trimStr.split(".");this.integer=BigInt(i[0]);var o=i[1]||"0";this.decimal=BigInt(o),this.decimalLen=o.length}else this.nan=!0}}return(0,m.A)(e,[{key:"getMark",value:function(){return this.negative?"-":""}},{key:"getIntegerStr",value:function(){return this.integer.toString()}},{key:"getDecimalStr",value:function(){return this.decimal.toString().padStart(this.decimalLen,"0")}},{key:"alignDecimal",value:function(e){var t="".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(e,"0"));return BigInt(t)}},{key:"negate",value:function(){var t=new e(this.toString());return t.negative=!t.negative,t}},{key:"cal",value:function(t,n,r){var i=Math.max(this.getDecimalStr().length,t.getDecimalStr().length),o=n(this.alignDecimal(i),t.alignDecimal(i)).toString(),a=r(i),s=w(o),l=s.negativeStr,d=s.trimStr,u="".concat(l).concat(d.padStart(a+1,"0"));return new e("".concat(u.slice(0,-a),".").concat(u.slice(-a)))}},{key:"add",value:function(t){if(this.isInvalidate())return new e(t);var n=new e(t);return n.isInvalidate()?this:this.cal(n,(function(e,t){return e+t}),(function(e){return e}))}},{key:"multi",value:function(t){var n=new e(t);return this.isInvalidate()||n.isInvalidate()?new e(NaN):this.cal(n,(function(e,t){return e*t}),(function(e){return 2*e}))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return this.nan}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(e){return this.toString()===(null==e?void 0:e.toString())}},{key:"lessEquals",value:function(e){return this.add(e.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.isNaN()?NaN:Number(this.toString())}},{key:"toString",value:function(){return arguments.length>0&&void 0!==arguments[0]&&!arguments[0]?this.origin:this.isInvalidate()?"":w("".concat(this.getMark()).concat(this.getIntegerStr(),".").concat(this.getDecimalStr())).fullStr}}]),e}(),j=function(){function e(t){(0,v.A)(this,e),(0,f.A)(this,"origin",""),(0,f.A)(this,"number",void 0),(0,f.A)(this,"empty",void 0),y(t)?this.empty=!0:(this.origin=String(t),this.number=Number(t))}return(0,m.A)(e,[{key:"negate",value:function(){return new e(-this.toNumber())}},{key:"add",value:function(t){if(this.isInvalidate())return new e(t);var n=Number(t);if(Number.isNaN(n))return this;var r=this.number+n;if(r>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(r<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var i=Math.max(k(this.number),k(n));return new e(r.toFixed(i))}},{key:"multi",value:function(t){var n=Number(t);if(this.isInvalidate()||Number.isNaN(n))return new e(NaN);var r=this.number*n;if(r>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(r<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var i=Math.max(k(this.number),k(n));return new e(r.toFixed(i))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return Number.isNaN(this.number)}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(e){return this.toNumber()===(null==e?void 0:e.toNumber())}},{key:"lessEquals",value:function(e){return this.add(e.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.number}},{key:"toString",value:function(){return arguments.length>0&&void 0!==arguments[0]&&!arguments[0]?this.origin:this.isInvalidate()?"":x(this.number)}}]),e}();function E(e){return b()?new C(e):new j(e)}function A(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(""===e)return"";var i=w(e),o=i.negativeStr,a=i.integerStr,s=i.decimalStr,l="".concat(t).concat(s),d="".concat(o).concat(a);if(n>=0){var u=Number(s[n]);return u>=5&&!r?A(E(e).add("".concat(o,"0.").concat("0".repeat(n)).concat(10-u)).toString(),t,n,r):0===n?d:"".concat(d).concat(t).concat(s.padEnd(n,"0").slice(0,n))}return".0"===l?d:"".concat(d).concat(l)}const $=E;var O=n(88815),L=n(78294),M=n(2620),D=n(33717),_=n(42243);var I=n(32664);function B(e){var t=e.prefixCls,n=e.upNode,i=e.downNode,a=e.upDisabled,s=e.downDisabled,l=e.onStep,d=r.useRef(),u=r.useRef([]),h=r.useRef();h.current=l;var g,v,m,b,y=function(){clearTimeout(d.current)},w=function(e,t){e.preventDefault(),y(),h.current(t),d.current=setTimeout((function e(){h.current(t),d.current=setTimeout(e,200)}),600)};if(r.useEffect((function(){return function(){y(),u.current.forEach((function(e){return I.A.cancel(e)}))}}),[]),g=(0,r.useState)(!1),v=(0,p.A)(g,2),m=v[0],b=v[1],(0,L.A)((function(){b((0,_.A)())}),[]),m)return null;var S="".concat(t,"-handler"),k=c()(S,"".concat(S,"-up"),(0,f.A)({},"".concat(S,"-up-disabled"),a)),x=c()(S,"".concat(S,"-down"),(0,f.A)({},"".concat(S,"-down-disabled"),s)),N=function(){return u.current.push((0,I.A)(y))},C={unselectable:"on",role:"button",onMouseUp:N,onMouseLeave:N};return r.createElement("div",{className:"".concat(S,"-wrap")},r.createElement("span",(0,o.A)({},C,{onMouseDown:function(e){w(e,!0)},"aria-label":"Increase Value","aria-disabled":a,className:k}),n||r.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-up-inner")})),r.createElement("span",(0,o.A)({},C,{onMouseDown:function(e){w(e,!1)},"aria-label":"Decrease Value","aria-disabled":s,className:x}),i||r.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-down-inner")})))}function P(e){var t="number"==typeof e?x(e):w(e).fullStr;return t.includes(".")?w(t.replace(/(\d)\.(\d)/g,"$1$2.")).fullStr:e+"0"}var R=n(38680),F=["prefixCls","className","style","min","max","step","defaultValue","value","disabled","readOnly","upHandler","downHandler","keyboard","changeOnWheel","controls","classNames","stringMode","parser","formatter","precision","decimalSeparator","onChange","onInput","onPressEnter","onStep","changeOnBlur","domRef"],T=["disabled","style","prefixCls","value","prefix","suffix","addonBefore","addonAfter","className","classNames"],H=function(e,t){return e||t.isEmpty()?t.toString():t.toNumber()},G=function(e){var t=$(e);return t.isInvalidate()?null:t},V=r.forwardRef((function(e,t){var n=e.prefixCls,i=e.className,a=e.style,s=e.min,l=e.max,d=e.step,u=void 0===d?1:d,v=e.defaultValue,m=e.value,b=e.disabled,y=e.readOnly,w=e.upHandler,S=e.downHandler,C=e.keyboard,j=e.changeOnWheel,E=void 0!==j&&j,O=e.controls,_=void 0===O||O,R=(e.classNames,e.stringMode),T=e.parser,V=e.formatter,W=e.precision,z=e.decimalSeparator,q=e.onChange,U=e.onInput,X=e.onPressEnter,J=e.onStep,K=e.changeOnBlur,Y=void 0===K||K,Z=e.domRef,Q=(0,g.A)(e,F),ee="".concat(n,"-input"),te=r.useRef(null),ne=r.useState(!1),re=(0,p.A)(ne,2),ie=re[0],oe=re[1],ae=r.useRef(!1),se=r.useRef(!1),le=r.useRef(!1),de=r.useState((function(){return $(null!=m?m:v)})),ue=(0,p.A)(de,2),ce=ue[0],fe=ue[1],he=r.useCallback((function(e,t){if(!t)return W>=0?W:Math.max(k(e),k(u))}),[W,u]),pe=r.useCallback((function(e){var t=String(e);if(T)return T(t);var n=t;return z&&(n=n.replace(z,".")),n.replace(/[^\w.-]+/g,"")}),[T,z]),ge=r.useRef(""),ve=r.useCallback((function(e,t){if(V)return V(e,{userTyping:t,input:String(ge.current)});var n="number"==typeof e?x(e):e;if(!t){var r=he(n,t);N(n)&&(z||r>=0)&&(n=A(n,z||".",r))}return n}),[V,he,z]),me=r.useState((function(){var e=null!=v?v:m;return ce.isInvalidate()&&["string","number"].includes((0,h.A)(e))?Number.isNaN(e)?"":e:ve(ce.toString(),!1)})),be=(0,p.A)(me,2),ye=be[0],we=be[1];function Se(e,t){we(ve(e.isInvalidate()?e.toString(!1):e.toString(!t),t))}ge.current=ye;var ke,xe,Ne,Ce,je,Ee=r.useMemo((function(){return G(l)}),[l,W]),Ae=r.useMemo((function(){return G(s)}),[s,W]),$e=r.useMemo((function(){return!(!Ee||!ce||ce.isInvalidate())&&Ee.lessEquals(ce)}),[Ee,ce]),Oe=r.useMemo((function(){return!(!Ae||!ce||ce.isInvalidate())&&ce.lessEquals(Ae)}),[Ae,ce]),Le=(ke=te.current,xe=ie,Ne=(0,r.useRef)(null),[function(){try{var e=ke.selectionStart,t=ke.selectionEnd,n=ke.value,r=n.substring(0,e),i=n.substring(t);Ne.current={start:e,end:t,value:n,beforeTxt:r,afterTxt:i}}catch(e){}},function(){if(ke&&Ne.current&&xe)try{var e=ke.value,t=Ne.current,n=t.beforeTxt,r=t.afterTxt,i=t.start,o=e.length;if(e.endsWith(r))o=e.length-Ne.current.afterTxt.length;else if(e.startsWith(n))o=n.length;else{var a=n[i-1],s=e.indexOf(a,i-1);-1!==s&&(o=s+1)}ke.setSelectionRange(o,o)}catch(e){(0,D.Ay)(!1,"Something warning of cursor restore. Please fire issue about this: ".concat(e.message))}}]),Me=(0,p.A)(Le,2),De=Me[0],_e=Me[1],Ie=function(e){return Ee&&!e.lessEquals(Ee)?Ee:Ae&&!Ae.lessEquals(e)?Ae:null},Be=function(e){return!Ie(e)},Pe=function(e,t){var n=e,r=Be(n)||n.isEmpty();if(n.isEmpty()||t||(n=Ie(n)||n,r=!0),!y&&!b&&r){var i=n.toString(),o=he(i,t);return o>=0&&(n=$(A(i,".",o)),Be(n)||(n=$(A(i,".",o,!0)))),n.equals(ce)||(void 0===m&&fe(n),null==q||q(n.isEmpty()?null:H(R,n)),void 0===m&&Se(n,t)),n}return ce},Re=(Ce=(0,r.useRef)(0),je=function(){I.A.cancel(Ce.current)},(0,r.useEffect)((function(){return je}),[]),function(e){je(),Ce.current=(0,I.A)((function(){e()}))}),Fe=function e(t){if(De(),ge.current=t,we(t),!se.current){var n=pe(t),r=$(n);r.isNaN()||Pe(r,!0)}null==U||U(t),Re((function(){var n=t;T||(n=t.replace(/。/g,".")),n!==t&&e(n)}))},Te=function(e){var t;if(!(e&&$e||!e&&Oe)){ae.current=!1;var n=$(le.current?P(u):u);e||(n=n.negate());var r=(ce||$(0)).add(n.toString()),i=Pe(r,!1);null==J||J(H(R,i),{offset:le.current?P(u):u,type:e?"up":"down"}),null===(t=te.current)||void 0===t||t.focus()}},He=function(e){var t,n=$(pe(ye));t=n.isNaN()?Pe(ce,e):Pe(n,e),void 0!==m?Se(ce,!1):t.isNaN()||Se(t,!1)};return r.useEffect((function(){if(E&&ie){var e=function(e){Te(e.deltaY<0),e.preventDefault()},t=te.current;if(t)return t.addEventListener("wheel",e,{passive:!1}),function(){return t.removeEventListener("wheel",e)}}})),(0,L.o)((function(){ce.isInvalidate()||Se(ce,!1)}),[W,V]),(0,L.o)((function(){var e=$(m);fe(e);var t=$(pe(ye));e.equals(t)&&ae.current&&!V||Se(e,ae.current)}),[m]),(0,L.o)((function(){V&&_e()}),[ye]),r.createElement("div",{ref:Z,className:c()(n,i,(0,f.A)((0,f.A)((0,f.A)((0,f.A)((0,f.A)({},"".concat(n,"-focused"),ie),"".concat(n,"-disabled"),b),"".concat(n,"-readonly"),y),"".concat(n,"-not-a-number"),ce.isNaN()),"".concat(n,"-out-of-range"),!ce.isInvalidate()&&!Be(ce))),style:a,onFocus:function(){oe(!0)},onBlur:function(){Y&&He(!1),oe(!1),ae.current=!1},onKeyDown:function(e){var t=e.key,n=e.shiftKey;ae.current=!0,le.current=n,"Enter"===t&&(se.current||(ae.current=!1),He(!1),null==X||X(e)),!1!==C&&!se.current&&["Up","ArrowUp","Down","ArrowDown"].includes(t)&&(Te("Up"===t||"ArrowUp"===t),e.preventDefault())},onKeyUp:function(){ae.current=!1,le.current=!1},onCompositionStart:function(){se.current=!0},onCompositionEnd:function(){se.current=!1,Fe(te.current.value)},onBeforeInput:function(){ae.current=!0}},_&&r.createElement(B,{prefixCls:n,upNode:w,downNode:S,upDisabled:$e,downDisabled:Oe,onStep:Te}),r.createElement("div",{className:"".concat(ee,"-wrap")},r.createElement("input",(0,o.A)({autoComplete:"off",role:"spinbutton","aria-valuemin":s,"aria-valuemax":l,"aria-valuenow":ce.isInvalidate()?null:ce.toString(),step:u},Q,{ref:(0,M.K4)(te,t),className:ee,value:ye,onChange:function(e){Fe(e.target.value)},disabled:b,readOnly:y}))))})),W=r.forwardRef((function(e,t){var n=e.disabled,i=e.style,a=e.prefixCls,s=void 0===a?"rc-input-number":a,l=e.value,d=e.prefix,u=e.suffix,c=e.addonBefore,f=e.addonAfter,h=e.className,p=e.classNames,v=(0,g.A)(e,T),m=r.useRef(null),b=r.useRef(null),y=r.useRef(null);return r.useImperativeHandle(t,(function(){return e=y.current,t={nativeElement:m.current.nativeElement||b.current},"undefined"!=typeof Proxy&&e?new Proxy(e,{get:function(e,n){if(t[n])return t[n];var r=e[n];return"function"==typeof r?r.bind(e):r}}):e;var e,t})),r.createElement(O.a,{className:h,triggerFocus:function(e){y.current&&(0,R.F4)(y.current,e)},prefixCls:s,value:l,disabled:n,style:i,prefix:d,suffix:u,addonAfter:f,addonBefore:c,classNames:p,components:{affixWrapper:"div",groupWrapper:"div",wrapper:"div",groupAddon:"div"},ref:m},r.createElement(V,(0,o.A)({prefixCls:s,disabled:n,ref:y,domRef:b,className:null==p?void 0:p.input},v)))}));const z=W;var q=n(58145),U=n(80840),X=n(65666),J=n(77648),K=n(51471),Y=n(31754),Z=n(70284),Q=n(86221),ee=n(15460),te=n(78052),ne=n(68485),re=n(92888),ie=n(87843),oe=n(71094),ae=n(88431),se=n(52146),le=n(63829),de=n(26411);const ue=(e,t)=>{let{componentCls:n,borderRadiusSM:r,borderRadiusLG:i}=e;const o="lg"===t?i:r;return{[`&-${t}`]:{[`${n}-handler-wrap`]:{borderStartEndRadius:o,borderEndEndRadius:o},[`${n}-handler-up`]:{borderStartEndRadius:o},[`${n}-handler-down`]:{borderEndEndRadius:o}}}},ce=e=>{const{componentCls:t,lineWidth:n,lineType:r,borderRadius:i,fontSizeLG:o,controlHeightLG:a,controlHeightSM:s,colorError:l,paddingInlineSM:d,paddingBlockSM:u,paddingBlockLG:c,paddingInlineLG:f,colorTextDescription:h,motionDurationMid:p,handleHoverColor:g,paddingInline:v,paddingBlock:m,handleBg:b,handleActiveBg:y,colorTextDisabled:w,borderRadiusSM:S,borderRadiusLG:k,controlWidth:x,handleOpacity:N,handleBorderColor:C,filledHandleBg:j,lineHeightLG:E,calc:A}=e;return[{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,oe.dF)(e)),(0,ne.wj)(e)),{display:"inline-block",width:x,margin:0,padding:0,borderRadius:i}),(0,ie.Eb)(e,{[`${t}-handler-wrap`]:{background:b,[`${t}-handler-down`]:{borderBlockStart:`${(0,te.zA)(n)} ${r} ${C}`}}})),(0,ie.sA)(e,{[`${t}-handler-wrap`]:{background:j,[`${t}-handler-down`]:{borderBlockStart:`${(0,te.zA)(n)} ${r} ${C}`}},"&:focus-within":{[`${t}-handler-wrap`]:{background:b}}})),(0,ie.lB)(e)),{"&-rtl":{direction:"rtl",[`${t}-input`]:{direction:"rtl"}},"&-lg":{padding:0,fontSize:o,lineHeight:E,borderRadius:k,[`input${t}-input`]:{height:A(a).sub(A(n).mul(2)).equal(),padding:`${(0,te.zA)(c)} ${(0,te.zA)(f)}`}},"&-sm":{padding:0,borderRadius:S,[`input${t}-input`]:{height:A(s).sub(A(n).mul(2)).equal(),padding:`${(0,te.zA)(u)} ${(0,te.zA)(d)}`}},"&-out-of-range":{[`${t}-input-wrap`]:{input:{color:l}}},"&-group":Object.assign(Object.assign(Object.assign({},(0,oe.dF)(e)),(0,ne.XM)(e)),{"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",textAlign:"start",verticalAlign:"top",[`${t}-affix-wrapper`]:{width:"100%"},"&-lg":{[`${t}-group-addon`]:{borderRadius:k,fontSize:e.fontSizeLG}},"&-sm":{[`${t}-group-addon`]:{borderRadius:S}}},(0,ie.nm)(e)),(0,ie.Vy)(e)),{[`&:not(${t}-compact-first-item):not(${t}-compact-last-item)${t}-compact-item`]:{[`${t}, ${t}-group-addon`]:{borderRadius:0}},[`&:not(${t}-compact-last-item)${t}-compact-first-item`]:{[`${t}, ${t}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${t}-compact-first-item)${t}-compact-last-item`]:{[`${t}, ${t}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})}),[`&-disabled ${t}-input`]:{cursor:"not-allowed"},[t]:{"&-input":Object.assign(Object.assign(Object.assign(Object.assign({},(0,oe.dF)(e)),{width:"100%",padding:`${(0,te.zA)(m)} ${(0,te.zA)(v)}`,textAlign:"start",backgroundColor:"transparent",border:0,borderRadius:i,outline:0,transition:`all ${p} linear`,appearance:"textfield",fontSize:"inherit"}),(0,ne.j_)(e.colorTextPlaceholder)),{'&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button':{margin:0,webkitAppearance:"none",appearance:"none"}})}})},{[t]:Object.assign(Object.assign(Object.assign({[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{opacity:1},[`${t}-handler-wrap`]:{position:"absolute",insetBlockStart:0,insetInlineEnd:0,width:e.handleWidth,height:"100%",borderStartStartRadius:0,borderStartEndRadius:i,borderEndEndRadius:i,borderEndStartRadius:0,opacity:N,display:"flex",flexDirection:"column",alignItems:"stretch",transition:`opacity ${p} linear ${p}`,[`${t}-handler`]:{display:"flex",alignItems:"center",justifyContent:"center",flex:"auto",height:"40%",[`\n              ${t}-handler-up-inner,\n              ${t}-handler-down-inner\n            `]:{marginInlineEnd:0,fontSize:e.handleFontSize}}},[`${t}-handler`]:{height:"50%",overflow:"hidden",color:h,fontWeight:"bold",lineHeight:0,textAlign:"center",cursor:"pointer",borderInlineStart:`${(0,te.zA)(n)} ${r} ${C}`,transition:`all ${p} linear`,"&:active":{background:y},"&:hover":{height:"60%",[`\n              ${t}-handler-up-inner,\n              ${t}-handler-down-inner\n            `]:{color:g}},"&-up-inner, &-down-inner":Object.assign(Object.assign({},(0,oe.Nk)()),{color:h,transition:`all ${p} linear`,userSelect:"none"})},[`${t}-handler-up`]:{borderStartEndRadius:i},[`${t}-handler-down`]:{borderEndEndRadius:i}},ue(e,"lg")),ue(e,"sm")),{"&-disabled, &-readonly":{[`${t}-handler-wrap`]:{display:"none"},[`${t}-input`]:{color:"inherit"}},[`\n          ${t}-handler-up-disabled,\n          ${t}-handler-down-disabled\n        `]:{cursor:"not-allowed"},[`\n          ${t}-handler-up-disabled:hover &-handler-up-inner,\n          ${t}-handler-down-disabled:hover &-handler-down-inner\n        `]:{color:w}})}]},fe=e=>{const{componentCls:t,paddingBlock:n,paddingInline:r,inputAffixPadding:i,controlWidth:o,borderRadiusLG:a,borderRadiusSM:s,paddingInlineLG:l,paddingInlineSM:d,paddingBlockLG:u,paddingBlockSM:c}=e;return{[`${t}-affix-wrapper`]:Object.assign(Object.assign({[`input${t}-input`]:{padding:`${(0,te.zA)(n)} 0`}},(0,ne.wj)(e)),{position:"relative",display:"inline-flex",width:o,padding:0,paddingInlineStart:r,"&-lg":{borderRadius:a,paddingInlineStart:l,[`input${t}-input`]:{padding:`${(0,te.zA)(u)} 0`}},"&-sm":{borderRadius:s,paddingInlineStart:d,[`input${t}-input`]:{padding:`${(0,te.zA)(c)} 0`}},[`&:not(${t}-disabled):hover`]:{zIndex:1},"&-focused, &:focus":{zIndex:1},[`&-disabled > ${t}-disabled`]:{background:"transparent"},[`> div${t}`]:{width:"100%",border:"none",outline:"none",[`&${t}-focused`]:{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[`${t}-handler-wrap`]:{zIndex:2},[t]:{color:"inherit","&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center",pointerEvents:"none"},"&-prefix":{marginInlineEnd:i},"&-suffix":{position:"absolute",insetBlockStart:0,insetInlineEnd:0,zIndex:1,height:"100%",marginInlineEnd:r,marginInlineStart:i}}})}},he=(0,se.OF)("InputNumber",(e=>{const t=(0,le.h1)(e,(0,re.C)(e));return[ce(t),fe(t),(0,ae.G)(t)]}),(e=>{var t;const n=null!==(t=e.handleVisible)&&void 0!==t?t:"auto";return Object.assign(Object.assign({},(0,re.b)(e)),{controlWidth:90,handleWidth:e.controlHeightSM-2*e.lineWidth,handleFontSize:e.fontSize/2,handleVisible:n,handleActiveBg:e.colorFillAlter,handleBg:e.colorBgContainer,filledHandleBg:new de.q(e.colorFillSecondary).onBackground(e.colorBgContainer).toHexString(),handleHoverColor:e.colorPrimary,handleBorderColor:e.colorBorder,handleOpacity:!0===n?1:0})}),{unitless:{handleOpacity:!0}});const pe=r.forwardRef(((e,t)=>{const{getPrefixCls:n,direction:o}=r.useContext(U.QO),a=r.useRef(null);r.useImperativeHandle(t,(()=>a.current));const{className:s,rootClassName:l,size:u,disabled:f,prefixCls:h,addonBefore:p,addonAfter:g,prefix:v,bordered:m,readOnly:b,status:y,controls:w,variant:S}=e,k=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}(e,["className","rootClassName","size","disabled","prefixCls","addonBefore","addonAfter","prefix","bordered","readOnly","status","controls","variant"]),x=n("input-number",h),N=(0,K.A)(x),[C,j,E]=he(x,N),{compactSize:A,compactItemClassnames:$}=(0,ee.RQ)(x,o);let O=r.createElement(d,{className:`${x}-handler-up-inner`}),L=r.createElement(i.A,{className:`${x}-handler-down-inner`});const M="boolean"==typeof w?w:void 0;"object"==typeof w&&(O=void 0===w.upIcon?O:r.createElement("span",{className:`${x}-handler-up-inner`},w.upIcon),L=void 0===w.downIcon?L:r.createElement("span",{className:`${x}-handler-down-inner`},w.downIcon));const{hasFeedback:D,status:_,isFormItemInput:I,feedbackIcon:B}=r.useContext(Z.$W),P=(0,q.v)(_,y),R=(0,Y.A)((e=>{var t;return null!==(t=null!=u?u:A)&&void 0!==t?t:e})),F=r.useContext(J.A),T=null!=f?f:F,[H,G]=(0,Q.A)(S,m),V=D&&r.createElement(r.Fragment,null,B),W=c()({[`${x}-lg`]:"large"===R,[`${x}-sm`]:"small"===R,[`${x}-rtl`]:"rtl"===o,[`${x}-in-form-item`]:I},j),X=`${x}-group`;return C(r.createElement(z,Object.assign({ref:a,disabled:T,className:c()(E,N,s,l,$),upHandler:O,downHandler:L,prefixCls:x,readOnly:b,controls:M,prefix:v,suffix:V,addonAfter:g&&r.createElement(ee.K6,null,r.createElement(Z.XB,{override:!0,status:!0},g)),addonBefore:p&&r.createElement(ee.K6,null,r.createElement(Z.XB,{override:!0,status:!0},p)),classNames:{input:W,variant:c()({[`${x}-${H}`]:G},(0,q.L)(x,P,D)),affixWrapper:c()({[`${x}-affix-wrapper-sm`]:"small"===R,[`${x}-affix-wrapper-lg`]:"large"===R,[`${x}-affix-wrapper-rtl`]:"rtl"===o},j),wrapper:c()({[`${X}-rtl`]:"rtl"===o},j),groupWrapper:c()({[`${x}-group-wrapper-sm`]:"small"===R,[`${x}-group-wrapper-lg`]:"large"===R,[`${x}-group-wrapper-rtl`]:"rtl"===o,[`${x}-group-wrapper-${H}`]:G},(0,q.L)(`${x}-group-wrapper`,P,D),j)}},k)))})),ge=pe;ge._InternalPanelDoNotUseOrYouWillBeFired=e=>r.createElement(X.Ay,{theme:{components:{InputNumber:{handleVisible:!0}}}},r.createElement(pe,Object.assign({},e)));const ve=ge},6326:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.convertChangesToDMP=function(e){for(var t,n,r=[],i=0;i<e.length;i++)n=(t=e[i]).added?1:t.removed?-1:0,r.push([n,t.value]);return r}},62758:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.convertChangesToXML=function(e){for(var t=[],n=0;n<e.length;n++){var r=e[n];r.added?t.push("<ins>"):r.removed&&t.push("<del>"),t.push((i=r.value,void 0,i.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"))),r.added?t.push("</ins>"):r.removed&&t.push("</del>")}var i;return t.join("")}},77274:(e,t,n)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.diffArrays=function(e,t,n){return i.diff(e,t,n)},t.arrayDiff=void 0;var i=new(((r=n(30914))&&r.__esModule?r:{default:r}).default);t.arrayDiff=i,i.tokenize=function(e){return e.slice()},i.join=i.removeEmpty=function(e){return e}},30914:(e,t)=>{function n(){}function r(e,t,n,r,i){for(var o=0,a=t.length,s=0,l=0;o<a;o++){var d=t[o];if(d.removed){if(d.value=e.join(r.slice(l,l+d.count)),l+=d.count,o&&t[o-1].added){var u=t[o-1];t[o-1]=t[o],t[o]=u}}else{if(!d.added&&i){var c=n.slice(s,s+d.count);c=c.map((function(e,t){var n=r[l+t];return n.length>e.length?n:e})),d.value=e.join(c)}else d.value=e.join(n.slice(s,s+d.count));s+=d.count,d.added||(l+=d.count)}}var f=t[a-1];return a>1&&"string"==typeof f.value&&(f.added||f.removed)&&e.equals("",f.value)&&(t[a-2].value+=f.value,t.pop()),t}Object.defineProperty(t,"__esModule",{value:!0}),t.default=n,n.prototype={diff:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=n.callback;"function"==typeof n&&(i=n,n={}),this.options=n;var o=this;function a(e){return i?(setTimeout((function(){i(void 0,e)}),0),!0):e}e=this.castInput(e),t=this.castInput(t),e=this.removeEmpty(this.tokenize(e));var s=(t=this.removeEmpty(this.tokenize(t))).length,l=e.length,d=1,u=s+l;n.maxEditLength&&(u=Math.min(u,n.maxEditLength));var c=[{newPos:-1,components:[]}],f=this.extractCommon(c[0],t,e,0);if(c[0].newPos+1>=s&&f+1>=l)return a([{value:this.join(t),count:t.length}]);function h(){for(var n=-1*d;n<=d;n+=2){var i=void 0,u=c[n-1],f=c[n+1],h=(f?f.newPos:0)-n;u&&(c[n-1]=void 0);var p=u&&u.newPos+1<s,g=f&&0<=h&&h<l;if(p||g){if(!p||g&&u.newPos<f.newPos?(i={newPos:(v=f).newPos,components:v.components.slice(0)},o.pushComponent(i.components,void 0,!0)):((i=u).newPos++,o.pushComponent(i.components,!0,void 0)),h=o.extractCommon(i,t,e,n),i.newPos+1>=s&&h+1>=l)return a(r(o,i.components,t,e,o.useLongestToken));c[n]=i}else c[n]=void 0}var v;d++}if(i)!function e(){setTimeout((function(){if(d>u)return i();h()||e()}),0)}();else for(;d<=u;){var p=h();if(p)return p}},pushComponent:function(e,t,n){var r=e[e.length-1];r&&r.added===t&&r.removed===n?e[e.length-1]={count:r.count+1,added:t,removed:n}:e.push({count:1,added:t,removed:n})},extractCommon:function(e,t,n,r){for(var i=t.length,o=n.length,a=e.newPos,s=a-r,l=0;a+1<i&&s+1<o&&this.equals(t[a+1],n[s+1]);)a++,s++,l++;return l&&e.components.push({count:l}),e.newPos=a,s},equals:function(e,t){return this.options.comparator?this.options.comparator(e,t):e===t||this.options.ignoreCase&&e.toLowerCase()===t.toLowerCase()},removeEmpty:function(e){for(var t=[],n=0;n<e.length;n++)e[n]&&t.push(e[n]);return t},castInput:function(e){return e},tokenize:function(e){return e.split("")},join:function(e){return e.join("")}}},90072:(e,t,n)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.diffChars=function(e,t,n){return i.diff(e,t,n)},t.characterDiff=void 0;var i=new(((r=n(30914))&&r.__esModule?r:{default:r}).default);t.characterDiff=i},89744:(e,t,n)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.diffCss=function(e,t,n){return i.diff(e,t,n)},t.cssDiff=void 0;var i=new(((r=n(30914))&&r.__esModule?r:{default:r}).default);t.cssDiff=i,i.tokenize=function(e){return e.split(/([{}:;,]|\s+)/)}},15377:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diffJson=function(e,t,n){return l.diff(e,t,n)},t.canonicalize=d,t.jsonDiff=void 0;var r,i=(r=n(30914))&&r.__esModule?r:{default:r},o=n(42041);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}var s=Object.prototype.toString,l=new i.default;function d(e,t,n,r,i){var o,l;for(t=t||[],n=n||[],r&&(e=r(i,e)),o=0;o<t.length;o+=1)if(t[o]===e)return n[o];if("[object Array]"===s.call(e)){for(t.push(e),l=new Array(e.length),n.push(l),o=0;o<e.length;o+=1)l[o]=d(e[o],t,n,r,i);return t.pop(),n.pop(),l}if(e&&e.toJSON&&(e=e.toJSON()),"object"===a(e)&&null!==e){t.push(e),l={},n.push(l);var u,c=[];for(u in e)e.hasOwnProperty(u)&&c.push(u);for(c.sort(),o=0;o<c.length;o+=1)l[u=c[o]]=d(e[u],t,n,r,u);t.pop(),n.pop()}else l=e;return l}t.jsonDiff=l,l.useLongestToken=!0,l.tokenize=o.lineDiff.tokenize,l.castInput=function(e){var t=this.options,n=t.undefinedReplacement,r=t.stringifyReplacer,i=void 0===r?function(e,t){return void 0===t?n:t}:r;return"string"==typeof e?e:JSON.stringify(d(e,null,null,i),i,"  ")},l.equals=function(e,t){return i.default.prototype.equals.call(l,e.replace(/,([\r\n])/g,"$1"),t.replace(/,([\r\n])/g,"$1"))}},42041:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diffLines=function(e,t,n){return a.diff(e,t,n)},t.diffTrimmedLines=function(e,t,n){var r=(0,o.generateOptions)(n,{ignoreWhitespace:!0});return a.diff(e,t,r)},t.lineDiff=void 0;var r,i=(r=n(30914))&&r.__esModule?r:{default:r},o=n(25456),a=new i.default;t.lineDiff=a,a.tokenize=function(e){var t=[],n=e.split(/(\n|\r\n)/);n[n.length-1]||n.pop();for(var r=0;r<n.length;r++){var i=n[r];r%2&&!this.options.newlineIsToken?t[t.length-1]+=i:(this.options.ignoreWhitespace&&(i=i.trim()),t.push(i))}return t}},522:(e,t,n)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.diffSentences=function(e,t,n){return i.diff(e,t,n)},t.sentenceDiff=void 0;var i=new(((r=n(30914))&&r.__esModule?r:{default:r}).default);t.sentenceDiff=i,i.tokenize=function(e){return e.split(/(\S.+?[.!?])(?=\s+|$)/)}},62075:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diffWords=function(e,t,n){return n=(0,o.generateOptions)(n,{ignoreWhitespace:!0}),l.diff(e,t,n)},t.diffWordsWithSpace=function(e,t,n){return l.diff(e,t,n)},t.wordDiff=void 0;var r,i=(r=n(30914))&&r.__esModule?r:{default:r},o=n(25456),a=/^[A-Za-z\xC0-\u02C6\u02C8-\u02D7\u02DE-\u02FF\u1E00-\u1EFF]+$/,s=/\S/,l=new i.default;t.wordDiff=l,l.equals=function(e,t){return this.options.ignoreCase&&(e=e.toLowerCase(),t=t.toLowerCase()),e===t||this.options.ignoreWhitespace&&!s.test(e)&&!s.test(t)},l.tokenize=function(e){for(var t=e.split(/([^\S\r\n]+|[()[\]{}'"\r\n]|\b)/),n=0;n<t.length-1;n++)!t[n+1]&&t[n+2]&&a.test(t[n])&&a.test(t[n+2])&&(t[n]+=t[n+2],t.splice(n+1,2),n--);return t}},2199:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Diff",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"diffChars",{enumerable:!0,get:function(){return o.diffChars}}),Object.defineProperty(t,"diffWords",{enumerable:!0,get:function(){return a.diffWords}}),Object.defineProperty(t,"diffWordsWithSpace",{enumerable:!0,get:function(){return a.diffWordsWithSpace}}),Object.defineProperty(t,"diffLines",{enumerable:!0,get:function(){return s.diffLines}}),Object.defineProperty(t,"diffTrimmedLines",{enumerable:!0,get:function(){return s.diffTrimmedLines}}),Object.defineProperty(t,"diffSentences",{enumerable:!0,get:function(){return l.diffSentences}}),Object.defineProperty(t,"diffCss",{enumerable:!0,get:function(){return d.diffCss}}),Object.defineProperty(t,"diffJson",{enumerable:!0,get:function(){return u.diffJson}}),Object.defineProperty(t,"canonicalize",{enumerable:!0,get:function(){return u.canonicalize}}),Object.defineProperty(t,"diffArrays",{enumerable:!0,get:function(){return c.diffArrays}}),Object.defineProperty(t,"applyPatch",{enumerable:!0,get:function(){return f.applyPatch}}),Object.defineProperty(t,"applyPatches",{enumerable:!0,get:function(){return f.applyPatches}}),Object.defineProperty(t,"parsePatch",{enumerable:!0,get:function(){return h.parsePatch}}),Object.defineProperty(t,"merge",{enumerable:!0,get:function(){return p.merge}}),Object.defineProperty(t,"structuredPatch",{enumerable:!0,get:function(){return g.structuredPatch}}),Object.defineProperty(t,"createTwoFilesPatch",{enumerable:!0,get:function(){return g.createTwoFilesPatch}}),Object.defineProperty(t,"createPatch",{enumerable:!0,get:function(){return g.createPatch}}),Object.defineProperty(t,"convertChangesToDMP",{enumerable:!0,get:function(){return v.convertChangesToDMP}}),Object.defineProperty(t,"convertChangesToXML",{enumerable:!0,get:function(){return m.convertChangesToXML}});var r,i=(r=n(30914))&&r.__esModule?r:{default:r},o=n(90072),a=n(62075),s=n(42041),l=n(522),d=n(89744),u=n(15377),c=n(77274),f=n(67450),h=n(86053),p=n(51454),g=n(80348),v=n(6326),m=n(62758)},67450:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.applyPatch=a,t.applyPatches=function(e,t){"string"==typeof e&&(e=(0,i.parsePatch)(e));var n=0;!function r(){var i=e[n++];if(!i)return t.complete();t.loadFile(i,(function(e,n){if(e)return t.complete(e);var o=a(n,i,t);t.patched(i,o,(function(e){if(e)return t.complete(e);r()}))}))}()};var r,i=n(86053),o=(r=n(6932))&&r.__esModule?r:{default:r};function a(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof t&&(t=(0,i.parsePatch)(t)),Array.isArray(t)){if(t.length>1)throw new Error("applyPatch only works with a single input.");t=t[0]}var r,a,s=e.split(/\r\n|[\n\v\f\r\x85]/),l=e.match(/\r\n|[\n\v\f\r\x85]/g)||[],d=t.hunks,u=n.compareLine||function(e,t,n,r){return t===r},c=0,f=n.fuzzFactor||0,h=0,p=0;function g(e,t){for(var n=0;n<e.lines.length;n++){var r=e.lines[n],i=r.length>0?r[0]:" ",o=r.length>0?r.substr(1):r;if(" "===i||"-"===i){if(!u(t+1,s[t],i,o)&&++c>f)return!1;t++}}return!0}for(var v=0;v<d.length;v++){for(var m=d[v],b=s.length-m.oldLines,y=0,w=p+m.oldStart-1,S=(0,o.default)(w,h,b);void 0!==y;y=S())if(g(m,w+y)){m.offset=p+=y;break}if(void 0===y)return!1;h=m.offset+m.oldStart+m.oldLines}for(var k=0,x=0;x<d.length;x++){var N=d[x],C=N.oldStart+N.offset+k-1;k+=N.newLines-N.oldLines;for(var j=0;j<N.lines.length;j++){var E=N.lines[j],A=E.length>0?E[0]:" ",$=E.length>0?E.substr(1):E,O=N.linedelimiters[j];if(" "===A)C++;else if("-"===A)s.splice(C,1),l.splice(C,1);else if("+"===A)s.splice(C,0,$),l.splice(C,0,O),C++;else if("\\"===A){var L=N.lines[j-1]?N.lines[j-1][0]:null;"+"===L?r=!0:"-"===L&&(a=!0)}}}if(r)for(;!s[s.length-1];)s.pop(),l.pop();else a&&(s.push(""),l.push("\n"));for(var M=0;M<s.length-1;M++)s[M]=s[M]+l[M];return s.join("")}},80348:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.structuredPatch=a,t.formatPatch=s,t.createTwoFilesPatch=l,t.createPatch=function(e,t,n,r,i,o){return l(e,e,t,n,r,i,o)};var r=n(42041);function i(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function a(e,t,n,o,a,s,l){l||(l={}),void 0===l.context&&(l.context=4);var d=(0,r.diffLines)(n,o,l);if(d){d.push({value:"",lines:[]});for(var u=[],c=0,f=0,h=[],p=1,g=1,v=function(e){var t=d[e],r=t.lines||t.value.replace(/\n$/,"").split("\n");if(t.lines=r,t.added||t.removed){var a;if(!c){var s=d[e-1];c=p,f=g,s&&(h=l.context>0?b(s.lines.slice(-l.context)):[],c-=h.length,f-=h.length)}(a=h).push.apply(a,i(r.map((function(e){return(t.added?"+":"-")+e})))),t.added?g+=r.length:p+=r.length}else{if(c)if(r.length<=2*l.context&&e<d.length-2){var v;(v=h).push.apply(v,i(b(r)))}else{var m,y=Math.min(r.length,l.context);(m=h).push.apply(m,i(b(r.slice(0,y))));var w={oldStart:c,oldLines:p-c+y,newStart:f,newLines:g-f+y,lines:h};if(e>=d.length-2&&r.length<=l.context){var S=/\n$/.test(n),k=/\n$/.test(o),x=0==r.length&&h.length>w.oldLines;!S&&x&&n.length>0&&h.splice(w.oldLines,0,"\\ No newline at end of file"),(S||x)&&k||h.push("\\ No newline at end of file")}u.push(w),c=0,f=0,h=[]}p+=r.length,g+=r.length}},m=0;m<d.length;m++)v(m);return{oldFileName:e,newFileName:t,oldHeader:a,newHeader:s,hunks:u}}function b(e){return e.map((function(e){return" "+e}))}}function s(e){var t=[];e.oldFileName==e.newFileName&&t.push("Index: "+e.oldFileName),t.push("==================================================================="),t.push("--- "+e.oldFileName+(void 0===e.oldHeader?"":"\t"+e.oldHeader)),t.push("+++ "+e.newFileName+(void 0===e.newHeader?"":"\t"+e.newHeader));for(var n=0;n<e.hunks.length;n++){var r=e.hunks[n];0===r.oldLines&&(r.oldStart-=1),0===r.newLines&&(r.newStart-=1),t.push("@@ -"+r.oldStart+","+r.oldLines+" +"+r.newStart+","+r.newLines+" @@"),t.push.apply(t,r.lines)}return t.join("\n")+"\n"}function l(e,t,n,r,i,o,l){return s(a(e,t,n,r,i,o,l))}},51454:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.calcLineCount=l,t.merge=function(e,t,n){e=d(e,n),t=d(t,n);var r={};(e.index||t.index)&&(r.index=e.index||t.index),(e.newFileName||t.newFileName)&&(u(e)?u(t)?(r.oldFileName=c(r,e.oldFileName,t.oldFileName),r.newFileName=c(r,e.newFileName,t.newFileName),r.oldHeader=c(r,e.oldHeader,t.oldHeader),r.newHeader=c(r,e.newHeader,t.newHeader)):(r.oldFileName=e.oldFileName,r.newFileName=e.newFileName,r.oldHeader=e.oldHeader,r.newHeader=e.newHeader):(r.oldFileName=t.oldFileName||e.oldFileName,r.newFileName=t.newFileName||e.newFileName,r.oldHeader=t.oldHeader||e.oldHeader,r.newHeader=t.newHeader||e.newHeader)),r.hunks=[];for(var i=0,o=0,a=0,s=0;i<e.hunks.length||o<t.hunks.length;){var l=e.hunks[i]||{oldStart:1/0},g=t.hunks[o]||{oldStart:1/0};if(f(l,g))r.hunks.push(h(l,a)),i++,s+=l.newLines-l.oldLines;else if(f(g,l))r.hunks.push(h(g,s)),o++,a+=g.newLines-g.oldLines;else{var v={oldStart:Math.min(l.oldStart,g.oldStart),oldLines:0,newStart:Math.min(l.newStart+a,g.oldStart+s),newLines:0,lines:[]};p(v,l.oldStart,l.lines,g.oldStart,g.lines),o++,i++,r.hunks.push(v)}}return r};var r=n(80348),i=n(86053),o=n(66921);function a(e){return function(e){if(Array.isArray(e))return s(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function l(e){var t=x(e.lines),n=t.oldLines,r=t.newLines;void 0!==n?e.oldLines=n:delete e.oldLines,void 0!==r?e.newLines=r:delete e.newLines}function d(e,t){if("string"==typeof e){if(/^@@/m.test(e)||/^Index:/m.test(e))return(0,i.parsePatch)(e)[0];if(!t)throw new Error("Must provide a base reference or pass in a patch");return(0,r.structuredPatch)(void 0,void 0,t,e)}return e}function u(e){return e.newFileName&&e.newFileName!==e.oldFileName}function c(e,t,n){return t===n?t:(e.conflict=!0,{mine:t,theirs:n})}function f(e,t){return e.oldStart<t.oldStart&&e.oldStart+e.oldLines<t.oldStart}function h(e,t){return{oldStart:e.oldStart,oldLines:e.oldLines,newStart:e.newStart+t,newLines:e.newLines,lines:e.lines}}function p(e,t,n,r,i){var o={offset:t,lines:n,index:0},s={offset:r,lines:i,index:0};for(b(e,o,s),b(e,s,o);o.index<o.lines.length&&s.index<s.lines.length;){var d=o.lines[o.index],u=s.lines[s.index];if("-"!==d[0]&&"+"!==d[0]||"-"!==u[0]&&"+"!==u[0])if("+"===d[0]&&" "===u[0]){var c;(c=e.lines).push.apply(c,a(w(o)))}else if("+"===u[0]&&" "===d[0]){var f;(f=e.lines).push.apply(f,a(w(s)))}else"-"===d[0]&&" "===u[0]?v(e,o,s):"-"===u[0]&&" "===d[0]?v(e,s,o,!0):d===u?(e.lines.push(d),o.index++,s.index++):m(e,w(o),w(s));else g(e,o,s)}y(e,o),y(e,s),l(e)}function g(e,t,n){var r=w(t),i=w(n);if(S(r)&&S(i)){var s,l;if((0,o.arrayStartsWith)(r,i)&&k(n,r,r.length-i.length))return void(s=e.lines).push.apply(s,a(r));if((0,o.arrayStartsWith)(i,r)&&k(t,i,i.length-r.length))return void(l=e.lines).push.apply(l,a(i))}else if((0,o.arrayEqual)(r,i)){var d;return void(d=e.lines).push.apply(d,a(r))}m(e,r,i)}function v(e,t,n,r){var i,o=w(t),s=function(e,t){for(var n=[],r=[],i=0,o=!1,a=!1;i<t.length&&e.index<e.lines.length;){var s=e.lines[e.index],l=t[i];if("+"===l[0])break;if(o=o||" "!==s[0],r.push(l),i++,"+"===s[0])for(a=!0;"+"===s[0];)n.push(s),s=e.lines[++e.index];l.substr(1)===s.substr(1)?(n.push(s),e.index++):a=!0}if("+"===(t[i]||"")[0]&&o&&(a=!0),a)return n;for(;i<t.length;)r.push(t[i++]);return{merged:r,changes:n}}(n,o);s.merged?(i=e.lines).push.apply(i,a(s.merged)):m(e,r?s:o,r?o:s)}function m(e,t,n){e.conflict=!0,e.lines.push({conflict:!0,mine:t,theirs:n})}function b(e,t,n){for(;t.offset<n.offset&&t.index<t.lines.length;){var r=t.lines[t.index++];e.lines.push(r),t.offset++}}function y(e,t){for(;t.index<t.lines.length;){var n=t.lines[t.index++];e.lines.push(n)}}function w(e){for(var t=[],n=e.lines[e.index][0];e.index<e.lines.length;){var r=e.lines[e.index];if("-"===n&&"+"===r[0]&&(n="+"),n!==r[0])break;t.push(r),e.index++}return t}function S(e){return e.reduce((function(e,t){return e&&"-"===t[0]}),!0)}function k(e,t,n){for(var r=0;r<n;r++){var i=t[t.length-n+r].substr(1);if(e.lines[e.index+r]!==" "+i)return!1}return e.index+=n,!0}function x(e){var t=0,n=0;return e.forEach((function(e){if("string"!=typeof e){var r=x(e.mine),i=x(e.theirs);void 0!==t&&(r.oldLines===i.oldLines?t+=r.oldLines:t=void 0),void 0!==n&&(r.newLines===i.newLines?n+=r.newLines:n=void 0)}else void 0===n||"+"!==e[0]&&" "!==e[0]||n++,void 0===t||"-"!==e[0]&&" "!==e[0]||t++})),{oldLines:t,newLines:n}}},86053:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parsePatch=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.split(/\r\n|[\n\v\f\r\x85]/),r=e.match(/\r\n|[\n\v\f\r\x85]/g)||[],i=[],o=0;function a(){var e={};for(i.push(e);o<n.length;){var r=n[o];if(/^(\-\-\-|\+\+\+|@@)\s/.test(r))break;var a=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(r);a&&(e.index=a[1]),o++}for(s(e),s(e),e.hunks=[];o<n.length;){var d=n[o];if(/^(Index:|diff|\-\-\-|\+\+\+)\s/.test(d))break;if(/^@@/.test(d))e.hunks.push(l());else{if(d&&t.strict)throw new Error("Unknown line "+(o+1)+" "+JSON.stringify(d));o++}}}function s(e){var t=/^(---|\+\+\+)\s+(.*)$/.exec(n[o]);if(t){var r="---"===t[1]?"old":"new",i=t[2].split("\t",2),a=i[0].replace(/\\\\/g,"\\");/^".*"$/.test(a)&&(a=a.substr(1,a.length-2)),e[r+"FileName"]=a,e[r+"Header"]=(i[1]||"").trim(),o++}}function l(){var e=o,i=n[o++].split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),a={oldStart:+i[1],oldLines:void 0===i[2]?1:+i[2],newStart:+i[3],newLines:void 0===i[4]?1:+i[4],lines:[],linedelimiters:[]};0===a.oldLines&&(a.oldStart+=1),0===a.newLines&&(a.newStart+=1);for(var s=0,l=0;o<n.length&&!(0===n[o].indexOf("--- ")&&o+2<n.length&&0===n[o+1].indexOf("+++ ")&&0===n[o+2].indexOf("@@"));o++){var d=0==n[o].length&&o!=n.length-1?" ":n[o][0];if("+"!==d&&"-"!==d&&" "!==d&&"\\"!==d)break;a.lines.push(n[o]),a.linedelimiters.push(r[o]||"\n"),"+"===d?s++:"-"===d?l++:" "===d&&(s++,l++)}if(s||1!==a.newLines||(a.newLines=0),l||1!==a.oldLines||(a.oldLines=0),t.strict){if(s!==a.newLines)throw new Error("Added line count did not match for hunk at line "+(e+1));if(l!==a.oldLines)throw new Error("Removed line count did not match for hunk at line "+(e+1))}return a}for(;o<n.length;)a();return i}},66921:(e,t)=>{function n(e,t){if(t.length>e.length)return!1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!1;return!0}Object.defineProperty(t,"__esModule",{value:!0}),t.arrayEqual=function(e,t){return e.length===t.length&&n(e,t)},t.arrayStartsWith=n},6932:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){var r=!0,i=!1,o=!1,a=1;return function s(){if(r&&!o){if(i?a++:r=!1,e+a<=n)return a;o=!0}if(!i)return o||(r=!0),t<=e-a?-a++:(i=!0,s())}}},25456:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.generateOptions=function(e,t){if("function"==typeof e)t.callback=e;else if(e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}},75304:(e,t,n)=>{n.r(t),n.d(t,{default:()=>o});var r=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function i(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!((i=e[n])===(o=t[n])||r(i)&&r(o)))return!1;var i,o;return!0}function o(e,t){void 0===t&&(t=i);var n=null;function r(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];if(n&&n.lastThis===this&&t(r,n.lastArgs))return n.lastResult;var o=e.apply(this,r);return n={lastResult:o,lastArgs:r,lastThis:this},o}return r.clear=function(){n=null},r}},39289:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.computeHiddenBlocks=void 0,t.computeHiddenBlocks=function(e,t,n){let r,i=0,o={},a=[];return e.forEach(((e,s)=>{const l=t.some((e=>e>=s-n&&e<=s+n));l||null!=r?l?r=void 0:(r.endLine=s,r.lines++,o[s]=r.index):(r={index:i,startLine:s,endLine:s,lines:1},a.push(r),o[s]=r.index,i++)})),{lineBlocks:o,blocks:a}}},60487:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.computeLineInformation=t.DiffMethod=t.DiffType=void 0;const a=o(n(2199)),s=a;var l,d;!function(e){e[e.DEFAULT=0]="DEFAULT",e[e.ADDED=1]="ADDED",e[e.REMOVED=2]="REMOVED",e[e.CHANGED=3]="CHANGED"}(l||(t.DiffType=l={})),function(e){e.CHARS="diffChars",e.WORDS="diffWords",e.WORDS_WITH_SPACE="diffWordsWithSpace",e.LINES="diffLines",e.TRIMMED_LINES="diffTrimmedLines",e.SENTENCES="diffSentences",e.CSS="diffCss",e.JSON="diffJson"}(d||(t.DiffMethod=d={}));const u=e=>""===e?[]:e.replace(/\n$/,"").split("\n");t.computeLineInformation=(e,t,n=!1,r=d.CHARS,i=0,o=[])=>{let c=[];c="string"==typeof e&&"string"==typeof t?a.diffLines(e.trimRight(),t.trimRight(),{newlineIsToken:!1,ignoreWhitespace:!1,ignoreCase:!1}):a.diffJson(e,t);let f=i,h=i,p=[],g=0;const v=[],m=[],b=(e,t,i,a,p)=>u(e).map(((e,y)=>{const w={},S={};if(!(m.includes(`${t}-${y}`)||p&&0!==y)){if(i||a){let i=!0;if(a){h+=1,w.lineNumber=h,w.type=l.REMOVED,w.value=e||" ";const o=c[t+1];if(o&&o.added){const a=u(o.value)[y];if(a){const o=b(a,t,!0,!1,!0),{value:u,lineNumber:c,type:f}=o[0].right;if(m.push(`${t+1}-${y}`),S.lineNumber=c,w.value===u)i=!1,S.type=0,w.type=0,S.value=u;else if(S.type=f,n)S.value=u;else{const t=((e,t,n=d.CHARS)=>{const r=s[n](e,t),i={left:[],right:[]};return r.forEach((({added:e,removed:t,value:n})=>{const r={};return e&&(r.type=l.ADDED,r.value=n,i.right.push(r)),t&&(r.type=l.REMOVED,r.value=n,i.left.push(r)),t||e||(r.type=l.DEFAULT,r.value=n,i.right.push(r),i.left.push(r)),r})),i})(e,u,r);S.value=t.right,w.value=t.left}}}}else f+=1,S.lineNumber=f,S.type=l.ADDED,S.value=e;i&&!p&&(v.includes(g)||v.push(g))}else h+=1,f+=1,w.lineNumber=h,w.type=l.DEFAULT,w.value=e,S.lineNumber=f,S.type=l.DEFAULT,S.value=e;return((null==o?void 0:o.includes(`L-${w.lineNumber}`))||(null==o?void 0:o.includes(`R-${S.lineNumber}`))&&!v.includes(g))&&v.push(g),p||(g+=1),{right:S,left:w}}})).filter(Boolean);return c.forEach((({added:e,removed:t,value:n},r)=>{p=[...p,...b(n,r,e,t)]})),{lineInformation:p,diffLines:v}}},53668:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return i(t,e),t},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.DiffMethod=t.LineNumberPrefix=void 0;const s=n(3713),l=o(n(41594)),d=a(n(21942)),u=n(60487);Object.defineProperty(t,"DiffMethod",{enumerable:!0,get:function(){return u.DiffMethod}});const c=a(n(35392)),f=n(39289),h=n(75304),p=h.default||h;var g;!function(e){e.LEFT="L",e.RIGHT="R"}(g||(t.LineNumberPrefix=g={}));class v extends l.Component{constructor(e){super(e),this.resetCodeBlocks=()=>this.state.expandedBlocks.length>0&&(this.setState({expandedBlocks:[]}),!0),this.onBlockExpand=e=>{const t=this.state.expandedBlocks.slice();t.push(e),this.setState({expandedBlocks:t})},this.computeStyles=p(c.default),this.onLineNumberClickProxy=e=>this.props.onLineNumberClick?t=>this.props.onLineNumberClick(e,t):()=>{},this.renderWordDiff=(e,t)=>e.map(((e,n)=>(0,s.jsx)("span",{className:(0,d.default)(this.styles.wordDiff,{[this.styles.wordAdded]:e.type===u.DiffType.ADDED,[this.styles.wordRemoved]:e.type===u.DiffType.REMOVED}),children:t?t(e.value):e.value},n))),this.renderLine=(e,t,n,r,i,o)=>{const a=`${n}-${e}`,c=`${o}-${i}`,f=this.props.highlightLines.includes(a)||this.props.highlightLines.includes(c),h=t===u.DiffType.ADDED,p=t===u.DiffType.REMOVED,g=t===u.DiffType.CHANGED;let v;return v=Array.isArray(r)?this.renderWordDiff(r,this.props.renderContent):this.props.renderContent?this.props.renderContent(r):r,(0,s.jsxs)(l.Fragment,{children:[!this.props.hideLineNumbers&&(0,s.jsx)("td",{onClick:e&&this.onLineNumberClickProxy(a),className:(0,d.default)(this.styles.gutter,{[this.styles.emptyGutter]:!e,[this.styles.diffAdded]:h,[this.styles.diffRemoved]:p,[this.styles.diffChanged]:g,[this.styles.highlightedGutter]:f}),children:(0,s.jsx)("pre",{className:this.styles.lineNumber,children:e})}),!this.props.splitView&&!this.props.hideLineNumbers&&(0,s.jsx)("td",{onClick:i&&this.onLineNumberClickProxy(c),className:(0,d.default)(this.styles.gutter,{[this.styles.emptyGutter]:!i,[this.styles.diffAdded]:h,[this.styles.diffRemoved]:p,[this.styles.diffChanged]:g,[this.styles.highlightedGutter]:f}),children:(0,s.jsx)("pre",{className:this.styles.lineNumber,children:i})}),this.props.renderGutter?this.props.renderGutter({lineNumber:e,type:t,prefix:n,value:r,additionalLineNumber:i,additionalPrefix:o,styles:this.styles}):null,!this.props.hideMarkers&&(0,s.jsx)("td",{className:(0,d.default)(this.styles.marker,{[this.styles.emptyLine]:!v,[this.styles.diffAdded]:h,[this.styles.diffRemoved]:p,[this.styles.diffChanged]:g,[this.styles.highlightedLine]:f}),children:(0,s.jsxs)("pre",{children:[h&&"+",p&&"-"]})}),(0,s.jsx)("td",{className:(0,d.default)(this.styles.content,{[this.styles.emptyLine]:!v,[this.styles.diffAdded]:h,[this.styles.diffRemoved]:p,[this.styles.diffChanged]:g,[this.styles.highlightedLine]:f}),children:(0,s.jsx)("pre",{className:this.styles.contentText,children:v})})]})},this.renderSplitView=({left:e,right:t},n)=>(0,s.jsxs)("tr",{className:this.styles.line,children:[this.renderLine(e.lineNumber,e.type,g.LEFT,e.value),this.renderLine(t.lineNumber,t.type,g.RIGHT,t.value)]},n),this.renderInlineView=({left:e,right:t},n)=>{let r;return e.type===u.DiffType.REMOVED&&t.type===u.DiffType.ADDED?(0,s.jsxs)(l.Fragment,{children:[(0,s.jsx)("tr",{className:this.styles.line,children:this.renderLine(e.lineNumber,e.type,g.LEFT,e.value,null)}),(0,s.jsx)("tr",{className:this.styles.line,children:this.renderLine(null,t.type,g.RIGHT,t.value,t.lineNumber)})]},n):(e.type===u.DiffType.REMOVED&&(r=this.renderLine(e.lineNumber,e.type,g.LEFT,e.value,null)),e.type===u.DiffType.DEFAULT&&(r=this.renderLine(e.lineNumber,e.type,g.LEFT,e.value,t.lineNumber,g.RIGHT)),t.type===u.DiffType.ADDED&&(r=this.renderLine(null,t.type,g.RIGHT,t.value,t.lineNumber)),(0,s.jsx)("tr",{className:this.styles.line,children:r},n))},this.onBlockClickProxy=e=>()=>this.onBlockExpand(e),this.renderSkippedLineIndicator=(e,t,n,r)=>{const{hideLineNumbers:i,splitView:o}=this.props,a=this.props.codeFoldMessageRenderer?this.props.codeFoldMessageRenderer(e,n,r):(0,s.jsxs)("pre",{className:this.styles.codeFoldContent,children:["Expand ",e," lines ..."]}),u=(0,s.jsx)("td",{children:(0,s.jsx)("a",{onClick:this.onBlockClickProxy(t),tabIndex:0,children:a})}),c=!o&&!i;return(0,s.jsxs)("tr",{className:this.styles.codeFold,children:[!i&&(0,s.jsx)("td",{className:this.styles.codeFoldGutter}),this.props.renderGutter?(0,s.jsx)("td",{className:this.styles.codeFoldGutter}):null,(0,s.jsx)("td",{className:(0,d.default)({[this.styles.codeFoldGutter]:c})}),c?(0,s.jsxs)(l.Fragment,{children:[(0,s.jsx)("td",{}),u]}):(0,s.jsxs)(l.Fragment,{children:[u,this.props.renderGutter?(0,s.jsx)("td",{}):null,(0,s.jsx)("td",{})]}),(0,s.jsx)("td",{}),(0,s.jsx)("td",{})]},`${n}-${r}`)},this.renderDiff=()=>{const{oldValue:e,newValue:t,splitView:n,disableWordDiff:r,compareMethod:i,linesOffset:o}=this.props,{lineInformation:a,diffLines:d}=(0,u.computeLineInformation)(e,t,r,i,o,this.props.alwaysShowLines),c=this.props.extraLinesSurroundingDiff<0?0:Math.round(this.props.extraLinesSurroundingDiff),{lineBlocks:h,blocks:p}=(0,f.computeHiddenBlocks)(a,d,c);return a.map(((e,t)=>{if(this.props.showDiffOnly){const n=h[t];if(void 0!==n){const r=p[n].endLine===t;if(!this.state.expandedBlocks.includes(n)&&r)return(0,s.jsx)(l.Fragment,{children:this.renderSkippedLineIndicator(p[n].lines,n,e.left.lineNumber,e.right.lineNumber)},t);if(!this.state.expandedBlocks.includes(n))return null}}return n?this.renderSplitView(e,t):this.renderInlineView(e,t)}))},this.render=()=>{const{oldValue:e,newValue:t,useDarkTheme:n,leftTitle:r,rightTitle:i,splitView:o,hideLineNumbers:a,hideMarkers:l,nonce:c}=this.props;if(this.props.compareMethod!==u.DiffMethod.JSON&&("string"!=typeof e||"string"!=typeof t))throw Error('"oldValue" and "newValue" should be strings');this.styles=this.computeStyles(this.props.styles,n,c);const f=this.renderDiff();let h=a?2:3,p=a?2:4;l&&(h-=1,p-=1);const g=this.props.renderGutter?1:0,v=(r||i)&&(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{colSpan:(o?h:p)+g,className:this.styles.titleBlock,children:(0,s.jsx)("pre",{className:this.styles.contentText,children:r})}),o&&(0,s.jsx)("td",{colSpan:h+g,className:this.styles.titleBlock,children:(0,s.jsx)("pre",{className:this.styles.contentText,children:i})})]});return(0,s.jsx)("table",{className:(0,d.default)(this.styles.diffContainer,{[this.styles.splitView]:o}),children:(0,s.jsxs)("tbody",{children:[v,f]})})},this.state={expandedBlocks:[]}}}v.defaultProps={oldValue:"",newValue:"",splitView:!0,highlightLines:[],disableWordDiff:!1,compareMethod:u.DiffMethod.CHARS,styles:{},hideLineNumbers:!1,hideMarkers:!1,extraLinesSurroundingDiff:3,showDiffOnly:!0,useDarkTheme:!1,linesOffset:0,nonce:""},t.default=v},35392:function(e,t,n){var r=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n},i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const o=i(n(76151));t.default=(e,t=!1,n="")=>{const{variables:i={}}=e,a=r(e,["variables"]),s={light:Object.assign({diffViewerBackground:"#fff",diffViewerColor:"#212529",addedBackground:"#e6ffed",addedColor:"#24292e",removedBackground:"#ffeef0",removedColor:"#24292e",changedBackground:"#fffbdd",wordAddedBackground:"#acf2bd",wordRemovedBackground:"#fdb8c0",addedGutterBackground:"#cdffd8",removedGutterBackground:"#ffdce0",gutterBackground:"#f7f7f7",gutterBackgroundDark:"#f3f1f1",highlightBackground:"#fffbdd",highlightGutterBackground:"#fff5b1",codeFoldGutterBackground:"#dbedff",codeFoldBackground:"#f1f8ff",emptyLineBackground:"#fafbfc",gutterColor:"#212529",addedGutterColor:"#212529",removedGutterColor:"#212529",codeFoldContentColor:"#212529",diffViewerTitleBackground:"#fafbfc",diffViewerTitleColor:"#212529",diffViewerTitleBorderColor:"#eee"},i.light||{}),dark:Object.assign({diffViewerBackground:"#2e303c",diffViewerColor:"#FFF",addedBackground:"#044B53",addedColor:"white",removedBackground:"#632F34",removedColor:"white",changedBackground:"#3e302c",wordAddedBackground:"#055d67",wordRemovedBackground:"#7d383f",addedGutterBackground:"#034148",removedGutterBackground:"#632b30",gutterBackground:"#2c2f3a",gutterBackgroundDark:"#262933",highlightBackground:"#2a3967",highlightGutterBackground:"#2d4077",codeFoldGutterBackground:"#21232b",codeFoldBackground:"#262831",emptyLineBackground:"#363946",gutterColor:"#666c87",addedGutterColor:"#8c8c8c",removedGutterColor:"#8c8c8c",codeFoldContentColor:"#656a8b",diffViewerTitleBackground:"#2f323e",diffViewerTitleColor:"#555a7b",diffViewerTitleBorderColor:"#353846"},i.dark||{})},l=t?s.dark:s.light,{css:d,cx:u}=(0,o.default)({key:"react-diff",nonce:n}),c=d({width:"100%",label:"content"}),f=d({[`.${c}`]:{width:"50%"},label:"split-view"}),h=d({width:"100%",background:l.diffViewerBackground,pre:{margin:0,whiteSpace:"pre-wrap",lineHeight:"25px"},label:"diff-container",borderCollapse:"collapse"}),p=d({color:l.codeFoldContentColor,label:"code-fold-content"}),g=d({color:l.diffViewerColor,label:"content-text"}),v=d({background:l.diffViewerTitleBackground,padding:10,borderBottom:`1px solid ${l.diffViewerTitleBorderColor}`,label:"title-block",":last-child":{borderLeft:`1px solid ${l.diffViewerTitleBorderColor}`},[`.${g}`]:{color:l.diffViewerTitleColor}}),m=d({color:l.gutterColor,label:"line-number"}),b=d({background:l.removedBackground,color:l.removedColor,pre:{color:l.removedColor},[`.${m}`]:{color:l.removedGutterColor},label:"diff-removed"}),y=d({background:l.addedBackground,color:l.addedColor,pre:{color:l.addedColor},[`.${m}`]:{color:l.addedGutterColor},label:"diff-added"}),w=d({background:l.changedBackground,[`.${m}`]:{color:l.gutterColor},label:"diff-changed"}),S=d({padding:2,display:"inline-flex",borderRadius:4,wordBreak:"break-all",label:"word-diff"}),k=d({background:l.wordAddedBackground,label:"word-added"}),x=d({background:l.wordRemovedBackground,label:"word-removed"}),N=d({backgroundColor:l.codeFoldGutterBackground,label:"code-fold-gutter"}),C=d({backgroundColor:l.codeFoldBackground,height:40,fontSize:14,fontWeight:700,label:"code-fold",a:{textDecoration:"underline !important",cursor:"pointer",pre:{display:"inline"}}}),j=d({backgroundColor:l.emptyLineBackground,label:"empty-line"}),E=d({width:25,paddingLeft:10,paddingRight:10,userSelect:"none",label:"marker",[`&.${y}`]:{pre:{color:l.addedColor}},[`&.${b}`]:{pre:{color:l.removedColor}}}),A=d({background:l.highlightBackground,label:"highlighted-line",[`.${k}, .${x}`]:{backgroundColor:"initial"}}),$=d({label:"highlighted-gutter"}),O=d({userSelect:"none",minWidth:50,padding:"0 10px",whiteSpace:"nowrap",label:"gutter",textAlign:"right",background:l.gutterBackground,"&:hover":{cursor:"pointer",background:l.gutterBackgroundDark,pre:{opacity:1}},pre:{opacity:.5},[`&.${y}`]:{background:l.addedGutterBackground},[`&.${b}`]:{background:l.removedGutterBackground},[`&.${$}`]:{background:l.highlightGutterBackground,"&:hover":{background:l.highlightGutterBackground}}}),L=d({"&:hover":{background:l.gutterBackground,cursor:"initial"},label:"empty-gutter"}),M={diffContainer:h,diffRemoved:b,diffAdded:y,diffChanged:w,splitView:f,marker:E,highlightedGutter:$,highlightedLine:A,gutter:O,line:d({verticalAlign:"baseline",label:"line"}),wordDiff:S,wordAdded:k,wordRemoved:x,codeFoldGutter:N,codeFold:C,emptyGutter:L,emptyLine:j,lineNumber:m,contentText:g,content:c,codeFoldContent:p,titleBlock:v},D=Object.keys(a).reduce(((e,t)=>Object.assign(Object.assign({},e),{[t]:d(a[t])})),{});return Object.keys(M).reduce(((e,t)=>Object.assign(Object.assign({},e),{[t]:D[t]?u(M[t],D[t]):M[t]})),{})}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/9a512037e9282aebba167494c7fa44f4/55.lite.js.map
