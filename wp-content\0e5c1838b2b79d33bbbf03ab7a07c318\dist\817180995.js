"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[227],{39227:(t,n,e)=>{function r(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,"symbol"==typeof(o=function(t,n){if("object"!=typeof t||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(r.key))?o:String(o),r)}var o}function o(t,n,e){return n&&r(t.prototype,n),e&&r(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function i(){return i=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},i.apply(this,arguments)}function u(t,n){t.prototype=Object.create(n.prototype),t.prototype.constructor=t,s(t,n)}function s(t,n){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},s(t,n)}function a(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function l(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function c(t,n){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(e)return(e=e.call(t)).next.bind(e);if(Array.isArray(t)||(e=function(t,n){if(t){if("string"==typeof t)return l(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?l(t,n):void 0}}(t))||n&&t&&"number"==typeof t.length){e&&(t=e);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var f;e.d(n,{xA:()=>On}),function(t){t[t.Init=0]="Init",t[t.Loading=1]="Loading",t[t.Loaded=2]="Loaded",t[t.Rendered=3]="Rendered",t[t.Error=4]="Error"}(f||(f={}));var p,d,h,_,m,v,y,g={},b=[],w=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function k(t,n){for(var e in n)t[e]=n[e];return t}function x(t){var n=t.parentNode;n&&n.removeChild(t)}function N(t,n,e){var r,o,i,u={};for(i in n)"key"==i?r=n[i]:"ref"==i?o=n[i]:u[i]=n[i];if(arguments.length>2&&(u.children=arguments.length>3?p.call(arguments,2):e),"function"==typeof t&&null!=t.defaultProps)for(i in t.defaultProps)void 0===u[i]&&(u[i]=t.defaultProps[i]);return P(t,u,r,o,null)}function P(t,n,e,r,o){var i={type:t,props:n,key:e,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++h:o};return null==o&&null!=d.vnode&&d.vnode(i),i}function S(t){return t.children}function C(t,n){this.props=t,this.context=n}function E(t,n){if(null==n)return t.__?E(t.__,t.__.__k.indexOf(t)+1):null;for(var e;n<t.__k.length;n++)if(null!=(e=t.__k[n])&&null!=e.__e)return e.__e;return"function"==typeof t.type?E(t):null}function I(t){var n,e;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,n=0;n<t.__k.length;n++)if(null!=(e=t.__k[n])&&null!=e.__e){t.__e=t.__c.base=e.__e;break}return I(t)}}function T(t){(!t.__d&&(t.__d=!0)&&m.push(t)&&!L.__r++||v!==d.debounceRendering)&&((v=d.debounceRendering)||setTimeout)(L)}function L(){for(var t;L.__r=m.length;)t=m.sort((function(t,n){return t.__v.__b-n.__v.__b})),m=[],t.some((function(t){var n,e,r,o,i,u;t.__d&&(i=(o=(n=t).__v).__e,(u=n.__P)&&(e=[],(r=k({},o)).__v=o.__v+1,R(u,o,r,n.__n,void 0!==u.ownerSVGElement,null!=o.__h?[i]:null,e,null==i?E(o):i,o.__h),U(e,o),o.__e!=i&&I(o)))}))}function A(t,n,e,r,o,i,u,s,a,l){var c,f,p,d,h,_,m,v=r&&r.__k||b,y=v.length;for(e.__k=[],c=0;c<n.length;c++)if(null!=(d=e.__k[c]=null==(d=n[c])||"boolean"==typeof d?null:"string"==typeof d||"number"==typeof d||"bigint"==typeof d?P(null,d,null,null,d):Array.isArray(d)?P(S,{children:d},null,null,null):d.__b>0?P(d.type,d.props,d.key,d.ref?d.ref:null,d.__v):d)){if(d.__=e,d.__b=e.__b+1,null===(p=v[c])||p&&d.key==p.key&&d.type===p.type)v[c]=void 0;else for(f=0;f<y;f++){if((p=v[f])&&d.key==p.key&&d.type===p.type){v[f]=void 0;break}p=null}R(t,d,p=p||g,o,i,u,s,a,l),h=d.__e,(f=d.ref)&&p.ref!=f&&(m||(m=[]),p.ref&&m.push(p.ref,null,d),m.push(f,d.__c||h,d)),null!=h?(null==_&&(_=h),"function"==typeof d.type&&d.__k===p.__k?d.__d=a=O(d,a,t):a=H(t,d,p,v,h,a),"function"==typeof e.type&&(e.__d=a)):a&&p.__e==a&&a.parentNode!=t&&(a=E(p))}for(e.__e=_,c=y;c--;)null!=v[c]&&q(v[c],v[c]);if(m)for(c=0;c<m.length;c++)W(m[c],m[++c],m[++c])}function O(t,n,e){for(var r,o=t.__k,i=0;o&&i<o.length;i++)(r=o[i])&&(r.__=t,n="function"==typeof r.type?O(r,n,e):H(e,r,r,o,r.__e,n));return n}function H(t,n,e,r,o,i){var u,s,a;if(void 0!==n.__d)u=n.__d,n.__d=void 0;else if(null==e||o!=i||null==o.parentNode)t:if(null==i||i.parentNode!==t)t.appendChild(o),u=null;else{for(s=i,a=0;(s=s.nextSibling)&&a<r.length;a+=1)if(s==o)break t;t.insertBefore(o,i),u=i}return void 0!==u?u:o.nextSibling}function j(t,n,e){"-"===n[0]?t.setProperty(n,e):t[n]=null==e?"":"number"!=typeof e||w.test(n)?e:e+"px"}function D(t,n,e,r,o){var i;t:if("style"===n)if("string"==typeof e)t.style.cssText=e;else{if("string"==typeof r&&(t.style.cssText=r=""),r)for(n in r)e&&n in e||j(t.style,n,"");if(e)for(n in e)r&&e[n]===r[n]||j(t.style,n,e[n])}else if("o"===n[0]&&"n"===n[1])i=n!==(n=n.replace(/Capture$/,"")),n=n.toLowerCase()in t?n.toLowerCase().slice(2):n.slice(2),t.l||(t.l={}),t.l[n+i]=e,e?r||t.addEventListener(n,i?F:M,i):t.removeEventListener(n,i?F:M,i);else if("dangerouslySetInnerHTML"!==n){if(o)n=n.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("href"!==n&&"list"!==n&&"form"!==n&&"tabIndex"!==n&&"download"!==n&&n in t)try{t[n]=null==e?"":e;break t}catch(t){}"function"==typeof e||(null==e||!1===e&&-1==n.indexOf("-")?t.removeAttribute(n):t.setAttribute(n,e))}}function M(t){this.l[t.type+!1](d.event?d.event(t):t)}function F(t){this.l[t.type+!0](d.event?d.event(t):t)}function R(t,n,e,r,o,i,u,s,a){var l,c,f,p,h,_,m,v,y,g,b,w,x,N,P,E=n.type;if(void 0!==n.constructor)return null;null!=e.__h&&(a=e.__h,s=n.__e=e.__e,n.__h=null,i=[s]),(l=d.__b)&&l(n);try{t:if("function"==typeof E){if(v=n.props,y=(l=E.contextType)&&r[l.__c],g=l?y?y.props.value:l.__:r,e.__c?m=(c=n.__c=e.__c).__=c.__E:("prototype"in E&&E.prototype.render?n.__c=c=new E(v,g):(n.__c=c=new C(v,g),c.constructor=E,c.render=z),y&&y.sub(c),c.props=v,c.state||(c.state={}),c.context=g,c.__n=r,f=c.__d=!0,c.__h=[],c._sb=[]),null==c.__s&&(c.__s=c.state),null!=E.getDerivedStateFromProps&&(c.__s==c.state&&(c.__s=k({},c.__s)),k(c.__s,E.getDerivedStateFromProps(v,c.__s))),p=c.props,h=c.state,f)null==E.getDerivedStateFromProps&&null!=c.componentWillMount&&c.componentWillMount(),null!=c.componentDidMount&&c.__h.push(c.componentDidMount);else{if(null==E.getDerivedStateFromProps&&v!==p&&null!=c.componentWillReceiveProps&&c.componentWillReceiveProps(v,g),!c.__e&&null!=c.shouldComponentUpdate&&!1===c.shouldComponentUpdate(v,c.__s,g)||n.__v===e.__v){for(c.props=v,c.state=c.__s,n.__v!==e.__v&&(c.__d=!1),c.__v=n,n.__e=e.__e,n.__k=e.__k,n.__k.forEach((function(t){t&&(t.__=n)})),b=0;b<c._sb.length;b++)c.__h.push(c._sb[b]);c._sb=[],c.__h.length&&u.push(c);break t}null!=c.componentWillUpdate&&c.componentWillUpdate(v,c.__s,g),null!=c.componentDidUpdate&&c.__h.push((function(){c.componentDidUpdate(p,h,_)}))}if(c.context=g,c.props=v,c.__v=n,c.__P=t,w=d.__r,x=0,"prototype"in E&&E.prototype.render){for(c.state=c.__s,c.__d=!1,w&&w(n),l=c.render(c.props,c.state,c.context),N=0;N<c._sb.length;N++)c.__h.push(c._sb[N]);c._sb=[]}else do{c.__d=!1,w&&w(n),l=c.render(c.props,c.state,c.context),c.state=c.__s}while(c.__d&&++x<25);c.state=c.__s,null!=c.getChildContext&&(r=k(k({},r),c.getChildContext())),f||null==c.getSnapshotBeforeUpdate||(_=c.getSnapshotBeforeUpdate(p,h)),P=null!=l&&l.type===S&&null==l.key?l.props.children:l,A(t,Array.isArray(P)?P:[P],n,e,r,o,i,u,s,a),c.base=n.__e,n.__h=null,c.__h.length&&u.push(c),m&&(c.__E=c.__=null),c.__e=!1}else null==i&&n.__v===e.__v?(n.__k=e.__k,n.__e=e.__e):n.__e=B(e.__e,n,e,r,o,i,u,a);(l=d.diffed)&&l(n)}catch(t){n.__v=null,(a||null!=i)&&(n.__e=s,n.__h=!!a,i[i.indexOf(s)]=null),d.__e(t,n,e)}}function U(t,n){d.__c&&d.__c(n,t),t.some((function(n){try{t=n.__h,n.__h=[],t.some((function(t){t.call(n)}))}catch(t){d.__e(t,n.__v)}}))}function B(t,n,e,r,o,i,u,s){var a,l,c,f=e.props,d=n.props,h=n.type,_=0;if("svg"===h&&(o=!0),null!=i)for(;_<i.length;_++)if((a=i[_])&&"setAttribute"in a==!!h&&(h?a.localName===h:3===a.nodeType)){t=a,i[_]=null;break}if(null==t){if(null===h)return document.createTextNode(d);t=o?document.createElementNS("http://www.w3.org/2000/svg",h):document.createElement(h,d.is&&d),i=null,s=!1}if(null===h)f===d||s&&t.data===d||(t.data=d);else{if(i=i&&p.call(t.childNodes),l=(f=e.props||g).dangerouslySetInnerHTML,c=d.dangerouslySetInnerHTML,!s){if(null!=i)for(f={},_=0;_<t.attributes.length;_++)f[t.attributes[_].name]=t.attributes[_].value;(c||l)&&(c&&(l&&c.__html==l.__html||c.__html===t.innerHTML)||(t.innerHTML=c&&c.__html||""))}if(function(t,n,e,r,o){var i;for(i in e)"children"===i||"key"===i||i in n||D(t,i,null,e[i],r);for(i in n)o&&"function"!=typeof n[i]||"children"===i||"key"===i||"value"===i||"checked"===i||e[i]===n[i]||D(t,i,n[i],e[i],r)}(t,d,f,o,s),c)n.__k=[];else if(_=n.props.children,A(t,Array.isArray(_)?_:[_],n,e,r,o&&"foreignObject"!==h,i,u,i?i[0]:e.__k&&E(e,0),s),null!=i)for(_=i.length;_--;)null!=i[_]&&x(i[_]);s||("value"in d&&void 0!==(_=d.value)&&(_!==t.value||"progress"===h&&!_||"option"===h&&_!==f.value)&&D(t,"value",_,f.value,!1),"checked"in d&&void 0!==(_=d.checked)&&_!==t.checked&&D(t,"checked",_,f.checked,!1))}return t}function W(t,n,e){try{"function"==typeof t?t(n):t.current=n}catch(t){d.__e(t,e)}}function q(t,n,e){var r,o;if(d.unmount&&d.unmount(t),(r=t.ref)&&(r.current&&r.current!==t.__e||W(r,null,n)),null!=(r=t.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(t){d.__e(t,n)}r.base=r.__P=null,t.__c=void 0}if(r=t.__k)for(o=0;o<r.length;o++)r[o]&&q(r[o],n,e||"function"!=typeof t.type);e||null==t.__e||x(t.__e),t.__=t.__e=t.__d=void 0}function z(t,n,e){return this.constructor(t,e)}function V(t,n,e){var r,o,i;d.__&&d.__(t,n),o=(r="function"==typeof e)?null:e&&e.__k||n.__k,i=[],R(n,t=(!r&&e||n).__k=N(S,null,[t]),o||g,g,void 0!==n.ownerSVGElement,!r&&e?[e]:o?null:n.firstChild?p.call(n.childNodes):null,i,!r&&e?e:o?o.__e:n.firstChild,r),U(i,t)}function $(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var n=16*Math.random()|0;return("x"==t?n:3&n|8).toString(16)}))}p=b.slice,d={__e:function(t,n,e,r){for(var o,i,u;n=n.__;)if((o=n.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(t)),u=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(t,r||{}),u=o.__d),u)return o.__E=o}catch(n){t=n}throw t}},h=0,_=function(t){return null!=t&&void 0===t.constructor},C.prototype.setState=function(t,n){var e;e=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=k({},this.state),"function"==typeof t&&(t=t(k({},e),this.props)),t&&k(e,t),null!=t&&this.__v&&(n&&this._sb.push(n),T(this))},C.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),T(this))},C.prototype.render=S,m=[],L.__r=0,y=0;var G=function(){function t(t){this._id=void 0,this._id=t||$()}return o(t,[{key:"id",get:function(){return this._id}}]),t}();function K(t){return N(t.parentElement||"span",{dangerouslySetInnerHTML:{__html:t.content}})}function X(t,n){return N(K,{content:t,parentElement:n})}var Z,J=function(t){function n(n){var e;return(e=t.call(this)||this).data=void 0,e.update(n),e}u(n,t);var e=n.prototype;return e.cast=function(t){return t instanceof HTMLElement?X(t.outerHTML):t},e.update=function(t){return this.data=this.cast(t),this},n}(G),Q=function(t){function n(n){var e;return(e=t.call(this)||this)._cells=void 0,e.cells=n||[],e}u(n,t);var e=n.prototype;return e.cell=function(t){return this._cells[t]},e.toArray=function(){return this.cells.map((function(t){return t.data}))},n.fromCells=function(t){return new n(t.map((function(t){return new J(t.data)})))},o(n,[{key:"cells",get:function(){return this._cells},set:function(t){this._cells=t}},{key:"length",get:function(){return this.cells.length}}]),n}(G),Y=function(t){function n(n){var e;return(e=t.call(this)||this)._rows=void 0,e._length=void 0,e.rows=n instanceof Array?n:n instanceof Q?[n]:[],e}return u(n,t),n.prototype.toArray=function(){return this.rows.map((function(t){return t.toArray()}))},n.fromRows=function(t){return new n(t.map((function(t){return Q.fromCells(t.cells)})))},n.fromArray=function(t){return new n((t=function(t){return!t[0]||t[0]instanceof Array?t:[t]}(t)).map((function(t){return new Q(t.map((function(t){return new J(t)})))})))},o(n,[{key:"rows",get:function(){return this._rows},set:function(t){this._rows=t}},{key:"length",get:function(){return this._length||this.rows.length},set:function(t){this._length=t}}]),n}(G),tt=function(){function t(){this.callbacks=void 0}var n=t.prototype;return n.init=function(t){this.callbacks||(this.callbacks={}),t&&!this.callbacks[t]&&(this.callbacks[t]=[])},n.listeners=function(){return this.callbacks},n.on=function(t,n){return this.init(t),this.callbacks[t].push(n),this},n.off=function(t,n){var e=t;return this.init(),this.callbacks[e]&&0!==this.callbacks[e].length?(this.callbacks[e]=this.callbacks[e].filter((function(t){return t!=n})),this):this},n.emit=function(t){var n=arguments,e=t;return this.init(e),this.callbacks[e].length>0&&(this.callbacks[e].forEach((function(t){return t.apply(void 0,[].slice.call(n,1))})),!0)},t}();function nt(t,n){if(typeof t!=typeof n)return!1;if(null===t&&null===n)return!0;if("object"!=typeof t)return t===n;if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return!1;for(var e=0;e<t.length;e++)if(!nt(t[e],n[e]))return!1;return!0}if(t.hasOwnProperty("constructor")&&n.hasOwnProperty("constructor")&&t.hasOwnProperty("props")&&n.hasOwnProperty("props")&&t.hasOwnProperty("key")&&n.hasOwnProperty("key")&&t.hasOwnProperty("ref")&&n.hasOwnProperty("ref")&&t.hasOwnProperty("type")&&n.hasOwnProperty("type"))return nt(t.props,n.props);var r=Object.keys(t),o=Object.keys(n);if(r.length!==o.length)return!1;for(var i=0,u=r;i<u.length;i++){var s=u[i];if(!n.hasOwnProperty(s)||!nt(t[s],n[s]))return!1}return!0}!function(t){t[t.Initiator=0]="Initiator",t[t.ServerFilter=1]="ServerFilter",t[t.ServerSort=2]="ServerSort",t[t.ServerLimit=3]="ServerLimit",t[t.Extractor=4]="Extractor",t[t.Transformer=5]="Transformer",t[t.Filter=6]="Filter",t[t.Sort=7]="Sort",t[t.Limit=8]="Limit"}(Z||(Z={}));var et=function(t){function n(n){var e;return(e=t.call(this)||this).id=void 0,e._props=void 0,e._props={},e.id=$(),n&&e.setProps(n),e}u(n,t);var e=n.prototype;return e.process=function(){var t=[].slice.call(arguments);this.validateProps instanceof Function&&this.validateProps.apply(this,t),this.emit.apply(this,["beforeProcess"].concat(t));var n=this._process.apply(this,t);return this.emit.apply(this,["afterProcess"].concat(t)),n},e.setProps=function(t){var n=i({},this._props,t);return nt(n,this._props)||(this._props=n,this.emit("propsUpdated",this)),this},o(n,[{key:"props",get:function(){return this._props}}]),n}(tt),rt=function(t){function n(){return t.apply(this,arguments)||this}return u(n,t),n.prototype._process=function(t){return this.props.keyword?(n=String(this.props.keyword).trim(),e=this.props.columns,r=this.props.ignoreHiddenColumns,o=t,i=this.props.selector,n=n.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),new Y(o.rows.filter((function(t,o){return t.cells.some((function(t,u){if(!t)return!1;if(r&&e&&e[u]&&"object"==typeof e[u]&&e[u].hidden)return!1;var s="";if("function"==typeof i)s=i(t.data,o,u);else if("object"==typeof t.data){var a=t.data;a&&a.props&&a.props.content&&(s=a.props.content)}else s=String(t.data);return new RegExp(n,"gi").test(s)}))})))):t;var n,e,r,o,i},o(n,[{key:"type",get:function(){return Z.Filter}}]),n}(et);function ot(){return"gridjs"+[].slice.call(arguments).reduce((function(t,n){return t+"-"+n}),"")}function it(){return[].slice.call(arguments).map((function(t){return t?t.toString():""})).filter((function(t){return t})).reduce((function(t,n){return(t||"")+" "+n}),"").trim()}var ut,st,at,lt,ct=function(t){function n(){return t.apply(this,arguments)||this}return u(n,t),n.prototype._process=function(t){if(!this.props.keyword)return t;var n={};return this.props.url&&(n.url=this.props.url(t.url,this.props.keyword)),this.props.body&&(n.body=this.props.body(t.body,this.props.keyword)),i({},t,n)},o(n,[{key:"type",get:function(){return Z.ServerFilter}}]),n}(et),ft=0,pt=[],dt=[],ht=d.__b,_t=d.__r,mt=d.diffed,vt=d.__c,yt=d.unmount;function gt(t,n){d.__h&&d.__h(st,t,ft||n),ft=0;var e=st.__H||(st.__H={__:[],__h:[]});return t>=e.__.length&&e.__.push({__V:dt}),e.__[t]}function bt(t){return ft=1,function(t,n,e){var r=gt(ut++,2);if(r.t=t,!r.__c&&(r.__=[Tt(void 0,n),function(t){var n=r.__N?r.__N[0]:r.__[0],e=r.t(n,t);n!==e&&(r.__N=[e,r.__[1]],r.__c.setState({}))}],r.__c=st,!st.u)){st.u=!0;var o=st.shouldComponentUpdate;st.shouldComponentUpdate=function(t,n,e){if(!r.__c.__H)return!0;var i=r.__c.__H.__.filter((function(t){return t.__c}));if(i.every((function(t){return!t.__N})))return!o||o.call(this,t,n,e);var u=!1;return i.forEach((function(t){if(t.__N){var n=t.__[0];t.__=t.__N,t.__N=void 0,n!==t.__[0]&&(u=!0)}})),!(!u&&r.__c.props===t)&&(!o||o.call(this,t,n,e))}}return r.__N||r.__}(Tt,t)}function wt(t,n){var e=gt(ut++,3);!d.__s&&It(e.__H,n)&&(e.__=t,e.i=n,st.__H.__h.push(e))}function kt(t){return ft=5,xt((function(){return{current:t}}),[])}function xt(t,n){var e=gt(ut++,7);return It(e.__H,n)?(e.__V=t(),e.i=n,e.__h=t,e.__V):e.__}function Nt(){for(var t;t=pt.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(Ct),t.__H.__h.forEach(Et),t.__H.__h=[]}catch(n){t.__H.__h=[],d.__e(n,t.__v)}}d.__b=function(t){st=null,ht&&ht(t)},d.__r=function(t){_t&&_t(t),ut=0;var n=(st=t.__c).__H;n&&(at===st?(n.__h=[],st.__h=[],n.__.forEach((function(t){t.__N&&(t.__=t.__N),t.__V=dt,t.__N=t.i=void 0}))):(n.__h.forEach(Ct),n.__h.forEach(Et),n.__h=[])),at=st},d.diffed=function(t){mt&&mt(t);var n=t.__c;n&&n.__H&&(n.__H.__h.length&&(1!==pt.push(n)&&lt===d.requestAnimationFrame||((lt=d.requestAnimationFrame)||St)(Nt)),n.__H.__.forEach((function(t){t.i&&(t.__H=t.i),t.__V!==dt&&(t.__=t.__V),t.i=void 0,t.__V=dt}))),at=st=null},d.__c=function(t,n){n.some((function(t){try{t.__h.forEach(Ct),t.__h=t.__h.filter((function(t){return!t.__||Et(t)}))}catch(e){n.some((function(t){t.__h&&(t.__h=[])})),n=[],d.__e(e,t.__v)}})),vt&&vt(t,n)},d.unmount=function(t){yt&&yt(t);var n,e=t.__c;e&&e.__H&&(e.__H.__.forEach((function(t){try{Ct(t)}catch(t){n=t}})),e.__H=void 0,n&&d.__e(n,e.__v))};var Pt="function"==typeof requestAnimationFrame;function St(t){var n,e=function(){clearTimeout(r),Pt&&cancelAnimationFrame(n),setTimeout(t)},r=setTimeout(e,100);Pt&&(n=requestAnimationFrame(e))}function Ct(t){var n=st,e=t.__c;"function"==typeof e&&(t.__c=void 0,e()),st=n}function Et(t){var n=st;t.__c=t.__(),st=n}function It(t,n){return!t||t.length!==n.length||n.some((function(n,e){return n!==t[e]}))}function Tt(t,n){return"function"==typeof n?n(t):n}function Lt(){return function(t){var n=st.context[t.__c],e=gt(ut++,9);return e.c=t,n?(null==e.__&&(e.__=!0,n.sub(st)),n.props.value):t.__}(dn)}var At={search:{placeholder:"Type a keyword..."},sort:{sortAsc:"Sort column ascending",sortDesc:"Sort column descending"},pagination:{previous:"Previous",next:"Next",navigate:function(t,n){return"Page "+t+" of "+n},page:function(t){return"Page "+t},showing:"Showing",of:"of",to:"to",results:"results"},loading:"Loading...",noRecordsFound:"No matching records found",error:"An error happened while fetching the data"},Ot=function(){function t(t){this._language=void 0,this._defaultLanguage=void 0,this._language=t,this._defaultLanguage=At}var n=t.prototype;return n.getString=function(t,n){if(!n||!t)return null;var e=t.split("."),r=e[0];if(n[r]){var o=n[r];return"string"==typeof o?function(){return o}:"function"==typeof o?o:this.getString(e.slice(1).join("."),o)}return null},n.translate=function(t){var n;return(n=this.getString(t,this._language)||this.getString(t,this._defaultLanguage))?n.apply(void 0,[].slice.call(arguments,1)):t},t}();function Ht(){var t=Lt();return function(n){var e;return(e=t.translator).translate.apply(e,[n].concat([].slice.call(arguments,1)))}}var jt=function(t){return function(n){return i({},n,{search:{keyword:t}})}};function Dt(){return Lt().store}function Mt(t){var n=Dt(),e=bt(t(n.getState())),r=e[0],o=e[1];return wt((function(){return n.subscribe((function(){var e=t(n.getState());r!==e&&o(e)}))}),[]),r}function Ft(){var t,n=bt(void 0),e=n[0],r=n[1],o=Lt(),i=o.search,u=Ht(),s=Dt().dispatch,a=Mt((function(t){return t.search}));wt((function(){e&&e.setProps({keyword:null==a?void 0:a.keyword})}),[a,e]),wt((function(){r(i.server?new ct({keyword:i.keyword,url:i.server.url,body:i.server.body}):new rt({keyword:i.keyword,columns:o.header&&o.header.columns,ignoreHiddenColumns:i.ignoreHiddenColumns||void 0===i.ignoreHiddenColumns,selector:i.selector})),i.keyword&&s(jt(i.keyword))}),[i]),wt((function(){if(e)return o.pipeline.register(e),function(){return o.pipeline.unregister(e)}}),[o,e]);var l,c,f,p=function(t,n){return ft=8,xt((function(){return t}),n)}((l=function(t){t.target instanceof HTMLInputElement&&s(jt(t.target.value))},c=e instanceof ct?i.debounceTimeout||250:0,function(){var t=arguments;return new Promise((function(n){f&&clearTimeout(f),f=setTimeout((function(){return n(l.apply(void 0,[].slice.call(t)))}),c)}))}),[i,e]);return N("div",{className:ot(it("search",null==(t=o.className)?void 0:t.search))},N("input",{type:"search",placeholder:u("search.placeholder"),"aria-label":u("search.placeholder"),onInput:p,className:it(ot("input"),ot("search","input")),defaultValue:(null==a?void 0:a.keyword)||""}))}var Rt=function(t){function n(){return t.apply(this,arguments)||this}u(n,t);var e=n.prototype;return e.validateProps=function(){if(isNaN(Number(this.props.limit))||isNaN(Number(this.props.page)))throw Error("Invalid parameters passed")},e._process=function(t){var n=this.props.page;return new Y(t.rows.slice(n*this.props.limit,(n+1)*this.props.limit))},o(n,[{key:"type",get:function(){return Z.Limit}}]),n}(et),Ut=function(t){function n(){return t.apply(this,arguments)||this}return u(n,t),n.prototype._process=function(t){var n={};return this.props.url&&(n.url=this.props.url(t.url,this.props.page,this.props.limit)),this.props.body&&(n.body=this.props.body(t.body,this.props.page,this.props.limit)),i({},t,n)},o(n,[{key:"type",get:function(){return Z.ServerLimit}}]),n}(et);function Bt(){var t=Lt(),n=t.pagination,e=n.server,r=n.summary,o=void 0===r||r,i=n.nextButton,u=void 0===i||i,s=n.prevButton,a=void 0===s||s,l=n.buttonsCount,c=void 0===l?3:l,f=n.limit,p=void 0===f?10:f,d=n.page,h=void 0===d?0:d,_=n.resetPageOnUpdate,m=void 0===_||_,v=kt(null),y=bt(h),g=y[0],b=y[1],w=bt(0),k=w[0],x=w[1],P=Ht();wt((function(){return e?(v.current=new Ut({limit:p,page:g,url:e.url,body:e.body}),t.pipeline.register(v.current)):(v.current=new Rt({limit:p,page:g}),t.pipeline.register(v.current)),v.current instanceof Ut?t.pipeline.on("afterProcess",(function(t){return x(t.length)})):v.current instanceof Rt&&v.current.on("beforeProcess",(function(t){return x(t.length)})),t.pipeline.on("updated",C),t.pipeline.on("error",(function(){x(0),b(0)})),function(){t.pipeline.unregister(v.current),t.pipeline.off("updated",C)}}),[]);var C=function(t){m&&t!==v.current&&(b(0),0!==v.current.props.page&&v.current.setProps({page:0}))},E=function(){return Math.ceil(k/p)},I=function(t){if(t>=E()||t<0||t===g)return null;b(t),v.current.setProps({page:t})};return N("div",{className:it(ot("pagination"),t.className.pagination)},N(S,null,o&&k>0&&N("div",{role:"status","aria-live":"polite",className:it(ot("summary"),t.className.paginationSummary),title:P("pagination.navigate",g+1,E())},P("pagination.showing")," ",N("b",null,P(""+(g*p+1)))," ",P("pagination.to")," ",N("b",null,P(""+Math.min((g+1)*p,k)))," ",P("pagination.of")," ",N("b",null,P(""+k))," ",P("pagination.results"))),N("div",{className:ot("pages")},a&&N("button",{tabIndex:0,role:"button",disabled:0===g,onClick:function(){return I(g-1)},title:P("pagination.previous"),"aria-label":P("pagination.previous"),className:it(t.className.paginationButton,t.className.paginationButtonPrev)},P("pagination.previous")),function(){if(c<=0)return null;var n=Math.min(E(),c),e=Math.min(g,Math.floor(n/2));return g+Math.floor(n/2)>=E()&&(e=n-(E()-g)),N(S,null,E()>n&&g-e>0&&N(S,null,N("button",{tabIndex:0,role:"button",onClick:function(){return I(0)},title:P("pagination.firstPage"),"aria-label":P("pagination.firstPage"),className:t.className.paginationButton},P("1")),N("button",{tabIndex:-1,className:it(ot("spread"),t.className.paginationButton)},"...")),Array.from(Array(n).keys()).map((function(t){return g+(t-e)})).map((function(n){return N("button",{tabIndex:0,role:"button",onClick:function(){return I(n)},className:it(g===n?it(ot("currentPage"),t.className.paginationButtonCurrent):null,t.className.paginationButton),title:P("pagination.page",n+1),"aria-label":P("pagination.page",n+1)},P(""+(n+1)))})),E()>n&&E()>g+e+1&&N(S,null,N("button",{tabIndex:-1,className:it(ot("spread"),t.className.paginationButton)},"..."),N("button",{tabIndex:0,role:"button",onClick:function(){return I(E()-1)},title:P("pagination.page",E()),"aria-label":P("pagination.page",E()),className:t.className.paginationButton},P(""+E()))))}(),u&&N("button",{tabIndex:0,role:"button",disabled:E()===g+1||0===E(),onClick:function(){return I(g+1)},title:P("pagination.next"),"aria-label":P("pagination.next"),className:it(t.className.paginationButton,t.className.paginationButtonNext)},P("pagination.next"))))}function Wt(t,n){return"string"==typeof t?t.indexOf("%")>-1?n/100*parseInt(t,10):parseInt(t,10):t}function qt(t){return t?Math.floor(t)+"px":""}function zt(t){var n=t.tableRef.cloneNode(!0);return n.style.position="absolute",n.style.width="100%",n.style.zIndex="-2147483640",n.style.visibility="hidden",N("div",{ref:function(t){t&&t.appendChild(n)}})}function Vt(t){if(!t)return"";var n=t.split(" ");return 1===n.length&&/([a-z][A-Z])+/g.test(t)?t:n.map((function(t,n){return 0==n?t.toLowerCase():t.charAt(0).toUpperCase()+t.slice(1).toLowerCase()})).join("")}var $t,Gt=new(function(){function t(){}var n=t.prototype;return n.format=function(t,n){return"[Grid.js] ["+n.toUpperCase()+"]: "+t},n.error=function(t,n){void 0===n&&(n=!1);var e=this.format(t,"error");if(n)throw Error(e);console.error(e)},n.warn=function(t){console.warn(this.format(t,"warn"))},n.info=function(t){console.info(this.format(t,"info"))},t}());!function(t){t[t.Header=0]="Header",t[t.Footer=1]="Footer",t[t.Cell=2]="Cell"}($t||($t={}));var Kt=function(){function t(){this.plugins=void 0,this.plugins=[]}var n=t.prototype;return n.get=function(t){return this.plugins.find((function(n){return n.id===t}))},n.add=function(t){return t.id?this.get(t.id)?(Gt.error("Duplicate plugin ID: "+t.id),this):(this.plugins.push(t),this):(Gt.error("Plugin ID cannot be empty"),this)},n.remove=function(t){var n=this.get(t);return n&&this.plugins.splice(this.plugins.indexOf(n),1),this},n.list=function(t){return(null!=t||null!=t?this.plugins.filter((function(n){return n.position===t})):this.plugins).sort((function(t,n){return t.order&&n.order?t.order-n.order:1}))},t}();function Xt(t){var n=this,e=Lt();if(t.pluginId){var r=e.plugin.get(t.pluginId);return r?N(S,{},N(r.component,i({plugin:r},t.props))):null}return void 0!==t.position?N(S,{},e.plugin.list(t.position).map((function(t){return N(t.component,i({plugin:t},n.props.props))}))):null}var Zt=function(t){function n(){var n;return(n=t.call(this)||this)._columns=void 0,n._columns=[],n}u(n,t);var e=n.prototype;return e.adjustWidth=function(t,e,r){var o=t.container,u=t.autoWidth;if(!o)return this;var s=o.clientWidth,a={};e.current&&u&&(V(N(zt,{tableRef:e.current}),r.current),a=function(t){var n=t.querySelector("table");if(!n)return{};var e=n.className,r=n.style.cssText;n.className=e+" "+ot("shadowTable"),n.style.tableLayout="auto",n.style.width="auto",n.style.padding="0",n.style.margin="0",n.style.border="none",n.style.outline="none";var o=Array.from(n.parentNode.querySelectorAll("thead th")).reduce((function(t,n){var e;return n.style.width=n.clientWidth+"px",i(((e={})[n.getAttribute("data-column-id")]={minWidth:n.clientWidth},e),t)}),{});return n.className=e,n.style.cssText=r,n.style.tableLayout="auto",Array.from(n.parentNode.querySelectorAll("thead th")).reduce((function(t,n){return t[n.getAttribute("data-column-id")].width=n.clientWidth,t}),o)}(r.current));for(var l,f=c(n.tabularFormat(this.columns).reduce((function(t,n){return t.concat(n)}),[]));!(l=f()).done;){var p=l.value;p.columns&&p.columns.length>0||(!p.width&&u?p.id in a&&(p.width=qt(a[p.id].width),p.minWidth=qt(a[p.id].minWidth)):p.width=qt(Wt(p.width,s)))}return e.current&&u&&V(null,r.current),this},e.setSort=function(t,n){for(var e,r=c(n||this.columns||[]);!(e=r()).done;){var o=e.value;o.columns&&o.columns.length>0?o.sort=void 0:void 0===o.sort&&t?o.sort={}:o.sort?"object"==typeof o.sort&&(o.sort=i({},o.sort)):o.sort=void 0,o.columns&&this.setSort(t,o.columns)}},e.setResizable=function(t,n){for(var e,r=c(n||this.columns||[]);!(e=r()).done;){var o=e.value;void 0===o.resizable&&(o.resizable=t),o.columns&&this.setResizable(t,o.columns)}},e.setID=function(t){for(var n,e=c(t||this.columns||[]);!(n=e()).done;){var r=n.value;r.id||"string"!=typeof r.name||(r.id=Vt(r.name)),r.id||Gt.error('Could not find a valid ID for one of the columns. Make sure a valid "id" is set for all columns.'),r.columns&&this.setID(r.columns)}},e.populatePlugins=function(t,n){for(var e,r=c(n);!(e=r()).done;){var o=e.value;void 0!==o.plugin&&t.add(i({id:o.id},o.plugin,{position:$t.Cell}))}},n.fromColumns=function(t){for(var e,r=new n,o=c(t);!(e=o()).done;){var i=e.value;if("string"==typeof i||_(i))r.columns.push({name:i});else if("object"==typeof i){var u=i;u.columns&&(u.columns=n.fromColumns(u.columns).columns),"object"==typeof u.plugin&&void 0===u.data&&(u.data=null),r.columns.push(i)}}return r},n.createFromConfig=function(t){var e=new n;return t.from?e.columns=n.fromHTMLTable(t.from).columns:t.columns?e.columns=n.fromColumns(t.columns).columns:!t.data||"object"!=typeof t.data[0]||t.data[0]instanceof Array||(e.columns=Object.keys(t.data[0]).map((function(t){return{name:t}}))),e.columns.length?(e.setID(),e.setSort(t.sort),e.setResizable(t.resizable),e.populatePlugins(t.plugin,e.columns),e):null},n.fromHTMLTable=function(t){for(var e,r=new n,o=c(t.querySelector("thead").querySelectorAll("th"));!(e=o()).done;){var i=e.value;r.columns.push({name:i.innerHTML,width:i.width})}return r},n.tabularFormat=function(t){var n=[],e=t||[],r=[];if(e&&e.length){n.push(e);for(var o,i=c(e);!(o=i()).done;){var u=o.value;u.columns&&u.columns.length&&(r=r.concat(u.columns))}r.length&&(n=n.concat(this.tabularFormat(r)))}return n},n.leafColumns=function(t){var n=[],e=t||[];if(e&&e.length)for(var r,o=c(e);!(r=o()).done;){var i=r.value;i.columns&&0!==i.columns.length||n.push(i),i.columns&&(n=n.concat(this.leafColumns(i.columns)))}return n},n.maximumDepth=function(t){return this.tabularFormat([t]).length-1},o(n,[{key:"columns",get:function(){return this._columns},set:function(t){this._columns=t}},{key:"visibleColumns",get:function(){return this._columns.filter((function(t){return!t.hidden}))}}]),n}(G),Jt=function(){},Qt=function(t){function n(n){var e;return(e=t.call(this)||this).data=void 0,e.set(n),e}u(n,t);var e=n.prototype;return e.get=function(){try{return Promise.resolve(this.data()).then((function(t){return{data:t,total:t.length}}))}catch(t){return Promise.reject(t)}},e.set=function(t){return t instanceof Array?this.data=function(){return t}:t instanceof Function&&(this.data=t),this},n}(Jt),Yt=function(t){function n(n){var e;return(e=t.call(this)||this).options=void 0,e.options=n,e}u(n,t);var e=n.prototype;return e.handler=function(t){return"function"==typeof this.options.handle?this.options.handle(t):t.ok?t.json():(Gt.error("Could not fetch data: "+t.status+" - "+t.statusText,!0),null)},e.get=function(t){var n=i({},this.options,t);return"function"==typeof n.data?n.data(n):fetch(n.url,n).then(this.handler.bind(this)).then((function(t){return{data:n.then(t),total:"function"==typeof n.total?n.total(t):void 0}}))},n}(Jt),tn=function(){function t(){}return t.createFromConfig=function(t){var n=null;return t.data&&(n=new Qt(t.data)),t.from&&(n=new Qt(this.tableElementToArray(t.from)),t.from.style.display="none"),t.server&&(n=new Yt(t.server)),n||Gt.error("Could not determine the storage type",!0),n},t.tableElementToArray=function(t){for(var n,e,r=[],o=c(t.querySelector("tbody").querySelectorAll("tr"));!(n=o()).done;){for(var i,u=[],s=c(n.value.querySelectorAll("td"));!(i=s()).done;){var a=i.value;1===a.childNodes.length&&a.childNodes[0].nodeType===Node.TEXT_NODE?u.push((e=a.innerHTML,(new DOMParser).parseFromString(e,"text/html").documentElement.textContent)):u.push(X(a.innerHTML))}r.push(u)}return r},t}(),nn="undefined"!=typeof Symbol?Symbol.iterator||(Symbol.iterator=Symbol("Symbol.iterator")):"@@iterator";function en(t,n,e){if(!t.s){if(e instanceof rn){if(!e.s)return void(e.o=en.bind(null,t,n));1&n&&(n=e.s),e=e.v}if(e&&e.then)return void e.then(en.bind(null,t,n),en.bind(null,t,2));t.s=n,t.v=e;var r=t.o;r&&r(t)}}var rn=function(){function t(){}return t.prototype.then=function(n,e){var r=new t,o=this.s;if(o){var i=1&o?n:e;if(i){try{en(r,1,i(this.v))}catch(t){en(r,2,t)}return r}return this}return this.o=function(t){try{var o=t.v;1&t.s?en(r,1,n?n(o):o):e?en(r,1,e(o)):en(r,2,o)}catch(t){en(r,2,t)}},r},t}();function on(t){return t instanceof rn&&1&t.s}var un=function(t){function n(n){var e;return(e=t.call(this)||this)._steps=new Map,e.cache=new Map,e.lastProcessorIndexUpdated=-1,n&&n.forEach((function(t){return e.register(t)})),e}u(n,t);var e=n.prototype;return e.clearCache=function(){this.cache=new Map,this.lastProcessorIndexUpdated=-1},e.register=function(t,n){if(void 0===n&&(n=null),!t)throw Error("Processor is not defined");if(null===t.type)throw Error("Processor type is not defined");if(this.findProcessorIndexByID(t.id)>-1)throw Error("Processor ID "+t.id+" is already defined");return t.on("propsUpdated",this.processorPropsUpdated.bind(this)),this.addProcessorByPriority(t,n),this.afterRegistered(t),t},e.tryRegister=function(t,n){void 0===n&&(n=null);try{return this.register(t,n)}catch(t){}},e.unregister=function(t){if(t&&-1!==this.findProcessorIndexByID(t.id)){var n=this._steps.get(t.type);n&&n.length&&(this._steps.set(t.type,n.filter((function(n){return n!=t}))),this.emit("updated",t))}},e.addProcessorByPriority=function(t,n){var e=this._steps.get(t.type);if(!e){var r=[];this._steps.set(t.type,r),e=r}if(null===n||n<0)e.push(t);else if(e[n]){var o=e.slice(0,n-1),i=e.slice(n+1);this._steps.set(t.type,o.concat(t).concat(i))}else e[n]=t},e.getStepsByType=function(t){return this.steps.filter((function(n){return n.type===t}))},e.getSortedProcessorTypes=function(){return Object.keys(Z).filter((function(t){return!isNaN(Number(t))})).map((function(t){return Number(t)}))},e.process=function(t){try{var n=function(t){return e.lastProcessorIndexUpdated=o.length,e.emit("afterProcess",i),i},e=this,r=e.lastProcessorIndexUpdated,o=e.steps,i=t,u=function(t,n){try{var u=function(t,n,e){if("function"==typeof t[nn]){var r,o,i,u=t[nn]();if(function t(e){try{for(;!(r=u.next()).done;)if((e=n(r.value))&&e.then){if(!on(e))return void e.then(t,i||(i=en.bind(null,o=new rn,2)));e=e.v}o?en(o,1,e):o=e}catch(t){en(o||(o=new rn),2,t)}}(),u.return){var s=function(t){try{r.done||u.return()}catch(t){}return t};if(o&&o.then)return o.then(s,(function(t){throw s(t)}));s()}return o}if(!("length"in t))throw new TypeError("Object is not iterable");for(var a=[],l=0;l<t.length;l++)a.push(t[l]);return function(t,n,e){var r,o,i=-1;return function e(u){try{for(;++i<t.length;)if((u=n(i))&&u.then){if(!on(u))return void u.then(e,o||(o=en.bind(null,r=new rn,2)));u=u.v}r?en(r,1,u):r=u}catch(t){en(r||(r=new rn),2,t)}}(),r}(a,(function(t){return n(a[t])}))}(o,(function(t){var n=e.findProcessorIndexByID(t.id),o=function(){if(n>=r)return Promise.resolve(t.process(i)).then((function(n){e.cache.set(t.id,i=n)}));i=e.cache.get(t.id)}();if(o&&o.then)return o.then((function(){}))}))}catch(t){return n(t)}return u&&u.then?u.then(void 0,n):u}(0,(function(t){throw Gt.error(t),e.emit("error",i),t}));return Promise.resolve(u&&u.then?u.then(n):n())}catch(t){return Promise.reject(t)}},e.findProcessorIndexByID=function(t){return this.steps.findIndex((function(n){return n.id==t}))},e.setLastProcessorIndex=function(t){var n=this.findProcessorIndexByID(t.id);this.lastProcessorIndexUpdated>n&&(this.lastProcessorIndexUpdated=n)},e.processorPropsUpdated=function(t){this.setLastProcessorIndex(t),this.emit("propsUpdated"),this.emit("updated",t)},e.afterRegistered=function(t){this.setLastProcessorIndex(t),this.emit("afterRegister"),this.emit("updated",t)},o(n,[{key:"steps",get:function(){for(var t,n=[],e=c(this.getSortedProcessorTypes());!(t=e()).done;){var r=this._steps.get(t.value);r&&r.length&&(n=n.concat(r))}return n.filter((function(t){return t}))}}]),n}(tt),sn=function(t){function n(){return t.apply(this,arguments)||this}return u(n,t),n.prototype._process=function(t){try{return Promise.resolve(this.props.storage.get(t))}catch(t){return Promise.reject(t)}},o(n,[{key:"type",get:function(){return Z.Extractor}}]),n}(et),an=function(t){function n(){return t.apply(this,arguments)||this}return u(n,t),n.prototype._process=function(t){var n=Y.fromArray(t.data);return n.length=t.total,n},o(n,[{key:"type",get:function(){return Z.Transformer}}]),n}(et),ln=function(t){function n(){return t.apply(this,arguments)||this}return u(n,t),n.prototype._process=function(){return Object.entries(this.props.serverStorageOptions).filter((function(t){return"function"!=typeof t[1]})).reduce((function(t,n){var e;return i({},t,((e={})[n[0]]=n[1],e))}),{})},o(n,[{key:"type",get:function(){return Z.Initiator}}]),n}(et),cn=function(t){function n(){return t.apply(this,arguments)||this}u(n,t);var e=n.prototype;return e.castData=function(t){if(!t||!t.length)return[];if(!this.props.header||!this.props.header.columns)return t;var n=Zt.leafColumns(this.props.header.columns);return t[0]instanceof Array?t.map((function(t){var e=0;return n.map((function(n,r){return void 0!==n.data?(e++,"function"==typeof n.data?n.data(t):n.data):t[r-e]}))})):"object"!=typeof t[0]||t[0]instanceof Array?[]:t.map((function(t){return n.map((function(n,e){return void 0!==n.data?"function"==typeof n.data?n.data(t):n.data:n.id?t[n.id]:(Gt.error("Could not find the correct cell for column at position "+e+".\n                          Make sure either 'id' or 'selector' is defined for all columns."),null)}))}))},e._process=function(t){return{data:this.castData(t.data),total:t.total}},o(n,[{key:"type",get:function(){return Z.Transformer}}]),n}(et),fn=function(){function t(){}return t.createFromConfig=function(t){var n=new un;return t.storage instanceof Yt&&n.register(new ln({serverStorageOptions:t.server})),n.register(new sn({storage:t.storage})),n.register(new cn({header:t.header})),n.register(new an),n},t}(),pn=function(t){var n=this;this.state=void 0,this.listeners=[],this.isDispatching=!1,this.getState=function(){return n.state},this.getListeners=function(){return n.listeners},this.dispatch=function(t){if("function"!=typeof t)throw new Error("Reducer is not a function");if(n.isDispatching)throw new Error("Reducers may not dispatch actions");n.isDispatching=!0;var e=n.state;try{n.state=t(n.state)}finally{n.isDispatching=!1}for(var r,o=c(n.listeners);!(r=o()).done;)(0,r.value)(n.state,e);return n.state},this.subscribe=function(t){if("function"!=typeof t)throw new Error("Listener is not a function");return n.listeners=[].concat(n.listeners,[t]),function(){return n.listeners=n.listeners.filter((function(n){return n!==t}))}},this.state=t},dn=function(t,n){var e={__c:n="__cC"+y++,__:null,Consumer:function(t,n){return t.children(n)},Provider:function(t){var e,r;return this.getChildContext||(e=[],(r={})[n]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(t){this.props.value!==t.value&&e.some(T)},this.sub=function(t){e.push(t);var n=t.componentWillUnmount;t.componentWillUnmount=function(){e.splice(e.indexOf(t),1),n&&n.call(t)}}),t.children}};return e.Provider.__=e.Consumer.contextType=e}(),hn=function(){function t(){Object.assign(this,t.defaultConfig())}var n=t.prototype;return n.assign=function(t){return Object.assign(this,t)},n.update=function(n){return n?(this.assign(t.fromPartialConfig(i({},this,n))),this):this},t.defaultConfig=function(){return{store:new pn({status:f.Init,header:void 0,data:null}),plugin:new Kt,tableRef:{current:null},width:"100%",height:"auto",processingThrottleMs:100,autoWidth:!0,style:{},className:{}}},t.fromPartialConfig=function(n){var e=(new t).assign(n);return"boolean"==typeof n.sort&&n.sort&&e.assign({sort:{multiColumn:!0}}),e.assign({header:Zt.createFromConfig(e)}),e.assign({storage:tn.createFromConfig(e)}),e.assign({pipeline:fn.createFromConfig(e)}),e.assign({translator:new Ot(e.language)}),e.plugin=new Kt,e.search&&e.plugin.add({id:"search",position:$t.Header,component:Ft}),e.pagination&&e.plugin.add({id:"pagination",position:$t.Footer,component:Bt}),e.plugins&&e.plugins.forEach((function(t){return e.plugin.add(t)})),e},t}();function _n(t){var n,e=Lt();return N("td",i({role:t.role,colSpan:t.colSpan,"data-column-id":t.column&&t.column.id,className:it(ot("td"),t.className,e.className.td),style:i({},t.style,e.style.td),onClick:function(n){t.messageCell||e.eventEmitter.emit("cellClick",n,t.cell,t.column,t.row)}},(n=t.column)?"function"==typeof n.attributes?n.attributes(t.cell.data,t.row,t.column):n.attributes:{}),t.column&&"function"==typeof t.column.formatter?t.column.formatter(t.cell.data,t.row,t.column):t.column&&t.column.plugin?N(Xt,{pluginId:t.column.id,props:{column:t.column,cell:t.cell,row:t.row}}):t.cell.data)}function mn(t){var n=Lt(),e=Mt((function(t){return t.header}));return N("tr",{className:it(ot("tr"),n.className.tr),onClick:function(e){t.messageRow||n.eventEmitter.emit("rowClick",e,t.row)}},t.children?t.children:t.row.cells.map((function(n,r){var o=function(t){if(e){var n=Zt.leafColumns(e.columns);if(n)return n[t]}return null}(r);return o&&o.hidden?null:N(_n,{key:n.id,cell:n,row:t.row,column:o})})))}function vn(t){return N(mn,{messageRow:!0},N(_n,{role:"alert",colSpan:t.colSpan,messageCell:!0,cell:new J(t.message),className:it(ot("message"),t.className?t.className:null)}))}function yn(){var t=Lt(),n=Mt((function(t){return t.data})),e=Mt((function(t){return t.status})),r=Mt((function(t){return t.header})),o=Ht(),i=function(){return r?r.visibleColumns.length:0};return N("tbody",{className:it(ot("tbody"),t.className.tbody)},n&&n.rows.map((function(t){return N(mn,{key:t.id,row:t})})),e===f.Loading&&(!n||0===n.length)&&N(vn,{message:o("loading"),colSpan:i(),className:it(ot("loading"),t.className.loading)}),e===f.Rendered&&n&&0===n.length&&N(vn,{message:o("noRecordsFound"),colSpan:i(),className:it(ot("notfound"),t.className.notfound)}),e===f.Error&&N(vn,{message:o("error"),colSpan:i(),className:it(ot("error"),t.className.error)}))}var gn=function(t){function n(){return t.apply(this,arguments)||this}u(n,t);var e=n.prototype;return e.validateProps=function(){for(var t,n=c(this.props.columns);!(t=n()).done;){var e=t.value;void 0===e.direction&&(e.direction=1),1!==e.direction&&-1!==e.direction&&Gt.error("Invalid sort direction "+e.direction)}},e.compare=function(t,n){return t>n?1:t<n?-1:0},e.compareWrapper=function(t,n){for(var e,r=0,o=c(this.props.columns);!(e=o()).done;){var i=e.value;if(0!==r)break;var u=t.cells[i.index].data,s=n.cells[i.index].data;r|="function"==typeof i.compare?i.compare(u,s)*i.direction:this.compare(u,s)*i.direction}return r},e._process=function(t){var n=[].concat(t.rows);n.sort(this.compareWrapper.bind(this));var e=new Y(n);return e.length=t.length,e},o(n,[{key:"type",get:function(){return Z.Sort}}]),n}(et),bn=function(t,n,e,r){return function(o){var u,s=null!=(u=o.sort)&&u.columns?o.sort.columns.map((function(t){return i({},t)})):[],a=s.length,l=s.find((function(n){return n.index===t})),c=!1,f=!1,p=!1,d=!1;if(void 0!==l?e?-1===l.direction?p=!0:d=!0:1===a?d=!0:a>1&&(f=!0,c=!0):0===a?c=!0:a>0&&!e?(c=!0,f=!0):a>0&&e&&(c=!0),f&&(s=[]),c)s.push({index:t,direction:n,compare:r});else if(d){var h=s.indexOf(l);s[h].direction=n}else if(p){var _=s.indexOf(l);s.splice(_,1)}return i({},o,{sort:{columns:s}})}},wn=function(t,n,e){return function(r){var o=(r.sort?[].concat(r.sort.columns):[]).find((function(n){return n.index===t}));return i({},r,o?bn(t,1===o.direction?-1:1,n,e)(r):bn(t,1,n,e)(r))}},kn=function(t){function n(){return t.apply(this,arguments)||this}return u(n,t),n.prototype._process=function(t){var n={};return this.props.url&&(n.url=this.props.url(t.url,this.props.columns)),this.props.body&&(n.body=this.props.body(t.body,this.props.columns)),i({},t,n)},o(n,[{key:"type",get:function(){return Z.ServerSort}}]),n}(et);function xn(t){var n=Lt(),e=Dt().dispatch,r=Ht(),o=bt(0),u=o[0],s=o[1],a=n.sort,l=Mt((function(t){return t.sort})),c="object"==typeof(null==a?void 0:a.server)?Z.ServerSort:Z.Sort,f=function(){var t=n.pipeline.getStepsByType(c);if(t.length)return t[0]};return wt((function(){var t=f()||(c===Z.ServerSort?new kn(i({columns:l?l.columns:[]},a.server)):new gn({columns:l?l.columns:[]}));return n.pipeline.tryRegister(t),function(){return n.pipeline.unregister(t)}}),[n]),wt((function(){if(l){var n,e=l.columns.find((function(n){return n.index===t.index}));e?(0===u&&(e.direction=null!=(n=t.direction)?n:1),s(e.direction)):s(0)}}),[l]),wt((function(){var t=f();t&&l&&t.setProps({columns:l.columns})}),[l]),N("button",{tabIndex:-1,"aria-label":r("sort.sort"+(1===u?"Desc":"Asc")),title:r("sort.sort"+(1===u?"Desc":"Asc")),className:it(ot("sort"),ot("sort",function(t){return 1===t?"asc":-1===t?"desc":"neutral"}(u)),n.className.sort),onClick:function(n){n.preventDefault(),n.stopPropagation(),e(wn(t.index,!0===n.shiftKey&&a.multiColumn,t.compare))}})}var Nn=function(t,n){var e;void 0===n&&(n=100);var r=Date.now(),o=function(){r=Date.now(),t.apply(void 0,[].slice.call(arguments))};return function(){var t=[].slice.call(arguments),i=Date.now()-r;i>=n?o.apply(void 0,t):(e&&clearTimeout(e),e=setTimeout((function(){o.apply(void 0,t),e=null}),n-i))}};function Pn(t){var n,e=function(t){return t instanceof MouseEvent?Math.floor(t.pageX):Math.floor(t.changedTouches[0].pageX)},r=function(r){r.stopPropagation();var u=parseInt(t.thRef.current.style.width,10)-e(r);n=Nn((function(t){return o(t,u)}),10),document.addEventListener("mouseup",i),document.addEventListener("touchend",i),document.addEventListener("mousemove",n),document.addEventListener("touchmove",n)},o=function(n,r){n.stopPropagation();var o=t.thRef.current;r+e(n)>=parseInt(o.style.minWidth,10)&&(o.style.width=r+e(n)+"px")},i=function t(e){e.stopPropagation(),document.removeEventListener("mouseup",t),document.removeEventListener("mousemove",n),document.removeEventListener("touchmove",n),document.removeEventListener("touchend",t)};return N("div",{className:it(ot("th"),ot("resizable")),onMouseDown:r,onTouchStart:r,onClick:function(t){return t.stopPropagation()}})}function Sn(t){var n=Lt(),e=kt(null),r=bt({}),o=r[0],u=r[1],s=Dt().dispatch;wt((function(){if(n.fixedHeader&&e.current){var t=e.current.offsetTop;"number"==typeof t&&u({top:t})}}),[e]);var a,l=function(){return null!=t.column.sort},c=function(e){e.stopPropagation(),l()&&s(wn(t.index,!0===e.shiftKey&&n.sort.multiColumn,t.column.sort.compare))};return N("th",i({ref:e,"data-column-id":t.column&&t.column.id,className:it(ot("th"),l()?ot("th","sort"):null,n.fixedHeader?ot("th","fixed"):null,n.className.th),onClick:c,style:i({},n.style.th,{minWidth:t.column.minWidth,width:t.column.width},o,t.style),onKeyDown:function(t){l()&&13===t.which&&c(t)},rowSpan:t.rowSpan>1?t.rowSpan:void 0,colSpan:t.colSpan>1?t.colSpan:void 0},(a=t.column)?"function"==typeof a.attributes?a.attributes(null,null,t.column):a.attributes:{},l()?{tabIndex:0}:{}),N("div",{className:ot("th","content")},void 0!==t.column.name?t.column.name:void 0!==t.column.plugin?N(Xt,{pluginId:t.column.plugin.id,props:{column:t.column}}):null),l()&&N(xn,i({index:t.index},t.column.sort)),t.column.resizable&&t.index<n.header.visibleColumns.length-1&&N(Pn,{column:t.column,thRef:e}))}function Cn(){var t,n=Lt(),e=Mt((function(t){return t.header}));return e?N("thead",{key:e.id,className:it(ot("thead"),n.className.thead)},(t=Zt.tabularFormat(e.columns)).map((function(n,r){return function(t,n,r){var o=Zt.leafColumns(e.columns);return N(mn,null,t.map((function(t){return t.hidden?null:function(t,n,e,r){var o=function(t,n,e){var r=Zt.maximumDepth(t),o=e-n;return{rowSpan:Math.floor(o-r-r/o),colSpan:t.columns&&t.columns.length||1}}(t,n,r);return N(Sn,{column:t,index:e,colSpan:o.colSpan,rowSpan:o.rowSpan})}(t,n,o.indexOf(t),r)})))}(n,r,t.length)}))):null}var En=function(t){return function(n){return i({},n,{header:t})}};function In(){var t=Lt(),n=kt(null),e=Dt().dispatch;return wt((function(){n&&e(function(t){return function(n){return i({},n,{tableRef:t})}}(n))}),[n]),N("table",{ref:n,role:"grid",className:it(ot("table"),t.className.table),style:i({},t.style.table,{height:t.height})},N(Cn,null),N(yn,null))}function Tn(){var t=bt(!0),n=t[0],e=t[1],r=kt(null),o=Lt();return wt((function(){0===r.current.children.length&&e(!1)}),[r]),n?N("div",{ref:r,className:it(ot("head"),o.className.header),style:i({},o.style.header)},N(Xt,{position:$t.Header})):null}function Ln(){var t=kt(null),n=bt(!0),e=n[0],r=n[1],o=Lt();return wt((function(){0===t.current.children.length&&r(!1)}),[t]),e?N("div",{ref:t,className:it(ot("footer"),o.className.footer),style:i({},o.style.footer)},N(Xt,{position:$t.Footer})):null}function An(){var t=Lt(),n=Dt().dispatch,e=Mt((function(t){return t.status})),r=Mt((function(t){return t.data})),o=Mt((function(t){return t.tableRef})),u={current:null},s=Nn((function(){try{n((function(t){return i({},t,{status:f.Loading})}));var e=function(e,r){try{var o=Promise.resolve(t.pipeline.process()).then((function(t){n(function(t){return function(n){return t?i({},n,{data:t,status:f.Loaded}):n}}(t)),setTimeout((function(){n((function(t){return t.status===f.Loaded?i({},t,{status:f.Rendered}):t}))}),0)}))}catch(t){return r(t)}return o&&o.then?o.then(void 0,r):o}(0,(function(t){Gt.error(t),n((function(t){return i({},t,{data:null,status:f.Error})}))}));return Promise.resolve(e&&e.then?e.then((function(){})):void 0)}catch(t){return Promise.reject(t)}}),t.processingThrottleMs);return wt((function(){return n(En(t.header)),s(),t.pipeline.on("updated",s),function(){return t.pipeline.off("updated",s)}}),[]),wt((function(){t.header&&e===f.Loaded&&null!=r&&r.length&&n(En(t.header.adjustWidth(t,o,u)))}),[r,t,u]),N("div",{role:"complementary",className:it("gridjs",ot("container"),e===f.Loading?ot("loading"):null,t.className.container),style:i({},t.style.container,{width:t.width})},e===f.Loading&&N("div",{className:ot("loading-bar")}),N(Tn,null),N("div",{className:ot("wrapper"),style:{height:t.height}},N(In,null)),N(Ln,null),N("div",{ref:u,id:"gridjs-temp",className:ot("temp")}))}var On=function(t){function n(n){var e;return(e=t.call(this)||this).config=void 0,e.plugin=void 0,e.config=(new hn).assign({instance:a(e),eventEmitter:a(e)}).update(n),e.plugin=e.config.plugin,e}u(n,t);var e=n.prototype;return e.updateConfig=function(t){return this.config.update(t),this},e.createElement=function(){return N(dn.Provider,{value:this.config,children:N(An,{})})},e.forceRender=function(){return this.config&&this.config.container||Gt.error("Container is empty. Make sure you call render() before forceRender()",!0),this.destroy(),V(this.createElement(),this.config.container),this},e.destroy=function(){this.config.pipeline.clearCache(),V(null,this.config.container)},e.render=function(t){return t||Gt.error("Container element cannot be null",!0),t.childNodes.length>0?(Gt.error("The container element "+t+" is not empty. Make sure the container is empty and call render() again"),this):(this.config.container=t,V(this.createElement(),t),this)},n}(tt)}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/87e91c30c0c34659986e0575fab01284/227.lite.js.map
