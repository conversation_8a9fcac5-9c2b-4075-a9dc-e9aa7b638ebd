"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[799],{43799:(e,t,n)=>{n.d(t,{A:()=>E});var a=n(41594),o=n(65924),r=n.n(o),i=n(15220),l=n(80840),c=n(31754),d=n(75792),s=n(5190);const u=e=>{var{prefixCls:t,className:n,hoverable:o=!0}=e,i=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n}(e,["prefixCls","className","hoverable"]);const{getPrefixCls:c}=a.useContext(l.QO),d=c("card",t),s=r()(`${d}-grid`,n,{[`${d}-grid-hoverable`]:o});return a.createElement("div",Object.assign({},i,{className:s}))};var p=n(78052),b=n(71094),v=n(52146),f=n(63829);const m=e=>{const{antCls:t,componentCls:n,headerHeight:a,cardPaddingBase:o,tabsMarginBottom:r}=e;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:a,marginBottom:-1,padding:`0 ${(0,p.zA)(o)}`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.headerFontSize,background:e.headerBg,borderBottom:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${(0,p.zA)(e.borderRadiusLG)} ${(0,p.zA)(e.borderRadiusLG)} 0 0`},(0,b.t6)()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},b.L9),{[`\n          > ${n}-typography,\n          > ${n}-typography-edit-content\n        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:r,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`}}})},g=e=>{const{cardPaddingBase:t,colorBorderSecondary:n,cardShadow:a,lineWidth:o}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`\n      ${(0,p.zA)(o)} 0 0 0 ${n},\n      0 ${(0,p.zA)(o)} 0 0 ${n},\n      ${(0,p.zA)(o)} ${(0,p.zA)(o)} 0 0 ${n},\n      ${(0,p.zA)(o)} 0 0 0 ${n} inset,\n      0 ${(0,p.zA)(o)} 0 0 ${n} inset;\n    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:a}}},h=e=>{const{componentCls:t,iconCls:n,actionsLiMargin:a,cardActionsIconSize:o,colorBorderSecondary:r,actionsBg:i}=e;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:i,borderTop:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${r}`,display:"flex",borderRadius:`0 0 ${(0,p.zA)(e.borderRadiusLG)} ${(0,p.zA)(e.borderRadiusLG)}`},(0,b.t6)()),{"& > li":{margin:a,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.calc(e.cardActionsIconSize).mul(2).equal(),fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${n}`]:{display:"inline-block",width:"100%",color:e.colorTextDescription,lineHeight:(0,p.zA)(e.fontHeight),transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${n}`]:{fontSize:o,lineHeight:(0,p.zA)(e.calc(o).mul(e.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${r}`}}})},$=e=>Object.assign(Object.assign({margin:`${(0,p.zA)(e.calc(e.marginXXS).mul(-1).equal())} 0`,display:"flex"},(0,b.t6)()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":Object.assign({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},b.L9),"&-description":{color:e.colorTextDescription}}),y=e=>{const{componentCls:t,cardPaddingBase:n,colorFillAlter:a}=e;return{[`${t}-head`]:{padding:`0 ${(0,p.zA)(n)}`,background:a,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${(0,p.zA)(e.padding)} ${(0,p.zA)(n)}`}}},A=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},S=e=>{const{antCls:t,componentCls:n,cardShadow:a,cardHeadPadding:o,colorBorderSecondary:r,boxShadowTertiary:i,cardPaddingBase:l,extraColor:c}=e;return{[n]:Object.assign(Object.assign({},(0,b.dF)(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${n}-bordered)`]:{boxShadow:i},[`${n}-head`]:m(e),[`${n}-extra`]:{marginInlineStart:"auto",color:c,fontWeight:"normal",fontSize:e.fontSize},[`${n}-body`]:Object.assign({padding:l,borderRadius:`0 0 ${(0,p.zA)(e.borderRadiusLG)} ${(0,p.zA)(e.borderRadiusLG)}`},(0,b.t6)()),[`${n}-grid`]:g(e),[`${n}-cover`]:{"> *":{display:"block",width:"100%"},[`img, img + ${t}-image-mask`]:{borderRadius:`${(0,p.zA)(e.borderRadiusLG)} ${(0,p.zA)(e.borderRadiusLG)} 0 0`}},[`${n}-actions`]:h(e),[`${n}-meta`]:$(e)}),[`${n}-bordered`]:{border:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${r}`,[`${n}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${n}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:a}},[`${n}-contain-grid`]:{borderRadius:`${(0,p.zA)(e.borderRadiusLG)} ${(0,p.zA)(e.borderRadiusLG)} 0 0 `,[`${n}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${n}-loading) ${n}-body`]:{marginBlockStart:e.calc(e.lineWidth).mul(-1).equal(),marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),padding:0}},[`${n}-contain-tabs`]:{[`> div${n}-head`]:{minHeight:0,[`${n}-head-title, ${n}-extra`]:{paddingTop:o}}},[`${n}-type-inner`]:y(e),[`${n}-loading`]:A(e),[`${n}-rtl`]:{direction:"rtl"}}},x=e=>{const{componentCls:t,cardPaddingSM:n,headerHeightSM:a,headerFontSizeSM:o}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:a,padding:`0 ${(0,p.zA)(n)}`,fontSize:o,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:n}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:0,display:"flex",alignItems:"center"}}}}},k=(0,v.OF)("Card",(e=>{const t=(0,f.h1)(e,{cardShadow:e.boxShadowCard,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardActionsIconSize:e.fontSize,cardPaddingSM:12});return[S(t),x(t)]}),(e=>({headerBg:"transparent",headerFontSize:e.fontSizeLG,headerFontSizeSM:e.fontSize,headerHeight:e.fontSizeLG*e.lineHeightLG+2*e.padding,headerHeightSM:e.fontSize*e.lineHeight+2*e.paddingXS,actionsBg:e.colorBgContainer,actionsLiMargin:`${e.paddingSM}px 0`,tabsMarginBottom:-e.padding-e.lineWidth,extraColor:e.colorText})));var C=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};const w=e=>{const{actionClasses:t,actions:n=[],actionStyle:o}=e;return a.createElement("ul",{className:t,style:o},n.map(((e,t)=>{const o=`action-${t}`;return a.createElement("li",{style:{width:100/n.length+"%"},key:o},a.createElement("span",null,e))})))},z=a.forwardRef(((e,t)=>{const{prefixCls:n,className:o,rootClassName:p,style:b,extra:v,headStyle:f={},bodyStyle:m={},title:g,loading:h,bordered:$=!0,size:y,type:A,cover:S,actions:x,tabList:z,children:_,activeTabKey:E,defaultActiveTabKey:O,tabBarExtraContent:P,hoverable:R,tabProps:T={},classNames:I,styles:L}=e,B=C(e,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:j,direction:N,card:M}=a.useContext(l.QO),D=e=>{var t;return r()(null===(t=null==M?void 0:M.classNames)||void 0===t?void 0:t[e],null==I?void 0:I[e])},G=e=>{var t;return Object.assign(Object.assign({},null===(t=null==M?void 0:M.styles)||void 0===t?void 0:t[e]),null==L?void 0:L[e])},H=a.useMemo((()=>{let e=!1;return a.Children.forEach(_,(t=>{t&&t.type&&t.type===u&&(e=!0)})),e}),[_]),W=j("card",n),[X,K,F]=k(W),q=a.createElement(d.A,{loading:!0,active:!0,paragraph:{rows:4},title:!1},_),V=void 0!==E,Y=Object.assign(Object.assign({},T),{[V?"activeKey":"defaultActiveKey"]:V?E:O,tabBarExtraContent:P});let Q;const J=(0,c.A)(y),U=J&&"default"!==J?J:"large",Z=z?a.createElement(s.A,Object.assign({size:U},Y,{className:`${W}-head-tabs`,onChange:t=>{var n;null===(n=e.onTabChange)||void 0===n||n.call(e,t)},items:z.map((e=>{var{tab:t}=e,n=C(e,["tab"]);return Object.assign({label:t},n)}))})):null;if(g||v||Z){const e=r()(`${W}-head`,D("header")),t=r()(`${W}-head-title`,D("title")),n=r()(`${W}-extra`,D("extra")),o=Object.assign(Object.assign({},f),G("header"));Q=a.createElement("div",{className:e,style:o},a.createElement("div",{className:`${W}-head-wrapper`},g&&a.createElement("div",{className:t,style:G("title")},g),v&&a.createElement("div",{className:n,style:G("extra")},v)),Z)}const ee=r()(`${W}-cover`,D("cover")),te=S?a.createElement("div",{className:ee,style:G("cover")},S):null,ne=r()(`${W}-body`,D("body")),ae=Object.assign(Object.assign({},m),G("body")),oe=a.createElement("div",{className:ne,style:ae},h?q:_),re=r()(`${W}-actions`,D("actions")),ie=x&&x.length?a.createElement(w,{actionClasses:re,actionStyle:G("actions"),actions:x}):null,le=(0,i.A)(B,["onTabChange"]),ce=r()(W,null==M?void 0:M.className,{[`${W}-loading`]:h,[`${W}-bordered`]:$,[`${W}-hoverable`]:R,[`${W}-contain-grid`]:H,[`${W}-contain-tabs`]:z&&z.length,[`${W}-${J}`]:J,[`${W}-type-${A}`]:!!A,[`${W}-rtl`]:"rtl"===N},o,p,K,F),de=Object.assign(Object.assign({},null==M?void 0:M.style),b);return X(a.createElement("div",Object.assign({ref:t},le,{className:ce,style:de}),Q,te,oe,ie))}));const _=z;_.Grid=u,_.Meta=e=>{const{prefixCls:t,className:n,avatar:o,title:i,description:c}=e,d=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n}(e,["prefixCls","className","avatar","title","description"]),{getPrefixCls:s}=a.useContext(l.QO),u=s("card",t),p=r()(`${u}-meta`,n),b=o?a.createElement("div",{className:`${u}-meta-avatar`},o):null,v=i?a.createElement("div",{className:`${u}-meta-title`},i):null,f=c?a.createElement("div",{className:`${u}-meta-description`},c):null,m=v||f?a.createElement("div",{className:`${u}-meta-detail`},v,f):null;return a.createElement("div",Object.assign({},d,{className:p}),b,m)};const E=_},5190:(e,t,n)=>{n.d(t,{A:()=>Ce});var a=n(41594),o=n.n(a),r=n(43012),i=n(85081),l=n(96431),c=n(65924),d=n.n(c),s=n(2464),u=n(21483),p=n(58187),b=n(61129),v=n(81188),f=n(4105),m=n(74188),g=n(42243);const h=(0,a.createContext)(null);var $=n(18539),y=n(87458),A=n(35649),S=n(2620),x=n(32664);var k={width:0,height:0,left:0,top:0};function C(e,t){var n=a.useRef(e),o=a.useState({}),r=(0,b.A)(o,2)[1];return[n.current,function(e){var a="function"==typeof e?e(n.current):e;a!==n.current&&t(a,n.current),n.current=a,r({})}]}var w=Math.pow(.995,20),z=n(78294);function _(e){var t=(0,a.useState)(0),n=(0,b.A)(t,2),o=n[0],r=n[1],i=(0,a.useRef)(0),l=(0,a.useRef)();return l.current=e,(0,z.o)((function(){var e;null===(e=l.current)||void 0===e||e.call(l)}),[o]),function(){i.current===o&&(i.current+=1,r(i.current))}}var E={width:0,height:0,left:0,top:0,right:0};function O(e){var t;return e instanceof Map?(t={},e.forEach((function(e,n){t[n]=e}))):t=e,JSON.stringify(t)}function P(e){return String(e).replace(/"/g,"TABS_DQ")}function R(e,t,n,a){return!(!n||a||!1===e||void 0===e&&(!1===t||null===t))}var T=a.forwardRef((function(e,t){var n=e.prefixCls,o=e.editable,r=e.locale,i=e.style;return o&&!1!==o.showAdd?a.createElement("button",{ref:t,type:"button",className:"".concat(n,"-nav-add"),style:i,"aria-label":(null==r?void 0:r.addAriaLabel)||"Add tab",onClick:function(e){o.onEdit("add",{event:e})}},o.addIcon||"+"):null}));const I=T,L=a.forwardRef((function(e,t){var n,o=e.position,r=e.prefixCls,i=e.extra;if(!i)return null;var l={};return"object"!==(0,v.A)(i)||a.isValidElement(i)?l.right=i:l=i,"right"===o&&(n=l.right),"left"===o&&(n=l.left),n?a.createElement("div",{className:"".concat(r,"-extra-content"),ref:t},n):null}));var B=n(573),j=n(5446),N=n(81739),M=a.forwardRef((function(e,t){var n=e.prefixCls,o=e.id,r=e.tabs,i=e.locale,l=e.mobile,c=e.more,p=void 0===c?{}:c,v=e.style,f=e.className,m=e.editable,g=e.tabBarGutter,h=e.rtl,$=e.removeAriaLabel,y=e.onTabClick,A=e.getPopupContainer,S=e.popupClassName,x=(0,a.useState)(!1),k=(0,b.A)(x,2),C=k[0],w=k[1],z=(0,a.useState)(null),_=(0,b.A)(z,2),E=_[0],O=_[1],P=p.icon,T=void 0===P?"More":P,L="".concat(o,"-more-popup"),M="".concat(n,"-dropdown"),D=null!==E?"".concat(L,"-").concat(E):null,G=null==i?void 0:i.dropdownAriaLabel,H=a.createElement(j.Ay,{onClick:function(e){var t=e.key,n=e.domEvent;y(t,n),w(!1)},prefixCls:"".concat(M,"-menu"),id:L,tabIndex:-1,role:"listbox","aria-activedescendant":D,selectedKeys:[E],"aria-label":void 0!==G?G:"expanded dropdown"},r.map((function(e){var t=e.closable,n=e.disabled,r=e.closeIcon,i=e.key,l=e.label,c=R(t,r,m,n);return a.createElement(j.Dr,{key:i,id:"".concat(L,"-").concat(i),role:"option","aria-controls":o&&"".concat(o,"-panel-").concat(i),disabled:n},a.createElement("span",null,l),c&&a.createElement("button",{type:"button","aria-label":$||"remove",tabIndex:0,className:"".concat(M,"-menu-item-remove"),onClick:function(e){e.stopPropagation(),function(e,t){e.preventDefault(),e.stopPropagation(),m.onEdit("remove",{key:t,event:e})}(e,i)}},r||m.removeIcon||"×"))})));function W(e){for(var t=r.filter((function(e){return!e.disabled})),n=t.findIndex((function(e){return e.key===E}))||0,a=t.length,o=0;o<a;o+=1){var i=t[n=(n+e+a)%a];if(!i.disabled)return void O(i.key)}}(0,a.useEffect)((function(){var e=document.getElementById(D);e&&e.scrollIntoView&&e.scrollIntoView(!1)}),[E]),(0,a.useEffect)((function(){C||O(null)}),[C]);var X=(0,u.A)({},h?"marginRight":"marginLeft",g);r.length||(X.visibility="hidden",X.order=1);var K=d()((0,u.A)({},"".concat(M,"-rtl"),h)),F=l?null:a.createElement(B.A,(0,s.A)({prefixCls:M,overlay:H,visible:!!r.length&&C,onVisibleChange:w,overlayClassName:d()(K,S),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:A},p),a.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:X,tabIndex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":L,id:"".concat(o,"-more"),"aria-expanded":C,onKeyDown:function(e){var t=e.which;if(C)switch(t){case N.A.UP:W(-1),e.preventDefault();break;case N.A.DOWN:W(1),e.preventDefault();break;case N.A.ESC:w(!1);break;case N.A.SPACE:case N.A.ENTER:null!==E&&y(E,e)}else[N.A.DOWN,N.A.SPACE,N.A.ENTER].includes(t)&&(w(!0),e.preventDefault())}},T));return a.createElement("div",{className:d()("".concat(n,"-nav-operations"),f),style:v,ref:t},F,a.createElement(I,{prefixCls:n,locale:i,editable:m}))}));const D=a.memo(M,(function(e,t){return t.tabMoving})),G=function(e){var t=e.prefixCls,n=e.id,o=e.active,r=e.tab,i=r.key,l=r.label,c=r.disabled,s=r.closeIcon,p=r.icon,b=e.closable,v=e.renderWrapper,f=e.removeAriaLabel,m=e.editable,g=e.onClick,h=e.onFocus,$=e.style,y="".concat(t,"-tab"),A=R(b,s,m,c);function S(e){c||g(e)}var x=a.useMemo((function(){return p&&"string"==typeof l?a.createElement("span",null,l):l}),[l,p]),k=a.createElement("div",{key:i,"data-node-key":P(i),className:d()(y,(0,u.A)((0,u.A)((0,u.A)({},"".concat(y,"-with-remove"),A),"".concat(y,"-active"),o),"".concat(y,"-disabled"),c)),style:$,onClick:S},a.createElement("div",{role:"tab","aria-selected":o,id:n&&"".concat(n,"-tab-").concat(i),className:"".concat(y,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(i),"aria-disabled":c,tabIndex:c?null:0,onClick:function(e){e.stopPropagation(),S(e)},onKeyDown:function(e){[N.A.SPACE,N.A.ENTER].includes(e.which)&&(e.preventDefault(),S(e))},onFocus:h},p&&a.createElement("span",{className:"".concat(y,"-icon")},p),l&&x),A&&a.createElement("button",{type:"button","aria-label":f||"remove",tabIndex:0,className:"".concat(y,"-remove"),onClick:function(e){var t;e.stopPropagation(),(t=e).preventDefault(),t.stopPropagation(),m.onEdit("remove",{key:i,event:t})}},s||m.removeIcon||"×"));return v?v(k):k};var H=function(e){var t=e.current||{},n=t.offsetWidth,a=void 0===n?0:n,o=t.offsetHeight,r=void 0===o?0:o;if(e.current){var i=e.current.getBoundingClientRect(),l=i.width,c=i.height;if(Math.abs(l-a)<1)return[l,c]}return[a,r]},W=function(e,t){return e[t?0:1]},X=a.forwardRef((function(e,t){var n=e.className,r=e.style,i=e.id,l=e.animated,c=e.activeKey,v=e.rtl,f=e.extra,m=e.editable,g=e.locale,z=e.tabPosition,R=e.tabBarGutter,T=e.children,B=e.onTabClick,j=e.onTabScroll,N=e.indicator,M=a.useContext(h),X=M.prefixCls,K=M.tabs,F=(0,a.useRef)(null),q=(0,a.useRef)(null),V=(0,a.useRef)(null),Y=(0,a.useRef)(null),Q=(0,a.useRef)(null),J=(0,a.useRef)(null),U=(0,a.useRef)(null),Z="top"===z||"bottom"===z,ee=C(0,(function(e,t){Z&&j&&j({direction:e>t?"left":"right"})})),te=(0,b.A)(ee,2),ne=te[0],ae=te[1],oe=C(0,(function(e,t){!Z&&j&&j({direction:e>t?"top":"bottom"})})),re=(0,b.A)(oe,2),ie=re[0],le=re[1],ce=(0,a.useState)([0,0]),de=(0,b.A)(ce,2),se=de[0],ue=de[1],pe=(0,a.useState)([0,0]),be=(0,b.A)(pe,2),ve=be[0],fe=be[1],me=(0,a.useState)([0,0]),ge=(0,b.A)(me,2),he=ge[0],$e=ge[1],ye=(0,a.useState)([0,0]),Ae=(0,b.A)(ye,2),Se=Ae[0],xe=Ae[1],ke=function(e){var t=(0,a.useRef)([]),n=(0,a.useState)({}),o=(0,b.A)(n,2)[1],r=(0,a.useRef)("function"==typeof e?e():e),i=_((function(){var e=r.current;t.current.forEach((function(t){e=t(e)})),t.current=[],r.current=e,o({})}));return[r.current,function(e){t.current.push(e),i()}]}(new Map),Ce=(0,b.A)(ke,2),we=Ce[0],ze=Ce[1],_e=function(e,t,n){return(0,a.useMemo)((function(){for(var n,a=new Map,o=t.get(null===(n=e[0])||void 0===n?void 0:n.key)||k,r=o.left+o.width,i=0;i<e.length;i+=1){var l,c=e[i].key,d=t.get(c);d||(d=t.get(null===(l=e[i-1])||void 0===l?void 0:l.key)||k);var s=a.get(c)||(0,p.A)({},d);s.right=r-s.left-s.width,a.set(c,s)}return a}),[e.map((function(e){return e.key})).join("_"),t,n])}(K,we,ve[0]),Ee=W(se,Z),Oe=W(ve,Z),Pe=W(he,Z),Re=W(Se,Z),Te=Ee<Oe+Pe,Ie=Te?Ee-Re:Ee-Pe,Le="".concat(X,"-nav-operations-hidden"),Be=0,je=0;function Ne(e){return e<Be?Be:e>je?je:e}Z&&v?(Be=0,je=Math.max(0,Oe-Ie)):(Be=Math.min(0,Ie-Oe),je=0);var Me=(0,a.useRef)(null),De=(0,a.useState)(),Ge=(0,b.A)(De,2),He=Ge[0],We=Ge[1];function Xe(){We(Date.now())}function Ke(){Me.current&&clearTimeout(Me.current)}!function(e,t){var n=(0,a.useState)(),o=(0,b.A)(n,2),r=o[0],i=o[1],l=(0,a.useState)(0),c=(0,b.A)(l,2),d=c[0],s=c[1],u=(0,a.useState)(0),p=(0,b.A)(u,2),v=p[0],f=p[1],m=(0,a.useState)(),g=(0,b.A)(m,2),h=g[0],$=g[1],y=(0,a.useRef)(),A=(0,a.useRef)(),S=(0,a.useRef)(null);S.current={onTouchStart:function(e){var t=e.touches[0],n=t.screenX,a=t.screenY;i({x:n,y:a}),window.clearInterval(y.current)},onTouchMove:function(e){if(r){e.preventDefault();var n=e.touches[0],a=n.screenX,o=n.screenY;i({x:a,y:o});var l=a-r.x,c=o-r.y;t(l,c);var u=Date.now();s(u),f(u-d),$({x:l,y:c})}},onTouchEnd:function(){if(r&&(i(null),$(null),h)){var e=h.x/v,n=h.y/v,a=Math.abs(e),o=Math.abs(n);if(Math.max(a,o)<.1)return;var l=e,c=n;y.current=window.setInterval((function(){Math.abs(l)<.01&&Math.abs(c)<.01?window.clearInterval(y.current):t(20*(l*=w),20*(c*=w))}),20)}},onWheel:function(e){var n=e.deltaX,a=e.deltaY,o=0,r=Math.abs(n),i=Math.abs(a);r===i?o="x"===A.current?n:a:r>i?(o=n,A.current="x"):(o=a,A.current="y"),t(-o,-o)&&e.preventDefault()}},a.useEffect((function(){function t(e){S.current.onTouchMove(e)}function n(e){S.current.onTouchEnd(e)}return document.addEventListener("touchmove",t,{passive:!1}),document.addEventListener("touchend",n,{passive:!0}),e.current.addEventListener("touchstart",(function(e){S.current.onTouchStart(e)}),{passive:!0}),e.current.addEventListener("wheel",(function(e){S.current.onWheel(e)}),{passive:!1}),function(){document.removeEventListener("touchmove",t),document.removeEventListener("touchend",n)}}),[])}(Y,(function(e,t){function n(e,t){e((function(e){return Ne(e+t)}))}return!!Te&&(Z?n(ae,e):n(le,t),Ke(),Xe(),!0)})),(0,a.useEffect)((function(){return Ke(),He&&(Me.current=setTimeout((function(){We(0)}),100)),Ke}),[He]);var Fe=function(e,t,n,o,r,i,l){var c,d,s,u=l.tabs,p=l.tabPosition,b=l.rtl;return["top","bottom"].includes(p)?(c="width",d=b?"right":"left",s=Math.abs(n)):(c="height",d="top",s=-n),(0,a.useMemo)((function(){if(!u.length)return[0,0];for(var n=u.length,a=n,o=0;o<n;o+=1){var r=e.get(u[o].key)||E;if(r[d]+r[c]>s+t){a=o-1;break}}for(var i=0,l=n-1;l>=0;l-=1)if((e.get(u[l].key)||E)[d]<s){i=l+1;break}return i>=a?[0,0]:[i,a]}),[e,t,o,r,i,s,p,u.map((function(e){return e.key})).join("_"),b])}(_e,Ie,Z?ne:ie,Oe,Pe,Re,(0,p.A)((0,p.A)({},e),{},{tabs:K})),qe=(0,b.A)(Fe,2),Ve=qe[0],Ye=qe[1],Qe=(0,A.A)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=_e.get(e)||{width:0,height:0,left:0,right:0,top:0};if(Z){var n=ne;v?t.right<ne?n=t.right:t.right+t.width>ne+Ie&&(n=t.right+t.width-Ie):t.left<-ne?n=-t.left:t.left+t.width>-ne+Ie&&(n=-(t.left+t.width-Ie)),le(0),ae(Ne(n))}else{var a=ie;t.top<-ie?a=-t.top:t.top+t.height>-ie+Ie&&(a=-(t.top+t.height-Ie)),ae(0),le(Ne(a))}})),Je={};"top"===z||"bottom"===z?Je[v?"marginRight":"marginLeft"]=R:Je.marginTop=R;var Ue=K.map((function(e,t){var n=e.key;return a.createElement(G,{id:i,prefixCls:X,key:n,tab:e,style:0===t?void 0:Je,closable:e.closable,editable:m,active:n===c,renderWrapper:T,removeAriaLabel:null==g?void 0:g.removeAriaLabel,onClick:function(e){B(n,e)},onFocus:function(){Qe(n),Xe(),Y.current&&(v||(Y.current.scrollLeft=0),Y.current.scrollTop=0)}})})),Ze=function(){return ze((function(){var e,t=new Map,n=null===(e=Q.current)||void 0===e?void 0:e.getBoundingClientRect();return K.forEach((function(e){var a,o=e.key,r=null===(a=Q.current)||void 0===a?void 0:a.querySelector('[data-node-key="'.concat(P(o),'"]'));if(r){var i=function(e,t){var n=e.offsetWidth,a=e.offsetHeight,o=e.offsetTop,r=e.offsetLeft,i=e.getBoundingClientRect(),l=i.width,c=i.height,d=i.x,s=i.y;return Math.abs(l-n)<1?[l,c,d-t.x,s-t.y]:[n,a,r,o]}(r,n),l=(0,b.A)(i,4),c=l[0],d=l[1],s=l[2],u=l[3];t.set(o,{width:c,height:d,left:s,top:u})}})),t}))};(0,a.useEffect)((function(){Ze()}),[K.map((function(e){return e.key})).join("_")]);var et=_((function(){var e=H(F),t=H(q),n=H(V);ue([e[0]-t[0]-n[0],e[1]-t[1]-n[1]]);var a=H(U);$e(a);var o=H(J);xe(o);var r=H(Q);fe([r[0]-a[0],r[1]-a[1]]),Ze()})),tt=K.slice(0,Ve),nt=K.slice(Ye+1),at=[].concat((0,$.A)(tt),(0,$.A)(nt)),ot=_e.get(c),rt=function(e){var t=e.activeTabOffset,n=e.horizontal,r=e.rtl,i=e.indicator,l=void 0===i?{}:i,c=l.size,d=l.align,s=void 0===d?"center":d,u=(0,a.useState)(),p=(0,b.A)(u,2),v=p[0],f=p[1],m=(0,a.useRef)(),g=o().useCallback((function(e){return"function"==typeof c?c(e):"number"==typeof c?c:e}),[c]);function h(){x.A.cancel(m.current)}return(0,a.useEffect)((function(){var e={};if(t)if(n){e.width=g(t.width);var a=r?"right":"left";"start"===s&&(e[a]=t[a]),"center"===s&&(e[a]=t[a]+t.width/2,e.transform=r?"translateX(50%)":"translateX(-50%)"),"end"===s&&(e[a]=t[a]+t.width,e.transform="translateX(-100%)")}else e.height=g(t.height),"start"===s&&(e.top=t.top),"center"===s&&(e.top=t.top+t.height/2,e.transform="translateY(-50%)"),"end"===s&&(e.top=t.top+t.height,e.transform="translateY(-100%)");return h(),m.current=(0,x.A)((function(){f(e)})),h}),[t,n,r,s,g]),{style:v}}({activeTabOffset:ot,horizontal:Z,indicator:N,rtl:v}).style;(0,a.useEffect)((function(){Qe()}),[c,Be,je,O(ot),O(_e),Z]),(0,a.useEffect)((function(){et()}),[v]);var it,lt,ct,dt,st=!!at.length,ut="".concat(X,"-nav-wrap");return Z?v?(lt=ne>0,it=ne!==je):(it=ne<0,lt=ne!==Be):(ct=ie<0,dt=ie!==Be),a.createElement(y.A,{onResize:et},a.createElement("div",{ref:(0,S.xK)(t,F),role:"tablist",className:d()("".concat(X,"-nav"),n),style:r,onKeyDown:function(){Xe()}},a.createElement(L,{ref:q,position:"left",extra:f,prefixCls:X}),a.createElement(y.A,{onResize:et},a.createElement("div",{className:d()(ut,(0,u.A)((0,u.A)((0,u.A)((0,u.A)({},"".concat(ut,"-ping-left"),it),"".concat(ut,"-ping-right"),lt),"".concat(ut,"-ping-top"),ct),"".concat(ut,"-ping-bottom"),dt)),ref:Y},a.createElement(y.A,{onResize:et},a.createElement("div",{ref:Q,className:"".concat(X,"-nav-list"),style:{transform:"translate(".concat(ne,"px, ").concat(ie,"px)"),transition:He?"none":void 0}},Ue,a.createElement(I,{ref:U,prefixCls:X,locale:g,editable:m,style:(0,p.A)((0,p.A)({},0===Ue.length?void 0:Je),{},{visibility:st?"hidden":null})}),a.createElement("div",{className:d()("".concat(X,"-ink-bar"),(0,u.A)({},"".concat(X,"-ink-bar-animated"),l.inkBar)),style:rt}))))),a.createElement(D,(0,s.A)({},e,{removeAriaLabel:null==g?void 0:g.removeAriaLabel,ref:J,prefixCls:X,tabs:at,className:!st&&Le,tabMoving:!!He})),a.createElement(L,{ref:V,position:"right",extra:f,prefixCls:X})))}));const K=X;var F=a.forwardRef((function(e,t){var n=e.prefixCls,o=e.className,r=e.style,i=e.id,l=e.active,c=e.tabKey,s=e.children;return a.createElement("div",{id:i&&"".concat(i,"-panel-").concat(c),role:"tabpanel",tabIndex:l?0:-1,"aria-labelledby":i&&"".concat(i,"-tab-").concat(c),"aria-hidden":!l,style:r,className:d()(n,l&&"".concat(n,"-active"),o),ref:t},s)}));const q=F;var V=["renderTabBar"],Y=["label","key"];const Q=function(e){var t=e.renderTabBar,n=(0,f.A)(e,V),o=a.useContext(h).tabs;return t?t((0,p.A)((0,p.A)({},n),{},{panes:o.map((function(e){var t=e.label,n=e.key,o=(0,f.A)(e,Y);return a.createElement(q,(0,s.A)({tab:t,key:n,tabKey:n},o))}))}),K):a.createElement(K,n)};var J=n(88816),U=["key","forceRender","style","className","destroyInactiveTabPane"];const Z=function(e){var t=e.id,n=e.activeKey,o=e.animated,r=e.tabPosition,i=e.destroyInactiveTabPane,l=a.useContext(h),c=l.prefixCls,b=l.tabs,v=o.tabPane,m="".concat(c,"-tabpane");return a.createElement("div",{className:d()("".concat(c,"-content-holder"))},a.createElement("div",{className:d()("".concat(c,"-content"),"".concat(c,"-content-").concat(r),(0,u.A)({},"".concat(c,"-content-animated"),v))},b.map((function(e){var r=e.key,l=e.forceRender,c=e.style,u=e.className,b=e.destroyInactiveTabPane,g=(0,f.A)(e,U),h=r===n;return a.createElement(J.Ay,(0,s.A)({key:r,visible:h,forceRender:l,removeOnLeave:!(!i&&!b),leavedClassName:"".concat(m,"-hidden")},o.tabPaneMotion),(function(e,n){var o=e.style,i=e.className;return a.createElement(q,(0,s.A)({},g,{prefixCls:m,id:t,tabKey:r,animated:v,active:h,style:(0,p.A)((0,p.A)({},c),o),className:d()(u,i),ref:n}))}))}))))};n(33717);var ee=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],te=0,ne=a.forwardRef((function(e,t){var n=e.id,o=e.prefixCls,r=void 0===o?"rc-tabs":o,i=e.className,l=e.items,c=e.direction,$=e.activeKey,y=e.defaultActiveKey,A=e.editable,S=e.animated,x=e.tabPosition,k=void 0===x?"top":x,C=e.tabBarGutter,w=e.tabBarStyle,z=e.tabBarExtraContent,_=e.locale,E=e.more,O=e.destroyInactiveTabPane,P=e.renderTabBar,R=e.onChange,T=e.onTabClick,I=e.onTabScroll,L=e.getPopupContainer,B=e.popupClassName,j=e.indicator,N=(0,f.A)(e,ee),M=a.useMemo((function(){return(l||[]).filter((function(e){return e&&"object"===(0,v.A)(e)&&"key"in e}))}),[l]),D="rtl"===c,G=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(e=!1===t?{inkBar:!1,tabPane:!1}:!0===t?{inkBar:!0,tabPane:!1}:(0,p.A)({inkBar:!0},"object"===(0,v.A)(t)?t:{})).tabPaneMotion&&void 0===e.tabPane&&(e.tabPane=!0),!e.tabPaneMotion&&e.tabPane&&(e.tabPane=!1),e}(S),H=(0,a.useState)(!1),W=(0,b.A)(H,2),X=W[0],K=W[1];(0,a.useEffect)((function(){K((0,g.A)())}),[]);var F=(0,m.A)((function(){var e;return null===(e=M[0])||void 0===e?void 0:e.key}),{value:$,defaultValue:y}),q=(0,b.A)(F,2),V=q[0],Y=q[1],J=(0,a.useState)((function(){return M.findIndex((function(e){return e.key===V}))})),U=(0,b.A)(J,2),ne=U[0],ae=U[1];(0,a.useEffect)((function(){var e,t=M.findIndex((function(e){return e.key===V}));-1===t&&(t=Math.max(0,Math.min(ne,M.length-1)),Y(null===(e=M[t])||void 0===e?void 0:e.key)),ae(t)}),[M.map((function(e){return e.key})).join("_"),V,ne]);var oe=(0,m.A)(null,{value:n}),re=(0,b.A)(oe,2),ie=re[0],le=re[1];(0,a.useEffect)((function(){n||(le("rc-tabs-".concat(te)),te+=1)}),[]);var ce={id:ie,activeKey:V,animated:G,tabPosition:k,rtl:D,mobile:X},de=(0,p.A)((0,p.A)({},ce),{},{editable:A,locale:_,more:E,tabBarGutter:C,onTabClick:function(e,t){null==T||T(e,t);var n=e!==V;Y(e),n&&(null==R||R(e))},onTabScroll:I,extra:z,style:w,panes:null,getPopupContainer:L,popupClassName:B,indicator:j});return a.createElement(h.Provider,{value:{tabs:M,prefixCls:r}},a.createElement("div",(0,s.A)({ref:t,id:n,className:d()(r,"".concat(r,"-").concat(k),(0,u.A)((0,u.A)((0,u.A)({},"".concat(r,"-mobile"),X),"".concat(r,"-editable"),A),"".concat(r,"-rtl"),D),i)},N),a.createElement(Q,(0,s.A)({},de,{renderTabBar:P})),a.createElement(Z,(0,s.A)({destroyInactiveTabPane:O},ce,{animated:G}))))}));const ae=ne;var oe=n(80840),re=n(51471),ie=n(31754),le=n(17826);const ce={motionAppear:!1,motionEnter:!0,motionLeave:!0};var de=n(51963),se=n(78052),ue=n(71094),pe=n(52146),be=n(63829),ve=n(30656);const fe=e=>{const{componentCls:t,motionDurationSlow:n}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${n}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${n}`}}}}},[(0,ve._j)(e,"slide-up"),(0,ve._j)(e,"slide-down")]]},me=e=>{const{componentCls:t,tabsCardPadding:n,cardBg:a,cardGutter:o,colorBorderSecondary:r,itemSelectedColor:i}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:n,background:a,border:`${(0,se.zA)(e.lineWidth)} ${e.lineType} ${r}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:i,background:e.colorBgContainer},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:(0,se.zA)(o)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${(0,se.zA)(e.borderRadiusLG)} ${(0,se.zA)(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${(0,se.zA)(e.borderRadiusLG)} ${(0,se.zA)(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:(0,se.zA)(o)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,se.zA)(e.borderRadiusLG)} 0 0 ${(0,se.zA)(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,se.zA)(e.borderRadiusLG)} ${(0,se.zA)(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},ge=e=>{const{componentCls:t,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},(0,ue.dF)(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${(0,se.zA)(a)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},ue.L9),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${(0,se.zA)(e.paddingXXS)} ${(0,se.zA)(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},he=e=>{const{componentCls:t,margin:n,colorBorderSecondary:a,horizontalMargin:o,verticalItemPadding:r,verticalItemMargin:i,calc:l}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${(0,se.zA)(e.lineWidth)} ${e.lineType} ${a}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},\n            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,\n        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:l(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:r,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:i},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:(0,se.zA)(l(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${(0,se.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:l(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${(0,se.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},$e=e=>{const{componentCls:t,cardPaddingSM:n,cardPaddingLG:a,horizontalItemPaddingSM:o,horizontalItemPaddingLG:r}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:o,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:r,fontSize:e.titleFontSizeLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:n}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${(0,se.zA)(e.borderRadius)} ${(0,se.zA)(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${(0,se.zA)(e.borderRadius)} ${(0,se.zA)(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,se.zA)(e.borderRadius)} ${(0,se.zA)(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,se.zA)(e.borderRadius)} 0 0 ${(0,se.zA)(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:a}}}}}},ye=e=>{const{componentCls:t,itemActiveColor:n,itemHoverColor:a,iconCls:o,tabsHorizontalItemMargin:r,horizontalItemPadding:i,itemSelectedColor:l,itemColor:c}=e,d=`${t}-tab`;return{[d]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:i,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:c,"&-btn, &-remove":Object.assign({"&:focus:not(:focus-visible), &:active":{color:n}},(0,ue.K8)(e)),"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${d}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":{flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},"&:hover":{color:a},[`&${d}-active ${d}-btn`]:{color:l,textShadow:e.tabsActiveTextShadow},[`&${d}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${d}-disabled ${d}-btn, &${d}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${d}-remove ${o}`]:{margin:0},[`${o}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${d} + ${d}`]:{margin:{_skip_check_:!0,value:r}}}},Ae=e=>{const{componentCls:t,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:o,calc:r}=e,i=`${t}-rtl`;return{[i]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:n},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,se.zA)(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:(0,se.zA)(e.marginXS)},marginLeft:{_skip_check_:!0,value:(0,se.zA)(r(e.marginXXS).mul(-1).equal())},[a]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},Se=e=>{const{componentCls:t,tabsCardPadding:n,cardHeight:a,cardGutter:o,itemHoverColor:r,itemActiveColor:i,colorBorderSecondary:l}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,ue.dF)(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:n,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:a,minHeight:a,marginLeft:{_skip_check_:!0,value:o},padding:`0 ${(0,se.zA)(e.paddingXS)}`,background:"transparent",border:`${(0,se.zA)(e.lineWidth)} ${e.lineType} ${l}`,borderRadius:`${(0,se.zA)(e.borderRadiusLG)} ${(0,se.zA)(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:r},"&:active, &:focus:not(:focus-visible)":{color:i}},(0,ue.K8)(e))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),ye(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:{outline:"none","&-hidden":{display:"none"}}}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping'])`]:{justifyContent:"center"}}}}}},xe=(0,pe.OF)("Tabs",(e=>{const t=(0,be.h1)(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${(0,se.zA)(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${(0,se.zA)(e.horizontalItemGutter)}`});return[$e(t),Ae(t),he(t),ge(t),me(t),Se(t),fe(t)]}),(e=>{const t=e.controlHeightLG;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:t,cardPadding:`${(t-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${1.5*e.paddingXXS}px ${e.padding}px`,cardPaddingLG:`${e.paddingXS}px ${e.padding}px ${1.5*e.paddingXXS}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}}));const ke=e=>{var t,n,o,c,s,u,p,b,v,f,m;const{type:g,className:h,rootClassName:$,size:y,onEdit:A,hideAdd:S,centered:x,addIcon:k,removeIcon:C,moreIcon:w,more:z,popupClassName:_,children:E,items:O,animated:P,style:R,indicatorSize:T,indicator:I}=e,L=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n}(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator"]),{prefixCls:B}=L,{direction:j,tabs:N,getPrefixCls:M,getPopupContainer:D}=a.useContext(oe.QO),G=M("tabs",B),H=(0,re.A)(G),[W,X,K]=xe(G,H);let F;"editable-card"===g&&(F={onEdit:(e,t)=>{let{key:n,event:a}=t;null==A||A("add"===e?a:n,e)},removeIcon:null!==(t=null!=C?C:null==N?void 0:N.removeIcon)&&void 0!==t?t:a.createElement(r.A,null),addIcon:(null!=k?k:null==N?void 0:N.addIcon)||a.createElement(l.A,null),showAdd:!0!==S});const q=M(),V=(0,ie.A)(y),Y=function(e,t){return e||function(e){return e.filter((e=>e))}((0,de.A)(t).map((e=>{if(a.isValidElement(e)){const{key:t,props:n}=e,a=n||{},{tab:o}=a,r=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n}(a,["tab"]);return Object.assign(Object.assign({key:String(t)},r),{label:o})}return null})))}(O,E),Q=function(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{inkBar:!0,tabPane:!1};return t=!1===n?{inkBar:!1,tabPane:!1}:!0===n?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof n?n:{}),t.tabPane&&(t.tabPaneMotion=Object.assign(Object.assign({},ce),{motionName:(0,le.b)(e,"switch")})),t}(G,P),J=Object.assign(Object.assign({},null==N?void 0:N.style),R),U={align:null!==(n=null==I?void 0:I.align)&&void 0!==n?n:null===(o=null==N?void 0:N.indicator)||void 0===o?void 0:o.align,size:null!==(p=null!==(s=null!==(c=null==I?void 0:I.size)&&void 0!==c?c:T)&&void 0!==s?s:null===(u=null==N?void 0:N.indicator)||void 0===u?void 0:u.size)&&void 0!==p?p:null==N?void 0:N.indicatorSize};return W(a.createElement(ae,Object.assign({direction:j,getPopupContainer:D},L,{items:Y,className:d()({[`${G}-${V}`]:V,[`${G}-card`]:["card","editable-card"].includes(g),[`${G}-editable-card`]:"editable-card"===g,[`${G}-centered`]:x},null==N?void 0:N.className,h,$,X,K,H),popupClassName:d()(_,X,K,H),style:J,editable:F,more:Object.assign({icon:null!==(m=null!==(f=null!==(v=null===(b=null==N?void 0:N.more)||void 0===b?void 0:b.icon)&&void 0!==v?v:null==N?void 0:N.moreIcon)&&void 0!==f?f:w)&&void 0!==m?m:a.createElement(i.A,null),transitionName:`${q}-slide-up`},z),prefixCls:G,animated:Q,indicator:U})))};ke.TabPane=()=>null;const Ce=ke}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.19/b5411f71d6c238924e521f522b75a371/799.lite.js.map
