"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[97],{95962:(e,t,o)=>{o.d(t,{A:()=>_});var n=o(41594),r=o(52066),i=o(65924),l=o.n(i),a=o(573),d=o(52733),s=o(74188),c=o(15220),m=o(51628),u=o(54176),p=o(42182),g=o(79045),b=o(82606),$=o(26623),v=o(80840),h=o(51471),f=o(3589),C=o(30941),I=o(50969),S=o(78052),y=o(71094),w=o(30656),B=o(75752),x=o(58542),O=o(70136),z=o(67142),A=o(52146),k=o(63829);const j=e=>{const{componentCls:t,menuCls:o,colorError:n,colorTextLightSolid:r}=e,i=`${o}-item`;return{[`${t}, ${t}-menu-submenu`]:{[`${o} ${i}`]:{[`&${i}-danger:not(${i}-disabled)`]:{color:n,"&:hover":{color:r,backgroundColor:n}}}}}},E=e=>{const{componentCls:t,menuCls:o,zIndexPopup:n,dropdownArrowDistance:r,sizePopupArrow:i,antCls:l,iconCls:a,motionDurationMid:d,paddingBlock:s,fontSize:c,dropdownEdgeChildPadding:m,colorTextDisabled:u,fontSizeIcon:p,controlPaddingHorizontal:g,colorBgElevated:b}=e;return[{[t]:Object.assign(Object.assign({},(0,y.dF)(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:n,display:"block","&::before":{position:"absolute",insetBlock:e.calc(i).div(2).sub(r).equal(),zIndex:-9999,opacity:1e-4,content:'""'},[`&-trigger${l}-btn`]:{[`& > ${a}-down, & > ${l}-btn-icon > ${a}-down`]:{fontSize:p}},[`${t}-wrap`]:{position:"relative",[`${l}-btn > ${a}-down`]:{fontSize:p},[`${a}-down::before`]:{transition:`transform ${d}`}},[`${t}-wrap-open`]:{[`${a}-down::before`]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},[`&${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottomLeft,\n          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottomLeft,\n          &${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottom,\n          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottom,\n          &${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottomRight,\n          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottomRight`]:{animationName:w.ox},[`&${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-topLeft,\n          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-topLeft,\n          &${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-top,\n          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-top,\n          &${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-topRight,\n          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-topRight`]:{animationName:w.nP},[`&${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottomLeft,\n          &${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottom,\n          &${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottomRight`]:{animationName:w.vR},[`&${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-topLeft,\n          &${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-top,\n          &${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-topRight`]:{animationName:w.YU}})},(0,O.Ay)(e,b,{arrowPlacement:{top:!0,bottom:!0}}),{[`${t} ${o}`]:{position:"relative",margin:0},[`${o}-submenu-popup`]:{position:"absolute",zIndex:n,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${t}, ${t}-menu-submenu`]:{[o]:Object.assign(Object.assign({padding:m,listStyleType:"none",backgroundColor:b,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},(0,y.K8)(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${o}-item-group-title`]:{padding:`${(0,S.zA)(s)} ${(0,S.zA)(g)}`,color:e.colorTextDescription,transition:`all ${d}`},[`${o}-item`]:{position:"relative",display:"flex",alignItems:"center"},[`${o}-item-icon`]:{minWidth:c,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${o}-title-content`]:{flex:"auto","> a":{color:"inherit",transition:`all ${d}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}}},[`${o}-item, ${o}-submenu-title`]:Object.assign(Object.assign({clear:"both",margin:0,padding:`${(0,S.zA)(s)} ${(0,S.zA)(g)}`,color:e.colorText,fontWeight:"normal",fontSize:c,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${d}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},(0,y.K8)(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:u,cursor:"not-allowed","&:hover":{color:u,backgroundColor:b,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${(0,S.zA)(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${t}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${t}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorTextDescription,fontSize:p,fontStyle:"normal"}}}),[`${o}-item-group-list`]:{margin:`0 ${(0,S.zA)(e.marginXS)}`,padding:0,listStyle:"none"},[`${o}-submenu-title`]:{paddingInlineEnd:e.calc(g).add(e.fontSizeSM).equal()},[`${o}-submenu-vertical`]:{position:"relative"},[`${o}-submenu${o}-submenu-disabled ${t}-menu-submenu-title`]:{[`&, ${t}-menu-submenu-arrow-icon`]:{color:u,backgroundColor:b,cursor:"not-allowed"}},[`${o}-submenu-selected ${t}-menu-submenu-title`]:{color:e.colorPrimary}})}},[(0,w._j)(e,"slide-up"),(0,w._j)(e,"slide-down"),(0,B.Mh)(e,"move-up"),(0,B.Mh)(e,"move-down"),(0,x.aB)(e,"zoom-big")]]},H=(0,A.OF)("Dropdown",(e=>{const{marginXXS:t,sizePopupArrow:o,paddingXXS:n,componentCls:r}=e,i=(0,k.h1)(e,{menuCls:`${r}-menu`,dropdownArrowDistance:e.calc(o).div(2).add(t).equal(),dropdownEdgeChildPadding:n});return[E(i),j(i)]}),(e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},(0,O.Ke)({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),(0,z.n)(e))),{resetStyle:!1}),T=e=>{const{menu:t,arrow:o,prefixCls:i,children:p,trigger:S,disabled:y,dropdownRender:w,getPopupContainer:B,overlayClassName:x,rootClassName:O,overlayStyle:z,open:A,onOpenChange:k,visible:j,onVisibleChange:E,mouseEnterDelay:T=.15,mouseLeaveDelay:D=.1,autoAdjustOverflow:P=!0,placement:N="",overlay:R,transitionName:M}=e,{getPopupContainer:L,getPrefixCls:W,direction:X,dropdown:q}=n.useContext(v.QO);(0,b.rJ)("Dropdown");const _=n.useMemo((()=>{const e=W();return void 0!==M?M:N.includes("top")?`${e}-slide-down`:`${e}-slide-up`}),[W,N,M]),G=n.useMemo((()=>N?N.includes("Center")?N.slice(0,N.indexOf("Center")):N:"rtl"===X?"bottomRight":"bottomLeft"),[N,X]),Y=W("dropdown",i),F=(0,h.A)(Y),[V,K,Q]=H(Y,F),[,U]=(0,I.Ay)(),J=n.Children.only(p),Z=(0,g.Ob)(J,{className:l()(`${Y}-trigger`,{[`${Y}-rtl`]:"rtl"===X},J.props.className),disabled:y}),ee=y?[]:S;let te;ee&&ee.includes("contextMenu")&&(te=!0);const[oe,ne]=(0,s.A)(!1,{value:null!=A?A:j}),re=(0,d._q)((e=>{null==k||k(e,{source:"trigger"}),null==E||E(e),ne(e)})),ie=l()(x,O,K,Q,F,null==q?void 0:q.className,{[`${Y}-rtl`]:"rtl"===X}),le=(0,u.A)({arrowPointAtCenter:"object"==typeof o&&o.pointAtCenter,autoAdjustOverflow:P,offset:U.marginXXS,arrowWidth:o?U.sizePopupArrow:0,borderRadius:U.borderRadius}),ae=n.useCallback((()=>{(null==t?void 0:t.selectable)&&(null==t?void 0:t.multiple)||(null==k||k(!1,{source:"menu"}),ne(!1))}),[null==t?void 0:t.selectable,null==t?void 0:t.multiple]),[de,se]=(0,m.YK)("Dropdown",null==z?void 0:z.zIndex);let ce=n.createElement(a.A,Object.assign({alignPoint:te},(0,c.A)(e,["rootClassName"]),{mouseEnterDelay:T,mouseLeaveDelay:D,visible:oe,builtinPlacements:le,arrow:!!o,overlayClassName:ie,prefixCls:Y,getPopupContainer:B||L,transitionName:_,trigger:ee,overlay:()=>{let e;return e=(null==t?void 0:t.items)?n.createElement(f.A,Object.assign({},t)):"function"==typeof R?R():R,w&&(e=w(e)),e=n.Children.only("string"==typeof e?n.createElement("span",null,e):e),n.createElement(C.A,{prefixCls:`${Y}-menu`,rootClassName:l()(Q,F),expandIcon:n.createElement("span",{className:`${Y}-menu-submenu-arrow`},n.createElement(r.A,{className:`${Y}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:ae,validator:e=>{let{mode:t}=e}},e)},placement:G,onVisibleChange:re,overlayStyle:Object.assign(Object.assign(Object.assign({},null==q?void 0:q.style),z),{zIndex:de})}),Z);return de&&(ce=n.createElement($.A.Provider,{value:se},ce)),V(ce)},D=(0,p.A)(T,"dropdown",(e=>e),(function(e){return Object.assign(Object.assign({},e),{align:{overflow:{adjustX:!1,adjustY:!1}}})}));T._InternalPanelDoNotUseOrYouWillBeFired=e=>n.createElement(D,Object.assign({},e),n.createElement("span",null));const P=T;var N=o(85081),R=o(57333),M=o(65824),L=o(15460);const W=e=>{const{getPopupContainer:t,getPrefixCls:o,direction:r}=n.useContext(v.QO),{prefixCls:i,type:a="default",danger:d,disabled:s,loading:c,onClick:m,htmlType:u,children:p,className:g,menu:b,arrow:$,autoFocus:h,overlay:f,trigger:C,align:I,open:S,onOpenChange:y,placement:w,getPopupContainer:B,href:x,icon:O=n.createElement(N.A,null),title:z,buttonsRender:A=(e=>e),mouseEnterDelay:k,mouseLeaveDelay:j,overlayClassName:E,overlayStyle:H,destroyPopupOnHide:T,dropdownRender:D}=e,W=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o}(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyPopupOnHide","dropdownRender"]),X=o("dropdown",i),q=`${X}-button`,_={menu:b,arrow:$,autoFocus:h,align:I,disabled:s,trigger:s?[]:C,onOpenChange:y,getPopupContainer:B||t,mouseEnterDelay:k,mouseLeaveDelay:j,overlayClassName:E,overlayStyle:H,destroyPopupOnHide:T,dropdownRender:D},{compactSize:G,compactItemClassnames:Y}=(0,L.RQ)(X,r),F=l()(q,Y,g);"overlay"in e&&(_.overlay=f),"open"in e&&(_.open=S),_.placement="placement"in e?w:"rtl"===r?"bottomLeft":"bottomRight";const V=n.createElement(R.Ay,{type:a,danger:d,disabled:s,loading:c,onClick:m,htmlType:u,href:x,title:z},p),K=n.createElement(R.Ay,{type:a,danger:d,icon:O}),[Q,U]=A([V,K]);return n.createElement(M.A.Compact,Object.assign({className:F,size:G,block:!0},W),Q,n.createElement(P,Object.assign({},_),U))};W.__ANT_BUTTON=!0;const X=W,q=P;q.Button=X;const _=q},30941:(e,t,o)=>{o.d(t,{A:()=>a,h:()=>d});var n=o(41594),r=o(52733),i=o(15460);const l=n.createContext(null),a=n.forwardRef(((e,t)=>{const{children:o}=e,a=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o}(e,["children"]),d=n.useContext(l),s=n.useMemo((()=>Object.assign(Object.assign({},d),a)),[d,a.prefixCls,a.mode,a.selectable,a.rootClassName]),c=(0,r.H3)(o),m=(0,r.xK)(t,c?o.ref:null);return n.createElement(l.Provider,{value:s},n.createElement(i.K6,null,c?n.cloneElement(o,{ref:m}):o))})),d=l},3589:(e,t,o)=>{o.d(t,{A:()=>F});var n=o(41594),r=o(5446),i=o(65924),l=o.n(i);const a=n.createContext({});var d=o(85081),s=o(52733),c=o(15220),m=o(17826),u=o(79045),p=o(80840),g=o(51471);const b=e=>{const{prefixCls:t,className:o,dashed:i}=e,a=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o}(e,["prefixCls","className","dashed"]),{getPrefixCls:d}=n.useContext(p.QO),s=d("menu",t),c=l()({[`${s}-item-divider-dashed`]:!!i},o);return n.createElement(r.cG,Object.assign({className:c},a))},$=(0,n.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var v=o(51963),h=o(64715);const f=e=>{var t;const{className:o,children:i,icon:d,title:s,danger:m}=e,{prefixCls:p,firstLevel:g,direction:b,disableMenuItemTitleTooltip:f,inlineCollapsed:C}=n.useContext($),{siderCollapsed:I}=n.useContext(a);let S=s;void 0===s?S=g?i:"":!1===s&&(S="");const y={title:S};I||C||(y.title=null,y.open=!1);const w=(0,v.A)(i).length;let B=n.createElement(r.q7,Object.assign({},(0,c.A)(e,["title","icon","danger"]),{className:l()({[`${p}-item-danger`]:m,[`${p}-item-only-child`]:1===(d?w+1:w)},o),title:"string"==typeof s?s:void 0}),(0,u.Ob)(d,{className:l()(n.isValidElement(d)?null===(t=d.props)||void 0===t?void 0:t.className:"",`${p}-item-icon`)}),(e=>{const t=n.createElement("span",{className:`${p}-title-content`},i);return(!d||n.isValidElement(i)&&"span"===i.type)&&i&&e&&g&&"string"==typeof i?n.createElement("div",{className:`${p}-inline-collapsed-noicon`},i.charAt(0)):t})(C));return f||(B=n.createElement(h.A,Object.assign({},y,{placement:"rtl"===b?"left":"right",overlayClassName:`${p}-inline-collapsed-tooltip`}),B)),B};var C=o(30941),I=o(78052),S=o(26411),y=o(71094),w=o(81170),B=o(30656),x=o(58542),O=o(52146),z=o(63829);const A=e=>{const{componentCls:t,motionDurationSlow:o,horizontalLineHeight:n,colorSplit:r,lineWidth:i,lineType:l,itemPaddingInline:a}=e;return{[`${t}-horizontal`]:{lineHeight:n,border:0,borderBottom:`${(0,I.zA)(i)} ${l} ${r}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:a},[`> ${t}-item:hover,\n        > ${t}-item-active,\n        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:[`border-color ${o}`,`background ${o}`].join(",")},[`${t}-submenu-arrow`]:{display:"none"}}}},k=e=>{let{componentCls:t,menuArrowOffset:o,calc:n}=e;return{[`${t}-rtl`]:{direction:"rtl"},[`${t}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${t}-rtl${t}-vertical,\n    ${t}-submenu-rtl ${t}-vertical`]:{[`${t}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${(0,I.zA)(n(o).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${(0,I.zA)(o)})`}}}}},j=e=>Object.assign({},(0,y.jk)(e)),E=(e,t)=>{const{componentCls:o,itemColor:n,itemSelectedColor:r,groupTitleColor:i,itemBg:l,subMenuItemBg:a,itemSelectedBg:d,activeBarHeight:s,activeBarWidth:c,activeBarBorderWidth:m,motionDurationSlow:u,motionEaseInOut:p,motionEaseOut:g,itemPaddingInline:b,motionDurationMid:$,itemHoverColor:v,lineType:h,colorSplit:f,itemDisabledColor:C,dangerItemColor:S,dangerItemHoverColor:y,dangerItemSelectedColor:w,dangerItemActiveBg:B,dangerItemSelectedBg:x,popupBg:O,itemHoverBg:z,itemActiveBg:A,menuSubMenuBg:k,horizontalItemSelectedColor:E,horizontalItemSelectedBg:H,horizontalItemBorderRadius:T,horizontalItemHoverBg:D}=e;return{[`${o}-${t}, ${o}-${t} > ${o}`]:{color:n,background:l,[`&${o}-root:focus-visible`]:Object.assign({},j(e)),[`${o}-item-group-title`]:{color:i},[`${o}-submenu-selected`]:{[`> ${o}-submenu-title`]:{color:r}},[`${o}-item, ${o}-submenu-title`]:{color:n,[`&:not(${o}-item-disabled):focus-visible`]:Object.assign({},j(e))},[`${o}-item-disabled, ${o}-submenu-disabled`]:{color:`${C} !important`},[`${o}-item:not(${o}-item-selected):not(${o}-submenu-selected)`]:{[`&:hover, > ${o}-submenu-title:hover`]:{color:v}},[`&:not(${o}-horizontal)`]:{[`${o}-item:not(${o}-item-selected)`]:{"&:hover":{backgroundColor:z},"&:active":{backgroundColor:A}},[`${o}-submenu-title`]:{"&:hover":{backgroundColor:z},"&:active":{backgroundColor:A}}},[`${o}-item-danger`]:{color:S,[`&${o}-item:hover`]:{[`&:not(${o}-item-selected):not(${o}-submenu-selected)`]:{color:y}},[`&${o}-item:active`]:{background:B}},[`${o}-item a`]:{"&, &:hover":{color:"inherit"}},[`${o}-item-selected`]:{color:r,[`&${o}-item-danger`]:{color:w},"a, a:hover":{color:"inherit"}},[`& ${o}-item-selected`]:{backgroundColor:d,[`&${o}-item-danger`]:{backgroundColor:x}},[`&${o}-submenu > ${o}`]:{backgroundColor:k},[`&${o}-popup > ${o}`]:{backgroundColor:O},[`&${o}-submenu-popup > ${o}`]:{backgroundColor:O},[`&${o}-horizontal`]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{[`> ${o}-item, > ${o}-submenu`]:{top:m,marginTop:e.calc(m).mul(-1).equal(),marginBottom:0,borderRadius:T,"&::after":{position:"absolute",insetInline:b,bottom:0,borderBottom:`${(0,I.zA)(s)} solid transparent`,transition:`border-color ${u} ${p}`,content:'""'},"&:hover, &-active, &-open":{background:D,"&::after":{borderBottomWidth:s,borderBottomColor:E}},"&-selected":{color:E,backgroundColor:H,"&:hover":{backgroundColor:H},"&::after":{borderBottomWidth:s,borderBottomColor:E}}}}),[`&${o}-root`]:{[`&${o}-inline, &${o}-vertical`]:{borderInlineEnd:`${(0,I.zA)(m)} ${h} ${f}`}},[`&${o}-inline`]:{[`${o}-sub${o}-inline`]:{background:a},[`${o}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${(0,I.zA)(c)} solid ${r}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${$} ${g}`,`opacity ${$} ${g}`].join(","),content:'""'},[`&${o}-item-danger`]:{"&::after":{borderInlineEndColor:w}}},[`${o}-selected, ${o}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${$} ${p}`,`opacity ${$} ${p}`].join(",")}}}}}},H=e=>{const{componentCls:t,itemHeight:o,itemMarginInline:n,padding:r,menuArrowSize:i,marginXS:l,itemMarginBlock:a,itemWidth:d}=e,s=e.calc(i).add(r).add(l).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:o,lineHeight:(0,I.zA)(o),paddingInline:r,overflow:"hidden",textOverflow:"ellipsis",marginInline:n,marginBlock:a,width:d},[`> ${t}-item,\n            > ${t}-submenu > ${t}-submenu-title`]:{height:o,lineHeight:(0,I.zA)(o)},[`${t}-item-group-list ${t}-submenu-title,\n            ${t}-submenu-title`]:{paddingInlineEnd:s}}},T=e=>{const{componentCls:t,iconCls:o,itemHeight:n,colorTextLightSolid:r,dropdownWidth:i,controlHeightLG:l,motionDurationMid:a,motionEaseOut:d,paddingXL:s,itemMarginInline:c,fontSizeLG:m,motionDurationSlow:u,paddingXS:p,boxShadowSecondary:g,collapsedWidth:b,collapsedIconSize:$}=e,v={height:n,lineHeight:(0,I.zA)(n),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},H(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},H(e)),{boxShadow:g})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:i,maxHeight:`calc(100vh - ${(0,I.zA)(e.calc(l).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${u}`,`background ${u}`,`padding ${a} ${d}`,`padding-inline calc(50% - ${(0,I.zA)(e.calc(m).div(2).equal())} - ${(0,I.zA)(c)})`].join(","),[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:v,[`& ${t}-item-group-title`]:{paddingInlineStart:s}},[`${t}-item`]:v}},{[`${t}-inline-collapsed`]:{width:b,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:m,textAlign:"center"}}},[`> ${t}-item,\n          > ${t}-item-group > ${t}-item-group-list > ${t}-item,\n          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,\n          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${(0,I.zA)(e.calc(m).div(2).equal())} - ${(0,I.zA)(c)})`,textOverflow:"clip",[`\n            ${t}-submenu-arrow,\n            ${t}-submenu-expand-icon\n          `]:{opacity:0},[`${t}-item-icon, ${o}`]:{margin:0,fontSize:$,lineHeight:(0,I.zA)(n),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${o}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${o}`]:{display:"none"},"a, a:hover":{color:r}},[`${t}-item-group-title`]:Object.assign(Object.assign({},y.L9),{paddingInline:p})}}]},D=e=>{const{componentCls:t,motionDurationSlow:o,motionDurationMid:n,motionEaseInOut:r,motionEaseOut:i,iconCls:l,iconSize:a,iconMarginInlineEnd:d}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${o}`,`background ${o}`,`padding ${o} ${r}`].join(","),[`${t}-item-icon, ${l}`]:{minWidth:a,fontSize:a,transition:[`font-size ${n} ${i}`,`margin ${o} ${r}`,`color ${o}`].join(","),"+ span":{marginInlineStart:d,opacity:1,transition:[`opacity ${o} ${r}`,`margin ${o}`,`color ${o}`].join(",")}},[`${t}-item-icon`]:Object.assign({},(0,y.Nk)()),[`&${t}-item-only-child`]:{[`> ${l}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},P=e=>{const{componentCls:t,motionDurationSlow:o,motionEaseInOut:n,borderRadius:r,menuArrowSize:i,menuArrowOffset:l}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:i,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${o} ${n}, opacity ${o}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(i).mul(.6).equal(),height:e.calc(i).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:r,transition:[`background ${o} ${n}`,`transform ${o} ${n}`,`top ${o} ${n}`,`color ${o} ${n}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(${(0,I.zA)(e.calc(l).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${(0,I.zA)(l)})`}}}}},N=e=>{const{antCls:t,componentCls:o,fontSize:n,motionDurationSlow:r,motionDurationMid:i,motionEaseInOut:l,paddingXS:a,padding:d,colorSplit:s,lineWidth:c,zIndexPopup:m,borderRadiusLG:u,subMenuItemBorderRadius:p,menuArrowSize:g,menuArrowOffset:b,lineType:$,groupTitleLineHeight:v,groupTitleFontSize:h}=e;return[{"":{[`${o}`]:Object.assign(Object.assign({},(0,y.t6)()),{"&-hidden":{display:"none"}})},[`${o}-submenu-hidden`]:{display:"none"}},{[o]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,y.dF)(e)),(0,y.t6)()),{marginBottom:0,paddingInlineStart:0,fontSize:n,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${r} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${o}-item`]:{flex:"none"}},[`${o}-item, ${o}-submenu, ${o}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${o}-item-group-title`]:{padding:`${(0,I.zA)(a)} ${(0,I.zA)(d)}`,fontSize:h,lineHeight:v,transition:`all ${r}`},[`&-horizontal ${o}-submenu`]:{transition:[`border-color ${r} ${l}`,`background ${r} ${l}`].join(",")},[`${o}-submenu, ${o}-submenu-inline`]:{transition:[`border-color ${r} ${l}`,`background ${r} ${l}`,`padding ${i} ${l}`].join(",")},[`${o}-submenu ${o}-sub`]:{cursor:"initial",transition:[`background ${r} ${l}`,`padding ${r} ${l}`].join(",")},[`${o}-title-content`]:{transition:`color ${r}`,[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"}},[`${o}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${o}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:s,borderStyle:$,borderWidth:0,borderTopWidth:c,marginBlock:c,padding:0,"&-dashed":{borderStyle:"dashed"}}}),D(e)),{[`${o}-item-group`]:{[`${o}-item-group-list`]:{margin:0,padding:0,[`${o}-item, ${o}-submenu-title`]:{paddingInline:`${(0,I.zA)(e.calc(n).mul(2).equal())} ${(0,I.zA)(d)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:m,borderRadius:u,boxShadow:"none",transformOrigin:"0 0",[`&${o}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${o}`]:Object.assign(Object.assign(Object.assign({borderRadius:u},D(e)),P(e)),{[`${o}-item, ${o}-submenu > ${o}-submenu-title`]:{borderRadius:p},[`${o}-submenu-title::after`]:{transition:`transform ${r} ${l}`}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),P(e)),{[`&-inline-collapsed ${o}-submenu-arrow,\n        &-inline ${o}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${(0,I.zA)(b)})`},"&::after":{transform:`rotate(45deg) translateX(${(0,I.zA)(e.calc(b).mul(-1).equal())})`}},[`${o}-submenu-open${o}-submenu-inline > ${o}-submenu-title > ${o}-submenu-arrow`]:{transform:`translateY(${(0,I.zA)(e.calc(g).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${(0,I.zA)(e.calc(b).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${(0,I.zA)(b)})`}}})},{[`${t}-layout-header`]:{[o]:{lineHeight:"inherit"}}}]},R=e=>{var t,o,n;const{colorPrimary:r,colorError:i,colorTextDisabled:l,colorErrorBg:a,colorText:d,colorTextDescription:s,colorBgContainer:c,colorFillAlter:m,colorFillContent:u,lineWidth:p,lineWidthBold:g,controlItemBgActive:b,colorBgTextHover:$,controlHeightLG:v,lineHeight:h,colorBgElevated:f,marginXXS:C,padding:I,fontSize:y,controlHeightSM:w,fontSizeLG:B,colorTextLightSolid:x,colorErrorHover:O}=e,z=null!==(t=e.activeBarWidth)&&void 0!==t?t:0,A=null!==(o=e.activeBarBorderWidth)&&void 0!==o?o:p,k=null!==(n=e.itemMarginInline)&&void 0!==n?n:e.marginXXS,j=new S.q(x).setAlpha(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:d,itemColor:d,colorItemTextHover:d,itemHoverColor:d,colorItemTextHoverHorizontal:r,horizontalItemHoverColor:r,colorGroupTitle:s,groupTitleColor:s,colorItemTextSelected:r,itemSelectedColor:r,colorItemTextSelectedHorizontal:r,horizontalItemSelectedColor:r,colorItemBg:c,itemBg:c,colorItemBgHover:$,itemHoverBg:$,colorItemBgActive:u,itemActiveBg:b,colorSubItemBg:m,subMenuItemBg:m,colorItemBgSelected:b,itemSelectedBg:b,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:z,colorActiveBarHeight:g,activeBarHeight:g,colorActiveBarBorderSize:p,activeBarBorderWidth:A,colorItemTextDisabled:l,itemDisabledColor:l,colorDangerItemText:i,dangerItemColor:i,colorDangerItemTextHover:i,dangerItemHoverColor:i,colorDangerItemTextSelected:i,dangerItemSelectedColor:i,colorDangerItemBgActive:a,dangerItemActiveBg:a,colorDangerItemBgSelected:a,dangerItemSelectedBg:a,itemMarginInline:k,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:v,groupTitleLineHeight:h,collapsedWidth:2*v,popupBg:f,itemMarginBlock:C,itemPaddingInline:I,horizontalLineHeight:1.15*v+"px",iconSize:y,iconMarginInlineEnd:w-y,collapsedIconSize:B,groupTitleFontSize:y,darkItemDisabledColor:new S.q(x).setAlpha(.25).toRgbString(),darkItemColor:j,darkDangerItemColor:i,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:x,darkItemSelectedBg:r,darkDangerItemSelectedBg:i,darkItemHoverBg:"transparent",darkGroupTitleColor:j,darkItemHoverColor:x,darkDangerItemHoverColor:O,darkDangerItemSelectedColor:x,darkDangerItemActiveBg:i,itemWidth:z?`calc(100% + ${A}px)`:`calc(100% - ${2*k}px)`}},M=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return(0,O.OF)("Menu",(e=>{const{colorBgElevated:t,controlHeightLG:o,fontSize:n,darkItemColor:r,darkDangerItemColor:i,darkItemBg:l,darkSubMenuItemBg:a,darkItemSelectedColor:d,darkItemSelectedBg:s,darkDangerItemSelectedBg:c,darkItemHoverBg:m,darkGroupTitleColor:u,darkItemHoverColor:p,darkItemDisabledColor:g,darkDangerItemHoverColor:b,darkDangerItemSelectedColor:$,darkDangerItemActiveBg:v,popupBg:h,darkPopupBg:f}=e,C=e.calc(n).div(7).mul(5).equal(),I=(0,z.h1)(e,{menuArrowSize:C,menuHorizontalHeight:e.calc(o).mul(1.15).equal(),menuArrowOffset:e.calc(C).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:h}),S=(0,z.h1)(I,{itemColor:r,itemHoverColor:p,groupTitleColor:u,itemSelectedColor:d,itemBg:l,popupBg:f,subMenuItemBg:a,itemActiveBg:"transparent",itemSelectedBg:s,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:m,itemDisabledColor:g,dangerItemColor:i,dangerItemHoverColor:b,dangerItemSelectedColor:$,dangerItemActiveBg:v,dangerItemSelectedBg:c,menuSubMenuBg:a,horizontalItemSelectedColor:d,horizontalItemSelectedBg:s});return[N(I),A(I),T(I),E(I,"light"),E(S,"dark"),k(I),(0,w.A)(I),(0,B._j)(I,"slide-up"),(0,B._j)(I,"slide-down"),(0,x.aB)(I,"zoom-big")]}),R,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:o,unitless:{groupTitleLineHeight:!0}})(e,t)};var L=o(51628);const W=e=>{var t;const{popupClassName:o,icon:i,title:a,theme:d}=e,s=n.useContext($),{prefixCls:m,inlineCollapsed:p,theme:g}=s,b=(0,r.Wj)();let v;if(i){const e=n.isValidElement(a)&&"span"===a.type;v=n.createElement(n.Fragment,null,(0,u.Ob)(i,{className:l()(n.isValidElement(i)?null===(t=i.props)||void 0===t?void 0:t.className:"",`${m}-item-icon`)}),e?a:n.createElement("span",{className:`${m}-title-content`},a))}else v=p&&!b.length&&a&&"string"==typeof a?n.createElement("div",{className:`${m}-inline-collapsed-noicon`},a.charAt(0)):n.createElement("span",{className:`${m}-title-content`},a);const h=n.useMemo((()=>Object.assign(Object.assign({},s),{firstLevel:!1})),[s]),[f]=(0,L.YK)("Menu");return n.createElement($.Provider,{value:h},n.createElement(r.g8,Object.assign({},(0,c.A)(e,["icon"]),{title:v,popupClassName:l()(m,o,`${m}-${d||g}`),popupStyle:{zIndex:f}})))};function X(e){return null===e||!1===e}const q={item:f,submenu:W,divider:b},_=(0,n.forwardRef)(((e,t)=>{var o;const i=n.useContext(C.h),a=i||{},{getPrefixCls:b,getPopupContainer:v,direction:h,menu:f}=n.useContext(p.QO),I=b(),{prefixCls:S,className:y,style:w,theme:B="light",expandIcon:x,_internalDisableMenuItemTitleTooltip:O,inlineCollapsed:z,siderCollapsed:A,rootClassName:k,mode:j,selectable:E,onClick:H,overflowedIndicatorPopupClassName:T}=e,D=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o}(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),P=(0,c.A)(D,["collapsedWidth"]);null===(o=a.validator)||void 0===o||o.call(a,{mode:j});const N=(0,s._q)((function(){var e;null==H||H.apply(void 0,arguments),null===(e=a.onClick)||void 0===e||e.call(a)})),R=a.mode||j,L=null!=E?E:a.selectable,W=n.useMemo((()=>void 0!==A?A:z),[z,A]),_={horizontal:{motionName:`${I}-slide-up`},inline:(0,m.A)(I),other:{motionName:`${I}-zoom-big`}},G=b("menu",S||a.prefixCls),Y=(0,g.A)(G),[F,V,K]=M(G,Y,!i),Q=l()(`${G}-${B}`,null==f?void 0:f.className,y),U=n.useMemo((()=>{var e,t;if("function"==typeof x||X(x))return x||null;if("function"==typeof a.expandIcon||X(a.expandIcon))return a.expandIcon||null;if("function"==typeof(null==f?void 0:f.expandIcon)||X(null==f?void 0:f.expandIcon))return(null==f?void 0:f.expandIcon)||null;const o=null!==(e=null!=x?x:null==a?void 0:a.expandIcon)&&void 0!==e?e:null==f?void 0:f.expandIcon;return(0,u.Ob)(o,{className:l()(`${G}-submenu-expand-icon`,n.isValidElement(o)?null===(t=o.props)||void 0===t?void 0:t.className:void 0)})}),[x,null==a?void 0:a.expandIcon,null==f?void 0:f.expandIcon,G]),J=n.useMemo((()=>({prefixCls:G,inlineCollapsed:W||!1,direction:h,firstLevel:!0,theme:B,mode:R,disableMenuItemTitleTooltip:O})),[G,W,h,O,B]);return F(n.createElement(C.h.Provider,{value:null},n.createElement($.Provider,{value:J},n.createElement(r.Ay,Object.assign({getPopupContainer:v,overflowedIndicator:n.createElement(d.A,null),overflowedIndicatorPopupClassName:l()(G,`${G}-${B}`,T),mode:R,selectable:L,onClick:N},P,{inlineCollapsed:W,style:Object.assign(Object.assign({},null==f?void 0:f.style),w),className:Q,prefixCls:G,direction:h,defaultMotions:_,expandIcon:U,ref:t,rootClassName:l()(k,V,a.rootClassName,K,Y),_internalComponents:q})))))})),G=_,Y=(0,n.forwardRef)(((e,t)=>{const o=(0,n.useRef)(null),r=n.useContext(a);return(0,n.useImperativeHandle)(t,(()=>({menu:o.current,focus:e=>{var t;null===(t=o.current)||void 0===t||t.focus(e)}}))),n.createElement(G,Object.assign({ref:o},e,r))}));Y.Item=f,Y.SubMenu=W,Y.Divider=b,Y.ItemGroup=r.te;const F=Y},11981:(e,t,o)=>{o.d(t,{Y:()=>n});const n=e=>({color:e.colorLink,textDecoration:"none",outline:"none",cursor:"pointer",transition:`color ${e.motionDurationSlow}`,"&:focus, &:hover":{color:e.colorLinkHover},"&:active":{color:e.colorLinkActive}})},78255:(e,t,o)=>{o.d(t,{F:()=>l});var n=o(39017),r=function(e){if((0,n.A)()&&window.document.documentElement){var t=Array.isArray(e)?e:[e],o=window.document.documentElement;return t.some((function(e){return e in o.style}))}return!1},i=function(e,t){if(!r(e))return!1;var o=document.createElement("div"),n=o.style[e];return o.style[e]=t,o.style[e]!==n};function l(e,t){return Array.isArray(e)||void 0===t?r(e):i(e,t)}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/3c20903b43e436fa539ee5a62bcca12b/97.lite.js.map
