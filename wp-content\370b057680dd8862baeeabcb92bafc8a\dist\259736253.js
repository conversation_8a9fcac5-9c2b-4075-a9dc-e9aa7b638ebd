/*! For license information please see vendor-customize_banner.lite.js.LICENSE.txt */
(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[344],{92936:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"}},37136:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M715.8 493.5L335 165.1c-14.2-12.2-35-1.2-35 18.5v656.8c0 19.7 20.8 30.7 35 18.5l380.8-328.4c10.9-9.4 10.9-27.6 0-37z"}}]},name:"caret-right",theme:"filled"}},93769:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"}},58285:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"}},8489:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"minus",theme:"outlined"}},22143:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"}},21503:(e,t,s)=>{"use strict";s.d(t,{Q:()=>o});var r=function(){return r=Object.assign||function(e){for(var t,s=1,r=arguments.length;s<r;s++)for(var n in t=arguments[s])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},r.apply(this,arguments)},n={primaryColor:"#333",secondaryColor:"#E6E6E6"};function o(e,t){if(void 0===t&&(t={}),"function"==typeof e.icon){var s=t.placeholders||n;return i(e.icon(s.primaryColor,s.secondaryColor),t)}return i(e.icon,t)}function i(e,t){var s="svg"===e.tag?r(r({},e.attrs),t.extraSVGAttrs||{}):e.attrs,n=Object.keys(s).reduce((function(e,t){var r=t,n=s[r],o="".concat(r,'="').concat(n,'"');return e.push(o),e}),[]),o=n.length?" "+n.join(" "):"",a=(e.children||[]).map((function(e){return i(e,t)})).join("");return a&&a.length?"<".concat(e.tag).concat(o,">").concat(a,"</").concat(e.tag,">"):"<".concat(e.tag).concat(o," />")}},71685:function(e,t,s){var r;!function(t){"use strict";var n=function(){},o=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.msRequestAnimationFrame||function(e){return setTimeout(e,16)};function i(){var e=this;e.reads=[],e.writes=[],e.raf=o.bind(t),n("initialized",e)}function a(e){e.scheduled||(e.scheduled=!0,e.raf(c.bind(null,e)),n("flush scheduled"))}function c(e){n("flush");var t,s=e.writes,r=e.reads;try{n("flushing reads",r.length),e.runTasks(r),n("flushing writes",s.length),e.runTasks(s)}catch(e){t=e}if(e.scheduled=!1,(r.length||s.length)&&a(e),t){if(n("task errored",t.message),!e.catch)throw t;e.catch(t)}}function u(e,t){var s=e.indexOf(t);return!!~s&&!!e.splice(s,1)}i.prototype={constructor:i,runTasks:function(e){var t;for(n("run tasks");t=e.shift();)t()},measure:function(e,t){n("measure");var s=t?e.bind(t):e;return this.reads.push(s),a(this),s},mutate:function(e,t){n("mutate");var s=t?e.bind(t):e;return this.writes.push(s),a(this),s},clear:function(e){return n("clear",e),u(this.reads,e)||u(this.writes,e)},extend:function(e){if(n("extend",e),"object"!=typeof e)throw new Error("expected object");var t=Object.create(this);return function(e,t){for(var s in t)t.hasOwnProperty(s)&&(e[s]=t[s])}(t,e),t.fastdom=this,t.initialize&&t.initialize(),t},catch:null};var l=t.fastdom=t.fastdom||new i;void 0===(r=function(){return l}.call(l,s,l,e))||(e.exports=r)}("undefined"!=typeof window?window:void 0!==this?this:globalThis)},57922:(e,t,s)=>{"use strict";s.d(t,{PA:()=>te});var r=s(44497),n=s(41594),o=s.n(n);if(!n.useState)throw new Error("mobx-react-lite requires React with Hooks support");if(!r.spy)throw new Error("mobx-react-lite requires mobx at least version 4 to be available");var i=s(75206),a=function(e,t){var s="function"==typeof Symbol&&e[Symbol.iterator];if(!s)return e;var r,n,o=s.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(e){n={error:e}}finally{try{r&&!r.done&&(s=o.return)&&s.call(o)}finally{if(n)throw n.error}}return i};function c(){var e=a((0,n.useState)(0),2)[1];return(0,n.useCallback)((function(){e((function(e){return e+1}))}),[])}var u,l=(u="observerBatching","function"==typeof Symbol?Symbol.for(u):"__$mobx-react "+u+"__");var d=!1;function p(){return d}function h(e){return(0,r.getDependencyTree)(e)}var f,g=1e4,m=1e4,v=new Set;function b(){void 0===f&&(f=setTimeout(y,m))}function y(){f=void 0;var e=Date.now();v.forEach((function(t){var s=t.current;s&&e>=s.cleanAt&&(s.reaction.dispose(),t.current=null,v.delete(t))})),v.size>0&&b()}var C=!1,w=[],E={};function S(e){return"observer"+e}function I(e,t,s){if(void 0===t&&(t="observed"),void 0===s&&(s=E),p())return e();var n,i,a=(i=(s.useForceUpdate||c)(),function(){C?w.push(i):i()}),u=o().useRef(null);if(!u.current){var l=new r.Reaction(S(t),(function(){d.mounted?a():(l.dispose(),u.current=null)})),d=function(e){return{cleanAt:Date.now()+g,reaction:e}}(l);u.current=d,n=u,v.add(n),b()}var f=u.current.reaction;return o().useDebugValue(f,h),o().useEffect((function(){var e;return e=u,v.delete(e),u.current?u.current.mounted=!0:(u.current={reaction:new r.Reaction(S(t),(function(){a()})),cleanAt:1/0},a()),function(){u.current.reaction.dispose(),u.current=null}}),[]),function(t){C=!0,w=[];try{var s=function(){var t,s;if(f.track((function(){try{t=e()}catch(e){s=e}})),s)throw s;return t}();C=!1;var r=w.length>0?w:void 0;return o().useLayoutEffect((function(){r&&r.forEach((function(e){return e()}))}),[r]),s}finally{C=!1}}()}var L=function(){return L=Object.assign||function(e){for(var t,s=1,r=arguments.length;s<r;s++)for(var n in t=arguments[s])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},L.apply(this,arguments)};var A,_={$$typeof:!0,render:!0,compare:!0,type:!0};function O(e){var t=e.children,s=e.render,r=t||s;return"function"!=typeof r?null:I(r)}function R(e,t,s,r,n){var o="children"===t?"render":"children",i="function"==typeof e[t],a="function"==typeof e[o];return i&&a?new Error("MobX Observer: Do not use children and render in the same time in`"+s):i||a?null:new Error("Invalid prop `"+n+"` of type `"+typeof e[t]+"` supplied to `"+s+"`, expected `function`.")}O.propTypes={children:R,render:R},O.displayName="Observer",(A=i.unstable_batchedUpdates)||(A=function(e){e()}),(0,r.configure)({reactionScheduler:A}),("undefined"!=typeof window?window:void 0!==s.g?s.g:"undefined"!=typeof self?self:{})[l]=!0;var V=0,P={};function T(e){return P[e]||(P[e]=function(e){if("function"==typeof Symbol)return Symbol(e);var t="__$mobx-react "+e+" ("+V+")";return V++,t}(e)),P[e]}function U(e,t){if(x(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var s=Object.keys(e),r=Object.keys(t);if(s.length!==r.length)return!1;for(var n=0;n<s.length;n++)if(!Object.hasOwnProperty.call(t,s[n])||!x(e[s[n]],t[s[n]]))return!1;return!0}function x(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function D(e,t,s){Object.hasOwnProperty.call(e,t)?e[t]=s:Object.defineProperty(e,t,{enumerable:!1,configurable:!0,writable:!0,value:s})}var N=T("patchMixins"),F=T("patchedDefinition");function k(e,t){for(var s=this,r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o];t.locks++;try{var i;return null!=e&&(i=e.apply(this,n)),i}finally{t.locks--,0===t.locks&&t.methods.forEach((function(e){e.apply(s,n)}))}}function M(e,t){return function(){for(var s=arguments.length,r=new Array(s),n=0;n<s;n++)r[n]=arguments[n];k.call.apply(k,[this,e,t].concat(r))}}function j(e,t,s){var r=function(e,t){var s=e[N]=e[N]||{},r=s[t]=s[t]||{};return r.locks=r.locks||0,r.methods=r.methods||[],r}(e,t);r.methods.indexOf(s)<0&&r.methods.push(s);var n=Object.getOwnPropertyDescriptor(e,t);if(!n||!n[F]){var o=e[t],i=H(e,t,n?n.enumerable:void 0,r,o);Object.defineProperty(e,t,i)}}function H(e,t,s,r,n){var o,i=M(n,r);return(o={})[F]=!0,o.get=function(){return i},o.set=function(n){if(this===e)i=M(n,r);else{var o=H(this,t,s,r,n);Object.defineProperty(this,t,o)}},o.configurable=!0,o.enumerable=s,o}var G=r.$mobx||"$mobx",B=T("isMobXReactObserver"),$=T("isUnmounted"),z=T("skipRender"),W=T("isForcingUpdate");function Y(e){var t=e.prototype;if(e[B]){var s=q(t);console.warn("The provided component class ("+s+") \n                has already been declared as an observer component.")}else e[B]=!0;if(t.componentWillReact)throw new Error("The componentWillReact life-cycle event is no longer supported");if(e.__proto__!==n.PureComponent)if(t.shouldComponentUpdate){if(t.shouldComponentUpdate!==Q)throw new Error("It is not allowed to use shouldComponentUpdate in observer based components.")}else t.shouldComponentUpdate=Q;J(t,"props"),J(t,"state");var r=t.render;return t.render=function(){return K.call(this,r)},j(t,"componentWillUnmount",(function(){var e;if(!0!==p()&&(null===(e=this.render[G])||void 0===e||e.dispose(),this[$]=!0,!this.render[G])){var t=q(this);console.warn("The reactive render of an observer class component ("+t+") \n                was overriden after MobX attached. This may result in a memory leak if the \n                overriden reactive render was not properly disposed.")}})),e}function q(e){return e.displayName||e.name||e.constructor&&(e.constructor.displayName||e.constructor.name)||"<component>"}function K(e){var t=this;if(!0===p())return e.call(this);D(this,z,!1),D(this,W,!1);var s=q(this),o=e.bind(this),i=!1,a=new r.Reaction(s+".render()",(function(){if(!i&&(i=!0,!0!==t[$])){var e=!0;try{D(t,W,!0),t[z]||n.Component.prototype.forceUpdate.call(t),e=!1}finally{D(t,W,!1),e&&a.dispose()}}}));function c(){i=!1;var e=void 0,t=void 0;if(a.track((function(){try{t=(0,r._allowStateChanges)(!1,o)}catch(t){e=t}})),e)throw e;return t}return a.reactComponent=this,c[G]=a,this.render=c,c.call(this)}function Q(e,t){return p()&&console.warn("[mobx-react] It seems that a re-rendering of a React component is triggered while in static (server-side) mode. Please make sure components are rendered only once server-side."),this.state!==t||!U(this.props,e)}function J(e,t){var s=T("reactProp_"+t+"_valueHolder"),n=T("reactProp_"+t+"_atomHolder");function o(){return this[n]||D(this,n,(0,r.createAtom)("reactive "+t)),this[n]}Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get:function(){var e=!1;return r._allowStateReadsStart&&r._allowStateReadsEnd&&(e=(0,r._allowStateReadsStart)(!0)),o.call(this).reportObserved(),r._allowStateReadsStart&&r._allowStateReadsEnd&&(0,r._allowStateReadsEnd)(e),this[s]},set:function(e){this[W]||U(this[s],e)?D(this,s,e):(D(this,s,e),D(this,z,!0),o.call(this).reportChanged(),D(this,z,!1))}})}var X="function"==typeof Symbol&&Symbol.for,Z=X?Symbol.for("react.forward_ref"):"function"==typeof n.forwardRef&&(0,n.forwardRef)((function(e){return null})).$$typeof,ee=X?Symbol.for("react.memo"):"function"==typeof n.memo&&(0,n.memo)((function(e){return null})).$$typeof;function te(e){if(!0===e.isMobxInjector&&console.warn("Mobx observer: You are trying to use 'observer' on a component that already has 'inject'. Please apply 'observer' before applying 'inject'"),ee&&e.$$typeof===ee)throw new Error("Mobx observer: You are trying to use 'observer' on a function component wrapped in either another observer or 'React.memo'. The observer already applies 'React.memo' for you.");if(Z&&e.$$typeof===Z){var t=e.render;if("function"!=typeof t)throw new Error("render property of ForwardRef was not a function");return(0,n.forwardRef)((function(){var e=arguments;return(0,n.createElement)(O,null,(function(){return t.apply(void 0,e)}))}))}return"function"!=typeof e||e.prototype&&e.prototype.render||e.isReactClass||Object.prototype.isPrototypeOf.call(n.Component,e)?Y(e):function(e,t){if(p())return e;var s,r,o,i=L({forwardRef:!1},t),a=e.displayName||e.name,c=function(t,s){return I((function(){return e(t,s)}),a)};return c.displayName=a,s=i.forwardRef?(0,n.memo)((0,n.forwardRef)(c)):(0,n.memo)(c),r=e,o=s,Object.keys(r).forEach((function(e){_[e]||Object.defineProperty(o,e,Object.getOwnPropertyDescriptor(r,e))})),s.displayName=a,s}(e)}if(!n.Component)throw new Error("mobx-react requires React to be available");if(!r.observable)throw new Error("mobx-react requires mobx to be available")},31611:(e,t,s)=>{"use strict";s.d(t,{S:()=>r});class r{clone(){const e=new this.constructor;return Object.keys(this).forEach((t=>{const s=this.deepClone(this[t]);void 0!==s&&(e[t]=s)})),e}deepClone(e){const t=typeof e;if("number"===t||"string"===t||"boolean"===t)return e;if(null!==e&&"object"===t){if("function"==typeof e.clone)return e.clone();if(e instanceof Date)return new Date(e.getTime());if(void 0!==e[Symbol.iterator]){const t=[];for(const s of e)t.push(this.deepClone(s));return e instanceof Array?t:new e.constructor(t)}{const t={};for(const s in e)e.hasOwnProperty(s)&&(t[s]=this.deepClone(e[s]));return t}}}}},47018:(e,t,s)=>{"use strict";s.d(t,{a:()=>a});var r=s(31611);class n extends Error{constructor(e){super(e),this.name="GVLError"}}class o{static absCall(e,t,s,r){return new Promise(((n,o)=>{const i=new XMLHttpRequest;i.withCredentials=s,i.addEventListener("load",(()=>{if(i.readyState==XMLHttpRequest.DONE)if(i.status>=200&&i.status<300){let e=i.response;if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}n(e)}else o(new Error(`HTTP Status: ${i.status} response type: ${i.responseType}`))})),i.addEventListener("error",(()=>{o(new Error("error"))})),i.addEventListener("abort",(()=>{o(new Error("aborted"))})),null===t?i.open("GET",e,!0):i.open("POST",e,!0),i.responseType="json",i.timeout=r,i.ontimeout=()=>{o(new Error("Timeout "+r+"ms "+e))},i.send(t)}))}static post(e,t,s=!1,r=0){return this.absCall(e,JSON.stringify(t),s,r)}static fetch(e,t=!1,s=0){return this.absCall(e,null,t,s)}}class i{static langSet=new Set(["AR","BG","BS","CA","CS","CY","DA","DE","EL","EN","ES","ET","EU","FI","FR","GL","HE","HI","HR","HU","ID","IT","JA","KA","KO","LT","LV","MK","MS","MT","NL","NO","PL","PT-BR","PT-PT","RO","RU","SK","SL","SQ","SR-LATN","SR-CYRL","SV","SW","TH","TL","TR","UK","VI","ZH","ZH-HANT"]);has(e){return i.langSet.has(e)}parseLanguage(e){const t=(e=e.toUpperCase()).split("-")[0];if(e.length>=2&&2==t.length){if(i.langSet.has(e))return e;if(i.langSet.has(t))return t;const s=t+"-"+t;if(i.langSet.has(s))return s;for(const s of i.langSet)if(-1!==s.indexOf(e)||-1!==s.indexOf(t))return s}throw new Error(`unsupported language ${e}`)}forEach(e){i.langSet.forEach(e)}get size(){return i.langSet.size}}class a extends r.S{static LANGUAGE_CACHE=new Map;static CACHE=new Map;static LATEST_CACHE_KEY=0;static DEFAULT_LANGUAGE="EN";static consentLanguages=new i;static baseUrl_;static set baseUrl(e){if(/^https?:\/\/vendorlist\.consensu\.org\//.test(e))throw new n("Invalid baseUrl!  You may not pull directly from vendorlist.consensu.org and must provide your own cache");e.length>0&&"/"!==e[e.length-1]&&(e+="/"),this.baseUrl_=e}static get baseUrl(){return this.baseUrl_}static latestFilename="vendor-list.json";static versionedFilename="archives/vendor-list-v[VERSION].json";static languageFilename="purposes-[LANG].json";readyPromise;gvlSpecificationVersion;vendorListVersion;tcfPolicyVersion;lastUpdated;purposes;specialPurposes;features;specialFeatures;isReady_=!1;vendors_;vendorIds;fullVendorList;byPurposeVendorMap;bySpecialPurposeVendorMap;byFeatureVendorMap;bySpecialFeatureVendorMap;stacks;dataCategories;lang_;cacheLang_;isLatest=!1;constructor(e,t){super();let s=a.baseUrl,r=t?.language;if(r)try{r=a.consentLanguages.parseLanguage(r)}catch(e){throw new n("Error during parsing the language: "+e.message)}if(this.lang_=r||a.DEFAULT_LANGUAGE,this.cacheLang_=r||a.DEFAULT_LANGUAGE,this.isVendorList(e))this.populate(e),this.readyPromise=Promise.resolve();else{if(!s)throw new n("must specify GVL.baseUrl before loading GVL json");if(e>0){const t=e;a.CACHE.has(t)?(this.populate(a.CACHE.get(t)),this.readyPromise=Promise.resolve()):(s+=a.versionedFilename.replace("[VERSION]",String(t)),this.readyPromise=this.fetchJson(s))}else a.CACHE.has(a.LATEST_CACHE_KEY)?(this.populate(a.CACHE.get(a.LATEST_CACHE_KEY)),this.readyPromise=Promise.resolve()):(this.isLatest=!0,this.readyPromise=this.fetchJson(s+a.latestFilename))}}static emptyLanguageCache(e){let t=!1;return null==e&&a.LANGUAGE_CACHE.size>0?(a.LANGUAGE_CACHE=new Map,t=!0):"string"==typeof e&&this.consentLanguages.has(e.toUpperCase())&&(a.LANGUAGE_CACHE.delete(e.toUpperCase()),t=!0),t}static emptyCache(e){let t=!1;return Number.isInteger(e)&&e>=0?(a.CACHE.delete(e),t=!0):void 0===e&&(a.CACHE=new Map,t=!0),t}cacheLanguage(){a.LANGUAGE_CACHE.has(this.cacheLang_)||a.LANGUAGE_CACHE.set(this.cacheLang_,{purposes:this.purposes,specialPurposes:this.specialPurposes,features:this.features,specialFeatures:this.specialFeatures,stacks:this.stacks,dataCategories:this.dataCategories})}async fetchJson(e){try{this.populate(await o.fetch(e))}catch(e){throw new n(e.message)}}getJson(){return{gvlSpecificationVersion:this.gvlSpecificationVersion,vendorListVersion:this.vendorListVersion,tcfPolicyVersion:this.tcfPolicyVersion,lastUpdated:this.lastUpdated,purposes:this.clonePurposes(),specialPurposes:this.cloneSpecialPurposes(),features:this.cloneFeatures(),specialFeatures:this.cloneSpecialFeatures(),stacks:this.cloneStacks(),...this.dataCategories?{dataCategories:this.cloneDataCategories()}:{},vendors:this.cloneVendors()}}cloneSpecialFeatures(){const e={};for(const t of Object.keys(this.specialFeatures))e[t]=a.cloneFeature(this.specialFeatures[t]);return e}cloneFeatures(){const e={};for(const t of Object.keys(this.features))e[t]=a.cloneFeature(this.features[t]);return e}cloneStacks(){const e={};for(const t of Object.keys(this.stacks))e[t]=a.cloneStack(this.stacks[t]);return e}cloneDataCategories(){const e={};for(const t of Object.keys(this.dataCategories))e[t]=a.cloneDataCategory(this.dataCategories[t]);return e}cloneSpecialPurposes(){const e={};for(const t of Object.keys(this.specialPurposes))e[t]=a.clonePurpose(this.specialPurposes[t]);return e}clonePurposes(){const e={};for(const t of Object.keys(this.purposes))e[t]=a.clonePurpose(this.purposes[t]);return e}static clonePurpose(e){return{id:e.id,name:e.name,description:e.description,...e.descriptionLegal?{descriptionLegal:e.descriptionLegal}:{},...e.illustrations?{illustrations:Array.from(e.illustrations)}:{}}}static cloneFeature(e){return{id:e.id,name:e.name,description:e.description,...e.descriptionLegal?{descriptionLegal:e.descriptionLegal}:{},...e.illustrations?{illustrations:Array.from(e.illustrations)}:{}}}static cloneDataCategory(e){return{id:e.id,name:e.name,description:e.description}}static cloneStack(e){return{id:e.id,name:e.name,description:e.description,purposes:Array.from(e.purposes),specialFeatures:Array.from(e.specialFeatures)}}static cloneDataRetention(e){return{..."number"==typeof e.stdRetention?{stdRetention:e.stdRetention}:{},purposes:{...e.purposes},specialPurposes:{...e.specialPurposes}}}static cloneVendorUrls(e){return e.map((e=>({langId:e.langId,privacy:e.privacy,...e.legIntClaim?{legIntClaim:e.legIntClaim}:{}})))}static cloneVendor(e){return{id:e.id,name:e.name,purposes:Array.from(e.purposes),legIntPurposes:Array.from(e.legIntPurposes),flexiblePurposes:Array.from(e.flexiblePurposes),specialPurposes:Array.from(e.specialPurposes),features:Array.from(e.features),specialFeatures:Array.from(e.specialFeatures),...e.overflow?{overflow:{httpGetLimit:e.overflow.httpGetLimit}}:{},..."number"==typeof e.cookieMaxAgeSeconds||null===e.cookieMaxAgeSeconds?{cookieMaxAgeSeconds:e.cookieMaxAgeSeconds}:{},...void 0!==e.usesCookies?{usesCookies:e.usesCookies}:{},...e.policyUrl?{policyUrl:e.policyUrl}:{},...void 0!==e.cookieRefresh?{cookieRefresh:e.cookieRefresh}:{},...void 0!==e.usesNonCookieAccess?{usesNonCookieAccess:e.usesNonCookieAccess}:{},...e.dataRetention?{dataRetention:this.cloneDataRetention(e.dataRetention)}:{},...e.urls?{urls:this.cloneVendorUrls(e.urls)}:{},...e.dataDeclaration?{dataDeclaration:Array.from(e.dataDeclaration)}:{},...e.deviceStorageDisclosureUrl?{deviceStorageDisclosureUrl:e.deviceStorageDisclosureUrl}:{},...e.deletedDate?{deletedDate:e.deletedDate}:{}}}cloneVendors(){const e={};for(const t of Object.keys(this.fullVendorList))e[t]=a.cloneVendor(this.fullVendorList[t]);return e}async changeLanguage(e){let t=e;try{t=a.consentLanguages.parseLanguage(e)}catch(e){throw new n("Error during parsing the language: "+e.message)}const s=e.toUpperCase();if((t.toLowerCase()!==a.DEFAULT_LANGUAGE.toLowerCase()||a.LANGUAGE_CACHE.has(s))&&t!==this.lang_)if(this.lang_=t,a.LANGUAGE_CACHE.has(s)){const e=a.LANGUAGE_CACHE.get(s);for(const t in e)e.hasOwnProperty(t)&&(this[t]=e[t])}else{const e=a.baseUrl+a.languageFilename.replace("[LANG]",this.lang_.toLowerCase());try{await this.fetchJson(e),this.cacheLang_=s,this.cacheLanguage()}catch(e){throw new n("unable to load language: "+e.message)}}}get language(){return this.lang_}isVendorList(e){return void 0!==e&&void 0!==e.vendors}populate(e){this.purposes=e.purposes,this.specialPurposes=e.specialPurposes,this.features=e.features,this.specialFeatures=e.specialFeatures,this.stacks=e.stacks,this.dataCategories=e.dataCategories,this.isVendorList(e)&&(this.gvlSpecificationVersion=e.gvlSpecificationVersion,this.tcfPolicyVersion=e.tcfPolicyVersion,this.vendorListVersion=e.vendorListVersion,this.lastUpdated=e.lastUpdated,"string"==typeof this.lastUpdated&&(this.lastUpdated=new Date(this.lastUpdated)),this.vendors_=e.vendors,this.fullVendorList=e.vendors,this.mapVendors(),this.isReady_=!0,this.isLatest&&a.CACHE.set(a.LATEST_CACHE_KEY,this.getJson()),a.CACHE.has(this.vendorListVersion)||a.CACHE.set(this.vendorListVersion,this.getJson())),this.cacheLanguage()}mapVendors(e){this.byPurposeVendorMap={},this.bySpecialPurposeVendorMap={},this.byFeatureVendorMap={},this.bySpecialFeatureVendorMap={},Object.keys(this.purposes).forEach((e=>{this.byPurposeVendorMap[e]={legInt:new Set,consent:new Set,flexible:new Set}})),Object.keys(this.specialPurposes).forEach((e=>{this.bySpecialPurposeVendorMap[e]=new Set})),Object.keys(this.features).forEach((e=>{this.byFeatureVendorMap[e]=new Set})),Object.keys(this.specialFeatures).forEach((e=>{this.bySpecialFeatureVendorMap[e]=new Set})),Array.isArray(e)||(e=Object.keys(this.fullVendorList).map((e=>+e))),this.vendorIds=new Set(e),this.vendors_=e.reduce(((e,t)=>{const s=this.vendors_[String(t)];return s&&void 0===s.deletedDate&&(s.purposes.forEach((e=>{this.byPurposeVendorMap[String(e)].consent.add(t)})),s.specialPurposes.forEach((e=>{this.bySpecialPurposeVendorMap[String(e)].add(t)})),s.legIntPurposes.forEach((e=>{this.byPurposeVendorMap[String(e)].legInt.add(t)})),s.flexiblePurposes&&s.flexiblePurposes.forEach((e=>{this.byPurposeVendorMap[String(e)].flexible.add(t)})),s.features.forEach((e=>{this.byFeatureVendorMap[String(e)].add(t)})),s.specialFeatures.forEach((e=>{this.bySpecialFeatureVendorMap[String(e)].add(t)})),e[t]=s),e}),{})}getFilteredVendors(e,t,s,r){const n=e.charAt(0).toUpperCase()+e.slice(1);let o;const i={};return o="purpose"===e&&s?this["by"+n+"VendorMap"][String(t)][s]:this["by"+(r?"Special":"")+n+"VendorMap"][String(t)],o.forEach((e=>{i[String(e)]=this.vendors[String(e)]})),i}getVendorsWithConsentPurpose(e){return this.getFilteredVendors("purpose",e,"consent")}getVendorsWithLegIntPurpose(e){return this.getFilteredVendors("purpose",e,"legInt")}getVendorsWithFlexiblePurpose(e){return this.getFilteredVendors("purpose",e,"flexible")}getVendorsWithSpecialPurpose(e){return this.getFilteredVendors("purpose",e,void 0,!0)}getVendorsWithFeature(e){return this.getFilteredVendors("feature",e)}getVendorsWithSpecialFeature(e){return this.getFilteredVendors("feature",e,void 0,!0)}get vendors(){return this.vendors_}narrowVendorsTo(e){this.mapVendors(e)}get isReady(){return this.isReady_}clone(){const e=new a(this.getJson());return this.lang_!==a.DEFAULT_LANGUAGE&&e.changeLanguage(this.lang_),e}static isInstanceOf(e){return"object"==typeof e&&"function"==typeof e.narrowVendorsTo}}},24824:(e,t,s)=>{"use strict";s.d(t,{j:()=>c});var r=s(31611),n=s(9262),o=s(47018),i=s(59561),a=s(65125);class c extends r.S{static consentLanguages=o.a.consentLanguages;isServiceSpecific_=!1;supportOOB_=!0;useNonStandardTexts_=!1;purposeOneTreatment_=!1;publisherCountryCode_="AA";version_=2;consentScreen_=0;policyVersion_=5;consentLanguage_="EN";cmpId_=0;cmpVersion_=0;vendorListVersion_=0;numCustomPurposes_=0;gvl_;created;lastUpdated;specialFeatureOptins=new i.M;purposeConsents=new i.M;purposeLegitimateInterests=new i.M;publisherConsents=new i.M;publisherLegitimateInterests=new i.M;publisherCustomConsents=new i.M;publisherCustomLegitimateInterests=new i.M;customPurposes;vendorConsents=new i.M;vendorLegitimateInterests=new i.M;vendorsDisclosed=new i.M;vendorsAllowed=new i.M;publisherRestrictions=new a.O;constructor(e){super(),e&&(this.gvl=e),this.updated()}set gvl(e){o.a.isInstanceOf(e)||(e=new o.a(e)),this.gvl_=e,this.publisherRestrictions.gvl=e}get gvl(){return this.gvl_}set cmpId(e){if(e=Number(e),!(Number.isInteger(e)&&e>1))throw new n.v("cmpId",e);this.cmpId_=e}get cmpId(){return this.cmpId_}set cmpVersion(e){if(e=Number(e),!(Number.isInteger(e)&&e>-1))throw new n.v("cmpVersion",e);this.cmpVersion_=e}get cmpVersion(){return this.cmpVersion_}set consentScreen(e){if(e=Number(e),!(Number.isInteger(e)&&e>-1))throw new n.v("consentScreen",e);this.consentScreen_=e}get consentScreen(){return this.consentScreen_}set consentLanguage(e){this.consentLanguage_=e}get consentLanguage(){return this.consentLanguage_}set publisherCountryCode(e){if(!/^([A-z]){2}$/.test(e))throw new n.v("publisherCountryCode",e);this.publisherCountryCode_=e.toUpperCase()}get publisherCountryCode(){return this.publisherCountryCode_}set vendorListVersion(e){if((e=0|Number(e))<0)throw new n.v("vendorListVersion",e);this.vendorListVersion_=e}get vendorListVersion(){return this.gvl?this.gvl.vendorListVersion:this.vendorListVersion_}set policyVersion(e){if(this.policyVersion_=parseInt(e,10),this.policyVersion_<0)throw new n.v("policyVersion",e)}get policyVersion(){return this.gvl?this.gvl.tcfPolicyVersion:this.policyVersion_}set version(e){this.version_=parseInt(e,10)}get version(){return this.version_}set isServiceSpecific(e){this.isServiceSpecific_=e}get isServiceSpecific(){return this.isServiceSpecific_}set useNonStandardTexts(e){this.useNonStandardTexts_=e}get useNonStandardTexts(){return this.useNonStandardTexts_}set supportOOB(e){this.supportOOB_=e}get supportOOB(){return this.supportOOB_}set purposeOneTreatment(e){this.purposeOneTreatment_=e}get purposeOneTreatment(){return this.purposeOneTreatment_}setAllVendorConsents(){this.vendorConsents.set(this.gvl.vendors)}unsetAllVendorConsents(){this.vendorConsents.empty()}setAllVendorsDisclosed(){this.vendorsDisclosed.set(this.gvl.vendors)}unsetAllVendorsDisclosed(){this.vendorsDisclosed.empty()}setAllVendorsAllowed(){this.vendorsAllowed.set(this.gvl.vendors)}unsetAllVendorsAllowed(){this.vendorsAllowed.empty()}setAllVendorLegitimateInterests(){this.vendorLegitimateInterests.set(this.gvl.vendors)}unsetAllVendorLegitimateInterests(){this.vendorLegitimateInterests.empty()}setAllPurposeConsents(){this.purposeConsents.set(this.gvl.purposes)}unsetAllPurposeConsents(){this.purposeConsents.empty()}setAllPurposeLegitimateInterests(){this.purposeLegitimateInterests.set(this.gvl.purposes)}unsetAllPurposeLegitimateInterests(){this.purposeLegitimateInterests.empty()}setAllSpecialFeatureOptins(){this.specialFeatureOptins.set(this.gvl.specialFeatures)}unsetAllSpecialFeatureOptins(){this.specialFeatureOptins.empty()}setAll(){this.setAllVendorConsents(),this.setAllPurposeLegitimateInterests(),this.setAllSpecialFeatureOptins(),this.setAllPurposeConsents(),this.setAllVendorLegitimateInterests()}unsetAll(){this.unsetAllVendorConsents(),this.unsetAllPurposeLegitimateInterests(),this.unsetAllSpecialFeatureOptins(),this.unsetAllPurposeConsents(),this.unsetAllVendorLegitimateInterests()}get numCustomPurposes(){let e=this.numCustomPurposes_;if("object"==typeof this.customPurposes){const t=Object.keys(this.customPurposes).sort(((e,t)=>Number(e)-Number(t)));e=parseInt(t.pop(),10)}return e}set numCustomPurposes(e){if(this.numCustomPurposes_=parseInt(e,10),this.numCustomPurposes_<0)throw new n.v("numCustomPurposes",e)}updated(){const e=new Date,t=new Date(Date.UTC(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()));this.created=t,this.lastUpdated=t}}},50903:(e,t,s)=>{"use strict";s.d(t,{d:()=>O});class r extends Error{constructor(e){super(e),this.name="EncodingError"}}var n,o=s(40570);class i{static processor=[e=>e,(e,t)=>{e.publisherRestrictions.gvl=t,e.purposeLegitimateInterests.unset([1,3,4,5,6]);const s=new Map;return s.set("legIntPurposes",e.vendorLegitimateInterests),s.set("purposes",e.vendorConsents),s.forEach(((s,r)=>{s.forEach(((n,i)=>{if(n){const n=t.vendors[i];if(!n||n.deletedDate)s.unset(i);else if(0===n[r].length)if("legIntPurposes"===r&&0===n.purposes.length&&0===n.legIntPurposes.length&&n.specialPurposes.length>0)s.set(i);else if("legIntPurposes"===r&&n.purposes.length>0&&0===n.legIntPurposes.length&&n.specialPurposes.length>0)s.set(i);else if(e.isServiceSpecific)if(0===n.flexiblePurposes.length)s.unset(i);else{const t=e.publisherRestrictions.getRestrictions(i);let n=!1;for(let e=0,s=t.length;e<s&&!n;e++)n=t[e].restrictionType===o.h.REQUIRE_CONSENT&&"purposes"===r||t[e].restrictionType===o.h.REQUIRE_LI&&"legIntPurposes"===r;n||s.unset(i)}else s.unset(i)}}))})),e.vendorsDisclosed.set(t.vendors),e}];static process(e,t){const s=e.gvl;if(!s)throw new r("Unable to encode TCModel without a GVL");if(!s.isReady)throw new r("Unable to encode TCModel tcModel.gvl.readyPromise is not resolved");(e=e.clone()).consentLanguage=s.language.slice(0,2).toUpperCase(),t?.version>0&&t?.version<=this.processor.length?e.version=t.version:e.version=this.processor.length;const n=e.version-1;if(!this.processor[n])throw new r(`Invalid version: ${e.version}`);return this.processor[n](e,s)}}!function(e){e.CORE="core",e.VENDORS_DISCLOSED="vendorsDisclosed",e.VENDORS_ALLOWED="vendorsAllowed",e.PUBLISHER_TC="publisherTC"}(n||(n={}));class a{static cmpId="cmpId";static cmpVersion="cmpVersion";static consentLanguage="consentLanguage";static consentScreen="consentScreen";static created="created";static supportOOB="supportOOB";static isServiceSpecific="isServiceSpecific";static lastUpdated="lastUpdated";static numCustomPurposes="numCustomPurposes";static policyVersion="policyVersion";static publisherCountryCode="publisherCountryCode";static publisherCustomConsents="publisherCustomConsents";static publisherCustomLegitimateInterests="publisherCustomLegitimateInterests";static publisherLegitimateInterests="publisherLegitimateInterests";static publisherConsents="publisherConsents";static publisherRestrictions="publisherRestrictions";static purposeConsents="purposeConsents";static purposeLegitimateInterests="purposeLegitimateInterests";static purposeOneTreatment="purposeOneTreatment";static specialFeatureOptins="specialFeatureOptins";static useNonStandardTexts="useNonStandardTexts";static vendorConsents="vendorConsents";static vendorLegitimateInterests="vendorLegitimateInterests";static vendorListVersion="vendorListVersion";static vendorsAllowed="vendorsAllowed";static vendorsDisclosed="vendorsDisclosed";static version="version"}class c{1=[n.CORE];2=[n.CORE];constructor(e,t){if(2===e.version)if(e.isServiceSpecific)this[2].push(n.PUBLISHER_TC);else{const s=!(!t||!t.isForVendors);s&&!0!==e[a.supportOOB]||this[2].push(n.VENDORS_DISCLOSED),s&&(e[a.supportOOB]&&e[a.vendorsAllowed].size>0&&this[2].push(n.VENDORS_ALLOWED),this[2].push(n.PUBLISHER_TC))}}}class u extends Error{constructor(e){super(e),this.name="DecodingError"}}class l{static DICT="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";static REVERSE_DICT=new Map([["A",0],["B",1],["C",2],["D",3],["E",4],["F",5],["G",6],["H",7],["I",8],["J",9],["K",10],["L",11],["M",12],["N",13],["O",14],["P",15],["Q",16],["R",17],["S",18],["T",19],["U",20],["V",21],["W",22],["X",23],["Y",24],["Z",25],["a",26],["b",27],["c",28],["d",29],["e",30],["f",31],["g",32],["h",33],["i",34],["j",35],["k",36],["l",37],["m",38],["n",39],["o",40],["p",41],["q",42],["r",43],["s",44],["t",45],["u",46],["v",47],["w",48],["x",49],["y",50],["z",51],["0",52],["1",53],["2",54],["3",55],["4",56],["5",57],["6",58],["7",59],["8",60],["9",61],["-",62],["_",63]]);static BASIS=6;static LCM=24;static encode(e){if(!/^[0-1]+$/.test(e))throw new r("Invalid bitField");const t=e.length%this.LCM;e+=t?"0".repeat(this.LCM-t):"";let s="";for(let t=0;t<e.length;t+=this.BASIS)s+=this.DICT[parseInt(e.substr(t,this.BASIS),2)];return s}static decode(e){if(!/^[A-Za-z0-9\-_]+$/.test(e))throw new u("Invalidly encoded Base64URL string");let t="";for(let s=0;s<e.length;s++){const r=this.REVERSE_DICT.get(e[s]).toString(2);t+="0".repeat(this.BASIS-r.length)+r}return t}}class d{static[a.cmpId]=12;static[a.cmpVersion]=12;static[a.consentLanguage]=12;static[a.consentScreen]=6;static[a.created]=36;static[a.isServiceSpecific]=1;static[a.lastUpdated]=36;static[a.policyVersion]=6;static[a.publisherCountryCode]=12;static[a.publisherLegitimateInterests]=24;static[a.publisherConsents]=24;static[a.purposeConsents]=24;static[a.purposeLegitimateInterests]=24;static[a.purposeOneTreatment]=1;static[a.specialFeatureOptins]=12;static[a.useNonStandardTexts]=1;static[a.vendorListVersion]=12;static[a.version]=6;static anyBoolean=1;static encodingType=1;static maxId=16;static numCustomPurposes=6;static numEntries=12;static numRestrictions=12;static purposeId=6;static restrictionType=2;static segmentType=3;static singleOrRange=1;static vendorId=16}class p{static encode(e,t){let s;if("string"==typeof e&&(e=parseInt(e,10)),s=e.toString(2),s.length>t||e<0)throw new r(`${e} too large to encode into ${t}`);return s.length<t&&(s="0".repeat(t-s.length)+s),s}static decode(e,t){if(t!==e.length)throw new u("invalid bit length");return parseInt(e,2)}}class h{static encode(e){return String(Number(e))}static decode(e){return"1"===e}}class f{static encode(e,t){return p.encode(Math.round(e.getTime()/100),t)}static decode(e,t){if(t!==e.length)throw new u("invalid bit length");const s=new Date;return s.setTime(100*p.decode(e,t)),s}}var g=s(59561);class m{static encode(e,t){let s="";for(let r=1;r<=t;r++)s+=h.encode(e.has(r));return s}static decode(e,t){if(e.length!==t)throw new u("bitfield encoding length mismatch");const s=new g.M;for(let r=1;r<=t;r++)h.decode(e[r-1])&&s.set(r);return s.bitLength=e.length,s}}class v{static encode(e,t){const s=(e=e.toUpperCase()).charCodeAt(0)-65,n=e.charCodeAt(1)-65;if(s<0||s>25||n<0||n>25)throw new r(`invalid language code: ${e}`);if(t%2==1)throw new r(`numBits must be even, ${t} is not valid`);return t/=2,p.encode(s,t)+p.encode(n,t)}static decode(e,t){let s;if(t!==e.length||e.length%2)throw new u("invalid bit length for language");{const t=65,r=e.length/2,n=p.decode(e.slice(0,r),r)+t,o=p.decode(e.slice(r),r)+t;s=String.fromCharCode(n)+String.fromCharCode(o)}return s}}var b,y=s(65125),C=s(58742);class w{static encode(e){let t=p.encode(e.numRestrictions,d.numRestrictions);if(!e.isEmpty()){const s=(t,s)=>{for(let r=t+1;r<=s;r++)if(e.gvl.vendorIds.has(r))return r;return t};e.getRestrictions().forEach((r=>{t+=p.encode(r.purposeId,d.purposeId),t+=p.encode(r.restrictionType,d.restrictionType);const n=e.getVendors(r),o=n.length;let i=0,a=0,c="";for(let e=0;e<o;e++){const t=n[e];if(0===a&&(i++,a=t),e===o-1||n[e+1]>s(t,n[o-1])){const e=!(t===a);c+=h.encode(e),c+=p.encode(a,d.vendorId),e&&(c+=p.encode(t,d.vendorId)),a=0}}t+=p.encode(i,d.numEntries),t+=c}))}return t}static decode(e){let t=0;const s=new y.O,r=p.decode(e.substr(t,d.numRestrictions),d.numRestrictions);t+=d.numRestrictions;for(let n=0;n<r;n++){const r=p.decode(e.substr(t,d.purposeId),d.purposeId);t+=d.purposeId;const n=p.decode(e.substr(t,d.restrictionType),d.restrictionType);t+=d.restrictionType;const o=new C.F(r,n),i=p.decode(e.substr(t,d.numEntries),d.numEntries);t+=d.numEntries;for(let r=0;r<i;r++){const r=h.decode(e.substr(t,d.anyBoolean));t+=d.anyBoolean;const n=p.decode(e.substr(t,d.vendorId),d.vendorId);if(t+=d.vendorId,r){const r=p.decode(e.substr(t,d.vendorId),d.vendorId);if(t+=d.vendorId,r<n)throw new u(`Invalid RangeEntry: endVendorId ${r} is less than ${n}`);for(let e=n;e<=r;e++)s.add(e,o)}else s.add(n,o)}}return s.bitLength=t,s}}!function(e){e[e.FIELD=0]="FIELD",e[e.RANGE=1]="RANGE"}(b||(b={}));class E{static encode(e){const t=[];let s,r=[],n=p.encode(e.maxId,d.maxId),o="";const i=d.maxId+d.encodingType,a=i+e.maxId,c=2*d.vendorId+d.singleOrRange+d.numEntries;let u=i+d.numEntries;return e.forEach(((n,i)=>{o+=h.encode(n),s=e.maxId>c&&u<a,s&&n&&(e.has(i+1)?0===r.length&&(r.push(i),u+=d.singleOrRange,u+=d.vendorId):(r.push(i),u+=d.vendorId,t.push(r),r=[]))})),s?(n+=String(b.RANGE),n+=this.buildRangeEncoding(t)):(n+=String(b.FIELD),n+=o),n}static decode(e,t){let s,r=0;const n=p.decode(e.substr(r,d.maxId),d.maxId);r+=d.maxId;const o=p.decode(e.charAt(r),d.encodingType);if(r+=d.encodingType,o===b.RANGE){if(s=new g.M,1===t){if("1"===e.substr(r,1))throw new u("Unable to decode default consent=1");r++}const n=p.decode(e.substr(r,d.numEntries),d.numEntries);r+=d.numEntries;for(let t=0;t<n;t++){const t=h.decode(e.charAt(r));r+=d.singleOrRange;const n=p.decode(e.substr(r,d.vendorId),d.vendorId);if(r+=d.vendorId,t){const t=p.decode(e.substr(r,d.vendorId),d.vendorId);r+=d.vendorId;for(let e=n;e<=t;e++)s.set(e)}else s.set(n)}}else{const t=e.substr(r,n);r+=n,s=m.decode(t,n)}return s.bitLength=r,s}static buildRangeEncoding(e){const t=e.length;let s=p.encode(t,d.numEntries);return e.forEach((e=>{const t=1===e.length;s+=h.encode(!t),s+=p.encode(e[0],d.vendorId),t||(s+=p.encode(e[1],d.vendorId))})),s}}function S(){return{[a.version]:p,[a.created]:f,[a.lastUpdated]:f,[a.cmpId]:p,[a.cmpVersion]:p,[a.consentScreen]:p,[a.consentLanguage]:v,[a.vendorListVersion]:p,[a.policyVersion]:p,[a.isServiceSpecific]:h,[a.useNonStandardTexts]:h,[a.specialFeatureOptins]:m,[a.purposeConsents]:m,[a.purposeLegitimateInterests]:m,[a.purposeOneTreatment]:h,[a.publisherCountryCode]:v,[a.vendorConsents]:E,[a.vendorLegitimateInterests]:E,[a.publisherRestrictions]:w,segmentType:p,[a.vendorsDisclosed]:E,[a.vendorsAllowed]:E,[a.publisherConsents]:m,[a.publisherLegitimateInterests]:m,[a.numCustomPurposes]:p,[a.publisherCustomConsents]:m,[a.publisherCustomLegitimateInterests]:m}}class I{1={[n.CORE]:[a.version,a.created,a.lastUpdated,a.cmpId,a.cmpVersion,a.consentScreen,a.consentLanguage,a.vendorListVersion,a.purposeConsents,a.vendorConsents]};2={[n.CORE]:[a.version,a.created,a.lastUpdated,a.cmpId,a.cmpVersion,a.consentScreen,a.consentLanguage,a.vendorListVersion,a.policyVersion,a.isServiceSpecific,a.useNonStandardTexts,a.specialFeatureOptins,a.purposeConsents,a.purposeLegitimateInterests,a.purposeOneTreatment,a.publisherCountryCode,a.vendorConsents,a.vendorLegitimateInterests,a.publisherRestrictions],[n.PUBLISHER_TC]:[a.publisherConsents,a.publisherLegitimateInterests,a.numCustomPurposes,a.publisherCustomConsents,a.publisherCustomLegitimateInterests],[n.VENDORS_ALLOWED]:[a.vendorsAllowed],[n.VENDORS_DISCLOSED]:[a.vendorsDisclosed]}}class L{static ID_TO_KEY=[n.CORE,n.VENDORS_DISCLOSED,n.VENDORS_ALLOWED,n.PUBLISHER_TC];static KEY_TO_ID={[n.CORE]:0,[n.VENDORS_DISCLOSED]:1,[n.VENDORS_ALLOWED]:2,[n.PUBLISHER_TC]:3}}class A{static fieldSequence=new I;static encode(e,t){let s;try{s=this.fieldSequence[String(e.version)][t]}catch(s){throw new r(`Unable to encode version: ${e.version}, segment: ${t}`)}let o="";t!==n.CORE&&(o=p.encode(L.KEY_TO_ID[t],d.segmentType));const i=S();return s.forEach((s=>{const n=e[s],c=i[s];let u=d[s];void 0===u&&this.isPublisherCustom(s)&&(u=Number(e[a.numCustomPurposes]));try{o+=c.encode(n,u)}catch(e){throw new r(`Error encoding ${t}->${s}: ${e.message}`)}})),l.encode(o)}static decode(e,t,s){const r=l.decode(e);let o=0;s===n.CORE&&(t.version=p.decode(r.substr(o,d[a.version]),d[a.version])),s!==n.CORE&&(o+=d.segmentType);const i=this.fieldSequence[String(t.version)][s],c=S();return i.forEach((e=>{const s=c[e];let n=d[e];if(void 0===n&&this.isPublisherCustom(e)&&(n=Number(t[a.numCustomPurposes])),0!==n){const i=r.substr(o,n);if(t[e]=s===E?s.decode(i,t.version):s.decode(i,n),Number.isInteger(n))o+=n;else{if(!Number.isInteger(t[e].bitLength))throw new u(e);o+=t[e].bitLength}}})),t}static isPublisherCustom(e){return 0===e.indexOf("publisherCustom")}}var _=s(24824);class O{static encode(e,t){let s,r="";return e=i.process(e,t),s=Array.isArray(t?.segments)?t.segments:new c(e,t)[""+e.version],s.forEach(((t,n)=>{let o="";n<s.length-1&&(o="."),r+=A.encode(e,t)+o})),r}static decode(e,t){const s=e.split("."),r=s.length;t||(t=new _.j);for(let e=0;e<r;e++){const r=s[e],n=l.decode(r.charAt(0)).substr(0,d.segmentType),o=L.ID_TO_KEY[p.decode(n,d.segmentType).toString()];A.decode(r,t,o)}return t}}},9262:(e,t,s)=>{"use strict";s.d(t,{v:()=>r});class r extends Error{constructor(e,t,s=""){super(`invalid value ${t} passed for ${e} ${s}`),this.name="TCModelError"}}},58742:(e,t,s)=>{"use strict";s.d(t,{F:()=>i});var r=s(31611),n=s(9262),o=s(40570);class i extends r.S{static hashSeparator="-";purposeId_;restrictionType;constructor(e,t){super(),void 0!==e&&(this.purposeId=e),void 0!==t&&(this.restrictionType=t)}static unHash(e){const t=e.split(this.hashSeparator),s=new i;if(2!==t.length)throw new n.v("hash",e);return s.purposeId=parseInt(t[0],10),s.restrictionType=parseInt(t[1],10),s}get hash(){if(!this.isValid())throw new Error("cannot hash invalid PurposeRestriction");return`${this.purposeId}${i.hashSeparator}${this.restrictionType}`}get purposeId(){return this.purposeId_}set purposeId(e){this.purposeId_=e}isValid(){return Number.isInteger(this.purposeId)&&this.purposeId>0&&(this.restrictionType===o.h.NOT_ALLOWED||this.restrictionType===o.h.REQUIRE_CONSENT||this.restrictionType===o.h.REQUIRE_LI)}isSameAs(e){return this.purposeId===e.purposeId&&this.restrictionType===e.restrictionType}}},65125:(e,t,s)=>{"use strict";s.d(t,{O:()=>i});var r=s(58742),n=s(40570),o=s(31611);class i extends o.S{bitLength=0;map=new Map;gvl_;has(e){return this.map.has(e)}isOkToHave(e,t,s){let r=!0;if(this.gvl?.vendors){const o=this.gvl.vendors[s];if(o)if(e===n.h.NOT_ALLOWED)r=o.legIntPurposes.includes(t)||o.purposes.includes(t);else if(o.flexiblePurposes.length)switch(e){case n.h.REQUIRE_CONSENT:r=o.flexiblePurposes.includes(t)&&o.legIntPurposes.includes(t);break;case n.h.REQUIRE_LI:r=o.flexiblePurposes.includes(t)&&o.purposes.includes(t)}else r=!1;else r=!1}return r}add(e,t){if(this.isOkToHave(t.restrictionType,t.purposeId,e)){const s=t.hash;this.has(s)||(this.map.set(s,new Set),this.bitLength=0),this.map.get(s).add(e)}}restrictPurposeToLegalBasis(e){const t=Array.from(this.gvl.vendorIds),s=e.hash,r=t[t.length-1],n=[...Array(r).keys()].map((e=>e+1));if(this.has(s))for(let e=1;e<=r;e++)this.map.get(s).add(e);else this.map.set(s,new Set(n)),this.bitLength=0}getVendors(e){let t=[];if(e){const s=e.hash;this.has(s)&&(t=Array.from(this.map.get(s)))}else{const e=new Set;this.map.forEach((t=>{t.forEach((t=>{e.add(t)}))})),t=Array.from(e)}return t.sort(((e,t)=>e-t))}getRestrictionType(e,t){let s;return this.getRestrictions(e).forEach((e=>{e.purposeId===t&&(void 0===s||s>e.restrictionType)&&(s=e.restrictionType)})),s}vendorHasRestriction(e,t){let s=!1;const r=this.getRestrictions(e);for(let e=0;e<r.length&&!s;e++)s=t.isSameAs(r[e]);return s}getMaxVendorId(){let e=0;return this.map.forEach((t=>{e=Math.max(Array.from(t)[t.size-1],e)})),e}getRestrictions(e){const t=[];return this.map.forEach(((s,n)=>{e?s.has(e)&&t.push(r.F.unHash(n)):t.push(r.F.unHash(n))})),t}getPurposes(){const e=new Set;return this.map.forEach(((t,s)=>{e.add(r.F.unHash(s).purposeId)})),Array.from(e)}remove(e,t){const s=t.hash,r=this.map.get(s);r&&(r.delete(e),0==r.size&&(this.map.delete(s),this.bitLength=0))}set gvl(e){this.gvl_||(this.gvl_=e,this.map.forEach(((e,t)=>{const s=r.F.unHash(t);Array.from(e).forEach((t=>{this.isOkToHave(s.restrictionType,s.purposeId,t)||e.delete(t)}))})))}get gvl(){return this.gvl_}isEmpty(){return 0===this.map.size}get numRestrictions(){return this.map.size}}},40570:(e,t,s)=>{"use strict";var r;s.d(t,{h:()=>r}),function(e){e[e.NOT_ALLOWED=0]="NOT_ALLOWED",e[e.REQUIRE_CONSENT=1]="REQUIRE_CONSENT",e[e.REQUIRE_LI=2]="REQUIRE_LI"}(r||(r={}))},59561:(e,t,s)=>{"use strict";s.d(t,{M:()=>o});var r=s(31611),n=s(9262);class o extends r.S{bitLength=0;maxId_=0;set_=new Set;*[Symbol.iterator](){for(let e=1;e<=this.maxId;e++)yield[e,this.has(e)]}values(){return this.set_.values()}get maxId(){return this.maxId_}has(e){return this.set_.has(e)}unset(e){Array.isArray(e)?e.forEach((e=>this.unset(e))):"object"==typeof e?this.unset(Object.keys(e).map((e=>Number(e)))):(this.set_.delete(Number(e)),this.bitLength=0,e===this.maxId&&(this.maxId_=0,this.set_.forEach((e=>{this.maxId_=Math.max(this.maxId,e)}))))}isIntMap(e){let t="object"==typeof e;return t=t&&Object.keys(e).every((t=>{let s=Number.isInteger(parseInt(t,10));return s=s&&this.isValidNumber(e[t].id),s=s&&void 0!==e[t].name,s})),t}isValidNumber(e){return parseInt(e,10)>0}isSet(e){let t=!1;return e instanceof Set&&(t=Array.from(e).every(this.isValidNumber)),t}set(e){if(Array.isArray(e))e.forEach((e=>this.set(e)));else if(this.isSet(e))this.set(Array.from(e));else if(this.isIntMap(e))this.set(Object.keys(e).map((e=>Number(e))));else{if(!this.isValidNumber(e))throw new n.v("set()",e,"must be positive integer array, positive integer, Set<number>, or IntMap");this.set_.add(e),this.maxId_=Math.max(this.maxId,e),this.bitLength=0}}empty(){this.set_=new Set,this.maxId_=0}forEach(e){for(let t=1;t<=this.maxId;t++)e(this.has(t),t)}get size(){return this.set_.size}setAll(e){this.set(e)}}},58552:(e,t,s)=>{"use strict";s.d(t,{y:()=>p});var r,n=s(41594),o=["bottom","height","left","right","top","width"],i=new Map,a=function e(){var t=[];i.forEach((function(e,s){var r,n,i=s.getBoundingClientRect();r=i,n=e.rect,void 0===r&&(r={}),void 0===n&&(n={}),o.some((function(e){return r[e]!==n[e]}))&&(e.rect=i,t.push(e))})),t.forEach((function(e){e.callbacks.forEach((function(t){return t(e.rect)}))})),r=window.requestAnimationFrame(e)};const c=function(e,t){return{observe:function(){var s=0===i.size;i.has(e)?i.get(e).callbacks.push(t):i.set(e,{rect:void 0,hasRectChanged:!1,callbacks:[t]}),s&&a()},unobserve:function(){var s=i.get(e);if(s){var n=s.callbacks.indexOf(t);n>=0&&s.callbacks.splice(n,1),s.callbacks.length||i.delete(e),i.size||cancelAnimationFrame(r)}}}};function u(e){return"boolean"==typeof e}function l(e){return!(!e||"[object Function]"!={}.toString.call(e))}var d="undefined"!=typeof window&&window.document&&window.document.createElement?n.useLayoutEffect:n.useEffect;function p(e,t,s){let r,o;u(t)?r=t:(r=t?.observe??!0,o=t?.onChange),l(s)&&(o=s),n.useEffect((()=>{u(t)&&console.warn("Passing `observe` as the second argument to `useRect` is deprecated and will be removed in a future version of Reach UI. Instead, you can pass an object of options with an `observe` property as the second argument (`useRect(ref, { observe })`).\nSee https://reach.tech/rect#userect-observe")}),[t]),n.useEffect((()=>{l(s)&&console.warn("Passing `onChange` as the third argument to `useRect` is deprecated and will be removed in a future version of Reach UI. Instead, you can pass an object of options with an `onChange` property as the second argument (`useRect(ref, { onChange })`).\nSee https://reach.tech/rect#userect-onchange")}),[s]);let[i,a]=n.useState(e.current),p=n.useRef(!1),h=n.useRef(!1),[f,g]=n.useState(null),m=n.useRef(o);return d((()=>{m.current=o,e.current!==i&&a(e.current)})),d((()=>{i&&!p.current&&(p.current=!0,g(i.getBoundingClientRect()))}),[i]),d((()=>{if(!r)return;let t=i;if(h.current||(h.current=!0,t=e.current),!t)return void console.warn("You need to place the ref");let s=c(t,(e=>{m.current?.(e),g(e)}));return s.observe(),()=>{s.unobserve()}}),[r,i,e]),f}},57177:(e,t,s)=>{"use strict";function r(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)e[r]=s[r]}return e}s.d(t,{A:()=>n});var n=function e(t,s){function n(e,n,o){if("undefined"!=typeof document){"number"==typeof(o=r({},s,o)).expires&&(o.expires=new Date(Date.now()+864e5*o.expires)),o.expires&&(o.expires=o.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var i="";for(var a in o)o[a]&&(i+="; "+a,!0!==o[a]&&(i+="="+o[a].split(";")[0]));return document.cookie=e+"="+t.write(n,e)+i}}return Object.create({set:n,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var s=document.cookie?document.cookie.split("; "):[],r={},n=0;n<s.length;n++){var o=s[n].split("="),i=o.slice(1).join("=");try{var a=decodeURIComponent(o[0]);if(r[a]=t.read(i,a),e===a)break}catch(e){}}return e?r[e]:r}},remove:function(e,t){n(e,"",r({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,r({},this.attributes,t))},withConverter:function(t){return e(r({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(s)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})},59670:(e,t,s)=>{"use strict";function r(e,t,s,r){var n,o=arguments.length,i=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,s):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,s,r);else for(var a=e.length-1;a>=0;a--)(n=e[a])&&(i=(o<3?n(i):o>3?n(t,s,i):n(t,s))||i);return o>3&&i&&Object.defineProperty(t,s,i),i}function n(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}s.d(t,{Cg:()=>r,Sn:()=>n}),Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError}}]);