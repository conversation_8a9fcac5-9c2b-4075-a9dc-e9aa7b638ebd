var realCookieBanner_customize_banner;(()=>{"use strict";var e,t,o,n,i,s={55285:(e,t,o)=>{var n,i,s,r,a;o.d(t,{ak:()=>s,iQ:()=>i}),Object.freeze(["name","headline","subHeadline","providerNotice","groupNotice","legalBasisNotice","technicalHandlingNotice","createContentBlockerNotice","sccConclusionInstructionsNotice"]),Object.freeze(["codeOnPageLoad","codeOptIn","codeOptOut","createContentBlockerNotice","deleteTechnicalDefinitionsAfterOptOut","dynamicFields","executeCodeOptInWhenNoTagManagerConsentIsGiven","executeCodeOptOutWhenNoTagManagerConsentIsGiven","googleConsentModeConsentTypes","groupNotice","isProviderCurrentWebsite","legalBasis","legalBasisNotice","provider","providerContact","providerLegalNoticeUrl","providerNotice","providerPrivacyPolicyUrl","providerText","purposes","purposeText","shouldUncheckContentBlockerCheckbox","shouldUncheckContentBlockerCheckboxWhenOneOf","tagManagerOptInEventName","tagManagerOptOutEventName","technicalHandlingNotice"]),Object.freeze(["name","codeOnPageLoad","googleConsentModeConsentTypes","codeOptIn","codeOptOut","createContentBlockerNotice","dataProcessingInCountries","dataProcessingInCountriesSpecialTreatments","deleteTechnicalDefinitionsAfterOptOut","dynamicFields","executeCodeOptInWhenNoTagManagerConsentIsGiven","executeCodeOptOutWhenNoTagManagerConsentIsGiven","executePriority","group","groupNotice","isCdn","isEmbeddingOnlyExternalResources","isProviderCurrentWebsite","legalBasis","legalBasisNotice","provider","providerNotice","providerPrivacyPolicyUrl","providerLegalNoticeUrl","purposes","sccConclusionInstructionsNotice","shouldUncheckContentBlockerCheckbox","shouldUncheckContentBlockerCheckboxWhenOneOf","tagManagerOptInEventName","tagManagerOptOutEventName","technicalDefinitions","technicalHandlingNotice"]),function(e){e.Essential="essential",e.Functional="functional",e.Statistics="statistics",e.Marketing="marketing"}(n||(n={})),function(e){e.Consent="consent",e.LegitimateInterest="legitimate-interest",e.LegalRequirement="legal-requirement"}(i||(i={})),function(e){e.ContractualAssurancesWithSubProcessors="contractual-assurances-with-sub-processors",e.ProviderIsSelfCertifiedTransAtlanticDataPrivacyFramework="provider-is-self-certified-trans-atlantic-data-privacy-framework",e.StandardContractualClauses="standard-contractual-clauses",e.BindingCorporateRules="binding-corporate-rules"}(s||(s={})),function(e){e.AdStorage="ad_storage",e.AdUserData="ad_user_data",e.AdPersonalization="ad_personalization",e.AnalyticsStorage="analytics_storage",e.FunctionalityStorage="functionality_storage",e.PersonalizationStorage="personalization_storage",e.SecurityStorage="security_storage"}(r||(r={})),function(e){e.CodeOnPageLoad="codeOnPageLoad",e.CodeOptIn="codeOptIn",e.CodeOptOut="codeOptOut",e.CreateContentBlockerNotice="createContentBlockerNotice",e.CreateContentBlockerNoticeTranslationFlags="createContentBlockerNoticeTranslationFlags",e.DataProcessingInCountries="dataProcessingInCountries",e.DataProcessingInCountriesSpecialTreatments="dataProcessingInCountriesSpecialTreatments",e.DeleteTechnicalDefinitionsAfterOptOut="deleteTechnicalDefinitionsAfterOptOut",e.DynamicFields="dynamicFields",e.DynamicFieldIds="dynamicFieldIds",e.ExecuteCodeOptInWhenNoTagManagerConsentIsGiven="executeCodeOptInWhenNoTagManagerConsentIsGiven",e.ExecuteCodeOptOutWhenNoTagManagerConsentIsGiven="executeCodeOptOutWhenNoTagManagerConsentIsGiven",e.ExecutePriority="executePriority",e.GoogleConsentModeConsentTypes="googleConsentModeConsentTypes",e.Group="group",e.GroupNotice="groupNotice",e.GroupNoticeTranslationFlags="groupNoticeTranslationFlags",e.IsCdn="isCdn",e.IsEmbeddingOnlyExternalResources="isEmbeddingOnlyExternalResources",e.IsProviderCurrentWebsite="isProviderCurrentWebsite",e.LegalBasis="legalBasis",e.LegalBasisNotice="legalBasisNotice",e.LegalBasisNoticeTranslationFlags="legalBasisNoticeTranslationFlags",e.Provider="provider",e.ProviderContact="providerContact",e.ProviderLegalNoticeUrl="providerLegalNoticeUrl",e.ProviderLegalNoticeUrlTranslationFlags="providerLegalNoticeUrlTranslationFlags",e.ProviderNotice="providerNotice",e.ProviderNoticeTranslationFlags="providerNoticeTranslationFlags",e.ProviderPrivacyPolicyUrl="providerPrivacyPolicyUrl",e.ProviderPrivacyPolicyUrlTranslationFlags="providerPrivacyPolicyUrlTranslationFlags",e.ProviderText="providerText",e.ProviderTranslationFlags="providerTranslationFlags",e.Purposes="purposes",e.PurposeIds="purposeIds",e.PurposeText="purposeText",e.SccConclusionInstructionsNotice="sccConclusionInstructionsNotice",e.SccConclusionInstructionsNoticeTranslationFlags="sccConclusionInstructionsNoticeTranslationFlags",e.ShouldUncheckContentBlockerCheckbox="shouldUncheckContentBlockerCheckbox",e.ShouldUncheckContentBlockerCheckboxWhenOneOf="shouldUncheckContentBlockerCheckboxWhenOneOf",e.TagManagerOptInEventName="tagManagerOptInEventName",e.TagManagerOptOutEventName="tagManagerOptOutEventName",e.TechnicalDefinitions="technicalDefinitions",e.TechnicalDefinitionIds="technicalDefinitionIds",e.TechnicalHandlingNotice="technicalHandlingNotice",e.TechnicalHandlingNoticeTranslationFlags="technicalHandlingNoticeTranslationFlags"}(a||(a={})),Object.freeze(["id","logo","logoId","release","releaseId","extends","next","nextId","pre","preId","extendsId","translationIds","extendedTemplateId","translationInfo","purposeIds","dynamicFieldIds","technicalDefinitionIds","translatableRequiredFields","translatedRequiredFields","translatableOptionalFields","translatedOptionalFields","translationFlaggedFields","version"])},5974:(e,t,o)=>{function n(e,t){void 0===t&&(t=!0);const o=e.find((e=>{let{isEssential:t}=e;return t})),n={[o.id]:o.items.map((e=>{let{id:t}=e;return t}))};if(t)for(const t of e){if(t===o)continue;const e=t.items.filter((e=>{let{legalBasis:t}=e;return"legitimate-interest"===t})).map((e=>{let{id:t}=e;return t}));e.length&&(n[t.id]=e)}return n}o.d(t,{w:()=>n})},50724:(e,t,o)=>{o.d(t,{y:()=>a});var n=o(57177);let i;const s=/^(?<createdAt>\d+)?:?(?<uuids>(?:[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}[,]?)+):(?<revisionHash>[a-f0-9]{32}):(?<json>.*)$/,r={};function a(e){const t=localStorage.getItem(e);if(t)return JSON.parse(t);const o=n.A.get(e);if(!o){const[t]=e.split("-");return function(e){if(void 0===e&&(e="test"),"boolean"==typeof i)return i;if(function(){const{userAgent:e}=navigator,{cookie:t}=document;if(e){if(/(cookiebot|2gdpr)\.com/i.test(e))return!0;if(/cmpcrawler(reject)?cookie=/i.test(t))return!0}return!1}())return!0;try{const t={sameSite:"Lax"};n.A.set(e,"1",t);const o=-1!==document.cookie.indexOf(`${e}=`);return n.A.remove(e,t),i=o,o}catch(e){return!1}}(t?`${t}-test`:void 0),!1}if(r[o])return r[o];const a=o.match(s);if(!a)return!1;const{groups:l}=a,c=l.uuids.split(","),d=c.shift();let p=JSON.parse(l.json);Object.hasOwn(p,"bc")||(p={d:p,bc:"none"});const u={uuid:d,previousUuids:c,created:l.createdAt?new Date(1e3*+l.createdAt):void 0,revision:l.revisionHash,consent:p.d,buttonClicked:p.bc};return r[o]=u,u}},80072:(e,t,o)=>{o.d(t,{r:()=>n});const n="RCB/Apply/Interactive"},19081:(e,t,o)=>{function n(e){return[...new Set(e.map((e=>{let{googleConsentModeConsentTypes:t}=e;return[...t]})).flat())]}o.d(t,{h:()=>n})},16215:(e,t,o)=>{o.d(t,{T:()=>r});var n=o(47018),i=o(24824),s=o(50903);function r(e){let{tcf:t,tcfMetadata:o,tcfString:r}=e;if(!t||!o||!Object.keys(t.vendors).length)return;const a=n.a.prototype.fetchJson;n.a.prototype.fetchJson=function(e){if(!e.startsWith("undefined"))return a.apply(this,arguments)};const l=new n.a(Object.assign({},t,o));l.lang_=o.language;const c=new i.j(l),{publisherCc:d}=o;return d&&(c.publisherCountryCode=d),r?s.d.decode(r,c):c.unsetAll(),{gvl:l,model:c,original:t,metadata:o}}},98927:(e,t,o)=>{o.d(t,{i:()=>i});var n=o(81116);function i(e,t,o,i,s){void 0===s&&(s=!1);const{[e]:r}=t,a=Object.values(r),l=a.reduce(((r,a)=>{let{id:l}=a;return r[l]=(0,n.L)(t,o,l,e,"legInt"===i,s).length,r}),{});return a.filter((e=>{let{id:t}=e;return l[t]}))}},81116:(e,t,o)=>{o.d(t,{L:()=>i});var n=o(53024);function i(e,t,o,i,s,r){void 0===r&&(r=!1);let a={};switch(i){case"features":a=e.getVendorsWithFeature(o);break;case"specialFeatures":a=e.getVendorsWithSpecialFeature(o);break;case"specialPurposes":a=e.getVendorsWithSpecialPurpose(o);break;case"purposes":a=s?e.getVendorsWithLegIntPurpose(o):e.getVendorsWithConsentPurpose(o);break;case"dataCategories":a=Object.values(e.vendors).reduce(((e,t)=>{var n;return(null==(n=t.dataDeclaration)?void 0:n.includes(o))&&(e[t.id]=t),e}),{});break;default:a=e.getVendorsWithConsentPurpose(o)}"purposes"===i&&(a={...a,...e.getVendorsWithFlexiblePurpose(o)});const l=Object.values(a).filter((e=>"purposes"!==i||(0,n.n)(t,o,s,e)));return r&&l.sort(((e,t)=>e.name.localeCompare(t.name))),l}},53024:(e,t,o)=>{o.d(t,{n:()=>i});var n=o(40570);function i(e,t,o,i){let{id:s,legIntPurposes:r}=i;var a;const l=e.publisherRestrictions.getRestrictions(s),c=l.map((e=>{let{purposeId:o,restrictionType:i}=e;return o===t&&i===n.h.NOT_ALLOWED&&o})).filter(Boolean);if(c.indexOf(t)>-1)return!1;let d=null==(a=l.filter((e=>{let{purposeId:o,restrictionType:i}=e;return o===t&&i!==n.h.NOT_ALLOWED}))[0])?void 0:a.restrictionType;return d||(d=r.indexOf(t)>-1?n.h.REQUIRE_LI:n.h.REQUIRE_CONSENT),!(o&&d===n.h.REQUIRE_CONSENT||!o&&d===n.h.REQUIRE_LI)}},20089:(e,t,o)=>{o.d(t,{o:()=>s});var n=o(50903),i=o(98927);function s(e,t){const{gvl:o}=e,s=e=>{let{id:t}=e;return t};switch(t){case"main_all":case"ind_all":case"implicit_all":e.setAllVendorConsents(),e.setAllVendorLegitimateInterests(),e.purposeLegitimateInterests.set((0,i.i)("purposes",o,e,"legInt").map(s)),e.purposeConsents.set((0,i.i)("purposes",o,e,"consent").map(s)),e.specialFeatureOptins.set((0,i.i)("specialFeatures",o,e,"consent").map(s));break;case"initial":case"main_essential":case"implicit_essential":case"ind_essential":case"ind_close_icon":case"main_close_icon":case"shortcode_revoke":e.unsetAll(),e.setAllVendorLegitimateInterests(),e.purposeLegitimateInterests.set((0,i.i)("purposes",o,e,"legInt").map(s));break;case"none":e.unsetAll()}return t.startsWith("main_")?e.consentScreen=0:t.startsWith("ind_")?e.consentScreen=1:"shortcode_revoke"===t?e.consentScreen=10:"unblock"===t&&(e.consentScreen=20),"initial"===t?void 0:()=>n.d.encode(e)}},43204:(e,t,o)=>{o.d(t,{t:()=>s});var n=o(3713),i=o(41594);const s=e=>{let{ms:t=100,chars:o=["&#9719;","&#9718;","&#9717;","&#9716;"]}=e;const[s,r]=(0,i.useState)(1);return(0,i.useEffect)((()=>{const e=setInterval((()=>{r(s+1)}),t);return()=>clearInterval(e)}),[s]),(0,n.jsx)("span",{dangerouslySetInnerHTML:{__html:o[s%4]}})}},20658:(e,t,o)=>{o.d(t,{r:()=>I,R:()=>T});var n=o(3713),i=o(41594),s=o(19081),r=o(80998);const a=[Symbol("extendBannerBodyStylesheet"),(e,t)=>{let{boolIf:o,boolNot:n,boolOr:i,boolSwitch:s,jsx:r,rule:a}=e,{scaleVertical:l,dimsContent:c,dimsHeader:[,d],dimsFooter:[,p],activeAction:u,boolLargeOrMobile:g,bodyDesign:h,isBanner:y,isDialog:b,isMobile:f,isMobileWidth:v,layout:m,individualLayout:S,individualPrivacyOpen:C,design:x,footerBorderStyle:k}=t;const{fontColor:w}=x,{padding:O}=h,[,{scrollbar:P,scrolledBottom:j}]=c,A=i([y,C]),I="300px",T=o(y,I,`calc(${m.dialogMaxWidth()} - ${g(O,o,1)} - ${g(O,o,3)} - (${g(x.borderWidth,o)} * 2))`),B=n(v),R=o(b,`${g(x.borderWidth,o)} solid ${x.borderColor()}`),[D]=r("div",{classNames:"body-container",background:x.bg(),lineHeight:1.4,paddingRight:g(h.padding,o,1),paddingLeft:g(h.padding,o,3),borderLeft:R,borderRight:R,pseudos:{":has(+div>div:empty)":k,">div":{transition:"width 500ms, max-width 500ms",margin:"auto",maxWidth:o({when:y,then:{when:[C,n(S.inheritBannerMaxWidth)],then:S.bannerMaxWidth(),or:m.bannerMaxWidth()}})},">div:after":{content:"''",display:"block",clear:"both"}}}),F=o(h.acceptAllOneRowLayout,"0 0 calc(50% - 5px)","1 1 100%"),N=o(h.acceptAllOneRowLayout,"5px"),E=new Array(4).fill(null).map(((e,t)=>a({order:t}))),[$]=r("div",{classNames:"tb-right",position:o(i([n(C),v]),"sticky"),margin:o({when:[B,b,C,n(S.inheritDialogMaxWidth)],then:"0 0 10px 10px"}),background:x.bg(),maxWidth:"100%",width:o(B,T,"auto"),float:o(B,o({when:A,then:"right"})),paddingTop:o(v,"10px",o({when:A,then:g(O,o,0),or:"10px"})),paddingBottom:o(C,o(v,"5px","10px"),g(O,o,2)),zIndex:1,display:"flex",flexWrap:"wrap",transition:"box-shadow ease-in-out .1s",boxShadow:o({when:[P(),n(j)],then:`0 -15px 15px -15px rgba(${w("r")} ${w("g")} ${w("b")} / 30%)`}),bottom:o(n(C),p.height()),top:o(C,d.height()),pseudos:{">a":{marginBottom:o(f,`calc(10px * ${l()})`,"10px")},[`>${E[0][0]}`]:{flex:F,marginRight:N},[`>${E[1][0]}`]:{flex:F,marginLeft:N}}}),M=[B,y,n(u["is-history"])],[L]=r("div",{classNames:"tb-left",float:o({when:M,then:"left"}),width:o({when:M,then:`calc(100% - ${I})`}),paddingRight:o({when:[y,B],then:"20px"}),paddingTop:o(C,"10px",g(h.padding,o,0)),paddingBottom:o({when:[B,n(C)],then:{when:i([y,C]),then:g(h.padding,o,2),or:"0px"},or:"10px"}),pseudos:{" img":{maxWidth:"100%",height:"auto"}}}),[_]=r("div",{position:"sticky",bottom:`calc(${p.height(!0,"0px")} - 1px)`,height:"0px",margin:"auto",transition:"box-shadow ease-in-out .1s",boxShadow:o({when:[P(),n(j)],then:`0 15px 15px 15px rgba(${x.fontColor("r")} ${x.fontColor("g")} ${x.fontColor("b")} / 20%)`}),display:s([[C,"block"],[[y,n(v)],"block"]],"none")});return{Container:D,RightSide:$,LeftSide:L,BeforeFooter:_,buttonOrderClasses:E}}];var l=o(84094),c=o(73950),d=o(30680),p=o(57114),u=o(8700),g=o(43204);const h=e=>{let{type:t,children:o,busyOnClick:s=!0,forceShow:r,onClick:a,framed:l,sticky:c,className:d,...h}=e;const{buttons:{[t]:y},framed:b,stickyButton:f,forceShowButton:v}=(0,u.o)().extend(...p.R),[m,S]=(0,i.useState)(!1),C=(0,i.useCallback)((e=>{m||(e.preventDefault(),s&&S(!0),null==a||a(e))}),[a,m,s]);return(0,n.jsx)(y,{onClick:C,onKeyDown:e=>{"space"===e.code.toLowerCase()&&e.target.click()},className:[l?b:void 0,c?f:void 0,r?v:void 0,d].filter(Boolean).join(" "),role:"button",...h,children:(0,n.jsx)("span",{children:m?(0,n.jsx)(g.t,{}):o})})},y=e=>{let{children:t}=e;const o=[...t].sort((()=>Math.random()-.5));return(0,n.jsx)(n.Fragment,{children:o})},b=()=>{const e=(0,l.Y)(),{isConsentRecord:t,activeAction:o,bodyDesign:{acceptEssentialsUseAcceptAll:s,acceptAllOneRowLayout:r},decision:{showGroups:p,groupsFirstView:u,saveButton:g,acceptAll:b,acceptEssentials:f,buttonOrder:v},texts:{acceptAll:m,acceptEssentials:S,acceptIndividual:C},saveButton:{type:x,useAcceptAll:k},individualTexts:{save:w},individualPrivacyOpen:O,didGroupFirstChange:P,productionNotice:j,buttonClicked:A="",fetchLazyLoadedDataForSecondView:I}=e,{a11yIds:{firstButton:T}}=(0,d.y)(),{buttonOrderClasses:B}=(0,d.y)().extend(...a),R=s&&b===f,D=k&&b===x,{all:F,essential:N,individual:E,save:$}=(0,i.useMemo)((()=>{const e=v.split(","),t=e.reduce(((t,o)=>(t[o]=e.indexOf(o),t)),{}),o=e.reduce(((t,o)=>(t[e.indexOf(o)]=o,t)),{});return r&&(t[o[0]]=1,t[o[1]]=0),t}),[v,r]),M=!1,{buttonClickedAll:L,buttonClickedEssentials:_,buttonClickedCustom:U,acceptAll:W,acceptEssentials:z,acceptIndividual:V,openIndividualPrivacy:H}=(0,c.C)(),G="change"===o&&!t,q=!O&&M,Y=O||M,Q=!O,J=(0,i.useRef)();return(0,n.jsxs)(y,{children:[q?(0,n.jsx)(h,{onClick:V,busyOnClick:G,className:B[F][1],type:"acceptAll",framed:A===U,id:T,children:w}):(0,n.jsx)(h,{onClick:W,busyOnClick:G,className:B[F][1],type:"acceptAll",framed:A===L,id:T,children:m}),(0,n.jsx)(h,{onClick:()=>z(),busyOnClick:G,className:B[N][1],type:R?"acceptAll":"acceptEssentials",framed:A===_,children:S}),Y&&(0,n.jsx)(h,{onClick:V,busyOnClick:G,className:B[$][1],type:D?"acceptAll":"save",framed:A===U,children:w}),Q&&(0,n.jsx)(h,{onClick:H,onMouseEnter:()=>{I&&(J.current=setTimeout(I,500))},onMouseLeave:()=>clearTimeout(J.current),busyOnClick:G,className:B[E][1],type:"acceptIndividual",framed:A.startsWith("ind_"),children:C}),j]})};var f=o(41520),v=o(96875);const m=[Symbol("extendTeachingStylesheet"),(e,t)=>{let{jsx:o,boolIf:n,boolOr:i}=e,{individualLayout:s,design:r,bodyDesign:a,boolLargeOrMobile:l,layout:c}=t;const[d,p]=o("p",{classNames:"teaching",all:"unset",display:"block",marginTop:"7px",textAlign:n(a.teachingsInheritTextAlign,"inherit",a.teachingsTextAlign()),color:n(a.teachingsInheritFontColor,"inherit",a.teachingsFontColor()),fontSize:n(a.teachingsInheritFontSize,"inherit",l(a.teachingsFontSize,n))}),[u]=o("div",{classNames:"description",lineHeight:1.5,textAlign:s.descriptionTextAlign(),fontSize:n(a.descriptionInheritFontSize,l(r.fontSize,n),l(a.descriptionFontSize,n)),pseudos:{[`> p:not(${p})`]:{all:"unset"}," a:not([aria-expanded])":{all:"unset",cursor:"pointer",color:"inherit",textDecoration:r.linkTextDecoration()}," a:not([aria-expanded]):hover":{textDecoration:r.linkTextDecoration()}}}),[g]=o("div",{classNames:"teaching-separator",display:n(a.teachingsSeparatorActive,"block","none"),pseudos:{":after":{content:"''",marginTop:"7px",display:"block",maxWidth:a.teachingsSeparatorWidth(),borderRadius:c.borderRadius(),height:a.teachingsSeparatorHeight(),background:a.teachingsSeparatorColor(),marginLeft:n(i([r.textAlign("is-center"),r.textAlign("is-right")]),"auto"),marginRight:n(r.textAlign("is-center"),"auto")}}},{"aria-hidden":!0});return{DescriptionContainer:u,Teaching:d,TeachingSeparator:g}}];var S=o(55548),C=o(45453),x=o(11801);o(4959);const k=e=>{let{group:t}=e;const{name:o}=t,s=(0,l.Y)(),{decision:{groupsFirstView:r},design:{fontSize:a}}=s;return(0,x.C)(t),(0,n.jsx)(i.Fragment,{children:(0,n.jsxs)("span",{children:[(0,n.jsx)("i",{}),(0,n.jsx)("span",{children:o})]})})},w=()=>{const{texts:{headline:e}}=(0,l.Y)(),{DottedGroupList:t,screenReaderOnlyClass:o}=(0,d.y)().extend(...C.C),{groups:i,decision:{showGroups:s}}=(0,l.Y)();return s?(0,n.jsxs)(t,{children:[(0,n.jsx)("legend",{className:o,children:e}),i.filter((e=>{let{items:t}=e;return!!t.length})).map((e=>(0,n.jsx)(k,{group:e},e.id)))]}):null},O=(0,r.g)(Promise.resolve((e=>{let{description:t,nl2br:o,teachings:s,isPostamble:r,children:a,...l}=e;const{individualPrivacyOpen:c}=(0,f.b)(),{DescriptionContainer:d,Teaching:p,TeachingSeparator:g}=(0,u.o)().extend(...m);return(0,n.jsxs)(d,{style:[!0,void 0].indexOf(c)>-1?void 0:{textAlign:"inherit"},children:[a,!!t&&(0,n.jsx)("p",{role:"presentation",...l,dangerouslySetInnerHTML:{__html:o?t.replace(/\n/gm,'<br aria-hidden="true" />'):t}}),(null==s?void 0:s.length)>0&&(0,n.jsxs)(i.Fragment,{children:[(!!t||r)&&(0,n.jsx)(g,{}),s.map((e=>"string"==typeof e?(0,n.jsx)(p,{style:{marginBottom:r?7:0},dangerouslySetInnerHTML:{__html:e}},e):e))]})]})})),"BodyDescription"),P=(0,r.g)(Promise.all([o.e(343),o.e(4)]).then(o.bind(o,53602)).then((e=>{let{BannerGroupList:t}=e;return t}))),j=(0,r.g)(Promise.all([o.e(343),o.e(4)]).then(o.bind(o,14685)).then((e=>{let{BannerTcfGroupList:t}=e;return t}))),A=(0,r.g)(Promise.all([o.e(343),o.e(4)]).then(o.bind(o,69558)).then((e=>{let{BannerHistorySelect:t}=e;return t}))),I={margin:"20px 0 10px 0"},T=e=>{let{leftSideContainerRef:t,rightSideContainerRef:o}=e;const{a11yIds:r,Container:c,RightSide:p,LeftSide:u,BeforeFooter:g}=(0,d.y)().extend(...a),h=(0,l.Y)(),{tcf:y,isGcm:m,individualPrivacyOpen:C,activeAction:x,individualTexts:{postamble:k},i18n:{nonStandard:T,nonStandardDesc:B},groups:R,designVersion:D}=h,F=function(e){const{groups:t}=(0,f.b)();let o=t.map((e=>{let{items:t}=e;return[...t]})).flat();return o}(),N=(0,v.bM)({services:F,disableListServicesNotice:D>9&&C}),E=m&&(0,s.h)(F).length>0,$=(0,i.useMemo)((()=>!!y),[y,E]),M=(0,n.jsxs)(u,{ref:t,children:[(0,n.jsx)(O,{id:r.description,...R.length>0?N:{},children:"history"===x&&(0,n.jsx)(A,{})}),R.length>0&&(0,n.jsxs)(i.Fragment,{children:[C?(0,n.jsxs)(i.Fragment,{children:[$&&(0,n.jsx)(S.Y,{headline:T,style:I,borderless:!0,children:B}),(0,n.jsx)(P,{}),[y&&(0,n.jsx)(j,{},"tcf"),!1].filter(Boolean).sort((()=>D<10?1:-1))]}):(0,n.jsx)(w,{}),!!k&&C&&(0,n.jsx)(O,{teachings:[k],isPostamble:!0})]})]},"leftSide"),L="history"===x?(0,n.jsx)("div",{ref:o}):(0,n.jsx)(p,{ref:o,children:(0,n.jsx)(b,{})},"rightSide");return(0,n.jsxs)(c,{children:[(0,n.jsx)("div",{children:C?[L,M]:[M,L]}),(0,n.jsx)(g,{})]})}},43494:(e,t,o)=>{o.d(t,{N:()=>u});var n=o(3713),i=o(37136),s=o(22143),r=o(92936),a=o(58285),l=o(41594),c=o(98927),d=o(84094),p=o(48165);const u=()=>{const e=(0,d.Y)(),{tcf:{gvl:t,model:o},texts:{tcfStacksCustomName:u,tcfStacksCustomDescription:g},bodyDesign:{accordionArrowType:h},isListServicesNotice:y}=e,{vendors:b,stacks:f,purposes:{1:v}}=t,m=(0,l.useMemo)((()=>{const e=(0,c.i)("purposes",t,o,"legInt").filter((e=>{let{id:t}=e;return 1===t})),n=(0,c.i)("purposes",t,o,"consent").filter((e=>{let{id:t}=e;return 1===t}));return e.length>0||n.length>0}),[b,t,o]),S="none"!==h?"filled"===h?i.A:s.A:void 0,C="none"!==h?"filled"===h?r.A:a.A:void 0;return(0,n.jsxs)(p.D,{children:[m&&(0,n.jsx)(p.A,{title:v.name,icon:S,iconExpanded:C,children:v.description}),Object.values(f).map((e=>{let{id:t,name:o,description:i}=e;return(0,n.jsx)(p.A,{title:o,icon:S,iconExpanded:C,children:i},t)})),!y&&(0,n.jsx)(p.A,{title:u,icon:S,iconExpanded:C,children:g})]})}},48165:(e,t,o)=>{o.d(t,{A:()=>d,D:()=>c});var n=o(3713),i=o(41594),s=o(45746),r=o(8700),a=o(17140),l=o(83016);const c=e=>{let{children:t}=e;const{AccordionList:o}=(0,r.o)().extend(...s.I);return(0,n.jsx)(o,{children:t})},d=e=>{let{children:t,title:o,icon:c,iconExpanded:d,expandable:p=!0}=e;const{accordionArrow:u,AccordionDescription:g,AccordionItem:h,AccordionButton:y,AccordionTitle:b,accordionItemActive:f,accordionItemDisabled:v}=(0,r.o)().extend(...s.I),[m,S]=(0,i.useState)(!1),C=(0,a.p)();return(0,n.jsxs)(h,{onClick:()=>p&&S(!m),className:[m&&f,!p&&v].filter(Boolean).join(" "),children:[(0,n.jsxs)(y,{...p?{}:{disabled:"disabled"},"aria-expanded":m,"aria-controls":C,href:"#",onClick:e=>e.preventDefault(),children:[!!c&&(0,n.jsx)(l.r,{icon:m&&d?d:c,className:u}),(0,n.jsx)(b,{children:o})]}),(0,n.jsx)("div",{id:C,children:m&&p&&(0,n.jsx)(g,{children:t})})]})}},4959:(e,t,o)=>{o.d(t,{S:()=>d});var n=o(3713),i=o(8489),s=o(93769),r=o(41594),a=o(45746),l=o(8700),c=o(83016);const d=e=>{let{hideCheckbox:t,isPartial:o,isChecked:d,isDisabled:p,fontSize:u,onToggle:g,children:h,after:y,"aria-describedby":b,...f}=e;const{checkbox:{className:v,style:m},Label:S}=(0,l.o)().extend(...a.I),C=o?i.A:s.A;return(0,n.jsxs)(S,{children:[!t&&(0,n.jsxs)(r.Fragment,{children:[(0,n.jsx)("input",{name:"checkbox[]",type:"checkbox",value:"1",checked:d,disabled:p,className:v,style:m({fontSize:u}),onChange:e=>{const{checked:t}=e.target;null==g||g(t)},"aria-describedby":b}),(0,n.jsx)(c.r,{"aria-hidden":!0,icon:C,...f})]}),h&&(0,n.jsxs)("span",{children:[(0,n.jsx)("span",{children:h}),y&&(0,n.jsx)("span",{onClick:e=>{e.stopPropagation()},children:y})]})]})}},22839:(e,t,o)=>{o.d(t,{N:()=>l});var n=o(3713),i=o(8489),s=o(19081),r=o(41520),a=o(48165);const l=e=>{let{services:t}=e;const o=(0,s.h)(t),{i18n:{gcm:{purposes:l}}}=(0,r.b)();return(0,n.jsx)(a.D,{children:o.map((e=>(0,n.jsx)(a.A,{title:l[e],icon:i.A,expandable:!1},e)))})}},55548:(e,t,o)=>{o.d(t,{Y:()=>r});var n=o(3713),i=o(45453),s=o(8700);const r=e=>{let{headline:t,borderless:o,children:r,legend:a,...l}=e;const{Group:c,GroupInner:d,GroupDescription:p,screenReaderOnlyClass:u}=(0,s.o)().extend(...i.C),g=a||("string"==typeof t?t:void 0),h=(0,n.jsxs)(d,{children:[g&&(0,n.jsx)("legend",{className:u,children:g}),(0,n.jsx)("span",{"aria-hidden":!!g,children:t}),r&&(0,n.jsx)(p,{children:r})]});return o?(0,n.jsx)("div",{className:"group",...l,children:h}):(0,n.jsx)(c,{...l,children:h})}},83016:(e,t,o)=>{o.d(t,{r:()=>r});var n=o(3713),i=o(21503),s=o(41594);const r=e=>{let{icon:t,...o}=e;const r=(0,s.useMemo)((()=>(0,i.Q)(t,{extraSVGAttrs:{style:"width:auto;height:100%;",fill:"currentColor"}})),[t]);return(0,n.jsx)("div",{...o,dangerouslySetInnerHTML:{__html:r}})}},84094:(e,t,o)=>{o.d(t,{Y:()=>s,d:()=>r});var n=o(52113);const i=Symbol(),s=()=>(0,n.NV)(i),r=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,n.gm)(i,...t)}},8700:(e,t,o)=>{o.d(t,{d:()=>r,o:()=>s});var n=o(52113);const i=Symbol(),s=()=>(0,n.NV)(i),r=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,n.gm)(i,...t)}},73950:(e,t,o)=>{o.d(t,{C:()=>r});var n=o(41594),i=o(66399),s=o(84094);function r(){const{individualPrivacyOpen:e,onSave:t,updateGroupChecked:o,updateCookieChecked:r,groups:a,activeAction:l,onClose:c,set:d}=(0,s.Y)(),p=e?"ind_all":"main_all",u=e?"ind_essential":"main_essential",g=e?"ind_close_icon":"main_close_icon",h=e?"ind_custom":"main_custom",y={buttonClickedAll:p,buttonClickedEssentials:u,buttonClickedCloseIcon:g,buttonClickedCustom:h,acceptAll:(0,n.useCallback)((async()=>{await(0,i.P)(),d((e=>{let{updateGroupChecked:t}=e;a.forEach((e=>t(e.id,!0)))})),await(0,i.P)(),t(!1,p)}),[p]),acceptEssentials:(0,n.useCallback)((e=>{void 0===e&&(e=!1),a.forEach((e=>{let{isEssential:t,id:n,items:i}=e;if(t)o(n,!0);else for(const{legalBasis:e,id:t}of i)r(n,t,"legitimate-interest"===e)})),t(!1,!0===e?g:u)}),[u]),acceptIndividual:(0,n.useCallback)((()=>t(!1,h)),[h]),openIndividualPrivacy:(0,n.useCallback)((()=>d({individualPrivacyOpen:!0})),[d])};return{...y,closeIcon:(0,n.useCallback)((()=>{l?c():y.acceptEssentials(!0)}),[l,c,y.acceptEssentials])}}},30680:(e,t,o)=>{o.d(t,{y:()=>s});var n=o(50151),i=o(8700);function s(){return(0,i.o)().extend(...n.Z)}},11801:(e,t,o)=>{o.d(t,{C:()=>s});var n=o(41594),i=o(84094);function s(e){var t;const o=(0,i.Y)(),{id:s,items:r,isEssential:a}=e,{previewCheckboxActiveState:l,consent:c,activeAction:d}=o,p=a||"history"===d,u=l||a||!!c[s];return{isDisabled:p,isChecked:u,isPartial:!a&&(null==(t=c[s])?void 0:t.length)&&JSON.stringify(r.map((e=>{let{id:t}=e;return t})).sort())!==JSON.stringify(c[s].sort()||[]),onToggle:(0,n.useCallback)((e=>o.updateGroupChecked(s,e)),[o,s,u])}}},96875:(e,t,o)=>{o.d(t,{$D:()=>g,BP:()=>y,JY:()=>h,bM:()=>f});var n=o(3713),i=o(55285),s=o(19081),r=o(43494),a=o(22839),l=o(8700),c=o(21917),d=o(54989),p=o(41520),u=o(64349);function g(e,t){const o=e.filter(Boolean);return o.length>1&&o.splice(o.length-1,0,"{{andSeparator}}"),o.join(", ").replace(/,\s+{{andSeparator}},\s+/g,t)}const h=e=>(t,o)=>{void 0===o&&(o="");const n=t.filter(Boolean),i=n.map((e=>{let[t]=e;return t})),s=n.map((e=>{let[,t]=e;return t})).filter(Boolean);return 0===i.length?o:`${o}${b}${i.join(",")}</sup>${s.length?`<span class="${e}">(${s.join(", ")})</span>`:""}`};function y(e,t,o){return`${e} ${e>1?o:t}`}const b='<sup aria-hidden="true" style="vertical-align:top;line-height:100%;position:initial;">';function f(e){let{services:t,disableListServicesNotice:o,disableTcfPurposes:b}=e;const{blocker:f,groups:v,isAgeNotice:m,isGcm:S,isGcmListPurposes:C,ageNoticeAgeLimit:x,isListServicesNotice:k,isDataProcessingInUnsafeCountries:w,dataProcessingInUnsafeCountriesSafeCountries:O,texts:{description:P,dataProcessingInUnsafeCountries:j,ageNoticeBanner:A,ageNoticeBlocker:I,listServicesNotice:T,listServicesLegitimateInterestNotice:B,consentForwardingExternalHosts:R},tcf:D,consentForwardingExternalHosts:F,individualPrivacyOpen:N,individualTexts:{description:E},designVersion:$,territorialLegalBasis:M,predefinedDataProcessingInSafeCountriesLists:L,i18n:{andSeparator:_,territorialLegalBasisArticles:U,gcm:W,tcf:z,deprecated:{dataProcessingInUnsafeCountries:V}},keepVariablesInTexts:H}=(0,p.b)(),{privacyPolicy:G}=(0,u.s)(),{screenReaderOnlyClass:q}=(0,l.o)(),[Y,Q]=[(0,d.E)("consent"),(0,d.E)("legInt")],J=h(q),Z=$>9?"D":"U",K=v.map(((e,t)=>e.items.map((o=>{const{legalBasis:n}=o;return{service:o,legalBasis:e.isEssential&&$>=4&&"consent"===n?i.iQ.LegitimateInterest:n,group:e,groupIdx:t}})))).flat();let X="";F&&!H&&(X=R.replace(/{{websites}}/g,F.join(", ")));let ee=[f?[f.description,$>2&&P].filter(Boolean).join("\n\n"):N?E:P,X].filter(Boolean).join(" ");H||(ee=ee.replace(/{{privacyPolicy}}(.*){{\/privacyPolicy}}/gi,G?`<a href="${G.url}" target="_blank">$1</a>`:"$1"));const te=t.filter((e=>{let{dataProcessingInCountries:t,dataProcessingInCountriesSpecialTreatments:o}=e;return($>9?(0,c.F)({predefinedDataProcessingInSafeCountriesLists:L,isDataProcessingInUnsafeCountries:w,territorialLegalBasis:M,service:{dataProcessingInCountries:t,dataProcessingInCountriesSpecialTreatments:o}}).filter((e=>e.startsWith("D"))):(0,c.z)({dataProcessingInCountries:t,safeCountries:O,specialTreatments:o})).length>0})),oe=(null==D?void 0:D.gvl)?Object.values(D.gvl.vendors).filter((e=>{const{dataProcessingInCountries:t,dataProcessingInCountriesSpecialTreatments:o}=D.original.vendorConfigurations[e.id];return($>9?(0,c.F)({predefinedDataProcessingInSafeCountriesLists:L,isDataProcessingInUnsafeCountries:w,territorialLegalBasis:M,service:{dataProcessingInCountries:t,dataProcessingInCountriesSpecialTreatments:o}}).filter((e=>e.startsWith("D"))):(0,c.z)({dataProcessingInCountries:t,safeCountries:O,specialTreatments:o})).length>0})):[];let ne=(te.length>0||oe.length>0)&&(w?j:"");ne&&!H&&(ne=ne.replace(/{{legalBasis}}/g,(()=>g(M.map((e=>U[e].dataProcessingInUnsafeCountries||"")),_))));let ie=m?f?I:A:"";ie&&!H&&(ie=ie.replace(/{{minAge}}/gi,`${x}`));let se="";if(k&&!o){const e=T.toLowerCase(),t=e.indexOf("{{services}}")>-1&&e.indexOf("{{servicegroups}}")>-1,o=g(K.map((e=>{let{service:o,legalBasis:n,groupIdx:i,group:{name:s,isEssential:r}}=e;const{name:a}=o;if(!("legal-requirement"===n||$<4&&r||B&&"consent"!==n))return J([t&&[`${i+1}`,s],ne&&te.indexOf(o)>-1&&[Z,V]],a)})),_),n=B?g(K.map((e=>{let{service:o,legalBasis:n,groupIdx:i,group:{name:s}}=e;const{name:r}=o;if("legitimate-interest"===n)return J([t&&[`${i+1}`,s],ne&&te.indexOf(o)>-1&&[Z,V]],r)})),_):"";if(o){const e=`${o}${n}`,i=g(v.map(((o,n)=>{let{name:i}=o;const s=`${n+1}`;return-1===e.indexOf(`>${s}`)?"":J([t&&[s]],i)})),_);se=`<span>${T}</span>`,H||(se=se.replace(/{{services}}/gi,o).replace(/{{serviceGroups}}/gi,i)),ne&&(ne+=J([[Z]]))}n&&(se+=` <span>${B}</span>`,H||(se=se.replace(/{{services}}/gi,n)))}const re=[];if(D&&!N&&!b){let{teaching:e}=z;const{vendorsCount:[t,o]}=z;H||(e=e.replace(/{{consentCount}}/gi,y(Y.length,t,o)).replace(/{{legIntCount}}/gi,y(Q.length,t,o)));const i=` <span>${e}</span>`;re.push((0,n.jsx)(r.N,{},"tcf")),$>7?re.unshift(i):se+=i}const ae=[];return!N&&S&&C&&(0,s.h)(t).length&&ae.push(W.teaching,(0,n.jsx)(a.N,{services:t},"gcm")),{description:ee,teachings:[ne,ie,se,re,ae].flat().filter(Boolean)}}},54989:(e,t,o)=>{o.d(t,{E:()=>s});var n=o(41594),i=o(84094);function s(e){const{tcf:t}=(0,i.Y)();if(!t)return[];const{gvl:{vendors:o},model:s}=t;return(0,n.useMemo)((()=>Object.values(o).filter((t=>{let{["consent"===e?"purposes":"legIntPurposes"]:o}=t;return"consent"===e||o.length>0})).sort(((e,t)=>{let{name:o}=e,{name:n}=t;return o<n?-1:o>n?1:0}))),[o,e,s])}},17140:(e,t,o)=>{o.d(t,{p:()=>s});var n=o(41594),i=o(8700);function s(e){const{functions:{className:t}}=e||(0,i.o)();return(0,n.useMemo)((()=>t()),[])}},41520:(e,t,o)=>{o.d(t,{b:()=>a});var n=o(84094),i=o(52113);const s=Symbol(),r=()=>(0,i.NV)(s);function a(){const e=(0,n.Y)(),t=r();return e.groups?e:t}},64349:(e,t,o)=>{o.d(t,{s:()=>i});var n=o(41520);function i(){const{links:e=[],websiteOperator:t}=(0,n.b)(),o=e.filter((e=>{let{label:t,url:o}=e;return t&&o})),i=o.find((e=>{let{pageType:t}=e;return"privacyPolicy"===t})),s=o.find((e=>{let{pageType:t}=e;return"legalNotice"===t}));return{privacyPolicy:!!i&&{url:i.url,label:i.label},legalNotice:!!s&&{url:s.url,label:s.label},contactForm:(null==t?void 0:t.contactFormUrl)||void 0}}},21917:(e,t,o)=>{o.d(t,{F:()=>i,z:()=>s});var n=o(55285);function i(e){let{predefinedDataProcessingInSafeCountriesLists:t,isDataProcessingInUnsafeCountries:o,territorialLegalBasis:i,service:s}=e,r=[];const a=i.indexOf("gdpr-eprivacy")>-1,l=i.indexOf("dsg-switzerland")>-1,{GDPR:c,DSG:d,"GDPR+DSG":p,ADEQUACY_CH:u,ADEQUACY_EU:g}=t;l&&a?r=p:a?r=c:l&&(r=d);let h=[];const y=[],b={};if(s){const{dataProcessingInCountries:e,dataProcessingInCountriesSpecialTreatments:t}=s;h=e.filter((e=>-1===r.indexOf(e)));const i=h.indexOf("US")>-1;if(i&&y.push(n.ak.ProviderIsSelfCertifiedTransAtlanticDataPrivacyFramework),h.length>0){const e=[...a?g:[],...l?u:[]];(h.filter((t=>-1===e.indexOf(t))).length>0||i)&&(y.push(n.ak.StandardContractualClauses),-1===t.indexOf(n.ak.StandardContractualClauses)&&y.push(n.ak.ContractualAssurancesWithSubProcessors));const s=t.indexOf(n.ak.StandardContractualClauses)>-1,r=!s&&t.indexOf(n.ak.ContractualAssurancesWithSubProcessors)>-1,p=i&&t.indexOf(n.ak.ProviderIsSelfCertifiedTransAtlanticDataPrivacyFramework)>-1,f=t.indexOf(n.ak.BindingCorporateRules)>-1,v=e=>!a&&!l||"US"!==e||p,m=s?"B":r?"C":f?"E":o?"D":void 0;for(const e of h){const t=[],o=g.filter(v).indexOf(e)>-1,n=u.filter(v).indexOf(e)>-1;l&&a?(-1===c.indexOf(e)&&t.push(o?"A-EU":"D"===m?"D-EU":m),-1===d.indexOf(e)&&t.push(n?"A-CH":"D"===m?"D-CH":m)):a?t.push(o?"A":m):l&&t.push(n?"A":m),b[e]=[...new Set(t.filter(Boolean))]}}}return{isGdpr:a,isDsg:l,safeCountries:r,unsafeCountries:h,allowedSpecialTreatments:y,result:b,filter:e=>Object.entries(b).map((t=>{let[o,n]=t;return n.some(e)?o:void 0})).filter(Boolean)}}function s(e){let{dataProcessingInCountries:t,safeCountries:o=[],specialTreatments:i=[],isDisplay:s}=e,r=i;return s&&(r=r.filter((e=>n.ak.StandardContractualClauses!==e))),t.filter((e=>!(r.indexOf(n.ak.StandardContractualClauses)>-1)&&(-1===o.indexOf(e)||"US"===e&&-1===r.indexOf(n.ak.ProviderIsSelfCertifiedTransAtlanticDataPrivacyFramework))))}},81257:(e,t,o)=>{o.d(t,{k:()=>a});var n=o(3713),i=o(41594),s=o(80998);const r=e=>{let{children:t}=e;return(0,n.jsx)(i.Fragment,{children:t})},a=e=>{let{promise:t,children:o,suspenseProbs:a}=e;const l=(0,i.useMemo)((()=>(0,s.g)((t||Promise.resolve()).then((()=>r)),void 0,a)),[t]);return(0,n.jsx)(l,{children:o})}},52113:(e,t,o)=>{o.d(t,{NV:()=>l,gm:()=>c});var n=o(41594),i=o(75933),s=o(56702);const r={};function a(e){let t=r[e];if(!t){const o=(0,n.createContext)({});t=[o,()=>(0,n.useContext)(o)],r[e]=t}return t}const l=e=>a(e)[1]();function c(e,t,o,r){void 0===o&&(o={}),void 0===r&&(r={});const{refActions:l,observe:c,inherit:d,deps:p}=r,u=a(e),[g,h]=(0,n.useState)((()=>{const e=Object.keys(o),n=Object.keys(l||{}),i=function(t){for(var i=arguments.length,r=new Array(i>1?i-1:0),a=1;a<i;a++)r[a-1]=arguments[a];return new Promise((i=>h((a=>{const c={...a},d=[];let p=!0;const u=new Proxy(c,{get:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];const[a,c]=i;let g=Reflect.get(...i);if(!p)return g;if(-1===d.indexOf(c)&&(g=(0,s.G)(g),Reflect.set(a,c,g),d.push(c)),"string"==typeof c){let t;if(e.indexOf(c)>-1?t=o[c]:n.indexOf(c)>-1&&(t=l[c]),t)return function(){for(var e=arguments.length,o=new Array(e),n=0;n<e;n++)o[n]=arguments[n];return t(u,...o)}}return g}}),g=t(u,...r),h=e=>{p=!1,i(e)};return g instanceof Promise?g.then(h):h(void 0),c}))))},r={set:e=>i("function"==typeof e?e:t=>Object.assign(t,e)),...t,...e.reduce(((e,t)=>(e[t]=function(){for(var e=arguments.length,n=new Array(e),s=0;s<e;s++)n[s]=arguments[s];return i(o[t],...n)},e)),{}),...n.reduce(((e,t)=>(e[t]=function(){for(var e=arguments.length,o=new Array(e),n=0;n<e;n++)o[n]=arguments[n];return l[t](g,...o)},e)),{})};return r.suspense||(r.suspense={}),r}));(null==c?void 0:c.length)&&(0,i.C)((()=>{c.filter((e=>t[e]!==g[e])).length&&g.set(c.reduce(((e,o)=>(e[o]=t[o],e)),{}))}),[c.map((e=>t[e]))]),Array.isArray(p)&&(0,i.C)((()=>{g.set(t)}),p);const[{Provider:y}]=u;let b=g;(null==d?void 0:d.length)&&(b={...g,...d.reduce(((e,o)=>(e[o]=t[o],e)),{})});const f=(0,n.useMemo)((()=>({})),[]);return(0,n.useEffect)((()=>{const{suspense:e}=g;if(e)for(const t in e){const o=e[t],n=f[t];o instanceof Promise&&n!==o&&(f[t]=o,o.then((e=>g.set({[t]:e}))))}}),[g]),[y,b]}},75933:(e,t,o)=>{o.d(t,{C:()=>i});var n=o(41594);const i=(e,t)=>{const o=(0,n.useRef)(0);(0,n.useEffect)((()=>{if(o.current++,1!==o.current)return e()}),t)}},88664:(e,t,o)=>{o.d(t,{F:()=>r,H:()=>s});var n=o(52113);const i=Symbol(),s=()=>(0,n.NV)(i);function r(e,t,o){return(0,n.gm)(i,{completed:!1,loaded:[]},{},{refActions:{onMounted:(n,i)=>{let{completed:s,loaded:r,set:a}=n;if(r.push(i),e.every((e=>r.indexOf(e)>-1))&&!s){const e=o||(()=>a({completed:!0}));t?t(e):e()}}}})}},56702:(e,t,o)=>{function n(e,t){if(void 0===t&&(t=new Map),t.has(e))return t.get(e);let o;if("structuredClone"in window&&(e instanceof Date||e instanceof RegExp||e instanceof Map||e instanceof Set))o=structuredClone(e),t.set(e,o);else if(Array.isArray(e)){o=new Array(e.length),t.set(e,o);for(let i=0;i<e.length;i++)o[i]=n(e[i],t)}else if(e instanceof Map){o=new Map,t.set(e,o);for(const[i,s]of e.entries())o.set(i,n(s,t))}else if(e instanceof Set){o=new Set,t.set(e,o);for(const i of e)o.add(n(i,t))}else{if(!function(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(e))return e;o={},t.set(e,o);for(const[i,s]of Object.entries(e))o[i]=n(s,t)}return o}o.d(t,{G:()=>n})},85360:(e,t,o)=>{o.d(t,{i:()=>s});var n=o(3713),i=o(41594);function s(e,t){const o=e.filter(Boolean);return 0===o.length?null:o.reduce(((e,s,r)=>e.length?[...e,(0,n.jsx)(i.Fragment,{children:"function"==typeof t?t(r,o.length):t},r),s]:[s]),[])}},22834:(e,t,o)=>{o.d(t,{G:()=>r});const n=()=>{let e;return[!1,new Promise((t=>e=t)),e]},i={loading:n(),complete:n(),interactive:n()},s=["readystatechange","rocket-readystatechange","DOMContentLoaded","rocket-DOMContentLoaded","rocket-allScriptsLoaded"],r=(e,t)=>(void 0===t&&(t="complete"),new Promise((o=>{let n=!1;const r=()=>{(()=>{const{readyState:e}=document,[t,,o]=i[e];if(!t){i[e][0]=!0,o();const[t,,n]=i.interactive;"complete"!==e||t||(i.interactive[0]=!0,n())}})(),!n&&i[t][0]&&(n=!0,null==e||e(),setTimeout(o,0))};r();for(const e of s)document.addEventListener(e,r);i[t][1].then(r)})))},7533:(e,t,o)=>{async function n(e,t,o){void 0===t&&(t=500),void 0===o&&(o=0);let n=0;for(;!e();){if(o>0&&n>=o)return;await new Promise((e=>setTimeout(e,t))),n++}return e()}o.d(t,{x:()=>n})},80998:(e,t,o)=>{o.d(t,{g:()=>a});var n=o(3713),i=o(41594),s=o(66399),r=o(88664);function a(e,t,o){void 0===o&&(o={fallback:null});const a=(0,i.lazy)((()=>e.then((e=>(0,s.P)({default:e})))));return(0,i.forwardRef)(((e,s)=>{const{onMounted:l}=(0,r.H)();return t&&(0,i.useEffect)((()=>{null==l||l(t)}),[]),(0,n.jsx)(i.Suspense,{...o,children:(0,n.jsx)(a,{...e,ref:s})})}))}},66399:(e,t,o)=>{o.d(t,{P:()=>n});const n=e=>new Promise((t=>setTimeout((()=>t(e)),0)))},50151:(e,t,o)=>{o.d(t,{Z:()=>l});var n=o(71685),i=o.n(n),s=o(29080);const r=(e,t)=>{let o,n,r;t?r=t({width:0,height:0,scrollbar:!1,scrolledTop:!1,scrolledBottom:!1},{width:s.dD,height:s.dD}):(o=e("width"),n=e("height"));const a=[],l=()=>a.forEach(((e,t,o)=>{e(),o.splice(t,1)})),c=(e,t)=>{let s;void 0===t&&(t=[]);const c=()=>{if(!e)return;const{width:t,height:a}=e.getBoundingClientRect(),{clientHeight:l,scrollHeight:c,scrollTop:d,offsetHeight:p}=e,u=l<c,g=Math.ceil(d+p+3)>=c;(0!==t||0!==a||e.offsetParent)&&(clearTimeout(s),s=setTimeout((()=>{r?r[1]({width:t,height:a,scrollbar:u,scrolledTop:0===d,scrolledBottom:g}):i().mutate((()=>{e.style.setProperty(o,`${t}px`),e.style.setProperty(n,`${a}px`)}))}),0))};for(const o of[e,...t]){if(!o)continue;o.addEventListener("animationend",c),o.addEventListener("scroll",c);const e=new ResizeObserver(c);e.observe(o),a.push((()=>{e.disconnect(),o.removeEventListener("animationend",c),o.removeEventListener("scroll",c)}))}return l};return t?[c,r[0],l]:[c,{width:o,height:n},l]},a=(e,t)=>`rgba(${e("r")}, ${e("g")}, ${e("b")}, calc(${t()}/100))`,l=[Symbol("extendBannerStylesheet"),(e,t)=>{let{computed:o,rule:n,boolIf:i,boolNot:l,boolSwitch:c,jsx:d,variable:p,className:u,vars:g,varName:h,plugin:y}=e,{unsetDialogStyles:b,customize:{activeAction:f,pageRequestUuid4:v},boolLargeOrMobile:m,isMobile:S,layout:{dialogPosition:C,dialogMaxWidth:x,dialogMargin:k,dialogBorderRadius:w,bannerPosition:O,overlay:P,overlayBg:j,overlayBgAlpha:A,overlayBlur:I},design:{boxShadowEnabled:T,boxShadowColor:B,boxShadowOffsetX:R,boxShadowOffsetY:D,boxShadowBlurRadius:F,boxShadowSpreadRadius:N,boxShadowColorAlpha:E,fontSize:$,borderWidth:M,borderColor:L,textAlign:_,fontInheritFamily:U,fontFamily:W,fontWeight:z,fontColor:V},mobile:H,layout:G,individualLayout:q}=t;const Y={headline:u(),description:u(),firstButton:u()},Q=r(h,g),J=r(h,g),Z=r(h,g),K=r(h,g),X=r(h,g),ee=p(f,(0,s.$S)(f,["history"])),te=p(!1,void 0,"individual"),{isDialog:oe,isBanner:ne}=o([S,G.type],(e=>{let[t,o]=e;const n=t?"banner":o;return{type:n,isDialog:"dialog"===n,isBanner:"banner"===n}})),ie=o([C],(e=>{let[t]=e;return[t.startsWith("top")?"flex-start":t.startsWith("bottom")?"flex-end":"center",t.endsWith("Center")?"center":t.endsWith("Left")?"flex-start":"flex-end"]}),"dialogAlign"),[se]=d("div",{classNames:"bann3r",position:"fixed",top:0,left:0,right:0,bottom:0,zIndex:999999,filter:"none",maxWidth:"100vw",maxHeight:"100vh",fontSize:$("l"),background:i(P,a(j,A)),pointerEvents:i(P,"all","none"),backdropFilter:"none",transform:"translateZ(0)"}),[re]=d("dialog",{classNames:["align",b],display:"flex",width:"100%",height:"100%",alignItems:i(oe,ie(0)),justifyContent:i(oe,ie(1))},{tabIndex:0,"aria-labelledby":Y.headline,"aria-describedby":Y.description,"aria-modal":"true"}),ae=i({when:oe,then:{when:[te,l(q.inheritDialogMaxWidth)],then:q.dialogMaxWidth(),or:x()},or:"100%"}),{alignment:le}=H,ce=`${m(M,i)} solid ${L()}`,de=i(oe,w()),[,pe]=n({classNames:"inner",pointerEvents:"all",transition:"width 500ms, max-width 500ms",overflow:"hidden",maxWidth:ae,width:ae,textAlign:_("val"),fontFamily:i(U,"inherit",W()),fontWeight:z(),color:V("hex"),margin:i({when:[oe,l(C["is-middlecenter"])],then:k(),or:i(oe,"10px")}),borderRadius:de,boxShadow:i(T,`${R()} ${D()} ${F()} ${N()} rgba(${B("r")} ${B("g")} ${B("b")} / ${E()})`),alignSelf:i({when:S,then:c([[le("is-bottom"),"flex-end"],[le("is-top"),"flex-start"],[le("is-center"),"center"]]),or:{when:ne,then:c([[O("is-top"),"flex-start"],[O("is-bottom"),"flex-end"]])}}),borderTop:i({when:[ne,O("is-bottom")],then:ce}),borderBottom:i({when:[ne,O("is-top")],then:ce})}),ue={boxSizing:"border-box",backfaceVisibility:"initial",textTransform:"initial","-webkit-text-size-adjust":"100%"};n({forceSelector:`#${v}, #${v} *`,...ue}),y("modifyRule",(e=>{if("unset"===e.all)for(const t in ue)Object.prototype.hasOwnProperty.call(e,t)||(e[t]=ue[t])}));const ge=`${i(oe,M("l"),"0px")} solid ${L()}`;return{a11yIds:Y,isDialog:oe,isBanner:ne,activeAction:ee,dimsOverlay:Q,dimsContent:J,dimsHeader:Z,dimsFooter:K,dimsRightSidebar:X,individualPrivacyOpen:te,Dialog:re,Overlay:se,inner:pe,footerBorderStyle:{borderBottomLeftRadius:de,borderBottomRightRadius:de,borderLeft:ge,borderRight:ge,borderBottom:ge},headerBorderStyle:{borderTopLeftRadius:de,borderTopRightRadius:de,borderLeft:ge,borderRight:ge,borderTop:ge}}}]},57114:(e,t,o)=>{o.d(t,{R:()=>n});const n=[Symbol("extendCommonButtonsStylesheet"),(e,t)=>{let{className:o,jsx:n,boolIf:i,boolSwitch:s,boolOr:r}=e,{screenReaderOnlyClass:a,isTcf:l,decision:c,layout:d,saveButton:p,bodyDesign:u,design:g,boolLargeOrMobile:h}=t;const y=o(),b=o(),f=o(),[v,m,S,C]=["acceptAll","acceptEssentials","acceptIndividual","save"].map((e=>{const t="save"===e,o=t?p.type:c[e],a=t?p.bg:u[`${e}Bg`],v=t?p.hoverBg:u[`${e}HoverBg`],m=t?p.padding:u[`${e}Padding`],S=t?p.fontSize:u[`${e}FontSize`],C=t?p.textAlign:u[`${e}TextAlign`],x=t?p.fontWeight:u[`${e}FontWeight`],k=t?p.fontColor:u[`${e}FontColor`],w=t?p.hoverFontColor:u[`${e}HoverFontColor`],O=t?p.borderColor:u[`${e}BorderColor`],P=t?p.hoverBorderColor:u[`${e}HoverBorderColor`],j=t?p.borderWidth:u[`${e}BorderWidth`],A=h(S,i),I=r([l,u.acceptAllOneRowLayout]),[T]=n("a",{classNames:`btn-${e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}`,all:"unset",display:"flex",...t?{}:{display:i(o("is-hide"),"none","flex")},borderRadius:d.borderRadius(),overflow:"hidden",textAlign:C("val"),color:k(),transition:"background-color 250ms, color 250ms, border-color 250ms",cursor:i(o("is-button"),"pointer"),textDecoration:i(o("is-link"),"underline"),backgroundColor:i(o("is-button"),a()),borderStyle:"solid",borderColor:O(),borderWidth:i(o("is-button"),h(j,i),"0px"),flex:"1 1 100%",alignItems:"center",justifyContent:s([[C("is-right"),"flex-end"],[C("is-center"),"center"]],"left"),pointerEvents:i(o("is-link"),"none"),...["acceptEssentials","save"].indexOf(e)>-1?{fontSize:i(I,h(u.acceptAllFontSize,i),A),fontWeight:i(I,u.acceptAllFontWeight(),x()),padding:i(I,u.acceptAllPadding("l"),m("l"))}:{fontSize:A,fontWeight:x(),padding:m("l")},pseudos:{[`.${b}`]:{display:"block"},">span":{cursor:i(o("is-link"),"pointer"),pointerEvents:i(o("is-link"),"all"),color:k()},":hover>span":{color:w()},[`.${y}`]:{outline:"rgb(255, 94, 94) solid 5px"},[`.${f}`]:{position:"sticky",bottom:"6px",boxShadow:`0px 10px 0px 0px ${g.bg()}, 0px -10px 0px ${g.bg()}`},":hover":{color:w(),backgroundColor:i(o("is-button"),v()),borderColor:P(),textDecoration:i(o("is-link"),"none")}}},{href:"#"});return T})),[x]=n("a",{classNames:["skip-to",a],pseudos:{":focus-visible":{clip:"initial",width:"initial",height:"initial",zIndex:10,left:"10px",top:"10px",padding:"5px 10px",background:"black",color:"white",fontSize:"14px"}}},{onClick:e=>{e.preventDefault(),document.getElementById(e.target.getAttribute("href").substr(1)).focus()}});return{A11ySkipToLink:x,framed:y,stickyButton:f,forceShowButton:b,buttons:{acceptAll:v,acceptEssentials:m,acceptIndividual:S,save:C}}}]},45746:(e,t,o)=>{o.d(t,{I:()=>i});var n=o(29080);const i=[Symbol("extendCommonStylesheet"),(e,t)=>{let{control:o,className:i,rule:s,boolIf:r,jsx:a}=e,{a11yFocusStyle:l,boolLargeOrMobile:c,bodyDesign:d,design:p,group:u,layout:g,screenReaderOnlyClass:h,scaleHorizontal:y,isMobile:b}=t;const[f]=a("a",{all:"unset",cursor:"pointer",color:u.linkColor(),textDecoration:p.linkTextDecoration(),pseudos:{":hover":{color:u.linkHoverColor(),textDecoration:"none"}}}),[v]=a("label",{all:"unset"}),[m,S]=o({fontSize:15},{fontSize:n.dD},(e=>{let{fontSize:t}=e;const o=c(u.checkboxBorderWidth,r),[,n]=s({classNames:["checkbox",h],pseudos:{"+div":{aspectRatio:"1/1",height:`calc((${t()} + ${o} * 2 + 6px) * ${r(b,y(),"1")})`,boxSizing:"border-box",display:"inline-block",marginRight:"10px",lineHeight:0,verticalAlign:"middle",padding:"3px",borderRadius:g.borderRadius(),cursor:"pointer",borderStyle:"solid",borderWidth:o,backgroundColor:u.checkboxBg(),color:u.checkboxBg(),borderColor:u.checkboxBorderColor()},":checked+div":{backgroundColor:u.checkboxActiveBg(),color:u.checkboxActiveColor(),borderColor:u.checkboxActiveBorderColor()},"[disabled]+div":{cursor:"not-allowed",opacity:"0.5"},":focus-visible+div":l.outline,"+div+span":{verticalAlign:"middle",cursor:"pointer"},":focus-visible+div+span>span:first-of-type":l.text}});return n})),{fontColor:C}=p,[x]=a("select",{background:"transparent",border:0,fontSize:r(d.descriptionInheritFontSize,c(p.fontSize,r),c(d.descriptionFontSize,r)),color:C("hex"),borderBottom:`1px solid rgba(${C("r")} ${C("g")} ${C("b")} / 50%)`,pseudos:{">option":{background:p.bg()}}}),[k]=a("fieldset",{classNames:"group-button",all:"unset",pseudos:{">label":{all:"unset"}}}),[,w]=s({classNames:["group-button-item",h],pseudos:{"+span":{padding:"5px 10px",color:u.linkColor(),borderRadius:g.borderRadius(),textDecoration:p.linkTextDecoration(),opacity:.8,cursor:"pointer",borderWidth:c(d.acceptAllBorderWidth,r),borderStyle:"solid",borderColor:"transparent"},":checked+span":{opacity:1,cursor:"initial",textDecoration:"initial",background:d.acceptAllBg(),color:d.acceptAllFontColor(),borderColor:d.acceptAllBorderColor()},":not(:checked)+span:hover,:focus-visible+span":{opacity:1,textDecoration:p.linkTextDecoration()},":focus-visible+span":l.outline}}),O=c(d.accordionBorderWidth,r),P=c(d.accordionTitleFontSize,r),[j]=a("div",{classNames:"accordions",margin:c(d.accordionMargin,r),textAlign:"left",lineHeight:"1.5",pseudos:{">div":{borderWidth:"0px",borderTopWidth:O,borderStyle:"solid",borderColor:d.accordionBorderColor()},">div:last-of-type":{borderBottomWidth:O},"+p":{marginTop:"15px"}}}),A=i(),I=i(),[T]=a("div",{classNames:"accordion-item",cursor:"pointer",padding:d.accordionPadding("l"),background:d.accordionBg(),pseudos:{[`.${A},:has(>a:focus-visible)`]:{background:d.accordionActiveBg()},[`:hover:not(.${A},.${I})`]:{background:d.accordionHoverBg()},">a":{display:"flex",alignItems:"center"},[`.${I}`]:{cursor:"initial"}}}),[B]=a("a",{classNames:"accordion-button",all:"unset"}),[,R]=s({classNames:"accordion-arrow",width:P,height:P,flex:`0 0 ${P}`,lineHeight:P,float:"left",marginRight:"10px",color:d.accordionArrowColor()}),[D]=a("div",{classNames:"accordion-title",fontSize:P,color:d.accordionTitleFontColor(),fontWeight:d.accordionTitleFontWeight()}),[F]=a("div",{classNames:"accordion-description",fontSize:c(d.accordionDescriptionFontSize,r),color:d.accordionDescriptionFontColor(),fontWeight:d.accordionDescriptionFontWeight(),margin:c(d.accordionDescriptionMargin,r)});return{checkbox:{style:m,className:S},Link:f,Label:v,Select:x,ButtonGroup:k,buttonGroupItem:w,AccordionList:j,AccordionItem:T,AccordionButton:B,AccordionTitle:D,AccordionDescription:F,accordionArrow:R,accordionItemActive:A,accordionItemDisabled:I}}]},45453:(e,t,o)=>{o.d(t,{C:()=>n});const n=[Symbol("extendCommonGroupsStylesheet"),(e,t)=>{let{jsx:o,boolIf:n,boolNot:i}=e,{group:s,design:r,decision:a,bodyDesign:l,boolLargeOrMobile:c}=t;const[d]=o("fieldset",{classNames:"dotted-groups",all:"unset",marginTop:"10px",lineBreak:"anywhere",lineHeight:2,pseudos:{">span,>label":{paddingRight:"10px",fontSize:n({when:l.dottedGroupsInheritFontSize(),then:c(r.fontSize,n),or:c(l.dottedGroupsFontSize,n)}),whiteSpace:"nowrap",display:"inline-block"},">span>i":{color:l.dottedGroupsBulletColor()},">span>i::after":{paddingRight:"5px",display:"inline-block",content:"'●'"},">span>span":{verticalAlign:"middle",cursor:n(a.groupsFirstView,"pointer")}}}),[p]=o("div",{classNames:"groups",marginTop:"10px",clear:"both",pseudos:{">div:not(:last-of-type)":{marginBottom:c(s.groupSpacing,n)}}}),[u]=o("div",{classNames:"group",background:n(i(s.groupInheritBg),s.groupBg()),padding:c(s.groupPadding,n),borderRadius:s.groupBorderRadius(),textAlign:"left",borderColor:s.groupBorderColor(),borderStyle:"solid",borderWidth:c(s.groupBorderWidth,n)}),[g]=o("fieldset",{classNames:"group-inner",all:"unset",textAlign:"left",color:s.headlineFontColor(),fontSize:c(s.headlineFontSize,n),fontWeight:s.headlineFontWeight()}),[h]=o("div",{classNames:"group-description",color:s.descriptionFontColor(),fontSize:c(s.descriptionFontSize,n),marginTop:"5px"}),[y]=o("div",{classNames:"cookie",marginTop:"10px",pseudos:{">label:first-of-type":{display:"block",marginBottom:"10px"}}}),[b]=o("div",{classNames:"cookie-prop",borderStyle:"solid",borderColor:s.groupBorderColor(),borderWidth:"0px",borderLeftWidth:c(s.groupBorderWidth,n),paddingLeft:"15px",pseudos:{">span>a":{wordBreak:"break-all"}}});return{DottedGroupList:d,GroupList:p,Group:u,GroupInner:g,GroupDescription:h,Cookie:y,CookieProperty:b}}]},88790:(e,t,o)=>{o.d(t,{G:()=>r,u:()=>s});var n=o(18084),i=o(17480);const s=(e,t)=>{let{mainElement:o}=t;o.dispatchEvent(new CustomEvent(`${n._2}${e}`,{detail:{}}))},r=(e,t)=>{let{mainElement:o,varsVal:s}=e,{variable:r,vars:a}=t;return(e,t,l,c)=>{let d;const p=e.map((e=>"function"==typeof e?e(!1):void 0)),u=()=>t(p.map((e=>s.get(e)))),g=(0,i.A)(c||0,(()=>d(u())));for(const e of p)o.addEventListener(`${n._2}${e}`,g);const h=u(),y="object"!=typeof h||Array.isArray(h)?(()=>{const e=r(h,void 0,l);return d=e.update,e})():(()=>{const e=a(h,void 0);return[,d]=e,e[0]})();return y.update=()=>g(),y}}},7900:(e,t,o)=>{o.d(t,{c:()=>n});const n=(e,t)=>{let{settings:{createElement:o,forwardRef:n}}=e,{rule:i}=t;const s=(e,t,s)=>{if(!o)throw new Error("No createElement function passed.");let r,a;if(Array.isArray(t))[r,a]=t;else{const[e,o]=i(t);r=e,a=o}const l="function"==typeof n,c=(t,n)=>{let{children:i,className:r,...c}=t;const d=[a,r].filter(Boolean),{modifyProps:p,...u}=s||{},g={className:d.join(" "),...l?{ref:n}:{},...u,...c};return null==p||p(g),o(e,g,i)},d=l?n(c):c;return d.ruleSelector=r,d.ruleClass=a,[d,r,a]};return{jsx:s,jsxControl:(e,t,o)=>{let[n,i,,r]=t;const{modifyProps:a,...l}=o||{},[c]=s(e,[void 0,i],{...l,modifyProps:e=>{e.style={...n(e),...e.style||{}};const t={};for(const o of r)t[o]=e[o],delete e[o];null==a||a(e,t)}});return c}}}},95680:(e,t,o)=>{o.d(t,{m:()=>i});var n=o(8731);const i=(e,t)=>(o,i)=>{const s=new Map,{rule:r}=(0,n.Z)({...e,rules:s},t);for(const e in i)r({forceSelector:e,...i[e]});e.rules.set(o,Object.fromEntries(s))}},8731:(e,t,o)=>{o.d(t,{Z:()=>s});var n=o(32115);const i=e=>Object.keys(e).reduce(((t,o)=>{let n=e[o];if(n="function"==typeof n?n():n,"string"==typeof n&&n.indexOf("function () {")>-1)throw new Error(`${o} contains a serialized function ("${n}").`);return t[(e=>{const[t]=e;return t.toUpperCase()===t.toLowerCase()||e.indexOf("-")>-1?e:e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))})(o)]=n,t}),{}),s=(e,t)=>{let{vars:o}=t;const{id:s,specifiedIds:r}=e,{runPlugin:a}=e,l=t=>((e,t)=>(void 0===t&&(t=!1),`${e.className.substr(t?0:1)}-${e.inc++}`))(e,t),c=t=>{a("modifyRule",t);const{classNames:o,pseudos:c,forceSelector:d,...p}=t,u=Array.isArray(d)?d.join(" "):d,g=Array.isArray(o)?o:o?o.split(" "):[],h=u||l(!0);if(e.rules.set((0,n.lw)(s,r,h),i(p)),c){const t=h.split(",");for(const o in c){const a=o.split(","),l=t.map((e=>a.map((t=>e===t?void 0:t.startsWith("<")?`${t.substr(1)}${e}`:`${e}${t}`)))).flat().filter(Boolean).join(",");e.rules.set((0,n.lw)(s,r,l),i(c[o]))}}const y=[h.substr(1)];return d||(a("filterClassName",g,y[0],e),y.push(...g)),[h,d?void 0:y.join(" ")]};return{className:l,rule:c,control:(e,t,n)=>{const[i,,s]=o(e,t,!1);return[s,n(i),i,Object.keys(e)]},variant:t=>{const o=l(!0);return[c(t.reduce(((e,t)=>{let[o,n]=t;return e[` ${o(!1)}`]=n,e}),{forceSelector:`${e.className}${o}`}))[0],o.substr(1)]}}}},32115:(e,t,o)=>{o.d(t,{TI:()=>v,fX:()=>m,lw:()=>f});var n=o(71685),i=o.n(n),s=o(88790),r=o(7900),a=o(95680),l=o(8731),c=o(27996),d=o(29080),p=o(18084),u=o(8547),g=o(69858);const h={};function y(e){const{className:t,element:o,extend:n,functions:i,meta:s,toggle:r,specify:a,...l}=e;return l}const b=/,(?![^(]*\))/;function f(e,t,o){const n=-1===o.indexOf(",")?[o]:o.split(b),i=[];for(const o of n)if(i.push(o),o.startsWith(`.${e}`))for(const e of t)i.push(`#${e} ${o}`);return i.join(",")}const v=(e,t,o)=>{void 0===t&&(t={});let{element:n,id:b,inc:m,varsVal:S,extended:C,specifiedIds:x,plugins:k,toggle:w,specify:O,detached:P}=void 0===o?{}:o;const{reuse:j}=t;if(j&&!b&&h[j])return h[j];const A=C||{},I=x||[],T=b?`${b}-ext-${Object.getOwnPropertySymbols(A).length}`:(0,u.$)(4),B=document.createElement("style");B.setAttribute("skip-rucss","true");const R={inc:m||1,id:T,varsVal:S||new Map,settings:t,plugins:k||{filterClassName:[t.filterClassName].filter(Boolean),modifyRule:[t.modifyRule].filter(Boolean)},runPlugin:function(e){for(var t=arguments.length,o=new Array(t>1?t-1:0),n=1;n<t;n++)o[n-1]=arguments[n];for(const t of R.plugins[e])t(...o)},className:`.${T}`,rules:new Map,isExtension:!!b,element:B,mainElement:n||B,specifiedIds:I,extended:A},D=w||(e=>i().mutate((()=>{const{element:t}=R,[o]=document.getElementsByTagName("head"),n=[t,...Object.getOwnPropertySymbols(A).map((e=>A[e].element))];for(const t of n)document.dispatchEvent(new CustomEvent(p.kt,{detail:{stylesheet:R,active:e}})),e?o.appendChild(t):o.removeChild(t)}))),F=O||(e=>{I.indexOf(e)>-1||(I.push(e),i().mutate((()=>{const t=new RegExp(`^[ ]*(\\.${T}.*) {`,"gm"),o=(t,o)=>`${f(T,[e],o)} {`;for(const e of[R.mainElement,...Object.getOwnPropertySymbols(A).map((e=>A[e].element))]){const{textContent:n}=e;e.textContent=n.replace(t,o)}})))}),N=(0,d.yq)(R,P),E=(0,l.Z)(R,N),$=(0,s.G)(R,N),M=(0,a.m)(R,N),L=(0,c.s)(R,N),_=(0,r.c)(R,E),U={...E,...N,...L,..._,nestedQuery:M,computed:$,plugin:(e,t)=>{R.plugins[e].push(t)}},W=e({meta:R,...U});i().mutate((()=>{B.textContent=(0,g.u)(R.rules);for(const e of[B,document])e.dispatchEvent(new CustomEvent(p.Iy,{detail:{stylesheet:R}}))}));const z=R.inc,V=function(e,o,n,i){void 0===i&&(i=[]);const{extended:s,mainElement:r}=R,a=Object.assign({_chain:i},W,...i.map((e=>y(s[e]))));if(!s[e]){s[e]=v((e=>o(e,a)),t,{toggle:D,detached:n||!1,...R,inc:n?z:R.inc});const i=Object.keys(a);for(const t of Object.keys(y(s[e])))i.indexOf(t)>-1&&console.warn(`"${t}" detected in multiple stylesheets. This will lead to side effects.`);r.isConnected&&D(!0)}return-1===i.indexOf(e)&&i.push(e),{...a,...s[e],extend:(e,t,o)=>V(e,t,o,i)}},H={...W,meta:R,element:R.element,className:R.id,specify:F,toggle:D,extend:V,functions:U};return j&&!b&&(h[j]=H),H},m=(e,t,o)=>{void 0===o&&(o=!1);const{id:n,overwrite:s=!0}="string"==typeof t?{id:t}:t||{},r=`pure-css-stylesheet-${n||(0,u.$)(5)}`;let a=document.getElementById(r);if(a){if(!s)return a.remove}else a=document.createElement("style"),a.setAttribute("skip-rucss","true"),a.style.type="text/css",a.id=r,i().mutate((()=>{document.getElementsByTagName(o?"body":"head")[0].appendChild(a)}));return a.innerHTML=e,a.remove}},27996:(e,t,o)=>{o.d(t,{G:()=>n,s:()=>i});const n=" ",i=(e,t)=>{let{variable:o,vars:i}=t;const s=(e,t,r)=>{let a,l,c;if("object"!=typeof e||Array.isArray(e))a=e,l=t,c=r;else{const{when:t,then:o,or:n}=e;a=t,l=o,c=n}if(c=c||n,Array.isArray(a)){const e={when:void 0,then:void 0,or:void 0};let t=e;const{length:o}=a;for(let e=0;e<o;e++)t.when=a[e],t.or=c,e===o-1?t.then=l:(t.then={when:void 0,then:void 0,or:c},t=t.then);return s(e)}{"string"==typeof a&&a.startsWith("--")&&(a=`var(${a})`);const[e]=i({true:"object"==typeof l?s(l):l,false:`${"function"==typeof a?a():a} ${"object"==typeof c?s(c):c}`});if("inherit"===c)throw new Error('Due to the nature how conditionals work in CSS, it is not allowed to use "inherit" as a falsy value. Please reverse your condition with the help of "boolNot" or use another value.');return o(e.false(!0,e.true()))()}},r=(e,t)=>{const o={when:void 0,then:void 0,or:void 0},{length:n}=e;let i=o;for(let o=0;o<n;o++){const[s,r]=e[o];i.when=s,i.then=r,o===n-1?i.or=t:(i.or={when:void 0,then:void 0,or:void 0},i=i.or)}return s(o)};return{boolIf:s,boolSwitch:r,boolNot:e=>{let t=e;return"string"==typeof t&&t.startsWith("var(")&&(t=t.slice(4,-1)),`var(${"function"==typeof t?t(!1):t}-not)`},boolOr:e=>r(e.map((e=>[e,"initial"])),n)}}},29080:(e,t,o)=>{o.d(t,{$S:()=>f,Kn:()=>b,a$:()=>v,dD:()=>h,g$:()=>y,gJ:()=>S,tD:()=>g,xj:()=>m,yq:()=>l});var n=o(88790),i=o(27996),s=o(18084),r=o(46256),a=o(52820);const l=(e,t)=>{const{className:o,isExtension:r,rules:l,id:p,element:g}=e,h=r&&!t?o.split("-ext")[0]:o,y=t?p.split("-ext")[0]:p,b=t=>`--${y}-${e.inc++}${t?`-${t}`:""}`,f=(t,o,i)=>{const r=b(i);e.varsVal.set(r,t),l.set(h,l.get(h)||{});const a=l.get(h),p=d(t,o);return u(r,p,((e,t)=>{a[e]=t})),((e,t,o,i)=>{const{element:r}=e,a=(e,o)=>{void 0===e&&(e=!0);const n=`${t}${["number","string"].indexOf(typeof e)>-1?`-${e}`:""}`;return["boolean","number","string"].indexOf(typeof e)>-1&&!1!==e?`var(${n}${o?`, ${o}`:""})`:t},l=new Map;return u(t,o,((e,t,o)=>{void 0!==o&&(a[o]=e),l.set(e,t)})),a.update=(o,p)=>{let g=p||r.textContent;if(!p&&!r.textContent)return r.addEventListener(s.Iy,(()=>a.update(o)),{once:!0}),g;let h=!1;const y=d(o,i);return u(t,y,((e,t)=>{l.get(e)!==t&&(l.set(e,t),g=c(g,e,t),h=!0)})),h&&(p||(r.textContent=g),e.varsVal.set(t,o),(0,n.u)(t,e)),g},a})(e,r,p,o)};return{varName:b,variable:f,vars:(e,t,o)=>{void 0===o&&(o=!0);const n={};for(const i in e){const s=e[i],r=null==t?void 0:t[i];n[i]=f(s,r,o?(0,a.Y)(i):void 0)}return[n,e=>{let{textContent:t}=g;for(const i in e){var o;t=null==(o=n[i])?void 0:o.update(e[i],t)}return t!==g.textContent&&(g.textContent=t),t},e=>{const o={},s=(e,t)=>{if(e.endsWith("-not"))throw new Error(`Boolish variable "${e}" cannot be created as style-attribute in your HTML tag as this is not supported by browsers. Alternatively, use a classname and pseudos to toggle styles.`);o[e]=""===t?i.G:t};for(const o in e){const i=n[o];if(!i)continue;const r=i(!1),a=null==t?void 0:t[o];u(r,d(e[o],a),s)}return o}]}}},c=(e,t,o)=>e.replace(new RegExp(`^((?:    |      )${t}: )(.*)?;$`,"m"),`$1${o};`),d=(e,t)=>"string"==typeof e&&e.startsWith("var(--")?e:t?t(e):e,p=e=>"boolean"==typeof e?e?"initial":"":Array.isArray(e)?e.join(" "):e,u=(e,t,o)=>{const n=[],i=(e,t)=>{"boolean"==typeof t&&o(`${e}-not`,p(!t))},s=(e,t,n)=>{if("string"==typeof t&&t.indexOf("function () {")>-1)throw new Error(`${e} contains a serialized function ("${t}").`);o(e,t,n)};if(Array.isArray(t)){s(e,t.map(p).join(" "));for(let o=0;o<t.length;o++){const r=`${e}-${o}`;i(r,t[o]),s(r,p(t[o]),o),n.push(o)}}else if("object"==typeof t)for(const o in t){const r=`${e}-${(0,a.Y)(o)}`;i(r,t[o]),s(r,p(t[o]),o),n.push(o)}else i(e,t),s(e,p(t));return n},g=e=>t=>`${t}${e}`,h=g("px"),y=("px",e=>e.map((e=>`${e}px`)));const b=e=>{const{r:t,g:o,b:n}=(0,r.E)(e);return{r:t,g:o,b:n,hex:e}},f=(e,t)=>e=>({...t.reduce(((t,o)=>(t[`is-${o.toLowerCase()}`]=e===o,t)),{}),...v(!1)(e)}),v=e=>(void 0===e&&(e=!0),t=>{const o=null==t?void 0:t.length,n=t||"";return{"is-empty":!o,"is-filled":!!o,val:e?JSON.stringify(n):n}}),m=e=>'"undefined"',S=function(e,t){return Object.keys(e).reduce(((e,o)=>(e[o]=t,e)),{})}},18084:(e,t,o)=>{o.d(t,{Iy:()=>n,_2:()=>s,kt:()=>i});const n="stylesheet-created",i="stylesheet-toggle",s="css-var-update-"},8547:(e,t,o)=>{o.d(t,{$:()=>n});const n=e=>{const t=new Uint8Array(e/2);return window.crypto.getRandomValues(t),`a${Array.from(t,(e=>`0${e.toString(16)}`.slice(-2))).join("")}`}},46256:(e,t,o)=>{o.d(t,{E:()=>n});const n=e=>{const t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:{r:0,g:0,b:0}}},69858:(e,t,o)=>{o.d(t,{u:()=>i});const n=4;function i(e,t){void 0===t&&(t=1);const o=" ".repeat(t*n);return[...e.keys()].map((s=>{const r=e.get(s);return`${s} {\n${Object.keys(r).map((e=>{const n=r[e];if("object"==typeof n){const s=new Map;return s.set(e,n),`${o}${i(s,t+1)}\n`}return`${o}${e}:${" ".repeat(1)}${n};\n`})).join("")}${t>1?" ".repeat((t-1)*n):""}}`})).join("\n")}},52820:(e,t,o)=>{o.d(t,{Y:()=>l});const n={},i="àáäâèéëêìíïîòóöôùúüûñç·/_,:;",s="aaaaeeeeiiiioooouuuunc------",r=i.replace(/\//g,"\\/"),a=new RegExp(`[${r}]`,"g");function l(e){if(n[e])return n[e];const t=e.trim().toLowerCase().replace(a,(e=>s.charAt(i.indexOf(e)))).replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-");return n[e]=t,t}},17480:(e,t,o)=>{o.d(t,{A:()=>n});const n=(e,t)=>{if("raf"===e){let e=!1;return()=>{e||(window.requestAnimationFrame((()=>{t(),e=!1})),e=!0)}}{let o;return()=>{clearTimeout(o),o=setTimeout(t,e)}}}},6995:(e,t,o)=>{o.r(t),o.d(t,{ChecklistStore:()=>Be,ConsentStore:()=>Ue,CookieStore:()=>nt,CustomizeBannerStore:()=>rt,OptionStore:()=>ft,RootStore:()=>Gt,ScannerStore:()=>Tt,StatsStore:()=>Dt,TcfStore:()=>Ht,useStores:()=>qt});var n=o(3713);const i=devowlWp_utils,s=wp;var r=o.n(s);const a=devowlWp_customize;var l=o(22834),c=o(7533),d=o(57922),p=o(50903),u=o(57177),g=o(16215),h=o(58742),y=o(40570),b=o(20089),f=o(41594),v=o(66399);var m=o(84094),S=o(80072),C=o(80998),x=o(88664),k=o(50151),w=o(71685),O=o.n(w),j=o(57114),A=o(29080);const I=[Symbol("extendBannerContentStylesheet"),(e,t)=>{let{boolIf:o,boolSwitch:n,boolOr:i,computed:s,boolNot:r,jsx:a,variable:l}=e,{dimsOverlay:c,dimsHeader:d,dimsFooter:p,dimsRightSidebar:u,boolLargeOrMobile:g,isMobile:h,isBanner:y,design:b,bodyDesign:f,headerDesign:v,layout:m,decision:S,mobile:C,texts:x,activeAction:k,footerDesign:w,individualLayout:O,individualPrivacyOpen:P,footerBorderStyle:j,headerBorderStyle:I}=t;const T=s([v.logo,v.logoRetina,v.logoFitDim,v.logoRetinaFitDim,v.logoMaxHeight],(e=>{let[t,o,n,i,s]=e;const r=o&&!(null==t?void 0:t.endsWith(".svg"))&&window.devicePixelRatio>1?i:n;return(null==r?void 0:r[0])>0?{width:(0,A.dD)(r[0]),height:(0,A.dD)(r[1])}:{width:"auto",height:(0,A.dD)(s)}})),B=o({when:y,then:{when:[P,r(O.inheritBannerMaxWidth)],then:O.bannerMaxWidth(),or:m.bannerMaxWidth()}}),R=g(v.borderWidth,o),[D]=a("div",{classNames:"header-container",position:"sticky",zIndex:9,top:0,background:o(v.inheritBg,b.bg(),v.bg()),padding:g(v.padding,o),paddingBottom:`calc(${R} + ${g(v.padding,o,2)})`,...I,pseudos:{":has(>div:empty)":{display:"none"},":has(>div:empty)+div":I,":after":{content:"''",display:"block",position:"absolute",left:"0px",right:"0px",bottom:"0px",background:v.borderColor(),height:R},">div":{transition:"width 500ms, max-width 500ms",maxWidth:B,margin:"auto",display:"flex",alignItems:"center",position:"relative",textAlign:o(v.inheritTextAlign,b.textAlign("val"),v.textAlign("val")),justifyContent:o(v.inheritTextAlign,n([[b.textAlign("is-center"),"center"],[b.textAlign("is-right"),"flex-end"]]),n([[v.textAlign("is-center"),"center"],[v.textAlign("is-right"),"flex-end"]])),flexDirection:o({when:[v.logo("is-filled"),x.headline("is-filled")],then:n([[v.logoPosition("is-left"),"row"],[v.logoPosition("is-right"),"row-reverse"]],"column")})},">div>img":{margin:g(v.logoMargin,o),width:T.width(),height:T.height()}}}),F=n([[[k("is-filled"),S.showCloseIcon()],"51px"]],"0px"),N=c[1].height(),E=l(`calc(${N} - ${o(y,"0px","20px")} - ${F})`),$=l(`calc(100px + ${u[1].height()} + ${d[1].height()} + ${p[1].height()})`),[M]=a("div",{classNames:"content",position:"relative",overflow:"auto",maxHeight:o({when:h,then:{when:P,then:`calc(${N} - ${F})`,or:`calc(min(${N}, ${C.maxHeight()}) - ${F})`},or:{when:i([P,r(m.maxHeightEnabled)]),then:E(),or:`min(max(${m.maxHeight()}, ${$()}), ${E()})`}}),..."Win32"===navigator.platform?{overflow:CSS.supports("overflow","overlay")?"overlay":"scroll",scrollbarWidth:"thin",scrollbarColor:`${f.teachingsFontColor()} transparent`,pseudos:{"::-webkit-scrollbar":{width:"11px"},"::-webkit-scrollbar-track":{background:"transparent"},"::-webkit-scrollbar-thumb":{background:f.teachingsFontColor(),borderRadius:m.dialogBorderRadius(),border:`3px solid ${b.bg()}`}}}:{}}),L=g(w.borderWidth,o),[_]=a("div",{classNames:"footer-container",fontWeight:w.fontWeight(),color:w.fontColor(),position:"sticky",bottom:"0px",zIndex:1,padding:g(w.padding,o),paddingTop:`calc(${L} + ${g(w.padding,o,0)})`,background:o(w.inheritBg,b.bg(),w.bg()),fontSize:g(w.fontSize,o),textAlign:o(w.inheritTextAlign,b.textAlign("val"),w.textAlign()),...j,pseudos:{":after":{content:"''",display:"block",position:"absolute",left:"0px",right:"0px",top:"0px",background:w.borderColor(),height:L},">div":{transition:"width 500ms, max-width 500ms",maxWidth:B,margin:"auto",lineHeight:"1.8"},":has(>div:empty)":{display:"none"}}});return{HeaderContainer:D,Content:M,FooterContainer:_}}];var B=o(20658),R=o(30680),D=o(85360);const F=[Symbol("extendCommonContentStylesheet"),(e,t)=>{let{className:o,rule:n,boolIf:i,control:s,jsx:r}=e,{boolLargeOrMobile:a,design:l,headerDesign:c,footerDesign:d}=t;const p=o(),[u,[g,h,y,b]]=s({color:"#000000",width:10,thickness:1,rectX:0,rectY:0},{width:A.dD,thickness:A.dD,rectX:A.dD,rectY:A.dD},(e=>{let{color:t,width:i,thickness:s,rectX:r,rectY:a}=e;const l=o(),[c,d]=n({display:"block"}),[p,u]=n({position:"fixed !important",zIndex:99,top:a(),left:r()}),[g,h]=n({display:"block"});return n({forceSelector:`${c},${g}`,position:"absolute",top:"50%",right:"0px",display:"block",pointerEvents:"none",marginTop:`calc(${i()} / 2 * -1)`}),n({forceSelector:`${g},${p}`,cursor:"pointer",pointerEvents:"all",pseudos:{[`.${l}`]:{outline:"rgb(255, 94, 94) solid 5px"},":after":{width:"auto !important"},">span>span":{position:"absolute",top:"50%",left:"50%",transform:"rotate(45deg) translate(-50%, -50%)",transformOrigin:"top left",transition:"all 420ms",opacity:"0.5",pointerEvents:"none",backgroundColor:t(),width:s(),height:i()},">span>span:nth-child(1)":{transform:"rotate(-45deg) translate(-50%, -50%)"},":hover>span>span":{opacity:1,width:`calc(${s()} + 1px)`}}}),n({forceSelector:`${g},${p},${c}`,width:i(),aspectRatio:"1/1"}),[l,d,u,h]})),[f]=r("div",{classNames:"headline",all:"unset",color:c.fontColor(),lineHeight:1.8,fontSize:a(c.fontSize,i),fontFamily:i(c.fontInheritFamily,"inherit",c.fontFamily()),fontWeight:c.fontWeight(),pseudos:{[`.${p}`]:{paddingRight:"20px"}}},{role:"heading","aria-level":"2"}),v=l.linkTextDecoration(),[m]=r("a",{classNames:"footer-link",textDecoration:v,fontSize:a(d.fontSize,i),color:d.fontColor(),fontFamily:i(d.fontInheritFamily,"inherit",d.fontFamily()),padding:"0 5px",pseudos:{":hover":{color:d.hoverFontColor(),textDecoration:"none"}}}),S=a(d.fontSize,i),[C]=r("span",{padding:"0 5px",position:"relative",cursor:"pointer",display:"inline-block",pseudos:{">select":{all:"unset",background:"transparent",border:0,fontSize:S,color:d.fontColor(),fontFamily:i(d.fontInheritFamily,"inherit",d.fontFamily()),textDecoration:"underline",paddingRight:"15px"},'[data-flag="true"]>select':{paddingLeft:"23px"}," option":{background:l.bg()},"::after":{position:"absolute",content:"''",border:`solid ${d.fontColor()}`,borderWidth:"0 2px 2px 0",display:"inline-block",padding:"2px",transform:"rotate(45deg) translate(-50%, -50%)",right:"8px",top:"50%",pointerEvents:"none"}," span":{position:"absolute",left:"6px",top:"0px",bottom:"0px",width:"17px",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"contain",pointerEvents:"none"}}});return{hasCloseIcon:p,closeIcon:{framed:g,closeIcon:u,portalPlaceholder:h,portalIcon:y,notPortalIcon:b},HeaderTitle:f,FooterLink:m,FooterLanguageSwitcherSelect:C}}];var N=o(8700);const E=e=>{let{children:t,...o}=e;const{FooterLink:i}=(0,N.o)().extend(...F);return(0,n.jsx)(i,{...o,children:t})};var $=o(32115);const M=e=>{let{title:t,position:n,color:i,size:s,always:r,rounded:a,noAnimate:l,bounce:c,className:d}=e;return Promise.all([o.e(343),o.e(4)]).then(o.t.bind(o,37386,17)).then((e=>{let{default:t}=e;return(0,$.fX)(t,{id:"hint-css",overwrite:!1})})),{"aria-label":t,className:`hint--${n} hint--${s} ${i?`hint--${i}`:""} ${r?"hint--always":""} ${a?"hint--rounded":""} ${l?"hint--no-animate":""} ${c?"hint--bounce":""} ${d||""}`}},L=(0,f.forwardRef)(((e,t)=>{let{children:o,title:i,position:s,color:r,size:a,always:l,rounded:c,noAnimate:d,bounce:p,className:u,...g}=e;return(0,n.jsx)("div",{ref:t,...g,...M({title:i,always:l,position:s,color:r,size:a,rounded:c,noAnimate:d,bounce:p,className:u}),children:o})})),_=e=>{let{poweredLink:t,affiliate:o}=e;return(0,n.jsxs)(E,{href:o?o.link:t.href,target:t.target,children:[(0,n.jsx)("span",{dangerouslySetInnerHTML:{__html:t.innerHTML}}),o&&(0,n.jsxs)(L,{title:o.description,position:"top-left",children:[" ",o.labelBehind]})]},"powered-by")};var U=o(41520);const W=()=>{const{FooterLanguageSwitcherSelect:e}=(0,N.o)().extend(...F),{footerDesign:{languageSwitcher:t},languageSwitcher:o,onLanguageSwitch:i}=(0,m.Y)(),s=(0,f.useMemo)((()=>o.find((e=>{let{current:t}=e;return t}))),[o]),r="flags"===t&&!!(null==s?void 0:s.flag);return(0,n.jsxs)(e,{"data-flag":r,children:[r&&(0,n.jsx)("span",{style:{backgroundImage:`url(${s.flag})`}}),(0,n.jsx)("select",{value:null==s?void 0:s.locale,"aria-label":null==s?void 0:s.name,onChange:e=>{null==i||i(o.find((t=>{let{locale:o}=t;return o===e.target.value})))},children:o.map((e=>{let{locale:t,name:o}=e;return(0,n.jsx)("option",{value:t,children:o},t)}))})]})},z=(0,f.forwardRef)(((e,t)=>{const{FooterContainer:o}=(0,R.y)().extend(...I),i=(0,m.Y)(),{isTcf:s,layout:{type:r},footerDesign:{languageSwitcher:a},individualPrivacyOpen:l,onClose:c,i18n:{tcf:d},isConsentRecord:p,languageSwitcher:u,set:g}=i,h=(0,f.useCallback)((e=>{c(),e.preventDefault()}),[c]),{rows:y,render:b}=function(e){let{onClose:t,putPoweredByLinkInRow:o,row1:i,row1End:s}=void 0===e?{}:e;const{i18n:{close:r},footerDesign:{poweredByLink:a},poweredLink:l,links:c=[],affiliate:d}=(0,U.b)(),p={target:"_blank",rel:"noopener"},u=[[t&&(0,n.jsx)(E,{href:"#",onClick:t,"interaction-player-skip":"1",children:(0,n.jsx)("strong",{children:r})},"close"),...i||[],...c.map(((e,t)=>{let{label:o,url:i,isTargetBlank:s}=e;return!!o&&!!i&&(0,n.jsx)(E,{href:i,...void 0===s||s?p:{},children:o},`${t.toString()}-links`)})),...s||[]].filter(Boolean),[]];l&&a&&u[o||0].push((0,n.jsx)(_,{poweredLink:l,affiliate:d},"poweredBy"));const g=(0,f.useCallback)((e=>(0,D.i)(e.map((e=>e.length?(0,n.jsx)(f.Fragment,{children:(0,D.i)(e,(0,n.jsx)("span",{"aria-hidden":!0,children:" • "}))},e[0].key):null)),(0,n.jsx)("br",{"aria-hidden":!0}))),[]);return{rows:u,render:g}}({onClose:p?h:void 0,putPoweredByLinkInRow:"banner"===r?0:1,row1:[s&&!l&&(0,n.jsx)(E,{href:"#",onClick:()=>g({individualPrivacyOpen:!0}),children:d.vendorList},"vendorList")],row1End:[(null==u?void 0:u.length)>0&&a&&"disabled"!==a&&(0,n.jsx)(W,{},"languageSwitcher")]});return(0,n.jsx)(o,{ref:t,children:(0,n.jsx)("div",{children:b(y)})})}));function V(e){const t=window.matchMedia(e);return[t,e=>{try{t.addEventListener("change",e)}catch(o){try{t.addListener(e)}catch(e){}}},e=>{try{t.removeEventListener("change",e)}catch(o){try{t.removeListener(e)}catch(e){}}}]}function H(e,t){const o=e.map((e=>V(e))),n=()=>o.map((e=>{let[t]=e;return t.matches})),[i,s]=(0,f.useState)(n);return(0,f.useLayoutEffect)((()=>{if(t)return()=>{};const e=()=>s(n);return o.forEach((t=>{let[,o]=t;return o(e)})),()=>o.forEach((t=>{let[,,o]=t;return o(e)}))}),[t]),i}const G=700;var q=o(73950),Y=o(58552),Q=o(75206);const J=e=>{let{active:t=!0,children:o,className:i,tag:s="div",renderInContainer:r}=e;const[a]=(0,f.useState)((()=>{const e=document.createElement(s);return i&&e.classList.add(...i.split(" ")),e}));return(0,f.useEffect)((()=>(document.body.appendChild(a),()=>{document.body.removeChild(a)})),[]),t?(0,Q.createPortal)(o,r||a):(0,n.jsx)(f.Fragment,{children:o})},Z=e=>{let{width:t,color:o,tooltipText:i,framed:s,renderInContainer:r,tooltipAlways:a,onClick:l,thickness:c=1}=e;const{closeIcon:{framed:d,closeIcon:p,notPortalIcon:u,portalIcon:g,portalPlaceholder:h}}=(0,N.o)().extend(...F),y=(0,f.useRef)(),b=(0,Y.y)(y,{observe:!!r}),v=p({color:o,width:t,thickness:c,...(null==b?void 0:b.y)>0?{rectX:b.x,rectY:b.y}:{}}),m=(0,n.jsx)(L,{"aria-hidden":!0,title:i,onClick:l,position:r?(null==b?void 0:b.y)>50?"top-left":"left":"top-left",always:a,className:`${r?g:u} ${s?d:""}`,style:v,children:(0,n.jsxs)("span",{"aria-hidden":!0,children:[(0,n.jsx)("span",{}),(0,n.jsx)("span",{})]})});return r?(0,n.jsxs)(f.Fragment,{children:[(0,n.jsx)("div",{className:h,style:v,ref:y}),(null==b?void 0:b.y)>0&&(0,n.jsx)(J,{renderInContainer:r,children:m})]}):m},K=()=>{var e;const{headerDesign:{fontColor:t,fontSize:o},texts:{acceptEssentials:i},activeAction:s,pageRequestUuid4:r,i18n:{close:a,closeWithoutSaving:l},buttonClicked:c=""}=(0,m.Y)(),{buttonClickedCloseIcon:d,closeIcon:p}=(0,q.C)(),[u]=H(["(max-width: 700px)"]);return(0,n.jsx)(Z,{width:o,color:t,tooltipText:s?"change"===s?l:a:i,tooltipAlways:u,framed:c===d,renderInContainer:null==(e=document.getElementById(r))?void 0:e.querySelector("dialog"),onClick:p})},X=(0,f.forwardRef)(((e,t)=>{let{className:o}=e;const i=(0,R.y)(),{a11yIds:s,HeaderContainer:r,hasCloseIcon:a,HeaderTitle:l}=i.extend(...I).extend(...F),{headerDesign:{logo:c,logoRetina:d,logoAlt:p},decision:{showCloseIcon:u},texts:{headline:g},activeAction:h,individualPrivacyOpen:y,individualTexts:b,i18n:{headerTitlePrivacyPolicyHistory:f}}=(0,m.Y)(),v=d&&!(null==c?void 0:c.endsWith(".svg"))&&window.devicePixelRatio>1?d:c,S=!!u||!!h,C=y?"history"===h?f:b.headline:g;return(0,n.jsx)(r,{ref:t,className:o,children:(0,n.jsxs)("div",{children:[!!v&&(0,n.jsx)("img",{"aria-hidden":!0,alt:p||"",src:v}),!!C&&(0,n.jsx)(l,{id:s.headline,className:S?a:void 0,children:C}),S&&(0,n.jsx)(K,{})]})})})),ee=(0,C.g)(Promise.resolve(X),"BannerHeader"),te=(0,C.g)(Promise.resolve(B.R),"BannerBody"),oe=(0,C.g)(Promise.resolve(z),"BannerFooter");function ne(e,t){return!(!e||1!==e.nodeType||!e.parentElement)&&e.matches(t)}const ie='[href^="#consent-"]';var se=o(46256);let re;function ae(e,t,o){void 0===t&&(t=""),clearTimeout(re),re=setTimeout((()=>{const n=t.replace(/\.rcb-([A-Za-z0-9_-]+)/g,((t,o)=>{var n;return`:is(.${(null==(n=e[`rcb-${o}`])?void 0:n.join(",."))||t.substring(1)})`}));(0,$.fX)(n,`custom-css-${o}`,!0)}),0)}function le(e,t){const o=(0,f.useRef)(0),n=(0,f.useRef)(0),[i,s]=(0,f.useState)(e),[r,a]=(0,f.useState)(void 0),[l,c]=(0,f.useState)(t),[d,p]=(0,f.useState)(void 0);return(0,f.useEffect)((()=>{o.current>0&&("none"===e?s(e):(s("none"),a(e))),o.current++}),[e]),(0,f.useEffect)((()=>{n.current>0&&(0===t?c(t):(c(0),p(t),s("none"),a(e))),n.current++}),[t]),(0,f.useEffect)((()=>{void 0!==r&&(s(r),a(void 0))}),[r]),(0,f.useEffect)((()=>{void 0!==d&&(c(d),p(void 0))}),[d]),[i,l]}function ce(e){let{isVisible:t,animationIn:o,animationOut:n,animationInDuration:i,animationOutDuration:s,animationInDelay:r,animationOutDelay:a}=e;return t?{animation:o,duration:i,delay:r}:{animation:n,duration:s,delay:a}}const de=e=>{let{animateOnMount:t=!0,isVisible:o=!0,animationIn:i="fadeIn",animationOut:s="fadeOut",animationInDelay:r=0,animationOutDelay:a=0,animationInDuration:l=1e3,animationOutDuration:c=1e3,className:d="",style:p={},children:u}=e;const[{animation:g,duration:h,delay:y},b]=(0,f.useState)(t?ce({isVisible:o,animationIn:i,animationOut:s,animationInDelay:r,animationOutDelay:a,animationInDuration:l,animationOutDuration:c}):{animation:"",delay:void 0,duration:0});(0,f.useEffect)((()=>{b(ce({isVisible:o,animationIn:i,animationOut:s,animationInDelay:r,animationOutDelay:a,animationInDuration:l,animationOutDuration:c}))}),[o,i,s,r,a,l,c]);const v=`animate__animated animate__${g} ${d}`,m=g?{}:{opacity:o?1:0,transition:`opacity ${y}ms`};return(0,n.jsx)("div",{className:v,style:{animationDelay:`${y}ms`,animationDuration:`${h}ms`,pointerEvents:o?"all":"none",...p,...m},children:u})},pe=(0,C.g)(Promise.resolve((()=>{const{Content:e,hideOnMobileClass:t,dimsContent:o,dimsOverlay:i,dimsHeader:s,dimsFooter:r,dimsRightSidebar:a,A11ySkipToLink:l,a11yIds:{firstButton:c}}=(0,R.y)().extend(...j.R).extend(...I),{decision:{acceptAll:d,acceptEssentials:p,showCloseIcon:u},mobile:g,individualPrivacyOpen:h,bodyDesign:{acceptEssentialsUseAcceptAll:y},activeAction:b,pageRequestUuid4:S,i18n:{skipToConsentChoices:C}}=(0,m.Y)(),x=(0,f.useRef)(),k=y&&d===p?d:p,w=!g.hideHeader||b||h||"hide"===k&&u?"":t,P=(0,f.useRef)();P.current=P.current||{};const A=(0,f.useCallback)((()=>[document.querySelector(`#${S} div[class*="animate__"]`)]),[S]),T=(0,f.useCallback)(((e,t)=>{let[o,,n]=e;t?o(t,A()):n()}),[A]),B=(0,f.useCallback)((e=>T(s,e)),[T]),D=(0,f.useCallback)((e=>T(r,e)),[T]),F=(0,f.useCallback)((e=>T(a,e)),[T]);return(0,f.useEffect)((()=>{const e=A(),t=[o[0](x.current),i[0](document.querySelector(`#${S}`),e)];return()=>t.forEach((e=>e()))}),[]),(0,f.useEffect)((()=>{O().mutate((()=>(0,v.P)().then((()=>x.current.scrollTop=0))))}),[h]),(0,n.jsxs)(e,{ref:x,children:[(0,n.jsx)(l,{href:`#${c}`,children:C}),(0,n.jsx)(ee,{ref:B,className:w}),(0,n.jsx)(te,{rightSideContainerRef:F}),(0,n.jsx)(oe,{ref:D})]})})),"BannerContent"),ue=(0,C.g)(Promise.all([o.e(343),o.e(4)]).then(o.bind(o,63362)).then((e=>{let{BannerSticky:t}=e;return t}))),ge=(e,t)=>{const{dataset:o,style:n}=document.body;void 0===o.rcbPreviousOverflow&&(o.rcbPreviousOverflow=n.overflow),n.overflow=e&&t?"hidden":o.rcbPreviousOverflow,document.body.parentElement.style.overflow=n.overflow},he=(0,C.g)(Promise.resolve((()=>{const e=(0,m.Y)(),{recorder:t,visible:o,activeAction:i,isConsentGiven:s,skipOverlay:r,pageRequestUuid4:a,individualPrivacyOpen:l,fetchLazyLoadedDataForSecondView:c,onClose:d,layout:{overlay:p,animationInDuration:u,animationOutDuration:g},sticky:h,keepVariablesInTexts:y}=e,b=(0,f.useRef)(),C=(0,f.useRef)(),w=(0,f.useRef)(!1),[P,j]=function(e){let{animationIn:t,animationInOnlyMobile:o,animationOut:n,animationOutOnlyMobile:i}=e;const[s]=H(["(max-width: 700px)"],!0);let r=o?s?t:"none":t,a=i?s?n:"none":n;return(0,f.useMemo)((()=>{const e=window.navigator.userAgent.toLowerCase();return 4===["firefox","gecko","mobile","android"].map((t=>e.indexOf(t)>-1)).filter(Boolean).length}),[])&&(r="none",a="none"),[r,a]}(e.layout),[A,I]=le(P,u),[T,B]=le("none"===j?"fadeOut":j,"none"===j?0:g),[R,D]=(0,x.F)(["BannerContent","BannerHeader","BannerBody","BannerFooter","BodyDescription"],(e=>{(0,v.P)().then(e)}),(()=>b.current.style.removeProperty("display"))),F=(0,N.o)(),{a11yIds:{firstButton:E},inner:$,Dialog:M,Overlay:L,individualPrivacyOpen:_,computedMobileUpdate:U}=F.extend(...k.Z);(0,f.useMemo)((()=>{_.update(l),l&&(null==c||c())}),[l]),(0,f.useEffect)((()=>{U()}),[]),(0,f.useEffect)((()=>()=>{ge(!1,p)}),[p]),function(){const{openBanner:e,openHistory:t,revokeConsent:o}=(0,m.Y)();(0,f.useEffect)((()=>{const n=(n,i,s)=>{if(e)switch(n){case"change":e(s);break;case"history":t(s);break;case"revoke":o(i,s)}},i=t=>{if(!e)return;const o=t.target;(function(e,t,o){void 0===o&&(o=0);const n=[];let i=e.parentElement,s=0;for(;null!==i;){const r=i.nodeType===Node.ELEMENT_NODE;if(0===s&&1===o&&r){const o=e.closest(t);return o?[o]:[]}if(r&&i.matches(t)&&n.push(i),i=i.parentElement,0!==o&&n.length>=o)break;s++}return n})(o,ie).concat(ne(o,ie)?[o]:[]).forEach((e=>{n(e.getAttribute("href").slice(9),e.getAttribute("data-success-message"),t)})),ne(o,".rcb-sc-link")&&n(o.getAttribute("href").slice(1),o.getAttribute("data-success-message"),t)},s=()=>{const{hash:e}=window.location;e.startsWith("#consent-")&&n(e.substring(9),void 0,void 0)};return s(),window.addEventListener("hashchange",s),document.addEventListener("click",i,!0),()=>{window.removeEventListener("hashchange",s),document.removeEventListener("click",i,!0)}}),[e,t,o])}(),(0,f.useEffect)((()=>{o&&t&&O().mutate((()=>{t.restart()}))}),[o,t]),(0,f.useEffect)((()=>{const e=b.current,t=C.current||document.getElementById(a),n=function(e){i?d():(this.querySelector(`a[href="#${E}"]`).focus(),e.preventDefault())};if(o?(w.current=!0,(null==e?void 0:e.isConnected)&&(e.open&&(null==e.close||e.close.call(e)),O().mutate((()=>{var t;null==(t=e[p&&!y?"showModal":"show"])||t.call(e)})),e.addEventListener("cancel",n))):e&&(null==e.close||e.close.call(e)),t){const e=0,n=o?"none"===P?e:u:"none"===j?e:g,i=n>0,s=e=>{i&&(t.style.transition=`background ${n}ms`),t.style.display=e?"block":"none",ge(e,p)};o?O().mutate((()=>{s(!0)})):w.current&&(setTimeout((()=>O().mutate((()=>s(!1)))),n),window.location.hash.startsWith("#consent-")&&(window.location.hash=""))}return()=>{null==e||e.removeEventListener("keyup",n)}}),[o,p,i,d]),(0,f.useEffect)((()=>{o&&O().mutate((()=>b.current.focus({preventScroll:!0})))}),[o,l]),(0,f.useEffect)((()=>{const e=e=>{let{detail:{triggeredByOtherTab:t}}=e;t&&d()};return document.addEventListener(S.r,e),()=>{document.removeEventListener(S.r,e)}}),[d]);const W=[];if(s&&h.enabled&&W.push((0,n.jsx)(ue,{},"sticky")),o||w.current){const e=(0,n.jsx)(M,{className:"wp-exclude-emoji "+(l?"second-layer":""),ref:b,style:{display:"none"},"data-nosnippet":!0,"data-lenis-prevent":!0,children:(0,n.jsx)(R,{value:D,children:(0,n.jsx)(de,{animationIn:A,animationInDuration:I,animationOut:T,animationOutDuration:B,isVisible:o,className:$,children:(0,n.jsx)(pe,{})})})},"dialog");W.push(r?e:(0,n.jsx)(L,{id:a,className:F.className,ref:C,children:e},"overlay"))}return(0,n.jsx)(f.Fragment,{children:W})}))),ye=()=>{const{pageRequestUuid4:e}=(0,m.Y)(),t=function(){var e;const t=(0,U.b)(),{customCss:{css:o,antiAdBlocker:n}}=t,i=null==(e=window.wp)?void 0:e.customize,s=((e,t,o)=>{const n=(0,f.useMemo)((()=>(0,$.TI)(e,t)),[]),{updater:i}=n;if(o&&i)for(const e in i){const t=(0,f.useRef)(!0);(0,f.useEffect)((()=>{var n;t.current?t.current=!1:null==(n=i[e])||n.call(i,o[e])}),[o[e]])}return(0,f.useEffect)((()=>(n.toggle(!0),()=>!(null==t?void 0:t.reuse)&&n.toggle(!1))),[]),n})((e=>function(e,t){let{rule:o,computed:n,variable:i,vars:s,meta:r,className:a,plugin:l}=t;l("modifyRule",(e=>{const{pseudos:t,forceSelector:o}=e,n=":focus-visible",i=".wheir-focus-visible";"string"==typeof o&&(null==o?void 0:o.indexOf(n))>-1&&(e.forceSelector+=`,${o.replace(new RegExp(n,"g"),i)}`);for(const e in t)e.indexOf(n)>-1&&(t[e.replace(new RegExp(n,"g"),i)]=t[e])}));const{pageRequestUuid4:c}=e,d=i(e.isTcf),p=a();o({background:"none",padding:"0px",margin:"0px",border:"none",maxWidth:"initial",maxHeight:"initial",position:"fixed",outline:"none !important",pseudos:{"::backdrop":{all:"unset"}},forceSelector:`.${p}`});const u=a();o({position:"absolute",clip:"rect(1px, 1px, 1px, 1px)",padding:"0px",border:"0px",height:"1px",width:"1px",overflow:"hidden",display:"block",forceSelector:`.${u}`});const g={outline:{outline:"black auto 1px !important",outlineOffset:"3px !important"},text:{textDecoration:"underline dashed !important",textUnderlineOffset:"3px !important"}};o({...g.outline,forceSelector:`.${r.id} :focus-visible, .${r.id}:focus-visible, .${r.id} *:has(+ .${u}:focus-visible)`});const h=a(),y=a(),[b,f]=s(e.mobile,{maxHeight:A.dD,alignment:(0,A.$S)(e.mobile.alignment,["bottom","center","top"])}),[v,m]=V("(max-width: 700px)"),{isMobile:S,isMobileWidth:C,update:x}=n([b.enabled],(e=>{let[t]=e;const{body:{classList:o}}=document,n=document.getElementById(c),{innerWidth:i}=window,s=((null==n?void 0:n.clientWidth)||i)<=G||v.matches,r=t&&s;return r?o.add(h):o.remove(h),{isMobile:r,isMobileWidth:s}}),"mobile","raf");m(x),o({forceSelector:`.${h} .${y}`,display:"none"});const[{x:k,y:w}]=s({x:`calc(${b.scalePercent()} / 100)`,y:`calc((${b.scalePercent()} + ${b.scalePercentVertical()}) / 100)`}),P=e=>t=>{const o=(0,A.g$)(t),n=(t,o)=>`calc(${t}*${e.indexOf(o)>-1?w():k()})`,i={l:o,m:o.map(n)};for(let e=0;e<o.length;e++){const t=o[e];i[`l${e}`]=t,i[`m${e}`]=n(t,e)}return i},j=P([0,2]),I=P([]),T=e=>{const t=(0,A.dD)(e);return{l:t,m:`calc(${t}*${k()})`}},[B,R]=s(e.decision,{acceptAll:(0,A.$S)(e.decision.acceptAll,["button","link","hide"]),acceptEssentials:(0,A.$S)(e.decision.acceptAll,["button","link","hide"]),acceptIndividual:(0,A.$S)(e.decision.acceptAll,["button","link","hide"])}),[D,F]=s(e.layout,{maxHeight:A.dD,dialogPosition:(0,A.$S)(e.layout.dialogPosition,["middleCenter"]),bannerPosition:(0,A.$S)(e.layout.bannerPosition,["top","bottom"]),borderRadius:A.dD,dialogMargin:A.g$,dialogBorderRadius:A.dD,dialogMaxWidth:A.dD,overlayBg:se.E,overlayBlur:A.dD,bannerMaxWidth:A.dD}),[N,E]=s(e.sticky,{alignment:(0,A.$S)(e.sticky.alignment,["left","center","right"]),bubbleBorderRadius:(0,A.tD)("%"),bubblePadding:T,bubbleMargin:I,bubbleBorderWidth:A.dD,iconSize:T,boxShadowBlurRadius:A.dD,boxShadowOffsetX:A.dD,boxShadowOffsetY:A.dD,boxShadowSpreadRadius:A.dD,boxShadowColor:A.Kn,boxShadowColorAlpha:(0,A.tD)("%"),menuBorderRadius:A.dD,menuFontSize:T,menuItemSpacing:T,menuItemPadding:I}),[$,M]=s({accordionMargin:[0,0,0,0],accordionPadding:[0,0,0,0],accordionArrowType:"none",accordionArrowColor:"white",accordionBg:"white",accordionActiveBg:"white",accordionHoverBg:"white",accordionBorderWidth:0,accordionBorderColor:"white",accordionTitleFontSize:0,accordionTitleFontColor:"white",accordionTitleFontWeight:"white",accordionDescriptionMargin:[0,0,0,0],accordionDescriptionFontSize:0,accordionDescriptionFontColor:"white",accordionDescriptionFontWeight:"white",...e.bodyDesign},{padding:j,descriptionFontSize:T,teachingsFontSize:T,teachingsSeparatorWidth:A.dD,teachingsSeparatorHeight:A.dD,dottedGroupsFontSize:T,acceptAllFontSize:T,acceptAllPadding:j,acceptAllBorderWidth:T,acceptAllTextAlign:(0,A.$S)(e.bodyDesign.acceptAllTextAlign,["center","right"]),acceptEssentialsFontSize:T,acceptEssentialsPadding:j,acceptEssentialsBorderWidth:T,acceptEssentialsTextAlign:(0,A.$S)(e.bodyDesign.acceptEssentialsTextAlign,["center","right"]),acceptIndividualFontSize:T,acceptIndividualPadding:j,acceptIndividualBorderWidth:T,acceptIndividualTextAlign:(0,A.$S)(e.bodyDesign.acceptIndividualTextAlign,["center","right"]),accordionMargin:j,accordionTitleFontSize:T,accordionBorderWidth:T,accordionPadding:j,accordionDescriptionFontSize:T,accordionDescriptionMargin:j}),[L,_]=s(e.saveButton,{type:(0,A.$S)(e.saveButton.type,["button","link"]),fontSize:T,padding:j,borderWidth:T,textAlign:(0,A.$S)(e.saveButton.textAlign,["center","right"])}),[U,W]=s(e.design,{fontColor:A.Kn,fontSize:T,borderWidth:T,textAlign:(0,A.$S)(e.design.textAlign,["center","right"]),boxShadowBlurRadius:A.dD,boxShadowOffsetX:A.dD,boxShadowOffsetY:A.dD,boxShadowSpreadRadius:A.dD,boxShadowColor:A.Kn,boxShadowColorAlpha:(0,A.tD)("%")}),[z,H]=s(e.headerDesign,{fontSize:T,borderWidth:T,padding:j,textAlign:(0,A.$S)(e.headerDesign.textAlign,["center","right"]),logoPosition:(0,A.$S)(e.headerDesign.logoPosition,["left","right"]),logo:(0,A.a$)(!1),logoMargin:j}),[q,Y]=s(e.footerDesign,{borderWidth:T,padding:j,fontSize:T}),[Q,J]=s(e.texts,{...(0,A.gJ)(e.texts,A.xj),headline:(0,A.a$)()}),[Z,K]=s(e.individualLayout,{dialogMaxWidth:A.dD,bannerMaxWidth:A.dD}),[X,ee]=s(e.group,{headlineFontSize:T,descriptionFontSize:T,groupPadding:j,groupBorderRadius:A.dD,groupBorderWidth:T,groupSpacing:T,checkboxBorderWidth:T}),te=document.getElementById(c);return te&&O().mutate((()=>{te.className=r.id})),{customize:e,unsetDialogStyles:p,a11yFocusStyle:g,scaleHorizontal:k,scaleVertical:w,isTcf:d,computedMobileUpdate:x,boolLargeOrMobile:(e,t,o)=>t(S,e("number"==typeof o?`m${o}`:"m"),e("number"==typeof o?`l${o}`:"l")),isMobile:S,isMobileWidth:C,isMobileClass:h,hideOnMobileClass:y,screenReaderOnlyClass:u,updater:{decision:R,layout:F,design:W,bodyDesign:M,headerDesign:H,footerDesign:Y,texts:J,mobile:f,sticky:E,individualLayout:K,group:ee,saveButton:_},decision:B,layout:D,design:U,bodyDesign:$,headerDesign:z,footerDesign:q,individualLayout:Z,group:X,saveButton:L,texts:Q,mobile:b,sticky:N}}(t,e)),{reuse:"react-cookie-banner",createElement:f.createElement,forwardRef:f.forwardRef,filterClassName:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];!function(e,t,o){for(var n=arguments.length,i=new Array(n>3?n-3:0),s=3;s<n;s++)i[s-3]=arguments[s];let[r,a,{mainElement:l,id:c}]=i;if(r.length){const n=`rcb-${r[0]}`;t?r[0]=n:r.splice(0,1),l[n]=l[n]||[],l[n].push(a),ae(l,o,e)}}(s.className,"n"===n||i,o,...t)}},t),r=(0,f.useMemo)((()=>Symbol(s.functions.className())),[]);return s.specify(r.description),s.specify(`${r.description}-o`),(0,f.useEffect)((()=>{const{element:e,className:t}=s;ae(e,o,t)}),[o]),{...s,reactRootSymbol:r}}();t.specify(e);const[o,i]=(0,N.d)(t);return(0,n.jsx)(o,{value:i,children:(0,n.jsx)(he,{})})};var be=o(81257),fe=o(5974),ve=o(50724);class me{static#e=this.BROADCAST_SIGNAL_APPLY_COOKIES="applyCookies";constructor(e){const{decisionCookieName:t}=e;this.options=e,this.options.tcfCookieName=`${t}-tcf`,this.options.gcmCookieName=`${t}-gcm`;let n=!1;window.addEventListener("storage",(e=>{let{key:o,oldValue:i,newValue:s,isTrusted:r}=e;if(!n&&o===this.getConsentQueueName()&&s&&r){const e=JSON.parse(i||"[]");if(JSON.parse(s).length>e.length){n=!0;const e=JSON.stringify((0,ve.y)(t));(0,c.x)((()=>JSON.stringify((0,ve.y)(t))!==e),500,20).then((()=>this.applyCookies({type:"consent",triggeredByOtherTab:!0})))}}}));const i=async()=>{const{retryPersistFromQueue:e}=await Promise.all([o.e(343),o.e(4)]).then(o.bind(o,17724)),t=t=>{const o=e(this,t);window.addEventListener("beforeunload",o)};if(this.getConsentQueue().length>0)t(!0);else{const e=o=>{let{key:n,newValue:i}=o;const s=n===this.getConsentQueueName()&&i,r=n===this.getConsentQueueName(!0)&&!i;(s||r)&&(t(r),window.removeEventListener("storage",e))};window.addEventListener("storage",e)}};window.requestIdleCallback?requestIdleCallback(i):(0,v.P)().then(i)}async applyCookies(e){const{apply:t}=await Promise.all([o.e(343),o.e(4)]).then(o.bind(o,18505));await t({...e,...this.options})}async persistConsent(e){const{persistWithQueueFallback:t}=await Promise.all([o.e(343),o.e(4)]).then(o.bind(o,86264));return await t(e,this)}getUserDecision(e){const t=(0,ve.y)(this.getOption("decisionCookieName"));return!0===e?!!t&&t.revision===this.getOption("revisionHash")&&t:t}getDefaultDecision(e){return void 0===e&&(e=!0),(0,fe.w)(this.options.groups,e)}getOption(e){return this.options[e]}getOptions(){return this.options}getConsentQueueName(e){return void 0===e&&(e=!1),`${this.options.consentQueueLocalStorageName}${e?"-lock":""}`}getConsentQueue(){return JSON.parse(localStorage.getItem(this.getConsentQueueName())||"[]")}setConsentQueue(e){const t=this.getConsentQueueName(),o=localStorage.getItem("test"),n=e.length>0?JSON.stringify(e):null;n?localStorage.setItem(t,n):localStorage.removeItem(t),window.dispatchEvent(new StorageEvent("storage",{key:t,oldValue:o,newValue:n}))}isConsentQueueLocked(e){const t=(new Date).getTime(),o=this.getConsentQueueName(!0);return!1===e?localStorage.removeItem(o):!0===e&&localStorage.setItem(o,`${t+6e4}`),!(t>+(localStorage.getItem(o)||0))}}let Se;function Ce(){const e=window["real-cookie-banner".replace(/-([a-z])/g,(e=>e[1].toUpperCase()))];if(!e){if(Se)return window[Se];for(const e in window){const t=window[e];if("real-cookie-banner"===(null==t?void 0:t.textDomain))return Se=e,t}}return e}function xe(){return Ce().others}function ke(e){const t=Ce().version.split(".");return+("major"===e?t[0]:t.map((e=>+e<10?`0${e}`:e)).join(""))}const we={path:"/consent",method:i.RouteHttpVerb.POST,obfuscatePath:"keep-last-part"};var Oe=o(59670),Pe=o(44497);let je;const Ae=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return(je||(je=(0,i.createRequestFactory)(window[i.BaseOptions.getPureSlug("real-cookie-banner",!0)]))).request(...t)},Ie={path:"/checklist",method:i.RouteHttpVerb.GET},Te={path:"/checklist/:id",method:i.RouteHttpVerb.PUT};class Be extends i.BaseOptions{constructor(e){super(),this.busyChecklist=!1,this.probablyFetchByChangedItem=(0,Pe.flow)((function*(e,t){if(t)return void(yield this.fetchChecklist());const o=Array.isArray(e)?e:[e];this.items.filter((e=>{let{id:t,checked:n}=e;return o.indexOf(t)>-1&&!n})).length>0&&(yield this.fetchChecklist())})),this.fetchChecklist=(0,Pe.flow)((function*(){this.busyChecklist=!0;try{this.checklist=yield Ae({location:Ie,sendReferer:!0})}catch(e){throw console.log(e),e}finally{this.busyChecklist=!1}})),this.toggleChecklistItem=(0,Pe.flow)((function*(e,t){this.busyChecklist=!0;try{this.checklist=yield Ae({location:Te,request:{state:t},sendReferer:!0,params:{id:e}})}catch(e){throw console.log(e),e}finally{this.busyChecklist=!1}})),this.rootStore=e}get items(){return this.checklist&&Object.keys(this.checklist.items).map((e=>({id:e,...this.checklist.items[e]})))||[]}get completed(){return this.items.filter((e=>{let{checked:t}=e;return t}))}get checkable(){const{isPro:e}=this.rootStore.optionStore.others;return this.items.filter((t=>{let{needsPro:o}=t;return!o||e&&o}))}get done(){var e;return this.completed.length>=this.checkable.length||!!(null==(e=this.checklist)?void 0:e.dismissed)}}(0,Oe.Cg)([Pe.observable],Be.prototype,"busyChecklist",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type","undefined"==typeof ResponseRouteChecklistGet?Object:ResponseRouteChecklistGet)],Be.prototype,"checklist",void 0),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Be.prototype,"items",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Be.prototype,"completed",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Be.prototype,"checkable",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Be.prototype,"done",null);const Re={path:"/consent/:id",method:i.RouteHttpVerb.DELETE};class De{get revision(){return this.store.revisions.get(this.revision_hash)}get revision_independent(){return this.store.revisionsIndependent.get(this.revision_independent_hash)}get custom_bypass_readable(){const{custom_bypass:e}=this;return e?e.charAt(0).toUpperCase()+e.slice(1):""}get export(){return JSON.parse(JSON.stringify({...this.plain,revision:this.revision.data,revision_independent:this.revision_independent.data}))}constructor(e,t){this.busy=!1,this.delete=(0,Pe.flow)((function*(){this.busy=!0;try{yield Ae({location:Re,params:{id:this.id}}),yield this.store.fetchAll()}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,Pe.runInAction)((()=>(0,Pe.set)(this,e))),this.store=t,this.plain=e}fetchRevisions(){return Promise.all([this.store.fetchRevision({hash:this.revision_hash}),this.store.fetchRevisionIndependent({hash:this.revision_independent_hash})])}}(0,Oe.Cg)([Pe.observable],De.prototype,"busy",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Number)],De.prototype,"id",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"plugin_version",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"design_version",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"ipv4",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"ipv6",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"ipv4_hash",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"ipv6_hash",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"uuid",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"previous_decision",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"decision",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"decision_labels",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"created",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"blocker",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"blocker_thumbnail",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"dnt",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"custom_bypass",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"user_country",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"revision_hash",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"revision_independent_hash",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"button_clicked",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"context",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"viewport_width",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"viewport_height",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"referer",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"url_imprint",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"url_privacy_policy",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"forwarded",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"forwarded_blocker",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"previous_tcf_string",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"tcf_string",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"previous_gcm_consent",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"gcm_consent",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"recorder",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],De.prototype,"ui_view",void 0),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],De.prototype,"revision",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],De.prototype,"revision_independent",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],De.prototype,"custom_bypass_readable",null);class Fe{constructor(e,t){this.data=e,this.store=t}}class Ne{constructor(e,t){this.data=e,this.store=t}}const Ee={path:"/consent/all",method:i.RouteHttpVerb.DELETE},$e={path:"/consent/all",method:i.RouteHttpVerb.GET},Me={path:"/consent/referer",method:i.RouteHttpVerb.GET},Le={path:"/revision/:hash",method:i.RouteHttpVerb.GET},_e={path:"/revision/independent/:hash",method:i.RouteHttpVerb.GET};class Ue extends i.BaseOptions{constructor(e){super(),this.busyConsent=!1,this.busyReferer=!1,this.count=0,this.truncatedIpsCount=0,this.perPage=50,this.offset=0,this.pageCollection=new Map,this.revisions=new Map,this.revisionsIndependent=new Map,this.referer=[],this.filters=Pe.observable.object({page:1,dates:[void 0,void 0],context:void 0,referer:void 0,ip:void 0,uuid:void 0},{},{deep:!1}),this.fetchAll=(0,Pe.flow)((function*(e){this.busyConsent=!0;try{const{page:t,referer:o,ip:n,uuid:i,context:s}=this.filters,r=this.filters.dates.map((e=>e?e.format("YYYY-MM-DD"):"")),{count:a,truncatedIpsCount:l,items:c}=yield Ae({location:$e,params:{per_page:this.perPage,offset:(t-1)*this.perPage,from:r[0],to:r[1],ip:n,uuid:i,referer:o,context:s,...e||{}}});this.count=a,l&&(this.truncatedIpsCount=l),this.pageCollection.clear();for(const e of c)this.pageCollection.set(e.id,new De(e,this))}catch(e){throw this.count=0,this.truncatedIpsCount=0,this.pageCollection.clear(),console.log(e),e}finally{this.busyConsent=!1}})),this.fetchRevision=(0,Pe.flow)((function*(e){try{const t=yield Ae({location:Le,params:e});this.revisions.set(e.hash,new Fe(t,this))}catch(e){throw console.log(e),e}})),this.fetchRevisionIndependent=(0,Pe.flow)((function*(e){try{const t=yield Ae({location:_e,params:e});this.revisionsIndependent.set(e.hash,new Ne(t,this))}catch(e){throw console.log(e),e}})),this.fetchReferer=(0,Pe.flow)((function*(e){this.busyReferer=!0;try{const t=yield Ae({location:Me,params:e});this.referer=t.items}catch(e){throw console.log(e),e}finally{this.busyReferer=!1}})),this.deleteAll=(0,Pe.flow)((function*(){this.busyConsent=!0;try{yield Ae({location:Ee}),this.applyPage(0),yield this.fetchAll()}catch(e){throw console.log(e),e}finally{this.busyConsent=!1}})),this.rootStore=e,(0,Pe.runInAction)((()=>{this.filters.context=this.rootStore.optionStore.others.context}))}applyPage(e){this.filters.page=e}applyDates(e){this.filters.dates=e}applyContext(e){this.filters.context=e}applyReferer(e){this.filters.referer=e}applyIp(e){this.filters.ip=e}applyUuid(e){this.filters.uuid=e}}(0,Oe.Cg)([Pe.observable],Ue.prototype,"busyConsent",void 0),(0,Oe.Cg)([Pe.observable],Ue.prototype,"busyReferer",void 0),(0,Oe.Cg)([Pe.observable],Ue.prototype,"count",void 0),(0,Oe.Cg)([Pe.observable],Ue.prototype,"truncatedIpsCount",void 0),(0,Oe.Cg)([Pe.observable],Ue.prototype,"perPage",void 0),(0,Oe.Cg)([Pe.observable],Ue.prototype,"offset",void 0),(0,Oe.Cg)([Pe.observable],Ue.prototype,"pageCollection",void 0),(0,Oe.Cg)([Pe.observable],Ue.prototype,"revisions",void 0),(0,Oe.Cg)([Pe.observable],Ue.prototype,"revisionsIndependent",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Array)],Ue.prototype,"referer",void 0),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object]),(0,Oe.Sn)("design:returntype",void 0)],Ue.prototype,"applyPage",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object]),(0,Oe.Sn)("design:returntype",void 0)],Ue.prototype,"applyDates",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object]),(0,Oe.Sn)("design:returntype",void 0)],Ue.prototype,"applyContext",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object]),(0,Oe.Sn)("design:returntype",void 0)],Ue.prototype,"applyReferer",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object]),(0,Oe.Sn)("design:returntype",void 0)],Ue.prototype,"applyIp",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object]),(0,Oe.Sn)("design:returntype",void 0)],Ue.prototype,"applyUuid",null);class We extends i.AbstractPost{get templateModel(){var e;return Gt.get.cookieStore.templatesBlocker.get(null==(e=this.data.meta)?void 0:e.presetId)}get rules(){var e;return null==(e=this.data)?void 0:e.meta.rules.split("\n")}get tcfVendors(){var e;return(null==(e=this.data)?void 0:e.meta.tcfVendors)?this.data.meta.tcfVendors.split(",").filter(Boolean).map(Number):[]}get tcfPurposes(){var e;return(null==(e=this.data)?void 0:e.meta.tcfPurposes)?this.data.meta.tcfPurposes.split(",").filter(Boolean).map(Number):[]}get services(){var e;return null==(e=this.data)?void 0:e.meta.services.split(",").filter(Boolean).map(Number)}get rootStore(){return this.collection.store.rootStore}get isUpdateAvailable(){for(const{post_id:e}of this.rootStore.optionStore.templateNeedsUpdate)if(e===this.data.id)return!0;return!1}constructor(e,t={}){super(e,t),(0,Pe.reaction)((()=>{var e;return null==(e=this.data)?void 0:e.usedTemplate}),(e=>(0,Pe.runInAction)((()=>{e&&Gt.get.cookieStore.addBlockerTemplates([e])}))),{fireImmediately:!0})}setName(e){this.data.title.raw=e}setStatus(e){this.data.status=e}setDescription(e){this.data.content.raw=e}setMeta(e){this.data.meta=e}transformDataForPatch(){const e=super.transformDataForPatch();return{title:e.title,content:e.content,status:e.status,meta:e.meta,slug:e.title}}afterPatch(){this.collection.store.blockers.store.rootStore.optionStore.fetchCurrentRevision()}afterDelete(){this.collection.store.blockers.store.rootStore.optionStore.fetchCurrentRevision()}afterPersist(){this.collection.store.blockers.store.rootStore.optionStore.fetchCurrentRevision()}}(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],We.prototype,"templateModel",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],We.prototype,"rules",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],We.prototype,"tcfVendors",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],We.prototype,"tcfPurposes",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],We.prototype,"services",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],We.prototype,"rootStore",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],We.prototype,"isUpdateAvailable",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[String]),(0,Oe.Sn)("design:returntype",void 0)],We.prototype,"setName",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof PostStatus?Object:PostStatus]),(0,Oe.Sn)("design:returntype",void 0)],We.prototype,"setStatus",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[String]),(0,Oe.Sn)("design:returntype",void 0)],We.prototype,"setDescription",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object]),(0,Oe.Sn)("design:returntype",void 0)],We.prototype,"setMeta",null),We=(0,Oe.Cg)([i.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:Ae,create:{path:"/rcb-blocker"},patch:{path:"/rcb-blocker/:id"},delete:{path:"/rcb-blocker/:id"}}),(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object,"undefined"==typeof Partial?Object:Partial])],We);class ze extends i.AbstractPostCollection{constructor(e){super(),this.store=e}get sortedBlockers(){const e=Array.from(this.entries.values());return e.sort(((e,t)=>e.data.menu_order<t.data.menu_order?-1:e.data.menu_order>t.data.menu_order||e.key<t.key?1:e.key>t.key?-1:0)),e}instance(e){return new We(this).fromResponse(e)}}(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],ze.prototype,"sortedBlockers",null),ze=(0,Oe.Cg)([i.ClientCollection.annotate({path:"/rcb-blocker",singlePath:"/rcb-blocker/:id",namespace:"wp/v2",methods:[i.RouteHttpVerb.GET],request:Ae}),(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof CookieStore?Object:CookieStore])],ze);const Ve={path:"/templates/blocker/:identifier",method:i.RouteHttpVerb.GET,obfuscatePath:"full"};class He{constructor(e,t){this.busy=!1,this.fetchUse=(0,Pe.flow)((function*(){try{this.busy=!0;const e=yield Ae({location:Ve,params:{identifier:this.data.identifier}});return this.use=e,this.store.addServiceTemplates(e.consumerData.serviceTemplates),this.use}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,Pe.runInAction)((()=>{this.data=e})),this.store=t}}(0,Oe.Cg)([Pe.observable],He.prototype,"busy",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],He.prototype,"data",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type","undefined"==typeof ResponseRouteTemplatesBlockerUseGet?Object:ResponseRouteTemplatesBlockerUseGet)],He.prototype,"use",void 0);class Ge extends i.AbstractPost{get templateModel(){var e;return Gt.get.cookieStore.templatesServices.get(null==(e=this.data.meta)?void 0:e.presetId)}get rootStore(){return this.collection.store.collection.store.rootStore}get technicalDefinitions(){return JSON.parse(this.data.meta.technicalDefinitions||"[]")}get googleConsentModeConsentTypes(){return JSON.parse(this.data.meta.googleConsentModeConsentTypes||"[]")}get dataProcessingInCountries(){return JSON.parse(this.data.meta.dataProcessingInCountries||"[]")}get dataProcessingInCountriesSpecialTreatments(){return JSON.parse(this.data.meta.dataProcessingInCountriesSpecialTreatments||"[]")}get isUpdateAvailable(){for(const{post_id:e}of this.rootStore.optionStore.templateNeedsUpdate)if(e===this.data.id)return!0;return!1}get codeDynamics(){return JSON.parse(this.data.meta.codeDynamics||"{}")}constructor(e,t={}){super(e,t),(0,Pe.reaction)((()=>{var e;return null==(e=this.data)?void 0:e.usedTemplate}),(e=>(0,Pe.runInAction)((()=>{e&&Gt.get.cookieStore.addServiceTemplates([e])}))),{fireImmediately:!0})}afterPatch(){const e=this.collection.store.collection,[t]=this.data["rcb-cookie-group"];e.entries.forEach((e=>{t!==e.key?e.cookies.entries.delete(this.key):e.cookies.entries.set(this.key,this)})),this.rootStore.optionStore.fetchCurrentRevision(),this.rootStore.cookieStore.unassignedCookies.delete(this.key)}setOrder(e){this.data.menu_order=e}setName(e){this.data.title.raw=e}setStatus(e){this.data.status=e}setPurpose(e){this.data.content.raw=e}setGroup(e){this.data["rcb-cookie-group"]=[e]}setMeta(e){this.data.meta=e}transformDataForPersist(){return{...super.transformDataForPersist(),"rcb-cookie-group":[this.collection.store.key]}}transformDataForPatch(){const e=super.transformDataForPatch();return{title:e.title,content:e.content,status:e.status,meta:e.meta,menu_order:e.menu_order,"rcb-cookie-group":this.data["rcb-cookie-group"],slug:e.title}}afterDelete(){this.collection.store.cookies.store.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterPersist(){const{optionStore:e,checklistStore:t}=this.collection.store.cookies.store.collection.store.rootStore;e.fetchCurrentRevision(),t.probablyFetchByChangedItem("add-cookie")}}(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Ge.prototype,"templateModel",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Ge.prototype,"rootStore",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",Array)],Ge.prototype,"technicalDefinitions",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",Array)],Ge.prototype,"googleConsentModeConsentTypes",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",Object)],Ge.prototype,"dataProcessingInCountries",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",Object)],Ge.prototype,"dataProcessingInCountriesSpecialTreatments",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Ge.prototype,"isUpdateAvailable",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype","undefined"==typeof Record?Object:Record)],Ge.prototype,"codeDynamics",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Ge.prototype,"afterPatch",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Number]),(0,Oe.Sn)("design:returntype",void 0)],Ge.prototype,"setOrder",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[String]),(0,Oe.Sn)("design:returntype",void 0)],Ge.prototype,"setName",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof PostStatus?Object:PostStatus]),(0,Oe.Sn)("design:returntype",void 0)],Ge.prototype,"setStatus",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[String]),(0,Oe.Sn)("design:returntype",void 0)],Ge.prototype,"setPurpose",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Number]),(0,Oe.Sn)("design:returntype",void 0)],Ge.prototype,"setGroup",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object]),(0,Oe.Sn)("design:returntype",void 0)],Ge.prototype,"setMeta",null),Ge=(0,Oe.Cg)([i.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:Ae,create:{path:"/rcb-cookie"},patch:{path:"/rcb-cookie/:id"},delete:{path:"/rcb-cookie/:id"}}),(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object,"undefined"==typeof Partial?Object:Partial])],Ge);const qe={path:"/cookies/order",method:i.RouteHttpVerb.PUT};class Ye extends i.AbstractPostCollection{get sortedCookies(){const e=Array.from(this.entries.values());return e.sort(((e,t)=>e.data.menu_order<t.data.menu_order?-1:e.data.menu_order>t.data.menu_order||e.key<t.key?1:e.key>t.key?-1:0)),e}constructor(e){super(),this.orderCookies=(0,Pe.flow)((function*(e){this.busy=!0;try{yield Ae({location:qe,request:{ids:e}});let t=0;for(const o of e)this.entries.get(o).setOrder(t),t++}catch(e){throw console.log(e),e}finally{this.busy=!1}})),this.store=e}instance(e){return new Ge(this).fromResponse(e)}}(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Ye.prototype,"sortedCookies",null),Ye=(0,Oe.Cg)([i.ClientCollection.annotate({path:"/rcb-cookie",singlePath:"/rcb-cookie/:id",namespace:"wp/v2",methods:[i.RouteHttpVerb.GET],request:Ae}),(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof CookieGroupModel?Object:CookieGroupModel])],Ye);class Qe extends i.AbstractCategory{get cookiesCount(){return this.fetchedAllCookies?this.cookies.entries.size:this.data.count}constructor(e,t={}){super(e,t),this.fetchedAllCookies=!1,this.fetchCookies=(0,Pe.flow)((function*(){yield this.cookies.get({request:{status:["draft","publish","private"]},params:{per_page:100,"rcb-cookie-group":this.key,context:"edit"}}),this.fetchedAllCookies=!0})),(0,Pe.runInAction)((()=>{this.cookies=new Ye(this)}))}setName(e){this.data.name=e}setDescription(e){this.data.description=e}setOrder(e){this.data.meta.order=e}afterDelete(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterPatch(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterPersist(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}}(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",void 0===Ye?Object:Ye)],Qe.prototype,"cookies",void 0),(0,Oe.Cg)([Pe.observable],Qe.prototype,"fetchedAllCookies",void 0),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Qe.prototype,"cookiesCount",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[String]),(0,Oe.Sn)("design:returntype",void 0)],Qe.prototype,"setName",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[String]),(0,Oe.Sn)("design:returntype",void 0)],Qe.prototype,"setDescription",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Number]),(0,Oe.Sn)("design:returntype",void 0)],Qe.prototype,"setOrder",null),Qe=(0,Oe.Cg)([i.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:Ae,create:{path:"/rcb-cookie-group"},patch:{path:"/rcb-cookie-group/:id"},delete:{path:"/rcb-cookie-group/:id"}}),(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof CookieGroupCollection?Object:CookieGroupCollection,"undefined"==typeof Partial?Object:Partial])],Qe);const Je={path:"/cookie-groups/order",method:i.RouteHttpVerb.PUT};class Ze extends i.AbstractCategoryCollection{get sortedGroups(){const e=Array.from(this.entries.values());return e.sort(((e,t)=>e.data.meta.order<t.data.meta.order?-1:e.data.meta.order>t.data.meta.order?1:0)),e}constructor(e){super(),this.orderCookieGroups=(0,Pe.flow)((function*(e){this.busy=!0;try{yield Ae({location:Je,request:{ids:e}});let t=0;for(const o of e)this.entries.get(o).setOrder(t),t++}catch(e){throw console.log(e),e}finally{this.busy=!1}})),this.store=e}instance(e){return new Qe(this).fromResponse(e)}}(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Ze.prototype,"sortedGroups",null),Ze=(0,Oe.Cg)([i.ClientCollection.annotate({path:"/rcb-cookie-group",singlePath:"/rcb-cookie-group/:id",namespace:"wp/v2",methods:[i.RouteHttpVerb.GET],request:Ae}),(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof CookieStore?Object:CookieStore])],Ze);const Ke={path:"/templates/services/:identifier",method:i.RouteHttpVerb.GET,obfuscatePath:"full"};class Xe{constructor(e,t){this.busy=!1,this.fetchUse=(0,Pe.flow)((function*(){try{this.busy=!0;const e=yield Ae({location:Ke,params:{identifier:this.data.identifier}});return this.use=e,this.use}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,Pe.runInAction)((()=>{this.data=e})),this.store=t}}(0,Oe.Cg)([Pe.observable],Xe.prototype,"busy",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],Xe.prototype,"data",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type","undefined"==typeof ResponseRouteTemplatesServiceUseGet?Object:ResponseRouteTemplatesServiceUseGet)],Xe.prototype,"use",void 0);const et={path:"/cookies/unassigned",method:i.RouteHttpVerb.GET},tt={path:"/templates/blocker",method:i.RouteHttpVerb.GET},ot={path:"/templates/services",method:i.RouteHttpVerb.GET};class nt{get blockersCount(){return this.fetchedAllBlockers?this.blockers.entries.size:this.rootStore.optionStore.allBlockerCount}get cookiesCount(){return Array.from(this.groups.entries.values()).map((e=>{let{cookiesCount:t}=e;return t})).reduce(((e,t)=>e+t),0)}constructor(e){this.busy=!1,this.unassignedCookies=new Map,this.templatesBlocker=new Map,this.busyTemplatesBlocker=!1,this.templatesServices=new Map,this.busyTemplatesServices=!1,this.fetchedAllBlockers=!1,this.fetchGroups=(0,Pe.flow)((function*(){yield this.groups.get({params:{per_page:100}}),yield this.fetchUnassignedCookies()})),this.fetchUnassignedCookies=(0,Pe.flow)((function*(){try{const e=yield Ae({location:et});for(const t of Object.values(e))this.unassignedCookies.set(t.id,t)}catch(e){throw console.log(e),e}})),this.fetchBlockers=(0,Pe.flow)((function*(){yield this.blockers.get({request:{status:["draft","publish","private"]},params:{per_page:100,context:"edit"}}),this.fetchedAllBlockers=!0})),this.fetchTemplatesBlocker=(0,Pe.flow)((function*(e){this.busyTemplatesBlocker=!0;try{const{items:t}=yield Ae({location:tt,params:e});this.templatesBlocker.clear(),this.addBlockerTemplates(t)}catch(e){throw console.log(e),e}finally{this.busyTemplatesBlocker=!1}})),this.fetchTemplatesServices=(0,Pe.flow)((function*(e){this.busyTemplatesServices=!0;try{const{items:t}=yield Ae({location:ot,params:e});if(["redownload","invalidate"].indexOf(null==e?void 0:e.storage)>-1){const{activeLanguages:t,currentLanguage:o}=this.rootStore.optionStore.others;for(const n of t)n!==o&&(yield Ae({location:ot,params:{...e,_dataLocale:n}}))}this.templatesServices.clear(),this.addServiceTemplates(t)}catch(e){throw console.log(e),e}finally{this.busyTemplatesServices=!1}})),this.rootStore=e,(0,Pe.runInAction)((()=>{this.groups=new Ze(this),this.blockers=new ze(this)}))}get essentialGroup(){if(0===this.groups.entries.size)return;const e=this.groups.entries.values();let t;for(;(t=e.next().value)&&!t.data.meta.isEssential;);return t}addBlockerTemplates(e){for(const t of e)this.templatesBlocker.set(t.identifier,new He(t,this))}addServiceTemplates(e){for(const t of e)this.templatesServices.set(t.identifier,new Xe(t,this))}}(0,Oe.Cg)([Pe.observable],nt.prototype,"busy",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",void 0===Ze?Object:Ze)],nt.prototype,"groups",void 0),(0,Oe.Cg)([Pe.observable],nt.prototype,"unassignedCookies",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",void 0===ze?Object:ze)],nt.prototype,"blockers",void 0),(0,Oe.Cg)([Pe.observable],nt.prototype,"templatesBlocker",void 0),(0,Oe.Cg)([Pe.observable],nt.prototype,"busyTemplatesBlocker",void 0),(0,Oe.Cg)([Pe.observable],nt.prototype,"templatesServices",void 0),(0,Oe.Cg)([Pe.observable],nt.prototype,"busyTemplatesServices",void 0),(0,Oe.Cg)([Pe.observable],nt.prototype,"fetchedAllBlockers",void 0),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],nt.prototype,"blockersCount",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],nt.prototype,"cookiesCount",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],nt.prototype,"essentialGroup",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Array]),(0,Oe.Sn)("design:returntype",void 0)],nt.prototype,"addBlockerTemplates",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Array]),(0,Oe.Sn)("design:returntype",void 0)],nt.prototype,"addServiceTemplates",null);class it{constructor(e,t){(0,Pe.runInAction)((()=>(0,Pe.set)(this,e))),this.store=t}static getIframeStore(){try{return document.querySelector("#customize-preview > iframe").contentWindow.realCookieBanner_customize_banner.RootStore.get}catch(e){return}}applyInUi(){if(!this.store.rootStore.optionStore.others.isPro&&this.needsPro)return!1;const e=(0,a.getSidebarCustomize)();return this.previewInUi(),setTimeout((()=>{this.store.presetDefaults.forEach(((t,o)=>{e(o).set(void 0===this.settings[o]?t:this.settings[o])}))}),100),!0}previewInUi(){const{presetDefaults:e}=this.store,{settings:t}=this.store.rootStore.optionStore.others.customizeIdsBanner,o=[];this.resetPreviewInUiSettings={};for(const n of Object.keys(t)){const i=t[n];for(const t of Object.keys(i)){const s=i[t];if(!e.has(s))continue;const r=(0,a.getSanitizedControlValue)(s,e.get(s));if(null!==r){this.resetPreviewInUiSettings[s]=[n,t,r];const i=Object.prototype.hasOwnProperty.call(this.settings,s)?this.settings[s]:e.get(s);o.push([n,t,i])}}}it.getIframeStore().customizeBannerStore.setBannerFromPreset(o)}resetPreviewInUi(){this.resetPreviewInUiSettings&&(it.getIframeStore().customizeBannerStore.setBannerFromPreset(Object.values(this.resetPreviewInUiSettings)),this.resetPreviewInUiSettings={})}}(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",String)],it.prototype,"id",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",String)],it.prototype,"name",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Boolean)],it.prototype,"needsPro",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",String)],it.prototype,"description",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type","undefined"==typeof Array?Object:Array)],it.prototype,"tags",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],it.prototype,"settings",void 0);const st={path:"/presets/banner",method:i.RouteHttpVerb.GET};class rt{constructor(e){this.visible=!1,this.individualPrivacyOpen=!1,this.previewCheckboxActiveState=!1,this.previewStickyMenuOpenState=!1,this.busyPresets=!1,this.presets=new Map,this.presetConstants=new Map,this.presetDefaults=new Map,this.debounceFromCustomize={},this.fetchPresets=(0,Pe.flow)((function*(){this.busyPresets=!0;try{const{defaults:e,constants:t,items:o}=yield Ae({location:st});for(const t of Object.keys(e))this.presetDefaults.set(t,e[t]);for(const e of Object.keys(t))this.presetConstants.set(e,t[e]);for(const e of Object.keys(o))this.presets.set(e,new it({id:e,...o[e]},this))}catch(e){throw console.log(e),e}finally{this.busyPresets=!1}})),this.rootStore=e}setBannerFromCustomize(e,t,o,n){void 0===n&&(n=!0);const{customizeValuesBanner:i}=this.rootStore.optionStore.others,s=t.toString();if(n&&["css","animationInDuration","animationOutDuration"].indexOf(s)>-1)clearTimeout(this.debounceFromCustomize[s]),this.debounceFromCustomize[s]=setTimeout((()=>this.setBannerFromCustomize(e,t,o,!1)),500);else{const n=i[e][t];i[e][t]=o,s.startsWith("animationOut")&&n!==o&&this.forceAnimationOutSimulation()}}setBannerFromPreset(e){for(const t of e){const[e,o,n]=t;this.rootStore.optionStore.others.customizeValuesBanner[e][o]=n}}forceAnimationOutSimulation(){const{customizeValuesBanner:e}=this.rootStore.optionStore.others;"none"!==e.layout.animationOut&&(this.visible=!1,setTimeout((()=>(0,Pe.runInAction)((()=>{this.visible=!0}))),+e.layout.animationOutDuration+1e3))}setVisible(e){this.visible=e}setIndividualPrivacyOpen(e){this.individualPrivacyOpen=e}setPreviewCheckboxActiveState(e){this.previewCheckboxActiveState=e}setPreviewStickyMenuOpenState(e){this.previewStickyMenuOpenState=e}exportPhp(){const e={},t=(0,a.getSidebarCustomize)();return this.presetDefaults.forEach(((o,n)=>{let i=t(n).get();"boolean"==typeof o?i=!!+i:isNaN(i)||""===i||(i=+i),JSON.stringify(o)!==JSON.stringify(i)&&(e[this.presetConstants.get(n)]=i)})),this.jsonToPHPArray(e)}jsonToPHPArray(e){const t=JSON.stringify(e,null,4).split("\n");return t.shift(),t.pop(),t.join("\n").replace(/^(\s+)"([A-Za-z\\]+::[A-Z_]+)"(:)/gm,"$1$2 =>").replace(/^(\s+)([A-Za-z\\]+)::/gm,((e,t,o)=>`${t}${o.replace(/\\\\/gm,"\\")}::`))}}(0,Oe.Cg)([Pe.observable],rt.prototype,"visible",void 0),(0,Oe.Cg)([Pe.observable],rt.prototype,"individualPrivacyOpen",void 0),(0,Oe.Cg)([Pe.observable],rt.prototype,"previewCheckboxActiveState",void 0),(0,Oe.Cg)([Pe.observable],rt.prototype,"previewStickyMenuOpenState",void 0),(0,Oe.Cg)([Pe.observable],rt.prototype,"busyPresets",void 0),(0,Oe.Cg)([Pe.observable],rt.prototype,"presets",void 0),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof T?Object:T,"undefined"==typeof P?Object:P,Object,void 0]),(0,Oe.Sn)("design:returntype",void 0)],rt.prototype,"setBannerFromCustomize",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof Array?Object:Array]),(0,Oe.Sn)("design:returntype",void 0)],rt.prototype,"setBannerFromPreset",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],rt.prototype,"forceAnimationOutSimulation",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Boolean]),(0,Oe.Sn)("design:returntype",void 0)],rt.prototype,"setVisible",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Boolean]),(0,Oe.Sn)("design:returntype",void 0)],rt.prototype,"setIndividualPrivacyOpen",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Boolean]),(0,Oe.Sn)("design:returntype",void 0)],rt.prototype,"setPreviewCheckboxActiveState",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Boolean]),(0,Oe.Sn)("design:returntype",void 0)],rt.prototype,"setPreviewStickyMenuOpenState",null);class at extends i.AbstractPost{get rootStore(){return this.collection.store.rootStore}constructor(e,t={}){super(e,t)}setOrder(e){this.data.menu_order=e}setLabel(e){this.data.title.raw=e}setStatus(e){this.data.status=e}setMeta(e){this.data.meta=e}transformDataForPatch(){const e=super.transformDataForPatch();return{title:e.title,content:"",status:e.status,meta:e.meta,menu_order:e.menu_order,slug:e.title}}}(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],at.prototype,"rootStore",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Number]),(0,Oe.Sn)("design:returntype",void 0)],at.prototype,"setOrder",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[String]),(0,Oe.Sn)("design:returntype",void 0)],at.prototype,"setLabel",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof PostStatus?Object:PostStatus]),(0,Oe.Sn)("design:returntype",void 0)],at.prototype,"setStatus",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object]),(0,Oe.Sn)("design:returntype",void 0)],at.prototype,"setMeta",null),at=(0,Oe.Cg)([i.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:Ae,create:{path:"/rcb-banner-link"},patch:{path:"/rcb-banner-link/:id"},delete:{path:"/rcb-banner-link/:id"}}),(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object,"undefined"==typeof Partial?Object:Partial])],at);const lt={path:"/banner-links/order",method:i.RouteHttpVerb.PUT};class ct extends i.AbstractPostCollection{get sortedBannerLinks(){const e=Array.from(this.entries.values());return e.sort(((e,t)=>e.data.menu_order<t.data.menu_order?-1:e.data.menu_order>t.data.menu_order||e.key<t.key?1:e.key>t.key?-1:0)),e}constructor(e){super(),this.orderCookies=(0,Pe.flow)((function*(e){this.busy=!0;try{yield Ae({location:lt,request:{ids:e}});let t=0;for(const o of e)this.entries.get(o).setOrder(t),t++}catch(e){throw console.log(e),e}finally{this.busy=!1}})),this.store=e}instance(e){return new at(this).fromResponse(e)}}(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],ct.prototype,"sortedBannerLinks",null),ct=(0,Oe.Cg)([i.ClientCollection.annotate({path:"/rcb-banner-link",singlePath:"/rcb-banner-link/:id",namespace:"wp/v2",methods:[i.RouteHttpVerb.GET],request:Ae}),(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof OptionStore?Object:OptionStore])],ct);const dt={path:"/country-bypass/database",method:i.RouteHttpVerb.PUT},pt={path:"/migration/:migration",method:i.RouteHttpVerb.DELETE},ut={path:"/nav-menu/add-links",method:i.RouteHttpVerb.POST},gt={path:"/revision/current",method:i.RouteHttpVerb.GET},ht={path:"/revision/current",method:i.RouteHttpVerb.PUT},yt={path:"/settings",method:i.RouteHttpVerb.GET},bt={path:"/settings",method:i.RouteHttpVerb.PATCH};class ft extends i.BaseOptions{get isOnlyRcbCookieCreated(){return!(1!==this.allCookieCount||this.isTcf&&this.allTcfVendorConfigurationCount)}get areSettingsFetched(){return void 0!==this.isRespectDoNotTrack}constructor(e){super(),this.busySettings=!1,this.busyCountryBypassUpdate=!1,this.busyAddLinksToNavigationMenu=!1,this.needsRevisionRetrigger=!1,this.fetchedBannerLinks=!1,this.publicCookieCount=0,this.allCookieCount=0,this.allBlockerCount=0,this.allTcfVendorConfigurationCount=0,this.allScannerResultTemplatesCount=0,this.allScannerResultExternalUrlsCount=0,this.cookieCounts={draft:0,private:0,publish:0},this.cloudReleaseInfo={blocker:null,service:null},this.navMenus=[],this.templateNeedsUpdate=[],this.googleConsentModeNoticesHtml=[],this.servicesDataProcessingInUnsafeCountriesNoticeHtml="",this.servicesWithEmptyPrivacyPolicyNoticeHtml="",this.createdTagManagers={gtm:[],mtm:[]},this.contexts={"":""},this.isBannerActive=!1,this.isBlockerActive=!1,this.hidePageIds=[],this.forwardTo=[],this.countryBypassCountries=[],this.isTcf=!1,this.isGcm=!1,this.isGcmShowRecommandationsWithoutConsent=!1,this.isGcmCollectAdditionalDataViaUrlParameters=!1,this.isGcmRedactAdsDataWithoutConsent=!0,this.isGcmListPurposes=!0,this.bannerlessConsentChecks={essential:[],legalBasisConsentWithoutVisualContentBlocker:[],legalBasisLegitimateInterest:[]},this.isBannerStickyLinksEnabled=!1,this.fetchSettings=(0,Pe.flow)((function*(e){this.busySettings=!0;try{const t=e||(yield Ae({location:yt}));this.isBannerActive=t["rcb-banner-active"],this.isBlockerActive=t["rcb-blocker-active"],this.hidePageIds=(t["rcb-hide-page-ids"]||"").split(",").map(Number).filter(Boolean),this.setCookiesViaManager=t["rcb-set-cookies-via-manager"]||"none",this.operatorCountry=t["rcb-operator-country"],this.operatorContactAddress=t["rcb-operator-contact-address"],this.operatorContactPhone=t["rcb-operator-contact-phone"],this.operatorContactEmail=t["rcb-operator-contact-email"],this.operatorContactFormId=t["rcb-operator-contact-form-id"],this.cookiePolicyId=t["rcb-cookie-policy-id"],this.territorialLegalBasis=t["rcb-territorial-legal-basis"].split(","),this.isAcceptAllForBots=t["rcb-accept-all-for-bots"],this.isRespectDoNotTrack=t["rcb-respect-do-not-track"],this.isBannerLessConsent=t["rcb-banner-less-consent"],this.bannerLessConsentShowOnPageIds=(t["rcb-banner-less-show-on-page-ids"]||"").split(",").map(Number).filter(Boolean),this.cookieDuration=t["rcb-cookie-duration"],this.failedConsentDocumentationHandling=t["rcb-failed-consent-documentation-handling"],this.isSaveIp=t["rcb-save-ip"],this.isDataProcessingInUnsafeCountries=t["rcb-data-processing-in-unsafe-countries"],this.isAgeNotice=t["rcb-age-notice"],this.ageNoticeAgeLimit=t["rcb-age-notice-age-limit"],this.isListServicesNotice=t["rcb-list-services-notice"],this.isConsentForwarding=t["rcb-consent-forwarding"]||!1,this.forwardTo=(t["rcb-forward-to"]||"").split("|").filter(Boolean),this.crossDomains=t["rcb-cross-domains"]||"",this.isCountryBypass=t["rcb-country-bypass"],this.countryBypassCountries=(t["rcb-country-bypass-countries"]||"").split(",").filter(Boolean),this.countryBypassType=t["rcb-country-bypass-type"],this.countryBypassDbDownloadTime=t["rcb-country-bypass-db-download-time"],this.isTcf=t["rcb-tcf"],this.tcfAcceptedTime=t["rcb-tcf-accepted-time"],this.tcfGvlDownloadTime=t["rcb-tcf-gvl-download-time"],this.consentDuration=t["rcb-consent-duration"],yield this.fetchCurrentRevision()}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.fetchBannerLinks=(0,Pe.flow)((function*(){yield this.bannerLinks.get({request:{status:["draft","publish","private"]},params:{per_page:100,context:"edit"}}),this.fetchedBannerLinks=!0})),this.updateSettings=(0,Pe.flow)((function*(e,t){let{isBannerActive:o,isBlockerActive:n,hidePageIds:i,setCookiesViaManager:s,operatorCountry:r,operatorContactAddress:a,operatorContactEmail:l,operatorContactFormId:c,operatorContactPhone:d,cookiePolicyId:p,territorialLegalBasis:u,isAcceptAllForBots:g,isRespectDoNotTrack:h,cookieDuration:y,failedConsentDocumentationHandling:b,isSaveIp:f,isDataProcessingInUnsafeCountries:v,isAgeNotice:m,isBannerLessConsent:S,bannerLessConsentShowOnPageIds:C,ageNoticeAgeLimit:x,isListServicesNotice:k,isConsentForwarding:w,forwardTo:O,crossDomains:P,affiliateLink:j,affiliateLabelBehind:A,affiliateLabelDescription:I,isCountryBypass:T,countryBypassCountries:B,countryBypassType:R,isTcf:D,isGcm:F,isGcmShowRecommandationsWithoutConsent:N,isGcmCollectAdditionalDataViaUrlParameters:E,isGcmRedactAdsDataWithoutConsent:$,isGcmListPurposes:M,consentDuration:L,isBannerStickyLinksEnabled:_}=e;this.busySettings=!0;try{const e=yield Ae({location:bt,request:{...void 0===o?{}:{"rcb-banner-active":o},...void 0===n?{}:{"rcb-blocker-active":n},...void 0===i?{}:{"rcb-hide-page-ids":i.join(",")},...void 0===s?{}:{"rcb-set-cookies-via-manager":s},...void 0===r?{}:{"rcb-operator-country":r},...void 0===a?{}:{"rcb-operator-contact-address":a},...void 0===d?{}:{"rcb-operator-contact-phone":d},...void 0===l?{}:{"rcb-operator-contact-email":l},...void 0===c?{}:{"rcb-operator-contact-form-id":c},...void 0===p?{}:{"rcb-cookie-policy-id":p},...void 0===u?{}:{"rcb-territorial-legal-basis":u.join(",")},...void 0===g?{}:{"rcb-accept-all-for-bots":g},...void 0===S?{}:{"rcb-banner-less-consent":S},...void 0===C?{}:{"rcb-banner-less-show-on-page-ids":C.join(",")},...void 0===h?{}:{"rcb-respect-do-not-track":h},...void 0===y?{}:{"rcb-cookie-duration":y},...void 0===b?{}:{"rcb-failed-consent-documentation-handling":b},...void 0===f?{}:{"rcb-save-ip":f},...void 0===v?{}:{"rcb-data-processing-in-unsafe-countries":v},...void 0===m?{}:{"rcb-age-notice":m},...void 0===x?{}:{"rcb-age-notice-age-limit":x},...void 0===k?{}:{"rcb-list-services-notice":k},...void 0===w?{}:{"rcb-consent-forwarding":w},...void 0===O?{}:{"rcb-forward-to":O.join("|")},...void 0===P?{}:{"rcb-cross-domains":P},...void 0===T?{}:{"rcb-country-bypass":T},...void 0===B?{}:{"rcb-country-bypass-countries":B.join(",")},...void 0===R?{}:{"rcb-country-bypass-type":R},...void 0===D?{}:{"rcb-tcf":D},...void 0===L?{}:{"rcb-consent-duration":L}}});if(this.fetchedBannerLinks&&t){const e=this.bannerLinks.sortedBannerLinks;for(const o of e)t.find((e=>{let{id:t}=e;return o.data.id===t}))||(yield o.delete());for(let o=0;o<t.length;o++){const{isExternalUrl:n,label:i,pageType:s,externalUrl:r,hideCookieBanner:a,id:l,pageId:c,isTargetBlank:d}=t[o],p={isExternalUrl:n,pageType:s,externalUrl:r,hideCookieBanner:a,pageId:c,isTargetBlank:d};if(l){const t=e.find((e=>{let{data:{id:t}}=e;return l===t}));if(t){const{data:{title:{raw:e},menu_order:l,meta:{isExternalUrl:d,pageType:u,externalUrl:g,hideCookieBanner:h,pageId:y}}}=t;e===i&&l===o&&d===n&&u===s&&y===c&&g===r&&h===a||(t.setLabel(i),t.setOrder(o),t.setMeta(p),yield t.patch())}}else{const e=new at(this.bannerLinks,{title:{raw:i},content:{raw:"",protected:!1},status:"publish",menu_order:o,meta:p});yield e.persist()}}}this.fetchSettings(e),this.rootStore.checklistStore.fetchChecklist()}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.fetchCurrentRevision=(0,Pe.flow)((function*(){this.busySettings=!0;try{this.setFromCurrentRevision(yield Ae({location:gt}))}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.updateCurrentRevision=(0,Pe.flow)((function*(e){this.busySettings=!0;try{this.setFromCurrentRevision(yield Ae({location:ht,request:e}))}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.setModalHintSeen=(0,Pe.flow)((function*(e){this.busySettings=!0;try{this.others.modalHints.push(e),yield Ae({location:i.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:`modal-hint-${e}`},request:{value:!0}})}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.dismissConfigProNotice=(0,Pe.flow)((function*(){try{this.others.isConfigProNoticeVisible=!1,yield Ae({location:i.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:"get-pro-main-button"},request:{value:!0}})}catch(e){throw console.log(e),e}})),this.dismissServiceDataProcessingInUnsafeCountriesNotice=(0,Pe.flow)((function*(){try{this.servicesDataProcessingInUnsafeCountriesNoticeHtml="",yield Ae({location:i.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:"service-data-processing-in-unsafe-countries"},request:{value:!1}})}catch(e){throw console.log(e),e}})),this.dismissBannerlessConsentLegitimateServicesNotice=(0,Pe.flow)((function*(){try{yield Ae({location:i.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:"dismissed-bannerless-consent-legint-services"},request:{value:!1}}),yield this.fetchCurrentRevision()}catch(e){throw console.log(e),e}})),this.dismissBannerlessConsentServicesWithoutVisualContentBlockerNotice=(0,Pe.flow)((function*(){try{yield Ae({location:i.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:"dismissed-bannerless-consent-services-without-visual-content-blocker"},request:{value:!1}}),yield this.fetchCurrentRevision()}catch(e){throw console.log(e),e}})),this.dismissMigration=(0,Pe.flow)((function*(){try{const{id:e}=this.dashboardMigration;this.dashboardMigration=void 0,yield Ae({location:pt,params:{migration:e}})}catch(e){throw console.log(e),e}})),this.addLinksToNavigationMenu=(0,Pe.flow)((function*(e){this.busyAddLinksToNavigationMenu=!0;try{const{success:t}=yield Ae({location:ut,request:{id:e}});return t&&(this.rootStore.checklistStore.fetchChecklist(),yield this.fetchCurrentRevision()),t}catch(e){throw console.log(e),e}finally{this.busyAddLinksToNavigationMenu=!1}})),this.updateCountryBypassDatabase=(0,Pe.flow)((function*(){this.busyCountryBypassUpdate=!0;try{const{dbDownloadTime:e}=yield Ae({location:dt});this.countryBypassDbDownloadTime=e}catch(e){throw console.log(e),e}finally{this.busyCountryBypassUpdate=!1}})),this.rootStore=e,this.pureSlug=i.BaseOptions.getPureSlug("real-cookie-banner"),this.pureSlugCamelCased=i.BaseOptions.getPureSlug("real-cookie-banner",!0),(0,Pe.runInAction)((()=>{Object.assign(this,window[this.pureSlugCamelCased]),this.bannerLinks=new ct(this),this.fomoCoupon=this.others.fomoCoupon}))}setFromCurrentRevision(e){let{contexts:t,created_tag_managers:o,needs_retrigger:n,public_cookie_count:i,all_cookie_count:s,all_blocker_count:r,all_tcf_vendor_configuration_count:a,all_scanner_result_templates_count:l,all_scanner_result_external_urls_count:c,cookie_counts:d,cloud_release_info:p,consents_deleted_at:u,bannerless_consent_checks:g,nav_menus:h,tcf_vendor_configuration_counts:y,dashboard_migration:b,fomo_coupon:f,template_needs_update:v,check_saving_consent_via_rest_api_endpoint_working_html:m,template_update_notice_html:S,template_successors_notice_html:C,google_consent_mode_notices_html:x,services_data_processing_in_unsafe_countries_notice_html:k,services_with_empty_privacy_policy_notice_html:w}=e;this.createdTagManagers=o,this.needsRevisionRetrigger=n,this.publicCookieCount=i,this.allCookieCount=s,this.allBlockerCount=r,this.allTcfVendorConfigurationCount=a,this.allScannerResultTemplatesCount=l,this.allScannerResultExternalUrlsCount=c,this.templateNeedsUpdate=v,this.templateUpdateNoticeHtml=S,this.checkSavingConsentViaRestApiEndpointWorkingHtml=m,this.templateSuccessorsNoticeHtml=C,this.googleConsentModeNoticesHtml=x,this.servicesDataProcessingInUnsafeCountriesNoticeHtml=k,this.servicesWithEmptyPrivacyPolicyNoticeHtml=w,this.cookieCounts=d,this.cloudReleaseInfo=p,this.consentsDeletedAt=u,this.bannerlessConsentChecks=g,this.navMenus=h,this.tcfVendorConfigurationCounts=y,this.contexts=t,this.dashboardMigration=b,this.fomoCoupon=f}setShowLicenseFormImmediate(e,t){this.others.showLicenseFormImmediate=e,this.others.isLicensed=t}}let vt;(0,Oe.Cg)([Pe.observable],ft.prototype,"busySettings",void 0),(0,Oe.Cg)([Pe.observable],ft.prototype,"busyCountryBypassUpdate",void 0),(0,Oe.Cg)([Pe.observable],ft.prototype,"busyAddLinksToNavigationMenu",void 0),(0,Oe.Cg)([Pe.observable],ft.prototype,"needsRevisionRetrigger",void 0),(0,Oe.Cg)([Pe.observable],ft.prototype,"fetchedBannerLinks",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",void 0===ct?Object:ct)],ft.prototype,"bannerLinks",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"publicCookieCount",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"allCookieCount",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"allBlockerCount",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"allTcfVendorConfigurationCount",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"allScannerResultTemplatesCount",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"allScannerResultExternalUrlsCount",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"cookieCounts",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"cloudReleaseInfo",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"navMenus",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"tcfVendorConfigurationCounts",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"templateNeedsUpdate",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"checkSavingConsentViaRestApiEndpointWorkingHtml",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"templateUpdateNoticeHtml",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"templateSuccessorsNoticeHtml",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"googleConsentModeNoticesHtml",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"servicesDataProcessingInUnsafeCountriesNoticeHtml",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"servicesWithEmptyPrivacyPolicyNoticeHtml",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"createdTagManagers",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"contexts",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"dashboardMigration",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"fomoCoupon",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isBannerActive",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isBlockerActive",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"hidePageIds",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"setCookiesViaManager",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"operatorCountry",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"operatorContactAddress",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"operatorContactPhone",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"operatorContactEmail",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"operatorContactFormId",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"cookiePolicyId",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"territorialLegalBasis",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isAcceptAllForBots",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isRespectDoNotTrack",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isBannerLessConsent",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"bannerLessConsentShowOnPageIds",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"cookieDuration",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"failedConsentDocumentationHandling",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isSaveIp",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isDataProcessingInUnsafeCountries",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isAgeNotice",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"ageNoticeAgeLimit",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isListServicesNotice",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isConsentForwarding",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"forwardTo",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"crossDomains",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"affiliateLink",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"affiliateLabelBehind",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"affiliateLabelDescription",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isCountryBypass",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"countryBypassCountries",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"countryBypassType",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"countryBypassDbDownloadTime",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isTcf",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"tcfAcceptedTime",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"tcfGvlDownloadTime",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isGcm",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isGcmShowRecommandationsWithoutConsent",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isGcmCollectAdditionalDataViaUrlParameters",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isGcmRedactAdsDataWithoutConsent",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isGcmListPurposes",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"consentDuration",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"consentsDeletedAt",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"bannerlessConsentChecks",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],ft.prototype,"isBannerStickyLinksEnabled",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type","undefined"==typeof OtherOptions?Object:OtherOptions)],ft.prototype,"others",void 0),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],ft.prototype,"isOnlyRcbCookieCreated",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],ft.prototype,"areSettingsFetched",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof ResponseRouteRevisionCurrentGet?Object:ResponseRouteRevisionCurrentGet]),(0,Oe.Sn)("design:returntype",void 0)],ft.prototype,"setFromCurrentRevision",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Boolean,Boolean]),(0,Oe.Sn)("design:returntype",void 0)],ft.prototype,"setShowLicenseFormImmediate",null);const mt=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return(vt||(vt=(0,i.createLocalizationFactory)(i.BaseOptions.getPureSlug("real-cookie-banner")))).__(...t)},St={path:"/scanner/result/ignore",method:i.RouteHttpVerb.POST};class Ct{get identifier(){return this.data.host}get inactive(){return"full"===this.blockedStatus||this.data.ignored}get blockedStatus(){const{foundCount:e,blockedCount:t}=this.data;return 0===t?"none":e===t?"full":"partial"}get blockedStatusText(){switch(this.blockedStatus){case"full":return mt("Fully blocked");case"partial":return mt("Partially blocked");default:return mt("Not blocked")}}constructor(e,t){this.busy=!1,this.ignore=(0,Pe.flow)((function*(e){this.busy=!0;try{yield Ae({location:St,request:{type:"host",value:this.data.host,ignored:e}}),this.data.ignored=e}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,Pe.runInAction)((()=>{this.data=e})),this.store=t}}function xt(e,t,o){if(void 0===t&&(t=50),void 0===o&&(o="..."),!e||e.length<=t)return e;const n=t-o.length,i=Math.ceil(n/2),s=Math.floor(n/2);return e.substr(0,i)+o+e.substr(e.length-s)}(0,Oe.Cg)([Pe.observable],Ct.prototype,"busy",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],Ct.prototype,"data",void 0),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Ct.prototype,"identifier",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Ct.prototype,"inactive",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Ct.prototype,"blockedStatus",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Ct.prototype,"blockedStatusText",null);class kt{get markup(){return this.store.resultMarkup.get(this.data.id)}get blockedUrlTruncate(){return xt(this.data.blockedUrl,50,"[...]")}get sourceUrlTruncate(){return xt(this.data.sourceUrl,50,"[...]")}constructor(e,t){this.busy=!1,this.fetchMarkup=(0,Pe.flow)((function*(){yield this.store.fetchMarkup(this.data.id)})),(0,Pe.runInAction)((()=>{this.data=e})),this.store=t}}(0,Oe.Cg)([Pe.observable],kt.prototype,"busy",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],kt.prototype,"data",void 0),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],kt.prototype,"markup",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],kt.prototype,"blockedUrlTruncate",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],kt.prototype,"sourceUrlTruncate",null);class wt{get identifier(){return this.data.identifier}get type(){return this.templateModel instanceof Xe?"service":"blocker"}get inactive(){return this.data.consumerData.isCreated||this.data.consumerData.isIgnored}constructor(e,t){this.busy=!1,this.ignore=(0,Pe.flow)((function*(e){this.busy=!0;try{yield Ae({location:St,request:{type:"template",value:this.identifier,ignored:e}}),this.data.consumerData.isIgnored=e}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,Pe.runInAction)((()=>{this.data=e})),this.store=t;const{cookieStore:o}=t.rootStore;Object.hasOwn(e,"rules")?(o.addBlockerTemplates([e]),this.templateModel=o.templatesBlocker.get(e.identifier)):(o.addServiceTemplates([e]),this.templateModel=o.templatesServices.get(e.identifier))}}(0,Oe.Cg)([Pe.observable],wt.prototype,"busy",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],wt.prototype,"data",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],wt.prototype,"templateModel",void 0),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],wt.prototype,"identifier",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],wt.prototype,"type",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],wt.prototype,"inactive",null);const Ot={path:"/scanner/queue",method:i.RouteHttpVerb.POST},Pt={path:"/scanner/result/externals/:type/:identifier",method:i.RouteHttpVerb.GET,obfuscatePath:"full"},jt={path:"/scanner/result/externals",method:i.RouteHttpVerb.GET},At={path:"/scanner/result/markup/:id",method:i.RouteHttpVerb.GET},It={path:"/scanner/result/templates",method:i.RouteHttpVerb.GET};class Tt{get sortedTemplates(){const e=Array.from(this.resultTemplates.values());return e.sort(((e,t)=>(e.data.consumerData.isIgnored,t.data.consumerData.isIgnored,e.data.headline.localeCompare(t.data.headline)))),e}get sortedExternalUrls(){const e=Array.from(this.resultExternalUrls.values());return e.sort(((e,t)=>e.inactive===t.inactive?0:e.inactive?1:-1)),e}get templatesCount(){return this.fetchedAllResultTemplates?this.resultTemplates.size:this.rootStore.optionStore.allScannerResultTemplatesCount}get externalUrlsCount(){return this.fetchedAllResultExternalUrls?this.resultExternalUrls.size:this.rootStore.optionStore.allScannerResultExternalUrlsCount}get canShowResults(){var e;return this.templatesCount+this.externalUrlsCount>0&&(null==(e=this.rootStore.checklistStore.checklist)?void 0:e.items.scanner.checked)&&this.rootStore.optionStore.others.isLicensed}get foundScanResultsCount(){return this.resultTemplates.size+this.resultExternalUrls.size}get needsAttentionCount(){return[...this.resultTemplates.values(),...this.resultExternalUrls.values()].filter((e=>{let{inactive:t}=e;return!t})).length}constructor(e){this.resultTemplates=new Map,this.busyResultTemplates=!1,this.fetchedAllResultTemplates=!1,this.resultExternalUrls=new Map,this.resultAllExternalUrls=new Map,this.busyExternalUrls=!1,this.fetchedAllResultExternalUrls=!1,this.busyMarkup=!1,this.resultMarkup=new Map,this.addUrlsToQueue=(0,Pe.flow)((function*(e){return yield Ae({location:Ot,request:e})})),this.fetchResultTemplates=(0,Pe.flow)((function*(){this.busyResultTemplates=!0;try{this.resultTemplatesFromResponse(yield Ae({location:It})),this.fetchedAllResultTemplates=!0}catch(e){throw console.log(e),e}finally{this.busyResultTemplates=!1}})),this.fetchResultExternals=(0,Pe.flow)((function*(){this.busyExternalUrls=!0;try{this.resultExternalUrlsFromResponse(yield Ae({location:jt})),this.fetchedAllResultExternalUrls=!0}catch(e){throw console.log(e),e}finally{this.busyExternalUrls=!1}})),this.fetchResultAllExternals=(0,Pe.flow)((function*(e){const t=e instanceof Ct?"host":"template",{identifier:o}=e;e.busy=!0;try{const{items:e}=yield Ae({location:Pt,params:{type:t,identifier:"host"===t?o.replace(/\./g,"_"):o}});let n=this.resultAllExternalUrls.get(o);if(n){const t=e.map((e=>{let{id:t}=e;return t}));for(const e of n.keys())-1===t.indexOf(e)&&n.delete(e)}else n=new Map;for(const t of Object.values(e))n.set(t.id,new kt(t,this)),this.resultAllExternalUrls.set(o,n)}catch(e){throw console.log(e),e}finally{e.busy=!1}})),this.fetchMarkup=(0,Pe.flow)((function*(e){this.busyMarkup=!0;try{const t=yield Ae({location:At,params:{id:e}});this.resultMarkup.set(e,t)}catch(e){throw console.log(e),e}finally{this.busyMarkup=!1}})),this.rootStore=e}resultTemplatesFromResponse(e){let{items:t}=e;const o=Object.keys(t);for(const e of this.resultTemplates.keys())-1===o.indexOf(e)&&this.resultTemplates.delete(e);for(const e of o)this.resultTemplates.set(e,new wt(t[e],this))}resultExternalUrlsFromResponse(e){let{items:t}=e;const o=Object.keys(t);for(const e of this.resultExternalUrls.keys())-1===o.indexOf(e)&&this.resultExternalUrls.delete(e);for(const e of o){const o=this.resultExternalUrls.get(e);o?(0,Pe.set)(o,{data:t[e]}):this.resultExternalUrls.set(e,new Ct(t[e],this))}}}(0,Oe.Cg)([Pe.observable],Tt.prototype,"resultTemplates",void 0),(0,Oe.Cg)([Pe.observable],Tt.prototype,"busyResultTemplates",void 0),(0,Oe.Cg)([Pe.observable],Tt.prototype,"fetchedAllResultTemplates",void 0),(0,Oe.Cg)([Pe.observable],Tt.prototype,"resultExternalUrls",void 0),(0,Oe.Cg)([Pe.observable],Tt.prototype,"resultAllExternalUrls",void 0),(0,Oe.Cg)([Pe.observable],Tt.prototype,"busyExternalUrls",void 0),(0,Oe.Cg)([Pe.observable],Tt.prototype,"fetchedAllResultExternalUrls",void 0),(0,Oe.Cg)([Pe.observable],Tt.prototype,"busyMarkup",void 0),(0,Oe.Cg)([Pe.observable],Tt.prototype,"resultMarkup",void 0),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Tt.prototype,"sortedTemplates",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Tt.prototype,"sortedExternalUrls",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Tt.prototype,"templatesCount",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Tt.prototype,"externalUrlsCount",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Tt.prototype,"canShowResults",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Tt.prototype,"foundScanResultsCount",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Tt.prototype,"needsAttentionCount",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof ResponseRouteScannerResultTemplatesGet?Object:ResponseRouteScannerResultTemplatesGet]),(0,Oe.Sn)("design:returntype",void 0)],Tt.prototype,"resultTemplatesFromResponse",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof ResponseRouteScannerResultExternalsGet?Object:ResponseRouteScannerResultExternalsGet]),(0,Oe.Sn)("design:returntype",void 0)],Tt.prototype,"resultExternalUrlsFromResponse",null);const Bt=moment;var Rt=o.n(Bt);class Dt extends i.BaseOptions{constructor(e){super(),this.busyStats={main:!1,buttonClicked:!1,customBypass:!1},this.stats=Pe.observable.object({main:void 0,buttonsClicked:void 0,customBypass:void 0},{},{deep:!1}),this.filters=Pe.observable.object({dates:void 0,context:void 0},{},{deep:!1}),this.fetchMain=(0,Pe.flow)((function*(){throw new Error("This feature is not available in the free version.")})),this.fetchButtonsClicked=(0,Pe.flow)((function*(){throw new Error("This feature is not available in the free version.")})),this.fetchCustomBypass=(0,Pe.flow)((function*(){throw new Error("This feature is not available in the free version.")})),this.rootStore=e,(0,Pe.runInAction)((()=>{this.filters.dates=[Rt()().subtract(30,"days"),Rt()()],this.filters.context=this.rootStore.optionStore.others.context}))}applyDates(e){this.filters.dates=e}applyContext(e){this.filters.context=e}}(0,Oe.Cg)([Pe.observable],Dt.prototype,"busyStats",void 0),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object]),(0,Oe.Sn)("design:returntype",void 0)],Dt.prototype,"applyDates",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object]),(0,Oe.Sn)("design:returntype",void 0)],Dt.prototype,"applyContext",null);class Ft{constructor(e,t){(0,Pe.runInAction)((()=>{this.data=e})),this.store=t}}(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],Ft.prototype,"data",void 0);class Nt{constructor(e,t,o){(0,Pe.runInAction)((()=>{this.special=t,this.data=e})),this.store=o}}(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],Nt.prototype,"data",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Boolean)],Nt.prototype,"special",void 0);class Et{constructor(e,t,o){(0,Pe.runInAction)((()=>{this.special=t,this.data=e})),this.store=o}}(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],Et.prototype,"data",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Boolean)],Et.prototype,"special",void 0);class $t{get vendorConfiguration(){for(const t of this.store.vendorConfigurations.entries.values()){var e;if((null==(e=t.vendorModel)?void 0:e.data.id)===this.data.id)return t}}get restrictivePurposes(){const e={normal:{}};for(const t of[...this.legIntPurposes,...this.purposes])e.normal[t.data.id.toString()]={enabled:!0,legInt:this.legIntPurposes.indexOf(t)>-1&&!t.special?"yes":"no"};return e}get purposes(){var e;return null==(e=this.data)?void 0:e.purposes.map((e=>this.store.purposes.get(`${e}`)))}get legIntPurposes(){var e;return null==(e=this.data)?void 0:e.legIntPurposes.map((e=>this.store.purposes.get(`${e}`)))}get specialPurposes(){var e;return null==(e=this.data)?void 0:e.specialPurposes.map((e=>this.store.specialPurposes.get(`${e}`)))}get features(){var e;return null==(e=this.data)?void 0:e.features.map((e=>this.store.features.get(`${e}`)))}get specialFeatures(){var e;return null==(e=this.data)?void 0:e.specialFeatures.map((e=>this.store.specialFeatures.get(`${e}`)))}get dataCategories(){var e;return null==(e=this.data)?void 0:e.dataDeclaration.map((e=>this.store.dataCategories.get(`${e}`)))}constructor(e,t){(0,Pe.runInAction)((()=>{this.data=e})),this.store=t}}(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],$t.prototype,"data",void 0),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],$t.prototype,"vendorConfiguration",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],$t.prototype,"restrictivePurposes",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],$t.prototype,"purposes",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],$t.prototype,"legIntPurposes",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],$t.prototype,"specialPurposes",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],$t.prototype,"features",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],$t.prototype,"specialFeatures",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],$t.prototype,"dataCategories",null);const Mt=jQuery;var Lt=o.n(Mt);class _t extends i.AbstractPost{get restrictivePurposes(){var e;const t=JSON.parse(this.data.meta.restrictivePurposes);return Lt().extend(!0,{},(null==(e=this.vendorModel)?void 0:e.restrictivePurposes)||{},t)}get dataProcessingInCountries(){return JSON.parse(this.data.meta.dataProcessingInCountries||"[]")}get dataProcessingInCountriesSpecialTreatments(){return JSON.parse(this.data.meta.dataProcessingInCountriesSpecialTreatments||"[]")}constructor(e,t={}){super(e,t),(0,Pe.reaction)((()=>this.data.vendor),(e=>(0,Pe.runInAction)((()=>{if(e){const{vendors:t}=this.collection.store,o=e.id.toString();let n=t.get(o);n||(n=new $t(e,this.collection.store),t.set(o,n)),this.vendorModel=n}}))),{fireImmediately:!0}),(0,Pe.reaction)((()=>{var e;return null==(e=this.data.meta)?void 0:e.vendorId}),(e=>{e&&(this.vendorModel=this.collection.store.vendors.get(e.toString()))}),{fireImmediately:!0})}setStatus(e){this.data.status=e}setMeta(e){this.data.meta=e}transformDataForPatch(){const e=super.transformDataForPatch();return{status:e.status,meta:e.meta}}afterPatch(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterDelete(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterPersist(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}}(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",void 0===$t?Object:$t)],_t.prototype,"vendorModel",void 0),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype","undefined"==typeof TcfVendorConfigurationRestrictivePurposes?Object:TcfVendorConfigurationRestrictivePurposes)],_t.prototype,"restrictivePurposes",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",Object)],_t.prototype,"dataProcessingInCountries",null),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",Object)],_t.prototype,"dataProcessingInCountriesSpecialTreatments",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof PostStatus?Object:PostStatus]),(0,Oe.Sn)("design:returntype",void 0)],_t.prototype,"setStatus",null),(0,Oe.Cg)([Pe.action,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object]),(0,Oe.Sn)("design:returntype",void 0)],_t.prototype,"setMeta",null),_t=(0,Oe.Cg)([i.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:Ae,create:{path:"/rcb-tcf-vendor-conf"},patch:{path:"/rcb-tcf-vendor-conf/:id"},delete:{path:"/rcb-tcf-vendor-conf/:id"}}),(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[Object,"undefined"==typeof Partial?Object:Partial])],_t);class Ut extends i.AbstractPostCollection{constructor(e){super(),this.store=e}instance(e){return new _t(this).fromResponse(e)}}Ut=(0,Oe.Cg)([i.ClientCollection.annotate({path:"/rcb-tcf-vendor-conf",singlePath:"/rcb-tcf-vendor-conf/:id",namespace:"wp/v2",methods:[i.RouteHttpVerb.GET],request:Ae}),(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",["undefined"==typeof TcfStore?Object:TcfStore])],Ut);const Wt={path:"/tcf/declarations",method:i.RouteHttpVerb.GET},zt={path:"/tcf/gvl",method:i.RouteHttpVerb.PUT},Vt={path:"/tcf/vendors",method:i.RouteHttpVerb.GET};class Ht extends i.BaseOptions{get vendorConfigurationCount(){return this.fetchedAllVendorConfigurations?this.vendorConfigurations.entries.size:this.rootStore.optionStore.allTcfVendorConfigurationCount}constructor(e){super(),this.busyGvl=!1,this.busyVendors=!1,this.busyDeclarations=!1,this.fetchedAllVendorConfigurations=!1,this.vendors=new Map,this.purposes=new Map,this.specialPurposes=new Map,this.features=new Map,this.specialFeatures=new Map,this.dataCategories=new Map,this.fetchVendorConfigurations=(0,Pe.flow)((function*(){const e=Math.ceil(this.vendorConfigurationCount/100);for(let t=0;t<e;t++)yield this.vendorConfigurations.get({request:{status:["draft","publish","private"]},params:{offset:100*t,per_page:100,context:"edit"}});this.fetchedAllVendorConfigurations=!0})),this.fetchVendors=(0,Pe.flow)((function*(){this.busyVendors=!0;try{const{vendorListVersion:e,vendors:t}=yield Ae({location:Vt});for(const e of Object.keys(t))this.vendors.set(e,new $t(t[e],this));this.vendorListVersion=e}catch(e){throw console.log(e),e}finally{this.busyVendors=!1}})),this.fetchDeclarations=(0,Pe.flow)((function*(){this.busyDeclarations=!0;try{const{gvlSpecificationVersion:e,tcfPolicyVersion:t,purposes:o,specialPurposes:n,features:i,specialFeatures:s,dataCategories:r}=yield Ae({location:Wt});for(const e of Object.keys(o))this.purposes.set(e,new Et(o[e],!1,this));for(const e of Object.keys(n))this.specialPurposes.set(e,new Et(n[e],!0,this));for(const e of Object.keys(i))this.features.set(e,new Nt(i[e],!1,this));for(const e of Object.keys(s))this.specialFeatures.set(e,new Nt(s[e],!0,this));for(const e of Object.keys(r))this.dataCategories.set(e,new Ft(r[e],this));this.declarations={purposes:o,specialPurposes:n,features:i,specialFeatures:s,dataCategories:r},this.gvlSpecificationVersion=e,this.tcfPolicyVersion=t}catch(e){throw console.log(e),e}finally{this.busyDeclarations=!1}})),this.updateGvl=(0,Pe.flow)((function*(){this.busyGvl=!0;try{const{gvlDownloadTime:e}=yield Ae({location:zt});this.rootStore.optionStore.tcfGvlDownloadTime=e}catch(e){throw console.log(e),e}finally{this.busyGvl=!1}})),this.rootStore=e,(0,Pe.runInAction)((()=>{this.vendorConfigurations=new Ut(this)}))}}(0,Oe.Cg)([Pe.observable],Ht.prototype,"busyGvl",void 0),(0,Oe.Cg)([Pe.observable],Ht.prototype,"busyVendors",void 0),(0,Oe.Cg)([Pe.observable],Ht.prototype,"busyDeclarations",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",void 0===Ut?Object:Ut)],Ht.prototype,"vendorConfigurations",void 0),(0,Oe.Cg)([Pe.observable],Ht.prototype,"fetchedAllVendorConfigurations",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],Ht.prototype,"vendorListVersion",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],Ht.prototype,"gvlSpecificationVersion",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type",Object)],Ht.prototype,"tcfPolicyVersion",void 0),(0,Oe.Cg)([Pe.observable,(0,Oe.Sn)("design:type","undefined"==typeof Omit?Object:Omit)],Ht.prototype,"declarations",void 0),(0,Oe.Cg)([Pe.observable],Ht.prototype,"vendors",void 0),(0,Oe.Cg)([Pe.observable],Ht.prototype,"purposes",void 0),(0,Oe.Cg)([Pe.observable],Ht.prototype,"specialPurposes",void 0),(0,Oe.Cg)([Pe.observable],Ht.prototype,"features",void 0),(0,Oe.Cg)([Pe.observable],Ht.prototype,"specialFeatures",void 0),(0,Oe.Cg)([Pe.observable],Ht.prototype,"dataCategories",void 0),(0,Oe.Cg)([Pe.computed,(0,Oe.Sn)("design:type",Function),(0,Oe.Sn)("design:paramtypes",[]),(0,Oe.Sn)("design:returntype",void 0)],Ht.prototype,"vendorConfigurationCount",null);class Gt{get context(){return this.contextMemo?this.contextMemo:this.contextMemo=(0,i.createContextFactory)(this)}constructor(){this.optionStore=new ft(this),this.customizeBannerStore=new rt(this),this.cookieStore=new nt(this),this.consentStore=new Ue(this),this.statsStore=new Dt(this),this.checklistStore=new Be(this),this.tcfStore=new Ht(this),this.scannerStore=new Tt(this)}static get StoreProvider(){return Gt.get.context.StoreProvider}static get get(){return Gt.me?Gt.me:Gt.me=new Gt}}const qt=()=>Gt.get.context.useStores(),Yt=(0,d.PA)((e=>{let{poweredLink:t}=e;const{optionStore:o,customizeBannerStore:s}=qt(),{others:{customizeValuesBanner:{layout:r,decision:a,design:l,headerDesign:c,bodyDesign:d,footerDesign:S,texts:C,individualLayout:x,saveButton:k,group:w,individualTexts:O,customCss:P,mobile:j,sticky:A},frontend:I,bannerDesignVersion:T,iso3166OneAlpha2:B,bannerI18n:R,pageRequestUuid4:D,isPoweredByLinkDisabledByException:F,affiliate:N}}=o,{visible:E,individualPrivacyOpen:$,previewCheckboxActiveState:M,previewStickyMenuOpenState:L}=s,_=(z=I.isTcf,V=I.tcf,H=I.tcfMetadata,G=function(){const{frontend:{decisionCookieName:e,groups:t,isGcm:o,revisionHash:n,setCookiesViaManager:s,failedConsentDocumentationHandling:r}}=xe();return window.rcbConsentManager||(window.rcbConsentManager=new me({decisionCookieName:e,groups:t,isGcm:o,revisionHash:n,setCookiesViaManager:s,consentQueueLocalStorageName:"real_cookie_banner-consent-queue",supportsCookiesName:"real_cookie_banner-test",skipOptIn:function(e){const{presetId:t}=e;return["amazon-associates-widget"].indexOf(t)>-1},cmpId:367,cmpVersion:ke("major"),failedConsentDocumentationHandling:r,persistConsent:async(e,t)=>{const{restNamespace:o,restRoot:n,restQuery:s,restNonce:r,restPathObfuscateOffset:a}=Ce(),{forward:l,uuid:c}=await(0,i.commonRequest)({location:we,options:{restNamespace:o,restRoot:n,restQuery:s,restNonce:r,restPathObfuscateOffset:a},sendRestNonce:!1,sendReferer:!0,request:{...e,setCookies:t,referer:window.location.href}});return l&&function(e){let{endpoints:t,data:o}=e;const{isPro:n}=xe();if(n){const e=[];for(const n of t)e.push(window.fetch(n,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json;charset=utf-8"},body:JSON.stringify(o)}));return Promise.all(e)}Promise.reject()}(l),c}})),window.rcbConsentManager}().getOptions(),Y=async()=>{if(z&&Object.values(V.vendors).length>0){let e,t;const o=function(e,t,o){let n=u.A.get(o);const i=localStorage.getItem(o);if(i&&(n=i),!n)return;const{gvl:s,model:r}=(0,g.T)({tcf:e,tcfMetadata:t,tcfString:n});return{gvl:s,model:r,tcfString:n,tcfStringForVendors:()=>p.d.encode(r,{isForVendors:!0})}}(V,H,G.tcfCookieName);if(o)({gvl:e,model:t}=o);else{const o=(0,g.T)({tcf:V,tcfMetadata:H,tcfString:""});({gvl:e,model:t}=o)}return function(e,t,o){let{cmpId:n,cmpVersion:i}=o;e.cmpId=n,e.isServiceSpecific="service-specific"===t.scope,e.cmpVersion=i}(t,H,G),function(e,t){e.isServiceSpecific&&Object.values(t).forEach((t=>{let{vendorId:o,restrictivePurposes:n}=t;for(const t in n.normal){const{enabled:i,legInt:s}=n.normal[t],r=new h.F;r.purposeId=+t,i?s&&(r.restrictionType="yes"===s?y.h.REQUIRE_LI:y.h.REQUIRE_CONSENT):r.restrictionType=y.h.NOT_ALLOWED,void 0!==r.restrictionType&&e.publisherRestrictions.add(o,r)}}))}(t,V.vendorConfigurations),(null==q?void 0:q.startsWith("implicit"))?(await(0,v.P)(),(0,b.o)(t,q)):o||(await(0,v.P)(),(0,b.o)(t,"initial")),{gvl:e,model:t,original:V,metadata:H}}},Q=[z,V,H,q],(0,f.useMemo)((()=>(0,v.P)().then(Y)),Q)),[U,W]=(0,m.d)({layout:{...r,animationInOnlyMobile:!1,animationOutOnlyMobile:!1},decision:{...a},design:{...l},headerDesign:{...c},bodyDesign:{...d},footerDesign:{...S,poweredByLink:!F&&S.poweredByLink},texts:{...C},individualLayout:{...x},saveButton:{...k},group:{...w},individualTexts:{...O},customCss:{...P},mobile:{...j},sticky:{...A},...I,blocker:void 0,isConsentGiven:!0,pageRequestUuid4:D,poweredLink:t,iso3166OneAlpha2:B,gcmConsent:[],tcf:void 0,tcfFilterBy:"legInt",visible:E,individualPrivacyOpen:$,previewCheckboxActiveState:M,previewStickyMenuOpenState:L,designVersion:T,i18n:R,affiliate:N,consent:{},didGroupFirstChange:!1,onPersistConsent:async()=>{},onApplyConsent:async()=>{},suspense:{tcf:_}},{onSave:()=>{},updateGroupChecked:()=>{},updateCookieChecked:()=>{},updateGcmConsentTypeChecked:()=>{}},{inherit:["visible","previewCheckboxActiveState","previewStickyMenuOpenState","individualPrivacyOpen","layout","decision","design","headerDesign","bodyDesign","footerDesign","texts","individualLayout","saveButton","group","individualTexts","customCss","mobile","sticky"]});var z,V,H,G,q,Y,Q;return(0,n.jsx)(U,{value:W,children:(0,n.jsx)(be.k,{promise:_,children:(0,n.jsx)(ye,{})})})})),Qt=document.createElement("div");function Jt(){const{customizeIdsBanner:{settings:{texts:{poweredBy:e}}},poweredByTexts:t}=xe(),o=r().customize(e).get(),s=document.createElement("a");s.target="_blank",s.href=Gt.get.optionStore.others.pluginUrl,s.innerHTML=t[o],(0,i.createRoot)(document.body.appendChild(Qt)).render((0,n.jsx)(Gt.StoreProvider,{children:(0,n.jsx)(Yt,{poweredLink:s})}))}Qt.className="rcb-customize-banner-container",(0,l.G)((async()=>{await(0,c.x)((()=>r().customize("rcb-banner-texts-powered-by")));const{customizeIdsBanner:{settings:{texts:{poweredBy:e}}}}=xe();(0,a.listenChanges)({texts:{poweredBy:e}},(()=>setTimeout(Jt,0))),Jt(),document.querySelectorAll(".devowl-wp-react-cookie-banner-cookie-policy").forEach((e=>{Promise.all([o.e(227),o.e(40)]).then(o.bind(o,76819)).then((t=>{let{createCookiePolicyTable:o}=t;return o(e)}))}))})),function(){const{customizeBannerStore:e,optionStore:t,checklistStore:o}=Gt.get,{others:{customizeIdsBanner:{panel:n,sections:s,settings:r}}}=t,l=e=>setTimeout(e,0),c=(0,a.getSidebarCustomize)(),{group:d,sticky:p,texts:u}=r;[d.checkboxActiveBg,d.checkboxActiveBorderColor,d.checkboxActiveColor].forEach((t=>c.control(t,(t=>{let{container:o}=t;return o.hover((()=>l((()=>e.setPreviewCheckboxActiveState(!0)))),(()=>l((()=>e.setPreviewCheckboxActiveState(!1)))))})))),[s.individualLayout,s.group,s.saveButton,s.individualTexts].forEach((t=>(0,a.listenPanelExpanded)(t,(t=>{l((()=>e.setIndividualPrivacyOpen(t)))})))),(0,a.listenPanelExpanded)(s.sticky,(t=>{l((()=>e.setVisible(!t)))})),(0,a.listenPanelExpanded)(s.cookiePolicy,(e=>c.section(s.cookiePolicy,(t=>{let{params:{customParams:{url:o}}}=t;const{previewUrl:n}=c.previewer,{href:s}=window.location,r=new URL(s).searchParams.get("customizePreviousUrl");e&&!r?n.set((0,i.applyQueryString)(new URL(o),{customizePreviousUrl:s}).toString()):!e&&r&&n.set(r)})))),[p.bubbleMargin,p.bubbleHoverBg,p.bubbleHoverBorderColor,p.hoverIconColor,p.hoverIconCustom,p.hoverIconCustomRetina,p.menuFontSize,p.menuBorderRadius,p.menuItemSpacing,p.menuItemPadding].forEach((t=>c.control(t,(t=>{let{container:o}=t;return o.hover((()=>l((()=>e.setPreviewStickyMenuOpenState(!0)))),(()=>l((()=>e.setPreviewStickyMenuOpenState(!1)))))})))),[u.stickyChange,u.stickyHistory,u.stickyRevoke,u.stickyRevokeSuccessMessage].forEach((t=>c.control(t,(t=>{let{container:o}=t;return o.hover((()=>l((()=>{e.setVisible(!1),e.setPreviewStickyMenuOpenState(!0)}))),(()=>l((()=>{e.setVisible(!0),e.setPreviewStickyMenuOpenState(!1)}))))})))),(0,a.previewFactory)({immediateApply:{texts:r.texts,individualTexts:r.individualTexts},onChange:[r,(t,o,n)=>l((()=>{const i=["individualTexts","texts"].indexOf(`${t}`)>-1?(0,a.xssHtmlSanitize)(n):n;e.setBannerFromCustomize(t,o,i)}))],onPanelChange:[n,t=>l((()=>{const n=c.section(s.cookiePolicy).expanded();e.setVisible(!n&&t),t&&o.toggleChecklistItem("customize-banner",!0)}))]})}()},41594:e=>{e.exports=React},75206:e=>{e.exports=ReactDOM},3713:e=>{e.exports=ReactJSXRuntime},44497:e=>{e.exports=mobx}},r={};function a(e){var t=r[e];if(void 0!==t)return t.exports;var o=r[e]={exports:{}};return s[e].call(o.exports,o,o.exports,a),o.exports}a.m=s,e=[],a.O=(t,o,n,i)=>{if(!o){var s=1/0;for(d=0;d<e.length;d++){for(var[o,n,i]=e[d],r=!0,l=0;l<o.length;l++)(!1&i||s>=i)&&Object.keys(a.O).every((e=>a.O[e](o[l])))?o.splice(l--,1):(r=!1,i<s&&(s=i));if(r){e.splice(d--,1);var c=n();void 0!==c&&(t=c)}}return t}i=i||0;for(var d=e.length;d>0&&e[d-1][2]>i;d--)e[d]=e[d-1];e[d]=[o,n,i]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,a.t=function(e,n){if(1&n&&(e=this(e)),8&n)return e;if("object"==typeof e&&e){if(4&n&&e.__esModule)return e;if(16&n&&"function"==typeof e.then)return e}var i=Object.create(null);a.r(i);var s={};t=t||[null,o({}),o([]),o(o)];for(var r=2&n&&e;"object"==typeof r&&!~t.indexOf(r);r=o(r))Object.getOwnPropertyNames(r).forEach((t=>s[t]=()=>e[t]));return s.default=()=>e,a.d(i,s),i},a.d=(e,t)=>{for(var o in t)a.o(t,o)&&!a.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},a.f={},a.e=e=>Promise.all(Object.keys(a.f).reduce(((t,o)=>(a.f[o](e,t),t)),[])),a.u=e=>(({4:"banner-lazy",40:"cookie-policy"}[e]||e)+".lite.js?ver="+{4:"75279a656f856e23",40:"34246544b898bf20",227:"eb91502fb5aa9e2c",343:"a12327ef46b2b83a"}[e]),a.miniCssF=e=>{},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n={},i="realCookieBanner_:",a.l=(e,t,o,s)=>{if(n[e])n[e].push(t);else{var r,l;if(void 0!==o)for(var c=document.getElementsByTagName("script"),d=0;d<c.length;d++){var p=c[d];if(p.getAttribute("src")==e||p.getAttribute("data-webpack")==i+o){r=p;break}}r||(l=!0,(r=document.createElement("script")).charset="utf-8",r.timeout=120,a.nc&&r.setAttribute("nonce",a.nc),r.setAttribute("data-webpack",i+o),r.src=e),n[e]=[t];var u=(t,o)=>{r.onerror=r.onload=null,clearTimeout(g);var i=n[e];if(delete n[e],r.parentNode&&r.parentNode.removeChild(r),i&&i.forEach((e=>e(o))),t)return t(o)},g=setTimeout(u.bind(null,void 0,{type:"timeout",target:r}),12e4);r.onerror=u.bind(null,r.onerror),r.onload=u.bind(null,r.onload),l&&document.head.appendChild(r)}},a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;a.g.importScripts&&(e=a.g.location+"");var t=a.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var o=t.getElementsByTagName("script");if(o.length)for(var n=o.length-1;n>-1&&(!e||!/^http(s?):/.test(e));)e=o[n--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),a.p=e})(),(()=>{var e={101:0};a.f.j=(t,o)=>{var n=a.o(e,t)?e[t]:void 0;if(0!==n)if(n)o.push(n[2]);else{var i=new Promise(((o,i)=>n=e[t]=[o,i]));o.push(n[2]=i);var s=a.p+a.u(t),r=new Error;a.l(s,(o=>{if(a.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var i=o&&("load"===o.type?"missing":o.type),s=o&&o.target&&o.target.src;r.message="Loading chunk "+t+" failed.\n("+i+": "+s+")",r.name="ChunkLoadError",r.type=i,r.request=s,n[1](r)}}),"chunk-"+t,t)}},a.O.j=t=>0===e[t];var t=(t,o)=>{var n,i,[s,r,l]=o,c=0;if(s.some((t=>0!==e[t]))){for(n in r)a.o(r,n)&&(a.m[n]=r[n]);if(l)var d=l(a)}for(t&&t(o);c<s.length;c++)i=s[c],a.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return a.O(d)},o=self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var l=a.O(void 0,[344],(()=>a(6995)));l=a.O(l),realCookieBanner_customize_banner=l})();
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.19/da1de98860238c84ee55581b9256d1bd/customize_banner.lite.js.map
