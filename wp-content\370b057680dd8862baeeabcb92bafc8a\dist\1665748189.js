"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[38],{388:(e,t,n)=>{n.d(t,{O:()=>r});var s=n(3713),o=n(12719);const r=e=>{let{children:t,wrapperAttributes:n={},...r}=e;const{modal:a,tag:i}=(0,o.WH)(r);return(0,s.jsxs)(s.Fragment,{children:[a,(0,s.jsx)("span",{...n,children:i})]})}},85360:(e,t,n)=>{n.d(t,{i:()=>r});var s=n(3713),o=n(41594);function r(e,t){const n=e.filter(Boolean);return 0===n.length?null:n.reduce(((e,r,a)=>e.length?[...e,(0,s.jsx)(o.Fragment,{children:"function"==typeof t?t(a,n.length):t},a),r]:[r]),[])}},7533:(e,t,n)=>{async function s(e,t,n){void 0===t&&(t=500),void 0===n&&(n=0);let s=0;for(;!e();){if(n>0&&s>=n)return;await new Promise((e=>setTimeout(e,t))),s++}return e()}n.d(t,{x:()=>s})},37682:(e,t,n)=>{n.d(t,{y:()=>o});var s=n(3713);const o=(0,n(57922).PA)((()=>(0,s.jsx)("div",{})))},51781:(e,t,n)=>{n.r(t),n.d(t,{ConsentTabRouter:()=>Ue});var s=n(3713),o=n(5190),r=n(57922),a=n(27667),i=n(79272),l=n(82031),c=n(12548),d=n(91502),h=n(19117),u=n(6196),p=n(19991),_=n(28101),m=n(73491),g=n(24262),y=n(64715),x=n(41594),f=n(37269),b=n(9551),v=n(59726),j=n(39795),w=n(85360),k=n(14383);const A=(0,r.PA)((e=>{let{record:t,visible:n,onClose:s,replayBannerRecord:o}=e}));var C=n(6099),S=n(92453),N=n(18197),$=n(78915),T=n(39227),P=n(7533),E=n(71685),D=n.n(E);const I="stylesheet-created",B="css-var-update-",O=e=>Object.keys(e).reduce(((t,n)=>{let s=e[n];if(s="function"==typeof s?s():s,"string"==typeof s&&s.indexOf("function () {")>-1)throw new Error(`${n} contains a serialized function ("${s}").`);return t[(e=>{const[t]=e;return t.toUpperCase()===t.toLowerCase()||e.indexOf("-")>-1?e:e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))})(n)]=s,t}),{}),F=(e,t)=>{let{vars:n}=t;const{id:s,specifiedIds:o}=e,{runPlugin:r}=e,a=t=>((e,t)=>(void 0===t&&(t=!1),`${e.className.substr(t?0:1)}-${e.inc++}`))(e,t),i=t=>{r("modifyRule",t);const{classNames:n,pseudos:i,forceSelector:l,...c}=t,d=Array.isArray(l)?l.join(" "):l,h=Array.isArray(n)?n:n?n.split(" "):[],u=d||a(!0);if(e.rules.set(Z(s,o,u),O(c)),i){const t=u.split(",");for(const n in i){const r=n.split(","),a=t.map((e=>r.map((t=>e===t?void 0:t.startsWith("<")?`${t.substr(1)}${e}`:`${e}${t}`)))).flat().filter(Boolean).join(",");e.rules.set(Z(s,o,a),O(i[n]))}}const p=[u.substr(1)];return l||(r("filterClassName",h,p[0],e),p.push(...h)),[u,l?void 0:p.join(" ")]};return{className:a,rule:i,control:(e,t,s)=>{const[o,,r]=n(e,t,!1);return[r,s(o),o,Object.keys(e)]},variant:t=>{const n=a(!0);return[i(t.reduce(((e,t)=>{let[n,s]=t;return e[` ${n(!1)}`]=s,e}),{forceSelector:`${e.className}${n}`}))[0],n.substr(1)]}}},L={},R="àáäâèéëêìíïîòóöôùúüûñç·/_,:;",M=R.replace(/\//g,"\\/"),W=new RegExp(`[${M}]`,"g");function U(e){if(L[e])return L[e];const t=e.trim().toLowerCase().replace(W,(e=>"aaaaeeeeiiiioooouuuunc------".charAt(R.indexOf(e)))).replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-");return L[e]=t,t}const Y=(e,t,n)=>e.replace(new RegExp(`^((?:    |      )${t}: )(.*)?;$`,"m"),`$1${n};`),G=(e,t)=>"string"==typeof e&&e.startsWith("var(--")?e:t?t(e):e,z=e=>"boolean"==typeof e?e?"initial":"":Array.isArray(e)?e.join(" "):e,V=(e,t,n)=>{const s=[],o=(e,t)=>{"boolean"==typeof t&&n(`${e}-not`,z(!t))},r=(e,t,s)=>{if("string"==typeof t&&t.indexOf("function () {")>-1)throw new Error(`${e} contains a serialized function ("${t}").`);n(e,t,s)};if(Array.isArray(t)){r(e,t.map(z).join(" "));for(let n=0;n<t.length;n++){const a=`${e}-${n}`;o(a,t[n]),r(a,z(t[n]),n),s.push(n)}}else if("object"==typeof t)for(const n in t){const a=`${e}-${U(n)}`;o(a,t[n]),r(a,z(t[n]),n),s.push(n)}else o(e,t),r(e,z(t));return s};const H=e=>{const t=new Uint8Array(e/2);return window.crypto.getRandomValues(t),`a${Array.from(t,(e=>`0${e.toString(16)}`.slice(-2))).join("")}`};function q(e,t){void 0===t&&(t=1);const n=" ".repeat(4*t);return[...e.keys()].map((s=>{const o=e.get(s);return`${s} {\n${Object.keys(o).map((e=>{const s=o[e];if("object"==typeof s){const o=new Map;return o.set(e,s),`${n}${q(o,t+1)}\n`}return`${n}${e}:${" ".repeat(1)}${s};\n`})).join("")}${t>1?" ".repeat(4*(t-1)):""}}`})).join("\n")}const J={};function K(e){const{className:t,element:n,extend:s,functions:o,meta:r,toggle:a,specify:i,...l}=e;return l}const X=/,(?![^(]*\))/;function Z(e,t,n){const s=-1===n.indexOf(",")?[n]:n.split(X),o=[];for(const n of s)if(o.push(n),n.startsWith(`.${e}`))for(const e of t)o.push(`#${e} ${n}`);return o.join(",")}const Q=(e,t,n)=>{void 0===t&&(t={});let{element:s,id:o,inc:r,varsVal:a,extended:i,specifiedIds:l,plugins:c,toggle:d,specify:h,detached:u}=void 0===n?{}:n;const{reuse:p}=t;if(p&&!o&&J[p])return J[p];const _=i||{},m=l||[],g=o?`${o}-ext-${Object.getOwnPropertySymbols(_).length}`:H(4),y=document.createElement("style");y.setAttribute("skip-rucss","true");const x={inc:r||1,id:g,varsVal:a||new Map,settings:t,plugins:c||{filterClassName:[t.filterClassName].filter(Boolean),modifyRule:[t.modifyRule].filter(Boolean)},runPlugin:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];for(const t of x.plugins[e])t(...n)},className:`.${g}`,rules:new Map,isExtension:!!o,element:y,mainElement:s||y,specifiedIds:m,extended:_},f=d||(e=>D().mutate((()=>{const{element:t}=x,[n]=document.getElementsByTagName("head"),s=[t,...Object.getOwnPropertySymbols(_).map((e=>_[e].element))];for(const t of s)document.dispatchEvent(new CustomEvent("stylesheet-toggle",{detail:{stylesheet:x,active:e}})),e?n.appendChild(t):n.removeChild(t)}))),b=h||(e=>{m.indexOf(e)>-1||(m.push(e),D().mutate((()=>{const t=new RegExp(`^[ ]*(\\.${g}.*) {`,"gm"),n=(t,n)=>`${Z(g,[e],n)} {`;for(const e of[x.mainElement,...Object.getOwnPropertySymbols(_).map((e=>_[e].element))]){const{textContent:s}=e;e.textContent=s.replace(t,n)}})))}),v=((e,t)=>{const{className:n,isExtension:s,rules:o,id:r,element:a}=e,i=s&&!t?n.split("-ext")[0]:n,l=t?r.split("-ext")[0]:r,c=t=>`--${l}-${e.inc++}${t?`-${t}`:""}`,d=(t,n,s)=>{const r=c(s);e.varsVal.set(r,t),o.set(i,o.get(i)||{});const a=o.get(i),l=G(t,n);return V(r,l,((e,t)=>{a[e]=t})),((e,t,n,s)=>{const{element:o}=e,r=(e,n)=>{void 0===e&&(e=!0);const s=`${t}${["number","string"].indexOf(typeof e)>-1?`-${e}`:""}`;return["boolean","number","string"].indexOf(typeof e)>-1&&!1!==e?`var(${s}${n?`, ${n}`:""})`:t},a=new Map;return V(t,n,((e,t,n)=>{void 0!==n&&(r[n]=e),a.set(e,t)})),r.update=(n,i)=>{let l=i||o.textContent;if(!i&&!o.textContent)return o.addEventListener(I,(()=>r.update(n)),{once:!0}),l;let c=!1;const d=G(n,s);return V(t,d,((e,t)=>{a.get(e)!==t&&(a.set(e,t),l=Y(l,e,t),c=!0)})),c&&(i||(o.textContent=l),e.varsVal.set(t,n),((e,t)=>{let{mainElement:n}=t;n.dispatchEvent(new CustomEvent(`${B}${e}`,{detail:{}}))})(t,e)),l},r})(e,r,l,n)};return{varName:c,variable:d,vars:(e,t,n)=>{void 0===n&&(n=!0);const s={};for(const o in e){const r=e[o],a=null==t?void 0:t[o];s[o]=d(r,a,n?U(o):void 0)}return[s,e=>{let{textContent:t}=a;for(const o in e){var n;t=null==(n=s[o])?void 0:n.update(e[o],t)}return t!==a.textContent&&(a.textContent=t),t},e=>{const n={},o=(e,t)=>{if(e.endsWith("-not"))throw new Error(`Boolish variable "${e}" cannot be created as style-attribute in your HTML tag as this is not supported by browsers. Alternatively, use a classname and pseudos to toggle styles.`);n[e]=""===t?" ":t};for(const n in e){const r=s[n];if(!r)continue;const a=r(!1),i=null==t?void 0:t[n];V(a,G(e[n],i),o)}return n}]}}})(x,u),j=F(x,v),w=((e,t)=>{let{mainElement:n,varsVal:s}=e,{variable:o,vars:r}=t;return(e,t,a,i)=>{let l;const c=e.map((e=>"function"==typeof e?e(!1):void 0)),d=()=>t(c.map((e=>s.get(e)))),h=((e,t)=>{if("raf"===e){let e=!1;return()=>{e||(window.requestAnimationFrame((()=>{t(),e=!1})),e=!0)}}{let n;return()=>{clearTimeout(n),n=setTimeout(t,e)}}})(i||0,(()=>l(d())));for(const e of c)n.addEventListener(`${B}${e}`,h);const u=d(),p="object"!=typeof u||Array.isArray(u)?(()=>{const e=o(u,void 0,a);return l=e.update,e})():(()=>{const e=r(u,void 0);return[,l]=e,e[0]})();return p.update=()=>h(),p}})(x,v),k=((e,t)=>(n,s)=>{const o=new Map,{rule:r}=F({...e,rules:o},t);for(const e in s)r({forceSelector:e,...s[e]});e.rules.set(n,Object.fromEntries(o))})(x,v),A=((e,t)=>{let{variable:n,vars:s}=t;const o=(e,t,r)=>{let a,i,l;if("object"!=typeof e||Array.isArray(e))a=e,i=t,l=r;else{const{when:t,then:n,or:s}=e;a=t,i=n,l=s}if(l=l||" ",Array.isArray(a)){const e={when:void 0,then:void 0,or:void 0};let t=e;const{length:n}=a;for(let e=0;e<n;e++)t.when=a[e],t.or=l,e===n-1?t.then=i:(t.then={when:void 0,then:void 0,or:l},t=t.then);return o(e)}{"string"==typeof a&&a.startsWith("--")&&(a=`var(${a})`);const[e]=s({true:"object"==typeof i?o(i):i,false:`${"function"==typeof a?a():a} ${"object"==typeof l?o(l):l}`});if("inherit"===l)throw new Error('Due to the nature how conditionals work in CSS, it is not allowed to use "inherit" as a falsy value. Please reverse your condition with the help of "boolNot" or use another value.');return n(e.false(!0,e.true()))()}},r=(e,t)=>{const n={when:void 0,then:void 0,or:void 0},{length:s}=e;let r=n;for(let n=0;n<s;n++){const[o,a]=e[n];r.when=o,r.then=a,n===s-1?r.or=t:(r.or={when:void 0,then:void 0,or:void 0},r=r.or)}return o(n)};return{boolIf:o,boolSwitch:r,boolNot:e=>{let t=e;return"string"==typeof t&&t.startsWith("var(")&&(t=t.slice(4,-1)),`var(${"function"==typeof t?t(!1):t}-not)`},boolOr:e=>r(e.map((e=>[e,"initial"]))," ")}})(0,v),C=((e,t)=>{let{settings:{createElement:n,forwardRef:s}}=e,{rule:o}=t;const r=(e,t,r)=>{if(!n)throw new Error("No createElement function passed.");let a,i;if(Array.isArray(t))[a,i]=t;else{const[e,n]=o(t);a=e,i=n}const l="function"==typeof s,c=(t,s)=>{let{children:o,className:a,...c}=t;const d=[i,a].filter(Boolean),{modifyProps:h,...u}=r||{},p={className:d.join(" "),...l?{ref:s}:{},...u,...c};return null==h||h(p),n(e,p,o)},d=l?s(c):c;return d.ruleSelector=a,d.ruleClass=i,[d,a,i]};return{jsx:r,jsxControl:(e,t,n)=>{let[s,o,,a]=t;const{modifyProps:i,...l}=n||{},[c]=r(e,[void 0,o],{...l,modifyProps:e=>{e.style={...s(e),...e.style||{}};const t={};for(const n of a)t[n]=e[n],delete e[n];null==i||i(e,t)}});return c}}})(x,j),S={...j,...v,...A,...C,nestedQuery:k,computed:w,plugin:(e,t)=>{x.plugins[e].push(t)}},N=e({meta:x,...S});D().mutate((()=>{y.textContent=q(x.rules);for(const e of[y,document])e.dispatchEvent(new CustomEvent(I,{detail:{stylesheet:x}}))}));const $=x.inc,T=function(e,n,s,o){void 0===o&&(o=[]);const{extended:r,mainElement:a}=x,i=Object.assign({_chain:o},N,...o.map((e=>K(r[e]))));if(!r[e]){r[e]=Q((e=>n(e,i)),t,{toggle:f,detached:s||!1,...x,inc:s?$:x.inc});const o=Object.keys(i);for(const t of Object.keys(K(r[e])))o.indexOf(t)>-1&&console.warn(`"${t}" detected in multiple stylesheets. This will lead to side effects.`);a.isConnected&&f(!0)}return-1===o.indexOf(e)&&o.push(e),{...i,...r[e],extend:(e,t,n)=>T(e,t,n,o)}},P={...N,meta:x,element:x.element,className:x.id,specify:b,toggle:f,extend:T,functions:S};return p&&!o&&(J[p]=P),P},ee=(e,t,n)=>{void 0===n&&(n=!1);const{id:s,overwrite:o=!0}="string"==typeof t?{id:t}:t||{},r=`pure-css-stylesheet-${s||H(5)}`;let a=document.getElementById(r);if(a){if(!o)return a.remove}else a=document.createElement("style"),a.setAttribute("skip-rucss","true"),a.style.type="text/css",a.id=r,D().mutate((()=>{document.getElementsByTagName(n?"body":"head")[0].appendChild(a)}));return a.innerHTML=e,a.remove};function te(e){const t=e.hasAttribute("data-gridjs-dark-mode"),s=Q((e=>{let{rule:n}=e;const[,s]=n({pseudos:{" ::-webkit-scrollbar":{appearance:"none",width:"7px",height:"7px",background:t?"#2c2c2c":void 0}," ::-webkit-scrollbar-thumb":{borderRadius:"4px",background:t?"#3f3f3f":"rgba(0,0,0,.5)",boxShadow:"0 0 1px rgba(255,255,255,.5)"}}}),[,o]=n({pointerEvents:"none",pseudos:{">.gridjs-sort":{display:"none"}}});return{container:s,disableSortingDueToBug:o}}));s.toggle(!0),Promise.all([n.e(227),n.e(40)]).then(n.t.bind(n,93194,17)).then((e=>{let{default:t}=e;return ee(t,{id:"grid-js",overwrite:!1})})),Promise.all([n.e(227),n.e(40)]).then(n.t.bind(n,62367,17)).then((e=>{let{default:n}=e;t&&ee(n,{id:"grid-js-dark",overwrite:!1})}));const o=JSON.parse(e.previousSibling.innerHTML),{pagination:{navigate:r,page:a}}=o;o.pagination.navigate=(e,t)=>r.replace("%1$d",`${e}`).replace("%2$d",`${t}`),o.pagination.page=e=>a.replace("%d",`${e}`);const i=document.createElement("div");e.parentElement.insertBefore(i,e),new T.xA({className:{container:s.container},from:e,sort:!0,search:!0,pagination:!0,autoWidth:!1,language:o}).render(i),(0,P.x)((()=>i.querySelector(".gridjs-thead>tr>th:nth-child(5)")),100,20).then((e=>e.classList.add(s.disableSortingDueToBug)))}var ne=n(30617);const se=(0,r.PA)((e=>{let{record:t}=e;const[n,o]=(0,x.useState)(!1),{cookiePolicy:r}=t.revision.data,a=(0,x.useRef)();return(0,x.useEffect)((()=>{n&&a.current&&(a.current.querySelectorAll(".devowl-wp-react-cookie-banner-cookie-policy").forEach(te),a.current.querySelectorAll('a[href^="#"]').forEach((e=>{e.style.cursor="not-allowed",e.addEventListener("click",(e=>e.preventDefault()))})))}),[n]),r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{children:(0,ne.__)("Cookie policy as at the time of consent")}),(0,s.jsx)("p",{className:"description",children:(0,ne.__)("Use the button below to see what the content of the cookie policy looked like at the time of the user's consent. Styles, e.g. from your theme, which may have influenced the cookie policy page, are not taken into account.")}),(0,s.jsx)($.A,{title:(0,ne.__)("Cookie policy as at the time of consent"),open:n,onCancel:()=>o(!1),cancelText:(0,ne.__)("Close"),okButtonProps:{style:{display:"none"}},width:"calc(100% - 50px)",destroyOnClose:!0,children:(0,s.jsx)("div",{ref:a,style:{marginTop:35},dangerouslySetInnerHTML:{__html:r}})}),(0,s.jsx)("button",{className:"button button-large",onClick:()=>o(!0),children:(0,ne.__)("Open cookie policy content")})]}):null}));var oe=n(97745);const re=(0,r.PA)((e=>{let{record:t}=e;const{message:n}=h.A.useApp();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{children:(0,ne.__)("Export consent")}),(0,s.jsx)("p",{className:"description",children:(0,ne.__)("Use the button below to export all consents in a machine readable form.")}),(0,s.jsx)("button",{className:"button button-large",onClick:()=>{(0,oe.l)(JSON.stringify(t.export)),n.success((0,ne.__)("Successfully copied to the clipboard!"))},children:(0,ne.__)("Export to clipboard")})]})}));var ae=n(388);const ie=(0,r.PA)((e=>{let{record:t,replayBlockerRecord:n,replayFinished:s}=e}));var le=n(71951);const ce=(0,r.PA)((e=>{let{record:t,onPreview:n}=e;const{optionStore:{others:{isPro:o}}}=(0,le.g)(),[r,a]=(0,x.useState)(!1),{custom_bypass:i,blocker:l,recorder:c,forwarded:d,referer:h,design_version:u}=t;return i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{children:(0,ne.__)("Bypassed banner")}),(0,s.jsx)("p",{className:"description",children:(0,ne._i)((0,ne.__)("There is no preview for this consent, as it was given implicitly by {{strong}}%s bypass{{/strong}}.",t.custom_bypass_readable),{strong:(0,s.jsx)("strong",{})})})]}):l>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{children:(0,ne.__)("Content Blocker as at the time of consent")}),(0,s.jsx)("p",{className:"description",children:(0,ne.__)("The consent to the service was given via a content blocker. Below you can see how the content blocker looked like when the user consented.")}),o?(0,s.jsx)(ie,{record:t,replayBlockerRecord:r,replayFinished:()=>a(!1)}):(0,s.jsx)(ae.O,{title:(0,ne.__)("You want to see what was displayed to the visitor?"),tagText:(0,ne.__)("Unlock preview"),testDrive:!0,feature:"consent-preview-blocker",assetName:(0,ne.__)("pro-modal/consent-preview-blocker.png"),description:(0,ne.__)("We generate the content blocker that your visitor has seen from all settings, design preferences and services. You can see exactly how his or her consent was obtained and track clicks on buttons.")}),o&&!!c&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{className:"description",style:{marginTop:15},children:(0,ne.__)("The process of how the website visitor interacted with the content blocker to give consent was recorded for documentation purposes. You can replay the interactions. Note that the dimensions of the content blocker and consent dialog do not have to be the same as the ones that were displayed to the website visitor, and the recording may not be fully accurate if, for example, you use custom CSS on your website or the visitor used a translating browser extension.")}),(0,s.jsx)("button",{className:"button-primary button-large",onClick:()=>{a(!r)},children:r?(0,ne.__)("Stop"):(0,ne.__)("Replay interactions")})]})]}):d>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{children:(0,ne.__)("Forwarded consent")}),(0,s.jsx)("p",{className:"description",children:(0,ne._i)((0,ne.__)("There is no preview for this consent, as it was given implicitly by forwarding the consent. The user has given his/her consent via {{a}}%s{{/a}}, and this consent has been forwarded automatically.",h),{a:(0,s.jsx)("a",{href:h,rel:"noopener noreferrer",target:"_blank"})})})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{children:(0,ne.__)("Cookie banner as at the time of consent")}),(0,s.jsx)("p",{className:"description",children:(0,ne.__)("Use the button below to see what the cookie banner looked like at the time of the user's consent. The services/groups selected there also look the way the user saw them. A button framed in red shows which button the user clicked on at the time of consent.")}),o?(0,s.jsx)("button",{className:"button-primary button-large",onClick:()=>{n(t,!1)},disabled:t.tcf_string&&u<7,children:(0,ne.__)("Open banner")}):(0,s.jsx)(ae.O,{title:(0,ne.__)("You want to see what was displayed to the visitor?"),tagText:(0,ne.__)("Unlock preview"),testDrive:!0,feature:"consent-preview-banner",assetName:(0,ne.__)("pro-modal/consent-preview-banner.png"),description:(0,ne.__)("We generate the cookie banner from all (design) settings and services that the visitor has seen. You can see exactly how consent was obtained and track clicks on buttons.")}),o&&!!c&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{className:"description",style:{marginTop:15},children:(0,ne.__)("The process of how the website visitor interacted with the cookie banner to give consent was recorded for documentation purposes. You can replay the interactions. Note that the recording may not be fully accurate if, for example, you use custom CSS on your website or the visitor used a translating browser extension.")}),(0,s.jsx)("button",{className:"button-primary button-large",onClick:()=>{n(t,!0)},disabled:u<6||t.tcf_string&&u<7,children:(0,ne.__)("Replay interactions")}),u<6?(0,s.jsx)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:{margin:"10px 0 0 0"},children:(0,s.jsx)("p",{children:(0,ne._i)((0,ne.__)("This consent was documented with a Real Cookie Banner version prior to 3.10.0. Due to rework to comply with the {{aAct}}European Accessibility Act{{/aAct}}, interactions with the currently installed version of Real Cookie Banner are no longer playable. However, they are still documented in the consent export. If you need to play the interactions, please request an old Real Cookie Banner version from the {{aSupport}}support{{/aSupport}}."),{aAct:(0,s.jsx)("a",{href:(0,ne.__)("https://eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX:32019L0882"),rel:"noreferrer",target:"_blank"}),aSupport:(0,s.jsx)("a",{href:(0,ne.__)("https://devowl.io/support/"),rel:"noreferrer",target:"_blank"})})})}):t.tcf_string&&u<7?(0,s.jsx)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:{margin:"10px 0 0 0"},children:(0,s.jsx)("p",{children:(0,ne._i)((0,ne.__)("This consent was documented with a Real Cookie Banner version prior to 4.1.0. Due to rework to comply with the TCF 2.2 standard, interactions with the currently installed version of Real Cookie Banner are no longer playable. However, they are still documented in the consent export. If you need to play the interactions, please request an old Real Cookie Banner version from the {{aSupport}}support{{/aSupport}}."),{aSupport:(0,s.jsx)("a",{href:(0,ne.__)("https://devowl.io/support/"),rel:"noreferrer",target:"_blank"})})})}):null]})]})})),de=(0,r.PA)((e=>{let{record:{forwarded:t,revision:{data:{options:n}},revision_independent:{data:{options:o}}}}=e;const{optionStore:{others:{pageByIdUrl:r,iso3166OneAlpha2:a}}}=(0,le.g)(),i={...o,...n};return t?null:(0,s.jsxs)(C.A,{children:[(0,s.jsx)(S.A,{span:24,children:(0,s.jsx)(p.A,{children:(0,ne.__)("Settings at the time of consent")})}),Object.keys(i).map((e=>{let t=e,n=i[e],o=!0;switch(e){case"cookieVersion":case"consentDuration":return null;case"isTcf":t=(0,ne.__)("Transparency & Consent Framework (TCF)"),n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"cookiePolicyId":t=(0,ne.__)("Cookie Policy Page"),n=n?(0,s.jsxs)("a",{href:`${r}=${n}`,target:"_blank",rel:"noopener noreferrer",style:{marginRight:5},children:[(0,ne.__)("Open site")," (ID ",n,")"]}):(0,ne.__)("Not defined");break;case"isGcm":t=(0,ne.__)("Google Consent Mode"),n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"isGcmCollectAdditionalDataViaUrlParameters":if(!i.isGcm)return null;t=`Google Consent Mode: ${(0,ne.__)("Collect additional data via URL parameters")}`,n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"isGcmRedactAdsDataWithoutConsent":if(!i.isGcm)return null;t=`Google Consent Mode: ${(0,ne.__)("Redact ads data without consent")}`,n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"isGcmListPurposes":if(!i.isGcm)return null;t=`Google Consent Mode: ${(0,ne.__)("Naming of requested consent types in first view")}`,n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"operatorCountry":t=(0,ne.__)("Website operator country"),n=a[n]||n||(0,ne.__)("Not defined");break;case"operatorContactAddress":t=(0,ne.__)("Website operator full address"),n=n||(0,ne.__)("Not defined");break;case"operatorContactEmail":t=(0,ne.__)("Website operator email"),n=n||(0,ne.__)("Not defined");break;case"operatorContactPhone":t=(0,ne.__)("Website operator phone"),n=n||(0,ne.__)("Not defined");break;case"operatorContactFormId":t=(0,ne.__)("Contact form page"),n=n?(0,s.jsxs)("a",{href:`${r}=${n}`,target:"_blank",rel:"noopener noreferrer",style:{marginRight:5},children:[(0,ne.__)("Open site")," (ID ",n,")"]}):(0,ne.__)("Not defined");break;case"territorialLegalBasis":t=(0,ne.__)("Legal basis to be applied"),n=n.map((e=>{switch(e){case"gdpr-eprivacy":return(0,ne.__)("GDPR / ePrivacy Directive");case"dsg-switzerland":return(0,ne.__)("DSG (Switzerland)");default:return e}})).join(", ");break;case"isBannerActive":t=(0,ne.__)("Cookie Banner/Dialog"),n=n?(0,ne.__)("Active"):(0,ne.__)("Deactivated");break;case"isBlockerActive":t=(0,ne.__)("Content Blocker"),n=n?(0,ne.__)("Active"):(0,ne.__)("Deactivated");break;case"setCookiesViaManager":t=(0,ne.__)("Set cookies after consent via"),n="googleTagManager"===n||"googleTagManagerWithGcm"===n?"Google Tag Manager":"matomoTagManager"===n?"Matomo Tag Manager":(0,ne.__)("Disabled");break;case"isAcceptAllForBots":t=(0,ne.__)("Automatically accept all services for bots"),n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"isRespectDoNotTrack":t=(0,ne.__)('Respect "Do Not Track"'),n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"cookieDuration":t=(0,ne.__)("Duration of cookie consent"),n=`${n} ${(0,ne.__)("days")}`;break;case"failedConsentDocumentationHandling":switch(t=(0,ne.__)("Handling of failed consent documentation"),n){case"essentials":n=(0,ne.__)("Allow only essential services");break;case"optimistic":n=(0,ne.__)("Respect user consent")}break;case"isSaveIp":t=(0,ne.__)("Save IP address on consent"),n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"isDataProcessingInUnsafeCountries":t=(0,ne.__)("Consent for data processing in unsafe third countries"),n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"dataProcessingInUnsafeCountriesSafeCountries":if(!i.isDataProcessingInUnsafeCountries)return null;if(t=(0,ne.__)("Safe countries in terms of the GDPR"),n){const e=n.map((e=>a[e]));n=(0,s.jsx)(y.A,{title:e.join(", "),children:(0,s.jsx)(m.A,{children:(0,ne.__)("%d countries",e.length)})})}else n=(0,ne.__)("Not defined");break;case"isAgeNotice":t=(0,ne.__)("Age notice for consent"),n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"ageNoticeAgeLimit":if(!i.isAgeNotice)return null;t=(0,ne.__)("Age limit");break;case"isBannerLessConsent":t=(0,ne.__)("Banner-less consent"),n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"bannerLessConsentShowOnPageIds":if(!i.isBannerLessConsent)return null;t=(0,ne.__)("Show cookie banner on specific pages"),n&&(n=n.map((e=>(0,s.jsxs)("a",{href:`${r}=${e}`,target:"_blank",rel:"noopener noreferrer",style:{marginRight:5},children:[(0,ne.__)("Open site")," (ID ",e,")"]},e))));break;case"isListServicesNotice":t=(0,ne.__)("Naming of all services in first view"),n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"isConsentForwarding":t=(0,ne.__)("Consent Forwarding"),n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"forwardTo":t=(0,ne.__)("Forward To"),n&&(n=n.map((e=>(0,s.jsx)("a",{href:e,target:"_blank",rel:"noopener noreferrer",style:{marginRight:5},children:(0,ne.__)("Open endpoint")},e)))),n=(null==n?void 0:n.length)?n:(0,ne.__)("Not defined");break;case"crossDomains":t=(0,ne.__)("Additional cross domain endpoints"),n&&(n=n.map((e=>(0,s.jsx)("a",{href:e,target:"_blank",rel:"noopener noreferrer",style:{marginRight:5},children:(0,ne.__)("Open endpoint")},e)))),n=(null==n?void 0:n.length)?n:(0,ne.__)("Not defined");break;case"hidePageIds":t=(0,ne.__)("Hide on additional pages"),n&&(n=n.map((e=>(0,s.jsxs)("a",{href:`${r}=${e}`,target:"_blank",rel:"noopener noreferrer",style:{marginRight:5},children:[(0,ne.__)("Open site")," (ID ",e,")"]},e)))),n=(null==n?void 0:n.length)?n:(0,ne.__)("Not defined");break;case"isCountryBypass":t=(0,ne.__)("Geo-restriction"),n=n?(0,ne.__)("Enabled"):(0,ne.__)("Disabled");break;case"countryBypassCountries":if(!i.isCountryBypass)return null;if(t=(0,ne.__)("Show banner only to users from these countries"),n){const e=n.map((e=>a[e]));n=(0,s.jsx)(y.A,{title:e.join(", "),children:(0,s.jsx)(m.A,{children:(0,ne.__)("%d countries",e.length)})})}else n=(0,ne.__)("Not defined");break;case"countryBypassType":if(!i.isCountryBypass)return null;t=(0,ne.__)("Implicit consent for users from third countries"),n="all"===n?(0,ne.__)("Accept all"):(0,ne.__)("Accept only essentials");break;default:o=!1}return(0,s.jsx)(S.A,{md:12,sm:24,children:(0,s.jsxs)("div",{style:{padding:"0 10px"},children:[o?(0,s.jsx)("strong",{children:t}):(0,s.jsx)("code",{children:t}),": ",n]})},e)}))]})})),he=(0,r.PA)((e=>{let{record:t,onPreview:n}=e;const[o,r]=(0,x.useState)(!1),{referer:a,context:i,viewport_width:l,viewport_height:c,forwarded:d}=t;return(0,x.useEffect)((()=>{t.fetchRevisions().then((()=>{r(!0)}))}),[t]),o?(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("strong",{children:[(0,ne.__)("Viewport (px)"),":"]})," ",l," x ",c]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("strong",{children:[(0,ne.__)("Viewed page"),":"]})," ",(0,s.jsx)("a",{href:a,rel:"noopener noreferrer",target:"_blank",children:a})]}),!!i&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("strong",{children:[(0,ne.__)("Context"),":"]})," ",(0,s.jsx)("code",{children:i})]}),(0,s.jsx)(de,{record:t}),(0,s.jsxs)(C.A,{children:[(0,s.jsx)(S.A,{md:d?void 0:12,sm:d?void 0:24,span:d?24:void 0,children:(0,s.jsx)("div",{style:{padding:10},children:(0,s.jsx)(ce,{record:t,onPreview:n})})}),!d&&(0,s.jsxs)(S.A,{md:12,sm:24,children:[(0,s.jsx)("div",{style:{padding:10},children:(0,s.jsx)(re,{record:t})}),(0,s.jsx)("div",{style:{padding:10},children:(0,s.jsx)(se,{record:t})})]})]})]}):(0,s.jsx)(N.A,{spinning:!0})}));var ue=n(38123),pe=n.n(ue);function _e(){return(0,x.useMemo)((()=>({[(0,ne.__)("Today")]:[pe()(),pe()()],[(0,ne.__)("This Year")]:[pe()().startOf("year"),pe()().endOf("year")],[(0,ne.__)("This Month")]:[pe()().startOf("month"),pe()().endOf("month")],[(0,ne.__)("This Week")]:[pe()().startOf("week"),pe()().endOf("week")],[(0,ne.__)("Last 30 days")]:[pe()().subtract(30,"days"),pe()()],[(0,ne.__)("Last 90 days")]:[pe()().subtract(30,"days"),pe()()]})),[])}var me=n(53603),ge=n(33464),ye=n(52624),xe=n(45377);const fe=(0,r.PA)((e=>{let{value:t,onChange:n}=e;const[o,r]=(0,x.useState)(t),[a,i]=(0,x.useState)(!1),{consentStore:l}=(0,le.g)(),{busyReferer:c,referer:d}=l;(0,x.useEffect)((()=>{a&&l.fetchReferer()}),[a]);const h=(0,x.useMemo)((()=>{const e=d.reduce(((e,t)=>{try{const{hostname:n,origin:s}=new URL(t);e[n]||(e[n]=[]),e[n].push({label:t.substring(s.length),value:t})}catch(e){}return e}),{});return Object.entries(e).map((e=>{let[t,n]=e;return{label:t,options:n.sort(((e,t)=>e.label.localeCompare(t.label)))}}))}),[d]);return(0,s.jsx)(u.A,{showSearch:!0,onFocus:()=>i(!0),value:o,notFoundContent:c?(0,s.jsx)(N.A,{size:"small"}):null,loading:c,allowClear:!0,onClear:()=>{r(void 0),null==n||n(void 0)},dropdownAlign:{points:["tr","br"]},dropdownStyle:{width:500},style:{width:200},placeholder:(0,ne.__)("Filter by URL..."),onChange:e=>{const t=e||void 0;r(t),null==n||n(t)},options:h})})),{Column:be}=d.A,ve=(0,r.PA)((()=>{const{message:e}=h.A.useApp(),{consentStore:t,optionStore:{contexts:n}}=(0,le.g)(),{busyConsent:o,pageCollection:r,perPage:a,count:C,truncatedIpsCount:S,filters:{page:N,referer:$,context:T,dates:P,ip:E}}=t,[D,I]=(0,x.useState)(),[B,O]=(0,x.useState)(!1),[F,L]=(0,x.useState)(!1),[R,M]=(0,x.useState)(!0),W=Object.keys(n),{percent:U,currentJob:Y,remaining:G,errors:z}=(0,k.useProgress)({type:ge.G7,fetchAdditionalData:ge.G7,onAdditionalData:e=>{let{[ge.G7]:t}=e;t.migrationNeeded!==F&&L(t.migrationNeeded)}}),V=_e(),H=(0,x.useCallback)((async()=>{try{await t.fetchAll()}catch(t){e.error(t.responseJSON.message)}}),[]);(0,x.useEffect)((()=>{H(),(0,k.fetchStatus)(!0)}),[]);const q=(0,me.m)("list-of-consents"),J=(0,me.m)("consents-deleted"),K=function(){const{__:e}=(0,v.s)();return(0,x.useCallback)((()=>({filterDropdown:t=>{let{setSelectedKeys:n,selectedKeys:o,confirm:r,clearFilters:a}=t;return(0,s.jsxs)("div",{style:{padding:8},children:[(0,s.jsx)(b.A,{autoFocus:!0,value:o[0],onChange:e=>n(e.target.value?[e.target.value]:[]),style:{width:188,marginBottom:8,display:"block"}}),(0,s.jsxs)("button",{className:"button-primary right",style:{marginLeft:10},onClick:()=>r(),children:[(0,s.jsx)(f.A,{})," ",e("Search")]}),(0,s.jsx)("button",{className:"button right",onClick:a,children:e("Reset")}),(0,s.jsx)("div",{className:"clear"})]})},filterIcon:e=>(0,s.jsx)(f.A,{style:{color:e?"#1890ff":void 0}})})),[])}();return(0,s.jsxs)(s.Fragment,{children:[D&&(0,s.jsx)(A,{record:D,visible:R,replayBannerRecord:B,onClose:()=>{M(!1),O(!1),I(void 0)}}),(0,s.jsxs)("div",{style:{textAlign:"right",marginBottom:15},children:[W.length>1&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("label",{children:(0,s.jsx)(u.A,{style:{width:200,textAlign:"left"},value:T,onSelect:e=>{t.applyPage(1),t.applyContext(e),H()},children:W.map((e=>(0,s.jsx)(u.A.Option,{value:e,children:n[e]},e)))})}),(0,s.jsx)(p.A,{type:"vertical"})]}),(0,s.jsxs)("label",{children:[(0,ne.__)("Period"),":"," ",(0,s.jsx)(ye.U,{value:P,ranges:V,onChange:e=>{t.applyPage(1),t.applyDates(e||[void 0,void 0]),H()}})]}),(0,s.jsx)(p.A,{type:"vertical"}),(0,s.jsx)("label",{style:{textAlign:"left"},children:(0,s.jsx)(fe,{value:$,onChange:e=>{t.applyPage(1),t.applyReferer(e),H()}})})]}),(0,s.jsx)(j.q,{notices:[S>0&&!!E&&{message:(0,ne.__)("For data protection reasons, IP addresses, depending on the configuration of the cookie banner, were only shortened by the last octet and stored hashed. In this case, you can only assign consents to individual IP addresses with a high probability, but not with absolute certainty. Therefore, also match the time of consent to narrow down your search for the proper consent!"),severity:"warning"},G>0&&U>0&&Y?{message:(0,ne.__)("Real Cookie Banner v5.0 introduces an optimized database schema that allows consent documents to be stored in less storage space. Previously stored consent data is currently being automatically migrated in the background (%s %%). Please have a little patience!",(0,xe.A)(Y.data)),severity:"info"}:F&&!z?{message:(0,ne._i)((0,ne.__)("Real Cookie Banner v5.0 introduces an optimized database schema that allows consent documents to be stored in less storage space. There are still consents left to migrate. {{a}}Click here to start the migration.{{/a}}"),{a:(0,s.jsx)("a",{onClick:()=>{L(!1),t.fetchAll({migration:ge.G7}).then((()=>(0,k.fetchStatus)(!0)))},children:(0,ne.__)("Start migration")})}),severity:"info"}:void 0]}),(0,s.jsxs)(d.A,{pagination:{current:N,pageSize:a,total:C,showTotal:(e,t)=>`${t[0]}-${t[1]} / ${e}`,showSizeChanger:!1},style:{marginTop:5},locale:{emptyText:(0,s.jsx)(_.A,{description:(0,ne.__)("No data")})},loading:o,dataSource:Array.from(r.values()),rowKey:"id",size:"small",bordered:!0,expandable:{expandedRowRender:e=>(0,s.jsx)("div",{style:{background:"white",padding:10},children:(0,s.jsx)(he,{record:e,onPreview:(e,t)=>{I(e),O(t),M(!0)}})}),rowExpandable:()=>!0,expandIcon:e=>{let{expanded:t,onExpand:n,record:o}=e;return(0,s.jsx)(m.A,{style:{cursor:"pointer"},onClick:e=>n(o,e),icon:t?(0,s.jsx)(i.A,{}):(0,s.jsx)(l.A,{}),children:t?(0,ne.__)("Less"):(0,ne.__)("More")})}},onChange:(e,n)=>{let{current:s}=e;var o,r;const a=(null==(o=n.uuid)?void 0:o[0])||"",i=(null==(r=n.ip)?void 0:r[0])||"";t.applyPage(s),t.applyUuid(a),t.applyIp(i),H()},footer:()=>(0,s.jsx)(g.A,{overlayStyle:{maxWidth:300},arrow:{pointAtCenter:!0},title:(0,ne._i)((0,ne.__)("Are you sure you want to delete all consents? You should only do this if it is absolutely necessary. In case you want to continue, make sure you have {{a}}exported{{/a}} all consents beforehand (for legal reasons)."),{a:(0,s.jsx)("a",{href:"#/import"})}),okText:(0,ne.__)("I am sure!"),cancelText:(0,ne.__)("Cancel"),placement:"topRight",onConfirm:()=>window.confirm((0,ne.__)("Just to be sure: Do you really want to delete all consents?"))&&t.deleteAll(),children:(0,s.jsx)("button",{className:"button-link",children:(0,ne.__)("Delete all consents")})}),children:[(0,s.jsx)(be,{title:(0,ne.__)("Time of consent documentation"),dataIndex:"created",render:(e,t)=>{const{created:n}=t;return(0,s.jsxs)(s.Fragment,{children:[new Date(n).toLocaleString(document.documentElement.lang)," ",t.dnt&&(0,s.jsx)(y.A,{title:(0,ne.__)("This consent was given automatically because the browser sent a 'Do Not Track' header. Accordingly, only essential services have been consented to."),children:(0,s.jsx)(m.A,{color:"magenta",children:(0,ne.__)("Do Not Track")})}),t.blocker>0&&(0,s.jsx)(y.A,{title:(0,ne.__)("This consent has been given in a content blocker."),children:(0,s.jsx)(m.A,{color:"cyan",children:(0,ne.__)("Content Blocker")})}),t.forwarded>0&&(0,s.jsx)(y.A,{title:(0,ne.__)("This consent was implicitly given by Consent Forwarding."),children:(0,s.jsx)(m.A,{color:"green",children:t.forwarded_blocker?(0,ne.__)("Forwarded through Content Blocker"):(0,ne.__)("Forwarded")})}),t.custom_bypass&&(0,s.jsx)(y.A,{title:(0,ne.__)("This consent was implicitly given by a configured bypass."),children:(0,s.jsx)(m.A,{color:"magenta",children:t.custom_bypass_readable})})]})}},"created"),(0,s.jsx)(be,{title:(0,s.jsx)(y.A,{title:(0,ne.__)("Unique name of the consent given"),children:(0,s.jsxs)("span",{children:[(0,ne.__)("UUID")," ",(0,s.jsx)(c.A,{style:{color:"#9a9a9a"}})]})}),dataIndex:"uuid",render:(e,t)=>(0,s.jsx)("code",{children:t.uuid}),...K()},"uuid"),(0,s.jsx)(be,{title:(0,s.jsx)(y.A,{title:(0,ne.__)("If you have allowed to log IP addresses, you can view them here. Otherwise you will see a salted hash of the IP address (truncated)."),children:(0,s.jsxs)("span",{children:[(0,ne.__)("IP")," ",(0,s.jsx)(c.A,{style:{color:"#9a9a9a"}})]})}),dataIndex:"ip",render:(e,t)=>t.ipv4?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("code",{children:t.ipv4}),!!E&&t.ipv4===E&&(0,s.jsx)(m.A,{style:{marginLeft:5},color:"blue",children:(0,ne.__)("Exact match")})]}):t.ipv6?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("code",{children:t.ipv6}),!!E&&t.ipv6===E&&(0,s.jsx)(m.A,{style:{marginLeft:5},color:"blue",children:(0,ne.__)("Exact match")})]}):t.ipv4_hash?(0,s.jsx)(y.A,{title:t.ipv4_hash,children:(0,s.jsx)("code",{children:t.ipv4_hash.slice(0,8)})}):(0,s.jsx)(y.A,{title:t.ipv6_hash,children:(0,s.jsx)("code",{children:t.ipv6_hash.slice(0,8)})}),...K()},"ip"),(0,s.jsx)(be,{title:(0,ne.__)("Accepted services"),dataIndex:"decision",render:(e,t)=>t.decision_labels.map((e=>(0,s.jsx)(m.A,{children:e},e)))},"decision"),(0,s.jsx)(be,{title:(0,ne.__)("Actions"),dataIndex:"actions",render:(e,t)=>(0,w.i)([(0,s.jsx)(g.A,{title:(0,ne.__)("Are you sure you want to delete this entry?"),placement:"bottomLeft",onConfirm:()=>t.delete(),okText:(0,ne.__)("Delete"),cancelText:(0,ne.__)("Cancel"),overlayStyle:{maxWidth:350},children:(0,s.jsx)("a",{children:(0,ne.__)("Delete")})},"delete")],(0,s.jsx)(p.A,{type:"vertical"}))},"actions")]}),(0,s.jsx)("p",{className:"description",style:{maxWidth:800,margin:"30px auto 0",textAlign:"center"},children:q}),(0,s.jsx)("p",{className:"description",style:{maxWidth:800,margin:"30px auto 0",textAlign:"center"},children:J})]})}));var je=n(27465),we=n(43799),ke=n(19393),Ae=n(91386),Ce=n(81533);const Se={labelCol:{span:24},wrapperCol:{span:24}},Ne=e=>{let{type:t}=e;const{message:n}=h.A.useApp(),[o,r]=(0,x.useState)(""),a={tag:"a",id:"",text:"history"===t?(0,ne._x)("Privacy settings history","legal-text"):"revoke"===t?(0,ne._x)("Revoke consents","legal-text"):(0,ne._x)("Change privacy settings","legal-text"),successmessage:"revoke"===t?(0,ne._x)("You have successfully revoked consent for services with its cookies and personal data processing. The page will be reloaded now!","legal-text"):""},[i]=Ae.A.useForm(),l=(0,x.useCallback)(((e,n)=>{const s=[];for(const e of Object.keys(n)){const t=n[e];t&&s.push(`${e}="${t.replace('"','\\"')}"`)}r(`[rcb-consent type="${t}" ${s.join(" ")}]`)}),[r]);(0,x.useEffect)((()=>{l(a,a)}),[]);const c=(0,x.useCallback)((()=>{(0,oe.l)(o),n.success((0,ne.__)("Successfully copied shortcode to clipboard!"))}),[o]);return(0,s.jsxs)(Ae.A,{name:`link-shortcode-${t}`,form:i,...Se,initialValues:a,onValuesChange:l,children:[(0,s.jsxs)(Ae.A.Item,{label:(0,ne.__)("Appearance"),children:[(0,s.jsx)(Ae.A.Item,{name:"tag",noStyle:!0,children:(0,s.jsxs)(Ce.Ay.Group,{children:[(0,s.jsx)(Ce.Ay.Button,{value:"a",children:(0,ne.__)("Link")}),(0,s.jsx)(Ce.Ay.Button,{value:"button",children:(0,ne.__)("Button")})]})}),(0,s.jsx)("p",{className:"description",children:(0,ne.__)("It is recommended to use a simple link in your footer instead of a button to avoid visual misunderstandings.")})]}),(0,s.jsxs)(Ae.A.Item,{label:(0,ne.__)("HTML ID"),children:[(0,s.jsx)(Ae.A.Item,{name:"id",noStyle:!0,children:(0,s.jsx)(b.A,{})}),(0,s.jsx)("p",{className:"description",children:(0,ne.__)("If you want to apply a custom style to the link/button, the shortcode output should be tagged with an ID attribute.")})]}),(0,s.jsxs)(Ae.A.Item,{label:(0,ne.__)("Text"),children:[(0,s.jsx)(Ae.A.Item,{name:"text",noStyle:!0,children:(0,s.jsx)(b.A,{})}),(0,s.jsx)("p",{className:"description",children:(0,ne.__)("The user must be able to clearly understand the behaviour of the link/button from the name.")})]}),"revoke"===t&&(0,s.jsxs)(Ae.A.Item,{label:(0,ne.__)("Success message"),children:[(0,s.jsx)(Ae.A.Item,{name:"successmessage",noStyle:!0,children:(0,s.jsx)(b.A.TextArea,{autoSize:!0})}),(0,s.jsx)("p",{className:"description",children:(0,ne.__)("After the consent is revoked, the page will be reloaded. Let the user know what happened with a success message.")})]}),(0,s.jsxs)(Ae.A.Item,{children:[(0,s.jsxs)(Ae.A.Item,{noStyle:!0,children:[(0,s.jsx)(p.A,{style:{marginTop:0},children:(0,ne.__)("Result")}),(0,s.jsx)(b.A.TextArea,{value:o,readOnly:!0,autoSize:!0})]}),(0,s.jsx)("p",{className:"description",children:(0,ne.__)("Copy the generated shortcode and paste it into your website.")}),(0,s.jsx)("button",{className:"button alignright",onClick:c,children:(0,ne.__)("Copy to clipboard")})]})]})};var $e=n(41415),Te=n(34650);const Pe=(0,r.PA)((()=>{const{message:e}=h.A.useApp(),{optionStore:t}=(0,le.g)(),{navMenus:n,busyCountryBypassUpdate:o,others:{adminUrl:r}}=t,a=(0,x.useCallback)((async n=>{try{await t.addLinksToNavigationMenu(n),e.success((0,ne.__)("Successfully added the links to your menu!"))}catch(t){e.error(t.responseJSON.message)}}),[]);return(0,s.jsx)(Te.A,{loading:o,itemLayout:"horizontal",dataSource:n,size:"small",locale:{emptyText:(0,s.jsx)(_.A,{description:(0,ne.__)("No WordPress menu created yet."),children:(0,s.jsx)("a",{href:`${r}nav-menus.php`,className:"button button-primary",children:(0,ne.__)("Create menu")})})},renderItem:e=>{const{id:t,applied:n,edit_url:o,languages:r,name:i}=e;return(0,s.jsx)(Te.A.Item,{actions:[n?(0,s.jsxs)("a",{children:[(0,s.jsx)($e.A,{style:{color:"#52c41a"}})," ",(0,ne.__)("Already added")]},"apply"):(0,s.jsx)("a",{onClick:()=>a(t),children:(0,ne.__)("Add all legal links")},"applied"),(0,s.jsx)("a",{target:"_blank",href:o,rel:"noreferrer",children:(0,ne.__)("Edit manually")},"edit-manually")],children:(0,s.jsx)(Te.A.Item.Meta,{title:(0,s.jsxs)("span",{children:[i," ",r.length>0&&(0,s.jsx)(m.A,{children:r[0].language})]}),description:"legacy_nav"===e.type&&Object.values(e.locations).join(", ")})})}})}));n(59627);var Ee=n(12719);const{Panel:De}=je.A,Ie=(0,r.PA)((()=>{const e=(0,me.m)("shortcodes"),{optionStore:t}=(0,le.g)(),{busySettings:n,isBannerStickyLinksEnabled:o,others:{customizeBannerUrl:r}}=t,{isPro:a,modal:i,tag:l}=(0,Ee.WH)({title:(0,ne.__)("Want to show legal links with a sticky icon?"),testDrive:!0,feature:"sticky-links",assetName:(0,ne.__)("pro-modal/sticky-legal-links.webm"),description:(0,ne.__)("Display a sticky icon on your website that makes the legal links easily accessible on every subpage. Not only does it look fancy, it also boosts your data protection!")}),{message:c}=h.A.useApp(),d=(0,x.useCallback)((e=>{t.updateSettings({isBannerStickyLinksEnabled:e}).then((()=>{c.success(e?(0,ne.__)("Sticky legal links are now enabled on your website"):(0,ne.__)("Sticky legal links are now disabled on your website"))}))}),[]);return(0,s.jsxs)(s.Fragment,{children:[i,(0,s.jsxs)(je.A,{defaultActiveKey:["nav","sticky"],ghost:!0,children:[(0,s.jsx)(De,{header:(0,s.jsx)("a",{children:(0,ne.__)("Add links to existing menu")}),children:(0,s.jsx)(we.A,{style:{margin:5},children:(0,s.jsx)(Pe,{})})},"nav"),(0,s.jsx)(De,{header:(0,s.jsx)("a",{children:(0,ne.__)("Sticky legal links widget")}),children:(0,s.jsx)(we.A,{style:{margin:5},children:(0,s.jsxs)(C.A,{align:"middle",children:[(0,s.jsxs)(S.A,{flex:"auto",children:[(0,s.jsx)(ke.A,{loading:n,onChange:d,value:!!a&&o,disabled:!a}),"  ",(0,ne.__)("Show a sticky legal links widget on the bottom of your website")," ",l,!a&&(0,s.jsx)("p",{className:"description",style:{marginTop:10},children:(0,ne.__)("You can add the legal links in the free version of Real Cookie Banner as menu items or shortcodes (e.g. in your website footer).")})]}),a&&(0,s.jsx)(S.A,{children:(0,s.jsx)("a",{href:`${r.replace(/autofocus\[panel]=[\w-]+/,"autofocus[section]=real-cookie-banner-banner-sticky-links")}&return=${encodeURIComponent(window.location.href)}`,className:"button button-primary",children:(0,ne.__)("Customize")})})]})})},"sticky"),(0,s.jsx)(De,{header:(0,s.jsx)("a",{children:(0,ne.__)("Generate shortcode (advanced)")}),children:(0,s.jsxs)(C.A,{children:[(0,s.jsx)(S.A,{xl:8,sm:12,xs:24,children:(0,s.jsx)(we.A,{style:{margin:5},title:(0,ne._x)("Change privacy settings","legal-text"),children:(0,s.jsx)(Ne,{type:"change"})})}),(0,s.jsx)(S.A,{xl:8,sm:12,xs:24,children:(0,s.jsx)(we.A,{style:{margin:5},title:(0,ne._x)("Privacy settings history","legal-text"),children:(0,s.jsx)(Ne,{type:"history"})})}),(0,s.jsx)(S.A,{xl:8,sm:12,xs:24,children:(0,s.jsx)(we.A,{style:{margin:5},title:(0,ne._x)("Revoke consents","legal-text"),children:(0,s.jsx)(Ne,{type:"revoke"})})})]})},"shortcode")]}),(0,s.jsx)("span",{className:"description",style:{display:"block",maxWidth:800,margin:"30px auto 0",textAlign:"center"},children:e})]})}));var Be=n(23291);const Oe=(0,r.PA)((()=>(0,s.jsx)("div",{}))),Fe=(0,r.PA)((()=>(0,s.jsx)("div",{})));var Le=n(37682);const Re=(0,r.PA)((()=>{const{optionStore:{others:{isPro:e,assetsUrl:t},contexts:n},statsStore:o}=(0,le.g)(),{filters:{dates:r,context:a}}=o,i=Object.keys(n),[l,c]=(0,x.useState)(),d=_e();return e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{style:{textAlign:"right"},children:[i.length>1&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("label",{children:(0,s.jsx)(u.A,{style:{width:200,textAlign:"left"},value:a,onSelect:e=>o.applyContext(e),children:i.map((e=>(0,s.jsx)(u.A.Option,{value:e,children:n[e]},e)))})}),(0,s.jsx)(p.A,{type:"vertical"})]}),(0,s.jsxs)("label",{children:[(0,ne.__)("Period"),":"," ",(0,s.jsx)(ye.U,{value:r,ranges:d,onChange:e=>o.applyDates(e)})]})]}),2===(null==r?void 0:r.length)?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(C.A,{children:[(0,s.jsxs)(S.A,{md:12,sm:24,children:[(0,s.jsx)(p.A,{children:(0,ne.__)("Consents by clicked button")}),(0,s.jsx)(Oe,{})]}),(0,s.jsxs)(S.A,{md:12,sm:24,children:[(0,s.jsx)(p.A,{children:(0,ne.__)("Cookie banner bypass")}),(0,s.jsx)(Fe,{})]})]}),(0,s.jsx)(C.A,{children:(0,s.jsxs)(S.A,{md:20,sm:24,style:{margin:"auto",paddingTop:20,marginTop:30},children:[(0,s.jsx)(p.A,{children:(0,ne.__)("Consents by group")}),(0,s.jsx)(Le.y,{})]})})]}):(0,s.jsx)(_.A,{description:(0,ne.__)("Please provide a date range!")})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(Be.X,{title:(0,ne.__)("Want to see detailed statistics about the consents of your visitors?"),inContainer:!0,inContainerElement:l,testDrive:!0,feature:"stats",description:(0,ne.__)("You can get several statistics about how your users use the cookie banner. This helps you to calculate the total number of users who do not want to be tracked, for example, by extrapolating data from Google Analytics.")}),(0,s.jsx)("div",{ref:c,className:"rcb-antd-modal-mount",style:{height:800,backgroundImage:`url('${t}statistics-blured.png')`}})]})}));var Me=n(40164),We=n(89657);const Ue=(0,r.PA)((()=>{const e=(0,a.g)().tab||"",t=(0,a.Zp)();return(0,s.jsx)(o.A,{defaultActiveKey:e,onChange:e=>{t(`/consent/${e}`)},items:[{key:"",label:(0,ne.__)("Statistics"),children:(0,s.jsx)(Me.e,{maxWidth:"fixed",style:{paddingTop:0},children:(0,s.jsx)(Re,{})})},{key:"list",label:(0,ne.__)("List of consents"),children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(ve,{}),(0,s.jsx)(We.b,{identifier:"list-of-consents"})]})},{key:"legal",label:(0,ne.__)("Legal links"),children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(Ie,{}),(0,s.jsx)(We.b,{identifier:"shortcodes"})]})}]})}))},40164:(e,t,n)=>{n.d(t,{e:()=>o});var s=n(3713);const o=e=>{let{children:t,maxWidth:n="auto",style:o={}}=e;return(0,s.jsx)("div",{className:"rcb-config-content",style:{maxWidth:"fixed"===n?1300:n,...o},children:t})}},52624:(e,t,n)=>{n.d(t,{U:()=>h});var s=n(3713),o=n(38123),r=n.n(o),a=n(32386),i=n(16983);const l=a.A.generatePicker(i.A);var c=n(30617);const{RangePicker:d}=l,h=e=>{const t=r().localeData();return(0,s.jsx)(d,{locale:{lang:{locale:r().locale(),placeholder:(0,c.__)("Select date"),rangePlaceholder:[(0,c.__)("Start date"),(0,c.__)("End date")],today:(0,c.__)("Today"),now:(0,c.__)("Now"),backToToday:(0,c.__)("Back to today"),ok:(0,c.__)("OK"),clear:(0,c.__)("Clear"),month:(0,c.__)("Month"),year:(0,c.__)("Year"),timeSelect:(0,c.__)("Select time"),dateSelect:(0,c.__)("Select date"),monthSelect:(0,c.__)("Choose a month"),yearSelect:(0,c.__)("Choose a year"),decadeSelect:(0,c.__)("Choose a decade"),yearFormat:"YYYY",dateFormat:t.longDateFormat("LL"),dayFormat:"D",dateTimeFormat:t.longDateFormat("LLL"),monthFormat:"MMMM",monthBeforeYear:!0,previousMonth:(0,c.__)("Previous month (PageUp)"),nextMonth:(0,c.__)("Next month (PageDown)"),previousYear:(0,c.__)("Last year (Control + left)"),nextYear:(0,c.__)("Next year (Control + right)"),previousDecade:(0,c.__)("Last decade"),nextDecade:(0,c.__)("Next decade"),previousCentury:(0,c.__)("Last century"),nextCentury:(0,c.__)("Next century")},timePickerLocale:{placeholder:(0,c.__)("Select time")},dateFormat:t.longDateFormat("LL"),dateTimeFormat:t.longDateFormat("LLL"),weekFormat:"YYYY-wo",monthFormat:"YYYY-MM"},...e})}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.19/98ebdd47f85fbcaf0951f085a5fb0e34/chunk-config-tab-consent.lite.js.map
