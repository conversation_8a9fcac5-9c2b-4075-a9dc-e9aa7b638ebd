"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[362],{77421:(e,t,n)=>{var r,s,a,o,i,l;n.d(t,{FX:()=>a}),function(e){e.Append="append",e.Overwrite="overwrite",e.Clear="clear",e.Keep="keep"}(r||(r={})),function(e){e.Draft="draft",e.Published="published"}(s||(s={})),function(e){e.Free="free",e.Pro="pro"}(a||(a={})),function(e){e.IsWordPressPluginOrThemeActive="is-wordpress-plugin-or-theme-active"}(o||(o={})),function(e){e.Initial="initial",e.Update="update",e.Notify="notify",e.Removed="removed",e.NeverReleased="never-released"}(i||(i={})),function(e){e.CreatedAt="createdAt",e.EnabledWhenOneOf="enabledWhenOneOf",e.Extends="extends",e.ExtendedMergeStrategies="extendedMergeStrategies",e.ExtendedTemplateId="extendedTemplateId",e.ExtendsId="extendsId",e.ExtendsIdentifier="extendsIdentifier",e.Headline="headline",e.Id="id",e.Identifier="identifier",e.IsDeleted="isDeleted",e.IsExtendingMetaData="isExtendingMetaData",e.IsFavourite="isFavourite",e.MachineTranslationStatus="machineTranslationStatus",e.IsHidden="isHidden",e.LastEditedBy="lastEditedBy",e.Logo="logo",e.LogoId="logoId",e.MinRequiredRcbVersion="minRequiredRcbVersion",e.Name="name",e.NameTranslationFlags="nameTranslationFlags",e.Next="next",e.NextId="nextId",e.Pre="pre",e.PreId="preId",e.RecommendedWhenOneOf="recommendedWhenOneOf",e.ReleaseStatus="releaseStatus",e.SubHeadline="subHeadline",e.Status="status",e.SuccessorOfIdentifier="successorOfIdentifier",e.SuccessorOfIdentifierInfo="successorOfIdentifierInfo",e.Tier="tier",e.TranslationInfo="translationInfo",e.Version="version"}(l||(l={}))},58025:(e,t,n)=>{n.d(t,{X:()=>y});var r=n(3713),s=n(18197),a=n(41594),o=n(43799),i=n(19117),l=n(19991),c=n(27465),d=n(64715),u=n(33146),m=n(73491),h=n(77421),p=n(59726),f=n(24325),g=n(12719);const{Meta:x}=o.A;function b(e){let{template:t,renderActions:n,renderTags:s,onSelect:a,grayOutAlreadyExisting:b,onAcknowledgementClick:y}=e;const{__:w,_i:k}=(0,p.s)(),{modal:j}=i.A.useApp(),{isPro:v,isDemoEnv:_}=(0,f.J)(),{headline:A,subHeadline:C,logoUrl:S,tier:I,consumerData:{tags:T,isDisabled:$,isCreated:E,isIgnored:R,acknowledgement:U}}=t,{technicalHandlingIntegration:P}=t.consumerData,N=w("Disabled"),D=I===h.FX.Pro&&(!v||_),{open:O,modal:L}=(0,g.WH)({title:w("Want to use %s template?",A),feature:"preset",description:`${w("Only a limited number of templates for services and content blockers are available in the %s version of Real Cookie Banner. Get the PRO version now and create a service or content blocker from this template with just one click!",w(_?"Demo":"Free").toLowerCase())}${_?"":`\n\n${w("You can create this service yourself in the free version without any restrictions and research the necessary information.")}`}`},!_&&void 0),B=e=>{if(e.target.closest(".rcb-antd-card,.rcb-template-card-create-link"))if(U){const{paragraphs:e,accordion:n,buttonAction:s,buttonLabel:a}=U,o=j.info({icon:null,width:800,closable:!0,okButtonProps:{style:{display:"none"}},content:(0,r.jsxs)("div",{style:{textAlign:"center"},children:[(0,r.jsx)("img",{src:t.logoUrl,style:{display:"block",paddingTop:15,margin:"auto",height:176}}),(0,r.jsx)("h3",{style:{margin:"10px 0 0"},children:t.headline}),(0,r.jsx)("p",{style:{marginTop:0},children:t.subHeadline}),(0,r.jsx)(l.A,{children:w("Special circumstances for this service")}),e.map((e=>(0,r.jsx)("p",{style:{textAlign:"left"},children:k(e,{strong:(0,r.jsx)("strong",{}),u:(0,r.jsx)("u",{style:{textDecorationStyle:"dashed"}})})},e))),n&&(0,r.jsx)(c.A,{style:{textAlign:"left"},defaultActiveKey:Object.keys(n)[0],items:Object.keys(n).map((e=>({key:e,label:e,children:(0,r.jsx)("p",{style:{margin:0},dangerouslySetInnerHTML:{__html:n[e]}})})))}),y&&(0,r.jsx)("div",{style:{marginTop:10},children:(0,r.jsx)("button",{className:"button button-large button-primary",onClick:()=>{o.destroy(),null==y||y(s,t)},children:a})})]})})}else D?O():$||null==a||a(t)},F=(0,r.jsx)("img",{style:{width:"90%"},src:S}),H=s?s({...T},t):T;return(0,r.jsxs)(r.Fragment,{children:[L,(0,r.jsx)(d.A,{title:$?(0,r.jsx)("span",{dangerouslySetInnerHTML:{__html:T[N]}}):void 0,children:(0,r.jsx)(o.A,{className:"rcb-antd-template-card",hoverable:!$,style:{opacity:$||b&&(E||R)?.6:1},onClick:B,cover:P?(0,r.jsx)(u.A.Ribbon,{text:(0,r.jsx)(d.A,{title:k(w("The {{strong}}%s{{/strong}} plugin is recommending this service.",P.name),{strong:(0,r.jsx)("strong",{})}),children:(0,r.jsx)("div",{children:w("Integration")})}),children:F}):F,actions:n?n(t,B):[],children:(0,r.jsx)(x,{title:(0,r.jsxs)("span",{children:[D&&(0,r.jsx)(m.A,{color:g.QB,children:"PRO"}),!!T&&Object.keys(H).map((e=>(0,r.jsx)(d.A,{title:e!==N&&T[e]?(0,r.jsx)("span",{dangerouslySetInnerHTML:{__html:T[e]}}):void 0,children:(0,r.jsx)(m.A,{children:e})},e))),(0,r.jsx)("br",{}),A]}),description:C||(0,r.jsx)("i",{children:w("No description")})})})})]})}function y(e){let{templates:t,showHidden:n,showDisabled:o=!0,...i}=e;const[l,c]=(0,a.useState)(!1);return(0,a.useEffect)((()=>{!l&&t.length>0&&c(!0)}),[l,t.length]),t.length>0||l?(0,r.jsx)(r.Fragment,{children:t.filter((e=>{let{isHidden:t}=e;return!!n||!t})).filter((e=>{let{consumerData:{isDisabled:t}}=e;return!!o||!t})).map((e=>(0,r.jsx)(b,{template:e,...i},e.identifier)))}):(0,r.jsx)(s.A,{spinning:!0,style:{marginTop:10}})}},66399:(e,t,n)=>{n.d(t,{P:()=>r});const r=e=>new Promise((t=>setTimeout((()=>t(e)),0)))},95122:(e,t,n)=>{n.d(t,{r:()=>i});var r=n(3713),s=n(41594),a=n(43244),o=n.n(a);const i=e=>{let{settings:t={},value:n="",onChange:a}=e;const i=(0,s.useRef)(),l=(0,s.useRef)(),{codeEditor:c}=o();(0,s.useEffect)((()=>{if(c){const{codemirror:e}=c.initialize(i.current,t);l.current=e,e.on("change",(e=>{null==a||a(e.getValue())}))}}),[]);const d=(0,s.useCallback)((()=>{}),[]);return(0,s.useEffect)((()=>{"string"==typeof n&&i.current&&l.current&&i.current.value!==l.current.getValue()&&l.current.setValue(i.current.value)}),[n]),(0,r.jsx)("textarea",{ref:i,value:n,onChange:c?d:e=>{let{target:{value:t}}=e;return a(t)},style:{width:"100%"}})}},40164:(e,t,n)=>{n.d(t,{e:()=>s});var r=n(3713);const s=e=>{let{children:t,maxWidth:n="auto",style:s={}}=e;return(0,r.jsx)("div",{className:"rcb-config-content",style:{maxWidth:"fixed"===n?1300:n,...s},children:t})}},73481:(e,t,n)=>{n.r(t),n.d(t,{ScannerList:()=>xe});var r=n(3713),s=n(19991),a=n(34650),o=n(57922),i=n(41594),l=n(24262),c=n(18197),d=n(73491),u=n(57333),m=n(14383),h=n(12548),p=n(35207),f=n(33631),g=n(91502),x=n(55221),b=n(28101),y=n(64715),w=n(19117),k=n(78915),j=n(95962),v=n(43244),_=n.n(v),A=n(30617),C=n(95122);const S=(0,o.PA)((e=>{let{record:t}=e;const{message:n}=w.A.useApp(),[s,a]=(0,i.useState)(!1),{data:{id:o},markup:l,store:d}=t,u=(0,i.useMemo)((()=>l?{...window.cm_settings,codemirror:{..._().codeEditor.defaultSettings.codemirror,mode:l.mime,lint:!1,readOnly:!0}}:{}),[l]),h=(0,i.useCallback)((()=>{s?a(!1):(a(!0),d.fetchMarkup(o))}),[s]),p=(0,i.useCallback)((async()=>{d.addUrlsToQueue({urls:[t.data.sourceUrl],purgeUnused:!1}),Promise.all([d.fetchResultExternals(),d.fetchResultTemplates(),(0,m.refreshQueue)()]),n.info((0,A.__)("Page is scheduled for scanning again..."))}),[]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(k.A,{title:(0,A.__)("Element found by markup"),open:s,width:700,bodyStyle:{paddingBottom:0},okButtonProps:{style:{display:"none"}},onCancel:h,cancelText:(0,A.__)("Close"),children:(0,r.jsx)(c.A,{spinning:!l,children:l&&(0,r.jsx)(C.r,{settings:u,value:l.markup})})}),(0,r.jsx)(j.A.Button,{onClick:h,menu:{items:[{key:"scan-again",onClick:p,label:(0,A.__)("Scan again")}]},children:(0,A.__)("Open markup")})]})}));var I=n(98348),T=n(71951);const{Column:$}=g.A,E=(0,o.PA)((e=>{let{instance:t,style:n,reloadDependencies:s=[],reload:a=!0,tableProps:o={}}=e;const{scannerStore:l}=(0,T.g)(),{identifier:c,busy:u}=t,m=l.resultAllExternalUrls.get(c),w=(0,i.useCallback)((e=>{let{data:{id:t}}=e;return t}),[]),{remaining:k}=(0,I.X)(),[j,v]=(0,i.useState)("");return(0,i.useEffect)((()=>{!async function(){if(a)try{await l.fetchResultAllExternals(t),v("")}catch(t){var e;v((null==(e=t.responseJSON)?void 0:e.message)||t.message)}}()}),[t,a,...s]),(0,r.jsxs)(r.Fragment,{children:[k>0&&(0,r.jsx)("div",{className:"notice notice-info below-h2 notice-alt",style:{margin:"0 0 10px"},children:(0,r.jsx)("p",{children:(0,A.__)("Since the scanner is currently still running, the table may be refreshed.")})}),(0,r.jsxs)(g.A,{locale:{emptyText:j?(0,r.jsx)(x.Ay,{title:(0,A.__)("Something went wrong."),subTitle:j,status:"500"}):(0,r.jsx)(b.A,{description:(0,A.__)("No external URL found")})},dataSource:Array.from(m?m.values():[]),rowKey:w,size:"small",bordered:!0,style:n,loading:(!m||0===m.size)&&u,...o,children:[(0,r.jsx)($,{title:(0,A.__)("Last scanned"),defaultSortOrder:"descend",sorter:(e,t)=>new Date(e.data.lastScanned).getTime()-new Date(t.data.lastScanned).getTime(),dataIndex:["data","lastScanned"],render:(e,t)=>{let{data:{lastScanned:n}}=t;return new Date(n).toLocaleString(document.documentElement.lang)}},"created"),(0,r.jsx)($,{title:(0,A.__)("HTML Tag"),dataIndex:["data","tag"],render:(e,t)=>{let{data:{tag:n}}=t;return(0,r.jsx)(d.A,{children:`<${n} />`})}},"tag"),(0,r.jsx)($,{title:(0,r.jsx)(y.A,{title:(0,A.__)("This status shows you if the URL was blocked by a content blocker you defined."),children:(0,r.jsxs)("span",{children:[(0,A.__)("Blocked?")," ",(0,r.jsx)(h.A,{style:{color:"#9a9a9a"}})]})}),sorter:(e,t)=>e.data.blocked===t.data.blocked?0:e.data.blocked?-1:1,dataIndex:["data","blocked"],render:(e,t)=>{let{data:{blocked:n}}=t;return n?(0,r.jsx)(p.A,{twoToneColor:"#52c41a"}):(0,r.jsx)(f.A,{twoToneColor:"#eb2f96"})}},"blocked"),(0,r.jsx)($,{title:(0,A.__)("Blocked URL"),dataIndex:["data","blockedUrl"],render:(e,t)=>{let{blockedUrlTruncate:n,data:{blockedUrl:s}}=t;return(0,r.jsx)("a",{href:s,target:"_blank",rel:"noreferrer",title:s,children:n})}},"blockedUrl"),(0,r.jsx)($,{title:(0,A.__)("Found on this URL"),sorter:(e,t)=>e.data.sourceUrl.localeCompare(t.data.sourceUrl),dataIndex:["data","sourceUrl"],render:(e,t)=>{let{sourceUrlTruncate:n,data:{sourceUrl:s}}=t;return(0,r.jsx)("a",{href:s,target:"_blank",rel:"noreferrer",children:n})}},"sourceUrl"),(0,r.jsx)($,{title:(0,A.__)("Actions"),dataIndex:["data","markup"],render:(e,t)=>t.data.markup?(0,r.jsx)(S,{record:t}):""},"markup")]})]})}));var R=n(30338),U=n(32041),P=n(36920);const N=(0,o.PA)((e=>{let{item:t}=e;const[n,s]=(0,i.useState)(!1),{cookieStore:{essentialGroup:a}}=(0,T.g)(),{openDialog:o}=(0,U.g)(),{data:{host:l}}=t,{addLink:c}=(0,P.t)(),d=(0,i.useCallback)((()=>{s(!n)}),[n]),u=(0,i.useCallback)((()=>{s(!1),o()}),[o]),m=`navigateAfterCreation=${encodeURIComponent(window.location.href)}`;return(0,r.jsx)(R.A,{open:n,content:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:(0,r.jsx)("strong",{children:(0,A._i)((0,A.__)("Does {{i}}%s{{/i}} belong to an essential service without which your website would technically no longer work?",l),{i:(0,r.jsx)("i",{})})})}),(0,r.jsxs)("p",{children:[(0,r.jsx)("a",{className:"button button-primary",href:`${c}?force=scratch&attributes=${JSON.stringify({rules:`*${l}*`})}&${m}`,children:(0,A.__)("No")})," ",(0,r.jsx)("a",{className:"button",href:`#/cookies/${null==a?void 0:a.key}/new?force=scratch&${m}`,children:(0,A.__)("Yes")})," ",(0,r.jsx)("button",{className:"button",onClick:u,children:(0,A.__)("I do not know")})]}),(0,r.jsx)("p",{className:"description",children:(0,A._i)((0,A.__)("{{strong}}No:{{/strong}} Non-essential services that process personal data (e.g. IP address in some countries) or set cookies may only be loaded after consent. You should block them using a content blocker until consent is given. {{i}}Examples would be iframes, YouTube and similar embeddings or tracking tools.{{/i}}"),{strong:(0,r.jsx)("strong",{}),i:(0,r.jsx)("i",{})})}),(0,r.jsx)("p",{className:"description",children:(0,A._i)((0,A.__)("{{strong}}Yes (rare cases):{{/strong}} You should inform your users about the use of the service in the essential services group. You do not need to create a content blocker, as the service can be loaded without consent. {{i}}Examples are privacy-friendly spam protection tools or security tools.{{/i}}"),{strong:(0,r.jsx)("strong",{}),i:(0,r.jsx)("i",{})})}),(0,r.jsx)("p",{children:(0,r.jsx)("button",{className:"button",onClick:d,children:(0,A.__)("Close")})})]}),placement:"right",overlayStyle:{maxWidth:350},children:(0,r.jsx)("a",{onClick:d,children:(0,A.__)("Handle external URL")})})})),D=(0,o.PA)((e=>{let{item:t}=e;const[n,s]=(0,i.useState)(!1),{data:{host:o,foundOnSitesCount:h,tags:p,ignored:f},inactive:g,blockedStatus:x,blockedStatusText:b,busy:y}=t,{scannerStore:w}=(0,T.g)(),k=(0,i.useCallback)((()=>{s(!n)}),[o,n]),j=(0,i.useCallback)((async()=>{const e=[];try{await w.fetchResultAllExternals(t),w.resultAllExternalUrls.get(t.data.host).forEach((t=>{e.push(t.data.sourceUrl)})),await w.addUrlsToQueue({urls:e,purgeUnused:!1}),await Promise.all([w.fetchResultExternals(),(0,m.refreshQueue)()])}catch(e){e instanceof Error&&console.log(e)}}),[t]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.A.Item,{itemID:o,actions:[(0,r.jsx)(N,{item:t},"handle"),f&&(0,r.jsx)("a",{onClick:()=>{t.ignore(!1),s(!1)},children:(0,A.__)("Show recommendation again")}),!g&&(0,r.jsx)(l.A,{title:(0,A.__)("Are you sure that you want to ignore this recommendation?"),placement:"bottomRight",onConfirm:()=>{t.ignore(!0),s(!1)},okText:(0,A.__)("Ignore recommendation"),cancelText:(0,A.__)("Cancel"),overlayStyle:{maxWidth:350},children:(0,r.jsx)("a",{children:(0,A.__)("Ignore recommendation")})},"delete")].filter(Boolean),style:{opacity:g&&!n?.6:1},children:(0,r.jsx)(c.A,{spinning:y,children:(0,r.jsx)(a.A.Item.Meta,{title:(0,r.jsxs)("span",{children:[o," ",p.map((e=>(0,r.jsx)(d.A,{children:`<${e} />`},e))),f&&(0,r.jsx)(d.A,{children:(0,A.__)("Ignored")}),"none"!==x&&(0,r.jsx)(d.A,{color:"partial"===x?"warning":"success",children:b})]}),description:(0,r.jsx)("button",{className:"button-link",onClick:k,children:n?(0,A.__)("Close"):(0,A._n)("Embeds found on %d page","Embeds found on %d pages",h,h)})})})}),n&&(0,r.jsx)(E,{instance:t,reload:n&&h>0,reloadDependencies:[n,h],style:{marginTop:"-6px"},tableProps:{footer:()=>(0,r.jsx)("div",{style:{textAlign:"right"},children:(0,r.jsx)(u.Ay,{onClick:j,children:(0,A.__)("Scan these pages again")})})}})]})}));var O=n(75792);const L=e=>{let{count:t}=e;const n=(0,i.useMemo)((()=>{const e=[];for(let n=0;n<t;n++)e.push({key:n});return e}),[t]);return(0,r.jsx)(a.A,{dataSource:n,renderItem:()=>(0,r.jsx)(a.A.Item,{children:(0,r.jsx)(O.A,{loading:!0,active:!0,avatar:!1,paragraph:{rows:1}})})})};var B=n(2464);const F={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm72-112c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48zm400-188h-59.3c-2.6 0-5 1.2-6.5 3.3L763.7 538.1l-49.9-68.8a7.92 7.92 0 00-6.5-3.3H648c-6.5 0-10.3 7.4-6.5 12.7l109.2 150.7a16.1 16.1 0 0026 0l165.8-228.7c3.8-5.3 0-12.7-6.5-12.7zm-44 306h-64.2c-5.5 0-10.6 2.9-13.6 7.5a352.2 352.2 0 01-49.8 62.2A355.92 355.92 0 01651.1 840a355 355 0 01-138.7 27.9c-48.1 0-94.8-9.4-138.7-27.9a355.92 355.92 0 01-113.3-76.3A353.06 353.06 0 01184 650.5c-18.6-43.8-28-90.5-28-138.5s9.4-94.7 28-138.5c17.9-42.4 43.6-80.5 76.4-113.2 32.8-32.7 70.9-58.4 113.3-76.3a355 355 0 01138.7-27.9c48.1 0 94.8 9.4 138.7 27.9 42.4 17.9 80.5 43.6 113.3 76.3 19 19 35.6 39.8 49.8 62.2 2.9 4.7 8.1 7.5 13.6 7.5H892c6 0 9.8-6.3 7.2-11.6C828.8 178.5 684.7 82 517.7 80 278.9 77.2 80.5 272.5 80 511.2 79.5 750.1 273.3 944 512.4 944c169.2 0 315.6-97 386.7-238.4A8 8 0 00892 694z"}}]},name:"issues-close",theme:"outlined"};var H=n(4679),M=function(e,t){return i.createElement(H.A,(0,B.A)({},e,{ref:t,icon:F}))};const W=i.forwardRef(M);var q=n(97276),Q=n(12719),V=n(76576);const Y=["sitemap.xml","sitemap_index.xml","sitemap-index.xml","sitemap/","post-sitemap.xml","sitemap/sitemap.xml","sitemap/index.xml","sitemapindex.xml","sitemap.php","sitemap.txt","index.php/sitemap_index.xml","index.php?xml_sitemap=params=","glossar/sitemap.xml"];async function X(e,t){void 0===t&&(t=Y);const n=t.map((t=>fetch(`${e}${t}`,{mode:"no-cors"})));for(const e of n)try{const t=await e,n=await t.text();if(n.indexOf("<sitemapindex")>-1||n.indexOf("<urlset")>-1)return t.url}catch(e){}return!1}const z=/.+?:\/\/.+?(\/.+?)(?:#|\?(.*)|$)/;function J(e,t,n){var r;void 0===n&&(n="");const s=(null==(r=e.match(z))?void 0:r[1])||"/",a=t.match(z);if(a){const[,t,r]=a,o=r?`?${r}${n?`?${n}`:""}`:n?`?${n}`:"";return`${e}${t.substr(s.length)}${o}`}return!1}const G="robots.txt",K="sitemap-crawler-filter",Z="X-Sitemap-Crawler-Filter";async function ee(e,t,n){try{const r=[];t&&r.push(`${t}=1`);const s=await fetch(`${e}${G}${r?`?${r}`:""}`);if(!s.ok)return!1;n&&r.push(`${K}=${n}`);const a=[...(await s.text()).matchAll(/^sitemap:(.*)$/gim)].map((e=>{let[,t]=e;return t.trim()}));let o=[];for(const t of a){const s=J(e,t,r.join("&"));if(s){const e=await fetch(s);if(!e.ok)continue;const t=await e.text();if(!/<(?:sitemap|urlset)/gm.test(t))continue;if(n&&"false"===e.headers.get(Z))continue;o.push(s)}}return o=[...new Set(o)],!!o.length&&o}catch(e){return!1}}const te="https://base";async function ne(e,t,n){if(n)try{const r=await async function(e){const t=await fetch(e),n=await t.text();return(new DOMParser).parseFromString(n.trim(),"application/xml")}(t),{protocol:s}=new URL(t,te),a=r.querySelector("sitemapindex");if(a){const t=Array.from(a.children).map((e=>{var t;return null==(t=e.querySelector("loc"))?void 0:t.textContent})).filter(Boolean);for(const r of t){const t=J(e,r)||r;await ne(e,t,n)}}const o=r.querySelector("urlset");if(o){const e=Array.from(o.children).map((e=>{var t;return null==(t=e.querySelector("loc"))?void 0:t.textContent})).filter(Boolean).map((e=>{try{const t=new URL(e,te);return"http:"===t.protocol&&(t.protocol=s),t.toString()}catch(t){return e}}));n.push(...e)}}catch(e){console.error(`Error occurred during "crawl('${t}')":\n\r Error: ${e}`)}else try{return(await ne(e,t,[])).sort(((e,t)=>e.length-t.length))}catch(e){return console.error(e),[]}return[...new Set(n)]}async function re(e){const t=(0,A.__)('If you think a sitemap exists but you get this error, please <a href="%s" target="_blank" />contact our support</a> and we will look individually where the problem is in your WordPress installation.',(0,A.__)("https://devowl.io/support/"));let n=window.realCookieBannerQueue.originalHomeUrl;const r=window.realCookieBannerQueue.blogId;[n]=n.split("?",2),null==e||e("find-sitemap");const s=await function(e,t,n,r){return new Promise(((s,a)=>{const o=e=>setTimeout((()=>s(e)),100);(async()=>{try{const s=await ee(e,void 0,r);if(s)return void o(s);const a=await X(e);if(a)return void o([a]);if(t){const n=await ee(e,t,r);if(n)return void o(n)}if(n){const t=await X(e,n);if(t)return void o([t])}o(!1)}catch(e){a(e)}})()}))}(n,"rcb-force-sitemap",["?sitemap=index&rcb-force-sitemap=1"],`${r}`);if(!1===s)throw new Error(`${(0,A.__)("We didn't find a sitemap on your website. We need it to scan all the subpages of your website. Do you have this feature disabled in your WordPress?")} ${t}`);null==e||e("collect-sitemap");let a=[];try{a=await async function(e,t){return[...new Set((await Promise.all(t.map((t=>ne(e,t))))).flat())]}(n,s)}catch(e){throw new Error(`${(0,A.__)("The sitemap could not be parsed. Therefore, we cannot check for services on your website.")} ${t}`)}if(0===a.length)throw new Error(`${(0,A.__)("The sitemap is empty. So, we could not add any URLs to the scanner.")} ${t}`);return a}const se=(0,o.PA)((()=>{const{optionStore:{others:{isPro:e}},scannerStore:{canShowResults:t,foundScanResultsCount:n,needsAttentionCount:s}}=(0,T.g)(),[a,o]=(0,i.useState)(void 0),{status:l,currentJob:d,total:u,percent:h,remaining:f,cancelling:g,handleStart:x,handleCancel:y,step:k,stepText:j}=function(e,t){const{modal:n}=w.A.useApp(),[s,a]=(0,i.useState)("idle"),{remaining:o,...l}=(0,I.X)(e,"rcb-scan-list",(0,i.useCallback)((()=>{a("idle")}),[])),{scannerStore:c,checklistStore:d}=(0,T.g)(),u=(0,i.useCallback)((async()=>{try{const e=await re(a);a("add-to-queue"),await c.addUrlsToQueue({urls:e,purgeUnused:!0}),await Promise.all([c.fetchResultExternals(),c.fetchResultTemplates(),(0,m.refreshQueue)()]),a("done"),d.fetchChecklist()}catch(e){e instanceof Error&&n.error({title:(0,A.__)("Whoops!"),content:(0,r.jsx)("span",{dangerouslySetInnerHTML:{__html:e.message}})}),a("idle")}}),[]);(0,i.useEffect)((()=>{(0,m.fetchStatus)(!0)}),[]);const h=(0,i.useMemo)((()=>{switch(s){case"idle":return(0,A.__)("Scan complete website");case"find-sitemap":return(0,A.__)("Find your website sitemap...");case"collect-sitemap":return(0,A.__)("Collect all scannable URLs...");case"add-to-queue":case"done":return(0,A.__)("Add URLs to queue...")}return""}),[s]);return{handleStart:u,step:s,stepText:h,setStep:a,remaining:o,...l}}(a),v="idle"!==k,{isLicensed:_,modal:C,open:S}=(0,Q.WH)({title:(0,A.__)("Scanner requires up-to-date search data from the Service Cloud"),description:(0,A.__)("The scanner can automatically search your website for used services. To do this, it needs an up-to-date database of search data for services, which must be downloaded from Real Cookie Banner's Service Cloud. The data will be downloaded locally to your server, so your website visitors will not need to connect to the cloud.")+(e?"":` ${(0,A.__)("You can activate your free licence at any time, without any costs.")}`),mode:"license-activation",feature:"scanner",assetName:"service-cloud.svg",assetMaxHeight:200}),$=(0,i.useCallback)((()=>{_?x():S()}),[x,_,S]),{start:E}=(0,V.f)();return(0,i.useEffect)((()=>{E&&$()}),[]),(0,i.useEffect)((()=>{o(f>0&&d&&u?5e3:void 0)}),[f,d,u]),void 0===f?(0,r.jsx)(c.A,{style:{display:"block"}}):(0,r.jsxs)("div",{className:"wp-clearfix",children:[C,t&&(0,r.jsx)("div",{style:{float:"left",margin:"5px 10px"},children:0===s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{twoToneColor:"#52c41a"})," ",(0,A.__)("All recommendations applied")]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(W,{style:{color:"#dba617"}})," ",(0,A.__)("%d of %d recommendations applied",n-s,n)]})}),"failed"===l?(0,r.jsx)(b.A,{style:{clear:"both"},description:(0,A.__)("Scan failed"),image:(0,r.jsx)(q.A,{type:"circle",width:100,percent:100,status:"exception"})}):"done"===l?(0,r.jsx)(b.A,{style:{clear:"both"},description:(0,A.__)("Scan completed"),image:(0,r.jsx)(q.A,{type:"circle",width:100,percent:100})}):f>0&&d&&u?(0,r.jsx)(b.A,{style:{clear:"both"},description:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{children:(0,A._i)((0,A.__)("Currently scanning {{code}}%s{{/code}} (%d of %d pages scanned)...",d.data.url,u-f,u),{code:(0,r.jsx)("code",{})})}),(0,r.jsx)("div",{className:"notice notice-info inline below-h2 notice-alt",style:{margin:"10px 0 0 0",display:"inline-block"},children:(0,r.jsx)("p",{children:(0,A.__)("You can add already found services, edit your website or have a coffee in the meantime. The scanner runs in the background.")})})]}),image:(0,r.jsx)(q.A,{type:"circle",width:100,percent:h}),children:(0,r.jsx)("button",{className:"button button-primary",onClick:y,disabled:g,children:(0,A.__)("Cancel scan")})}):t?(0,r.jsx)("button",{id:"rcb-btn-scan-start",className:"button button-primary alignright",onClick:$,disabled:v,style:{marginBottom:10,display:"done"!==k?void 0:"none"},children:j}):(0,r.jsx)(b.A,{description:(0,A.__)("Scan your website for services that may set cookies or process personal data to obtain consent."),children:(0,r.jsx)("button",{className:"button button-primary",onClick:$,disabled:v,children:j})})]})}));var ae=n(60531),oe=n(65824),ie=n(59726),le=n(58025);const ce=e=>{let{templates:t,onSelect:n,dropdownItems:s=(()=>[]),isTcf:a,onIgnoreToggle:o}=e;const{modal:l}=w.A.useApp(),{__:c}=(0,ie.s)(),d=(0,i.useCallback)(((e,t)=>{"ignore"===e&&o(t,!0)}),[o]),u=(0,i.useCallback)((e=>{const{tcfVendorIds:t,name:r,consumerData:{createAdNetwork:s}}=e;(null==t?void 0:t.length)>0&&!a?window.location.href=`#/settings/tcf?tcfIntegrationItem=${encodeURIComponent(r)}&navigateAfterTcfActivation=${encodeURIComponent(s?`#/cookies/tcf-vendors/new?adNetwork=${encodeURIComponent(s)}`:"#/scanner")}`:n(e)}),[n]);return(0,r.jsx)(le.X,{showHidden:!0,grayOutAlreadyExisting:!0,templates:t,onSelect:u,onAcknowledgementClick:d,renderTags:(0,i.useCallback)(((e,t)=>{const{isIgnored:n,acknowledgement:r}=t.consumerData;return n&&(e[c(r?"Acknowledged":"Ignored")]=""),e}),[]),renderActions:(0,i.useCallback)(((e,t)=>{const{tcfVendorIds:n,consumerData:{isDisabled:i,isIgnored:d,acknowledgement:u}}=e,m=(null==n?void 0:n.length)>0&&!a,h=c(u?"Show explanation":m?"Activate TCF":"Create now"),p=c(d?"Show recommendation again":u?"Acknowledge recommendation":"Ignore recommendation");return[!i&&(0,r.jsx)(j.A,{menu:{items:[{key:"create",label:(0,r.jsx)("a",{className:"rcb-template-card-create-link",children:h})},...s(e,t),!e.consumerData.isCreated&&{key:"ignore",label:(0,r.jsx)("a",{onClick:()=>{const t=()=>o(e,!d);d||u?t():l.confirm({title:c("Are you sure that you want to ignore this recommendation?"),onOk:t,okText:c("Ignore recommendation"),cancelText:c("Cancel")})},children:p})}].filter(Boolean)},children:(0,r.jsxs)(oe.A,{children:[h,(0,r.jsx)(ae.A,{})]})},"dropdown")].filter(Boolean)}),[])})};var de=n(66399);const ue=(0,o.PA)((e=>{let{template:t,onVisibleChange:n}=e;const{message:s}=w.A.useApp(),{scannerStore:a}=(0,T.g)(),o=[],{data:{identifier:l,name:c,consumerData:{scan:d}}}=t,[u,h]=(0,i.useState)(!1),p=(0,i.useCallback)((()=>{null==n||n(!u),h(!u)}),[l,u]),f=(0,i.useCallback)((async()=>{const e=a.resultAllExternalUrls.get(l),t=Array.from(e?e.values():[]);for(const e of t)o.push(e.data.sourceUrl);await a.addUrlsToQueue({urls:o,purgeUnused:!1}),s.info((0,A.__)("Pages are scheduled for scanning again...")),await Promise.all([a.fetchResultExternals(),a.fetchResultTemplates(),(0,m.refreshQueue)()])}),[]),g=!1===d?0:d.foundOnSitesCount;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(k.A,{title:c,open:u,width:1400,bodyStyle:{paddingBottom:0},onCancel:p,cancelText:(0,A.__)("Close"),cancelButtonProps:{style:{float:"right",marginLeft:"10px"}},okButtonProps:{type:"default"},onOk:f,okText:(0,A.__)("Scan these pages again"),children:(0,r.jsx)(E,{instance:t,reload:u&&g>0,reloadDependencies:[u,g]})}),(0,r.jsx)("a",{onClick:e=>{e.preventDefault(),p()},children:(0,A._n)("Embeds found on %d page","Embeds found on %d pages",g,g)})]})}));var me=n(40164);const he=(0,o.PA)((()=>{const[e,t]=(0,i.useState)(!1),{scannerStore:n,cookieStore:a,optionStore:{isTcf:o}}=(0,T.g)(),{sortedTemplates:l,resultTemplates:d,busyResultTemplates:u}=n,{remaining:m}=(0,I.X)(),{essentialGroup:h}=a,[p,f]=(0,i.useState)(!1),g=(0,i.useCallback)((async r=>{if(p||!r||e)return;t(!0);const{identifier:s,isHidden:a,name:i}=r,l=n.resultTemplates.get(s),{type:c,templateModel:d}=l,u=`navigateAfterCreation=${encodeURIComponent("#scanner")}`;if("service"===c)(0,de.P)().then((()=>window.location.href=`#/cookies/${null==h?void 0:h.key}/new?force=${s}&${u}`));else{const e=d;await e.fetchUse();const{use:{consumerData:{serviceTemplates:t,createAdNetwork:n}}}=e,r=t.filter((e=>{let{identifier:t}=e;return t===s}))[0]||t[0];let l="";if(r)if(!r.consumerData.isCreated||a){if(a)l=`#/cookies/${null==h?void 0:h.key}/new?force=${s}&${u}`;else if(!r.consumerData.isCreated){var m;l=`#/cookies/${null==h?void 0:h.key}/new?force=${r.identifier}&attributes=${JSON.stringify({createContentBlocker:(null==(m=r.group)?void 0:m.toLowerCase())!==(null==h?void 0:h.data.name.toLowerCase()),createContentBlockerId:s})}&${u}`}}else l=`#/blocker/new?force=${s}&${u}`;else n&&(l=o?`#/cookies/tcf-vendors/new?adNetwork=${encodeURIComponent(n)}`:`#/settings/tcf?tcfIntegrationItem=${encodeURIComponent(i)}&navigateAfterTcfActivation=${encodeURIComponent(`#/cookies/tcf-vendors/new?adNetwork=${encodeURIComponent(n)}`)}`);l&&(0,de.P)().then((()=>window.location.href=l))}t(!1)}),[e,p,h]);return(0,r.jsxs)(me.e,{style:{textAlign:"center"},children:[(0,r.jsx)(s.A,{children:(0,A.__)("Services, for which you should obtain consent")}),(0,r.jsx)(c.A,{spinning:u&&!m||e,children:(0,r.jsx)(ce,{templates:l.map((e=>{let{data:t}=e;return t})),onSelect:g,dropdownItems:e=>{let{identifier:t,consumerData:{scan:n}}=e;return[n&&{key:"table",label:(0,r.jsx)(ue,{template:d.get(t),onVisibleChange:f},"table")}]},onIgnoreToggle:(e,t)=>n.resultTemplates.get(e.identifier).ignore(t),isTcf:o})})]})}));var pe=n(53603),fe=n(70697),ge=n(89657);const xe=(0,o.PA)((()=>{const{scannerStore:e,cookieStore:t}=(0,T.g)(),{currentJob:n}=(0,I.X)(),{templatesCount:o,externalUrlsCount:l,busyExternalUrls:c,sortedExternalUrls:d,canShowResults:u}=e,m=(0,pe.m)("scanner");return(0,i.useEffect)((()=>{e.fetchResultTemplates(),e.fetchResultExternals(),t.fetchGroups()}),[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(fe.k,{style:{margin:"10px 0 0 0"}}),(0,r.jsxs)(me.e,{children:[(0,r.jsx)(se,{}),u&&o>0&&(0,r.jsx)(he,{}),u&&l>0&&(c&&!d.length&&void 0===n?(0,r.jsx)(L,{count:l}):(0,r.jsxs)("div",{children:[(0,r.jsx)(s.A,{children:(0,A.__)("Embeds of external URLs to be checked")}),(0,r.jsx)("div",{style:{maxWidth:800,margin:"0px auto 20px",textAlign:"center"},children:(0,r.jsx)("p",{className:"description",children:(0,A.__)("You are embedding scripts, styles, iframes or similar from the following third-party servers. We currently do not have service templates for these. Therefore, you may have to create a service and/or content blocker yourself after you have assessed the situation.")})}),(0,r.jsx)(a.A,{children:d.map((e=>(0,r.jsx)(D,{item:e},e.data.host)))})]})),(0,r.jsx)("div",{style:{maxWidth:800,margin:"30px auto 0",textAlign:"center"},children:m})]}),(0,r.jsx)(ge.b,{identifier:"scanner",title:(0,A.__)("What does the scanner find?"),width:900})]})}))},76576:(e,t,n)=>{n.d(t,{f:()=>a});var r=n(27667),s=n(68789);function a(){return s.qs.parse((0,r.zy)().search)}},36920:(e,t,n)=>{n.d(t,{t:()=>i});var r=n(41594),s=n(27667),a=n(44227),o=n(71951);const i=()=>{const e=(0,s.g)(),{cookieStore:t}=(0,o.g)(),n=+e.blocker,i=isNaN(+n)?0:+n,l=!!n,c=t.blockers.entries.get(i)||new a.g(t.blockers,{id:0}),d=(0,r.useCallback)((e=>{let{key:t}=e;return`#/blocker/edit/${t}`}),[c]);return{blocker:c,id:i,queried:l,fetched:0!==c.key,link:"#/blocker",editLink:d,addLink:"#/blocker/new"}}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/7a6db1bc5a92bb1bdb46939d4a042393/chunk-config-tab-scanner.lite.js.map
