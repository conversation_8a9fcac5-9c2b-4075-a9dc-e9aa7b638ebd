var realCookieBanner_customize;(()=>{"use strict";var e,t={98741:(e,t,o)=>{o.r(t),o.d(t,{ChecklistStore:()=>L,ConsentStore:()=>X,CookieStore:()=>ge,CustomizeBannerStore:()=>be,OptionStore:()=>Ae,RootStore:()=>rt,ScannerStore:()=>Ge,StatsStore:()=>$e,TcfStore:()=>it,useStores:()=>at});const n=ReactJSXRuntime,s=devowlWp_utils,i=devowlWp_customize;var r=o(29766),a=o(73491),c=o(41594),l=o(15582),d=o(78915);const p=/(\r\n|\r|\n|<br[ ]?\/>)/g;var u=o(19117),g=o(8116);const h=(e,t)=>{const o=(0,c.useRef)(0);(0,c.useEffect)((()=>{if(o.current++,1!==o.current)return e()}),t)};function y(e,t){if(void 0===t&&(t=new Map),t.has(e))return t.get(e);let o;if("structuredClone"in window&&(e instanceof Date||e instanceof RegExp||e instanceof Map||e instanceof Set))o=structuredClone(e),t.set(e,o);else if(Array.isArray(e)){o=new Array(e.length),t.set(e,o);for(let n=0;n<e.length;n++)o[n]=y(e[n],t)}else if(e instanceof Map){o=new Map,t.set(e,o);for(const[n,s]of e.entries())o.set(n,y(s,t))}else if(e instanceof Set){o=new Set,t.set(e,o);for(const n of e)o.add(y(n,t))}else{if(!function(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(e))return e;o={},t.set(e,o);for(const[n,s]of Object.entries(e))o[n]=y(s,t)}return o}const b={};function v(e){let t=b[e];if(!t){const o=(0,c.createContext)({});t=[o,()=>(0,c.useContext)(o)],b[e]=t}return t}const m=e=>v(e)[1]();function f(e,t,o,n){void 0===o&&(o={}),void 0===n&&(n={});const{refActions:s,observe:i,inherit:r,deps:a}=n,l=v(e),[d,p]=(0,c.useState)((()=>{const e=Object.keys(o),n=Object.keys(s||{}),i=function(t){for(var i=arguments.length,r=new Array(i>1?i-1:0),a=1;a<i;a++)r[a-1]=arguments[a];return new Promise((i=>p((a=>{const c={...a},l=[];let d=!0;const p=new Proxy(c,{get:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];const[a,c]=i;let u=Reflect.get(...i);if(!d)return u;if(-1===l.indexOf(c)&&(u=y(u),Reflect.set(a,c,u),l.push(c)),"string"==typeof c){let t;if(e.indexOf(c)>-1?t=o[c]:n.indexOf(c)>-1&&(t=s[c]),t)return function(){for(var e=arguments.length,o=new Array(e),n=0;n<e;n++)o[n]=arguments[n];return t(p,...o)}}return u}}),u=t(p,...r),g=e=>{d=!1,i(e)};return u instanceof Promise?u.then(g):g(void 0),c}))))},r={set:e=>i("function"==typeof e?e:t=>Object.assign(t,e)),...t,...e.reduce(((e,t)=>(e[t]=function(){for(var e=arguments.length,n=new Array(e),s=0;s<e;s++)n[s]=arguments[s];return i(o[t],...n)},e)),{}),...n.reduce(((e,t)=>(e[t]=function(){for(var e=arguments.length,o=new Array(e),n=0;n<e;n++)o[n]=arguments[n];return s[t](d,...o)},e)),{})};return r.suspense||(r.suspense={}),r}));(null==i?void 0:i.length)&&h((()=>{i.filter((e=>t[e]!==d[e])).length&&d.set(i.reduce(((e,o)=>(e[o]=t[o],e)),{}))}),[i.map((e=>t[e]))]),Array.isArray(a)&&h((()=>{d.set(t)}),a);const[{Provider:u}]=l;let g=d;(null==r?void 0:r.length)&&(g={...d,...r.reduce(((e,o)=>(e[o]=t[o],e)),{})});const b=(0,c.useMemo)((()=>({})),[]);return(0,c.useEffect)((()=>{const{suspense:e}=d;if(e)for(const t in e){const o=e[t],n=b[t];o instanceof Promise&&n!==o&&(b[t]=o,o.then((e=>d.set({[t]:e}))))}}),[d]),[u,g]}const S=Symbol(),C=()=>m(S),k=Symbol(),w=()=>m(k),x=e=>{let{style:t}=e;const{message:o}=u.A.useApp(),{__:s,_i:i}=C(),{fomoCoupon:r}=w(),l=null==r?void 0:r.coupon,d=null==r?void 0:r.valueInPercent,p=null==r?void 0:r.validUntil,h=(0,c.useCallback)((()=>{if(p){const e=new Date(p).getTime()-(new Date).getTime();if(e<=0)return;const t=e/1e3;return[Math.floor(t/3600),Math.floor(t/60)%60,Math.floor(t%60)].map((e=>e<10?`0${e}`:e)).filter(((e,t)=>"00"!==e||t>0)).join(":")}}),[p]),[,y]=(0,c.useState)();(0,c.useEffect)((()=>{const e=setInterval((()=>{y((new Date).getTime())}),1e3);return()=>{clearInterval(e)}}),[]);const b=h();return b?(0,n.jsx)(g.A,{style:t,message:i(s("Use coupon {{tag}}%s{{/tag}} in the next {{strongHours}}%s hours{{/strongHours}} and save {{strongPercent}}%d %%{{/strongPercent}} in the first year!",l,b,d),{tag:(0,n.jsx)(a.A,{color:"success",style:{marginRight:0,cursor:"pointer"},onClick:()=>{!function(e){const t=document.createElement("textarea");t.innerHTML=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),t.remove()}(l),o.success(s("Successfully copied coupon to clipboard!"))}}),strongHours:(0,n.jsx)("strong",{style:{color:"#d33131"}}),strongPercent:(0,n.jsx)("strong",{})})}):null};var j=o(19991),O=o(6099),A=o(92453);const F=e=>{let{title:t,description:o,link:s,linkText:i,logo:r}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(j.A,{children:t}),(0,n.jsxs)(O.A,{wrap:!1,style:{marginBottom:10},children:[(0,n.jsx)(A.A,{flex:"auto",children:!!o&&(0,n.jsxs)("p",{style:{margin:0},children:[(0,n.jsx)("span",{dangerouslySetInnerHTML:{__html:o}})," ",!!s&&!!i&&(0,n.jsx)("a",{href:s,target:"_blank",rel:"noreferrer",className:"button-link",children:i})]})}),(0,n.jsx)(A.A,{flex:"150px",style:{alignSelf:"center"},children:!!r&&(0,n.jsx)("img",{src:r,style:{maxWidth:"calc(100% - 20px)",height:"auto",marginLeft:20}})})]})]})},B=e=>{let{mode:t="pro",visible:o=!1,showHints:s=!0,showFomoCouponCounter:i=!0,title:r,testDrive:a=!1,assetName:u,assetMaxHeight:g,description:h,feature:y,onClose:b,inContainer:v,inContainerElement:m}=e;const{__:f,_i:S}=C(),{proUrl:k,hint:j,isPro:O}=w(),[A,P]=(0,c.useState)(),B=(0,c.useCallback)((()=>{"pro"===t?window.open(`${k}&feature=${y}&promo=in-app`,"_blank").focus():window.location.href=`#/licensing?navigateAfterActivation=${encodeURIComponent(window.location.href)}`,null==b||b()}),[b,t]);if((0,c.useEffect)((()=>{v&&A&&A.parentElement.parentElement.parentElement.removeAttribute("tabIndex")}),[A]),v&&!m)return null;const T=u?`https://assets.devowl.io/in-app/wp-real-cookie-banner/${u}`:void 0;return(0,n.jsxs)(d.A,{transitionName:v?null:void 0,open:!!v||o,title:(0,n.jsxs)("span",{children:[(0,n.jsx)(l.A,{})," ",r," ","pro"===t&&f("Get PRO!")]}),onOk:B,onCancel:b,cancelText:f("No, not interested..."),okText:f("pro"===t?"I want to learn more!":O?"Activate license":"Activate free license"),className:"rcb-antd-hero-modal",width:u?800:700,getContainer:v?m:void 0,children:[!!u&&(u.endsWith(".webm")?(0,n.jsx)("video",{autoPlay:!0,muted:!0,loop:!0,style:{marginTop:10,maxHeight:g},children:(0,n.jsx)("source",{src:T,type:"video/webm"})}):(0,n.jsx)("img",{style:{marginTop:10,maxHeight:g},src:T})),(0,n.jsxs)("div",{style:{maxWidth:600,margin:"auto"},ref:P,children:[(0,n.jsx)("p",{children:"string"==typeof h?(I=h,"string"==typeof I?I.split(p).map(((e,t)=>e.match(p)?(0,c.createElement)("br",{key:t}):e)):I):h}),a&&"pro"===t&&(0,n.jsx)("p",{children:S(f("Check out this feature with a {{a}}free sandbox{{/a}} before buying!"),{a:(0,n.jsx)("a",{href:f("https://try.devowl.io/?product=RCB"),target:"_blank",rel:"noreferrer"})})})]}),!!j&&s&&"pro"===t&&(0,n.jsx)("div",{style:{maxWidth:600,margin:"auto",textAlign:"left"},children:(0,n.jsx)(F,{...j})}),i&&"pro"===t&&(0,n.jsx)(x,{style:{marginBottom:15}})]});var I},I="#2db7f5";function R(e,t){const{__:o}=C(),{isPro:s,isLicensed:i}=w(),[l,d]=(0,c.useState)(!1),p="boolean"==typeof t?t:s,u=(0,c.useCallback)((e=>{d(!0),null==e||e.preventDefault()}),[d]),g=(0,c.useMemo)((()=>p?null:(0,n.jsx)(a.A,{icon:(0,n.jsx)(r.A,{}),color:I,style:{cursor:"pointer"},onClick:u,children:e.tagText||o("Unlock feature")})),[u,e]),h=(0,c.useMemo)((()=>p&&"license-activation"!==e.mode?null:(0,n.jsx)(B,{visible:l,onClose:()=>d(!1),...e})),[l,d,e]);return{isPro:p,isLicensed:i,tag:g,modal:h,open:u}}const _=e=>{let{children:t,wrapperAttributes:o={},...s}=e;const{modal:i,tag:r}=R(s);return(0,n.jsxs)(n.Fragment,{children:[i,(0,n.jsx)("span",{...o,children:r})]})};var E=o(59670),M=o(44497);let D;const H=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return(D||(D=(0,s.createRequestFactory)(window[s.BaseOptions.getPureSlug("real-cookie-banner",!0)]))).request(...t)},U={path:"/checklist",method:s.RouteHttpVerb.GET},N={path:"/checklist/:id",method:s.RouteHttpVerb.PUT};class L extends s.BaseOptions{constructor(e){super(),this.busyChecklist=!1,this.probablyFetchByChangedItem=(0,M.flow)((function*(e,t){if(t)return void(yield this.fetchChecklist());const o=Array.isArray(e)?e:[e];this.items.filter((e=>{let{id:t,checked:n}=e;return o.indexOf(t)>-1&&!n})).length>0&&(yield this.fetchChecklist())})),this.fetchChecklist=(0,M.flow)((function*(){this.busyChecklist=!0;try{this.checklist=yield H({location:U,sendReferer:!0})}catch(e){throw console.log(e),e}finally{this.busyChecklist=!1}})),this.toggleChecklistItem=(0,M.flow)((function*(e,t){this.busyChecklist=!0;try{this.checklist=yield H({location:N,request:{state:t},sendReferer:!0,params:{id:e}})}catch(e){throw console.log(e),e}finally{this.busyChecklist=!1}})),this.rootStore=e}get items(){return this.checklist&&Object.keys(this.checklist.items).map((e=>({id:e,...this.checklist.items[e]})))||[]}get completed(){return this.items.filter((e=>{let{checked:t}=e;return t}))}get checkable(){const{isPro:e}=this.rootStore.optionStore.others;return this.items.filter((t=>{let{needsPro:o}=t;return!o||e&&o}))}get done(){var e;return this.completed.length>=this.checkable.length||!!(null==(e=this.checklist)?void 0:e.dismissed)}}(0,E.Cg)([M.observable],L.prototype,"busyChecklist",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type","undefined"==typeof ResponseRouteChecklistGet?Object:ResponseRouteChecklistGet)],L.prototype,"checklist",void 0),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],L.prototype,"items",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],L.prototype,"completed",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],L.prototype,"checkable",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],L.prototype,"done",null);const V={path:"/consent/:id",method:s.RouteHttpVerb.DELETE};class z{get revision(){return this.store.revisions.get(this.revision_hash)}get revision_independent(){return this.store.revisionsIndependent.get(this.revision_independent_hash)}get custom_bypass_readable(){const{custom_bypass:e}=this;return e?e.charAt(0).toUpperCase()+e.slice(1):""}get export(){return JSON.parse(JSON.stringify({...this.plain,revision:this.revision.data,revision_independent:this.revision_independent.data}))}constructor(e,t){this.busy=!1,this.delete=(0,M.flow)((function*(){this.busy=!0;try{yield H({location:V,params:{id:this.id}}),yield this.store.fetchAll()}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,M.runInAction)((()=>(0,M.set)(this,e))),this.store=t,this.plain=e}fetchRevisions(){return Promise.all([this.store.fetchRevision({hash:this.revision_hash}),this.store.fetchRevisionIndependent({hash:this.revision_independent_hash})])}}(0,E.Cg)([M.observable],z.prototype,"busy",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Number)],z.prototype,"id",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"plugin_version",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"design_version",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"ipv4",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"ipv6",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"ipv4_hash",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"ipv6_hash",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"uuid",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"previous_decision",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"decision",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"decision_labels",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"created",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"blocker",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"blocker_thumbnail",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"dnt",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"custom_bypass",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"user_country",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"revision_hash",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"revision_independent_hash",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"button_clicked",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"context",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"viewport_width",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"viewport_height",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"referer",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"url_imprint",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"url_privacy_policy",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"forwarded",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"forwarded_blocker",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"previous_tcf_string",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"tcf_string",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"previous_gcm_consent",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"gcm_consent",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"recorder",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],z.prototype,"ui_view",void 0),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],z.prototype,"revision",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],z.prototype,"revision_independent",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],z.prototype,"custom_bypass_readable",null);class G{constructor(e,t){this.data=e,this.store=t}}class W{constructor(e,t){this.data=e,this.store=t}}const q={path:"/consent/all",method:s.RouteHttpVerb.DELETE},$={path:"/consent/all",method:s.RouteHttpVerb.GET},J={path:"/consent/referer",method:s.RouteHttpVerb.GET},Y={path:"/revision/:hash",method:s.RouteHttpVerb.GET},K={path:"/revision/independent/:hash",method:s.RouteHttpVerb.GET};class X extends s.BaseOptions{constructor(e){super(),this.busyConsent=!1,this.busyReferer=!1,this.count=0,this.truncatedIpsCount=0,this.perPage=50,this.offset=0,this.pageCollection=new Map,this.revisions=new Map,this.revisionsIndependent=new Map,this.referer=[],this.filters=M.observable.object({page:1,dates:[void 0,void 0],context:void 0,referer:void 0,ip:void 0,uuid:void 0},{},{deep:!1}),this.fetchAll=(0,M.flow)((function*(e){this.busyConsent=!0;try{const{page:t,referer:o,ip:n,uuid:s,context:i}=this.filters,r=this.filters.dates.map((e=>e?e.format("YYYY-MM-DD"):"")),{count:a,truncatedIpsCount:c,items:l}=yield H({location:$,params:{per_page:this.perPage,offset:(t-1)*this.perPage,from:r[0],to:r[1],ip:n,uuid:s,referer:o,context:i,...e||{}}});this.count=a,c&&(this.truncatedIpsCount=c),this.pageCollection.clear();for(const e of l)this.pageCollection.set(e.id,new z(e,this))}catch(e){throw this.count=0,this.truncatedIpsCount=0,this.pageCollection.clear(),console.log(e),e}finally{this.busyConsent=!1}})),this.fetchRevision=(0,M.flow)((function*(e){try{const t=yield H({location:Y,params:e});this.revisions.set(e.hash,new G(t,this))}catch(e){throw console.log(e),e}})),this.fetchRevisionIndependent=(0,M.flow)((function*(e){try{const t=yield H({location:K,params:e});this.revisionsIndependent.set(e.hash,new W(t,this))}catch(e){throw console.log(e),e}})),this.fetchReferer=(0,M.flow)((function*(e){this.busyReferer=!0;try{const t=yield H({location:J,params:e});this.referer=t.items}catch(e){throw console.log(e),e}finally{this.busyReferer=!1}})),this.deleteAll=(0,M.flow)((function*(){this.busyConsent=!0;try{yield H({location:q}),this.applyPage(0),yield this.fetchAll()}catch(e){throw console.log(e),e}finally{this.busyConsent=!1}})),this.rootStore=e,(0,M.runInAction)((()=>{this.filters.context=this.rootStore.optionStore.others.context}))}applyPage(e){this.filters.page=e}applyDates(e){this.filters.dates=e}applyContext(e){this.filters.context=e}applyReferer(e){this.filters.referer=e}applyIp(e){this.filters.ip=e}applyUuid(e){this.filters.uuid=e}}(0,E.Cg)([M.observable],X.prototype,"busyConsent",void 0),(0,E.Cg)([M.observable],X.prototype,"busyReferer",void 0),(0,E.Cg)([M.observable],X.prototype,"count",void 0),(0,E.Cg)([M.observable],X.prototype,"truncatedIpsCount",void 0),(0,E.Cg)([M.observable],X.prototype,"perPage",void 0),(0,E.Cg)([M.observable],X.prototype,"offset",void 0),(0,E.Cg)([M.observable],X.prototype,"pageCollection",void 0),(0,E.Cg)([M.observable],X.prototype,"revisions",void 0),(0,E.Cg)([M.observable],X.prototype,"revisionsIndependent",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Array)],X.prototype,"referer",void 0),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object]),(0,E.Sn)("design:returntype",void 0)],X.prototype,"applyPage",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object]),(0,E.Sn)("design:returntype",void 0)],X.prototype,"applyDates",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object]),(0,E.Sn)("design:returntype",void 0)],X.prototype,"applyContext",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object]),(0,E.Sn)("design:returntype",void 0)],X.prototype,"applyReferer",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object]),(0,E.Sn)("design:returntype",void 0)],X.prototype,"applyIp",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object]),(0,E.Sn)("design:returntype",void 0)],X.prototype,"applyUuid",null);class Z extends s.AbstractPost{get templateModel(){var e;return rt.get.cookieStore.templatesBlocker.get(null==(e=this.data.meta)?void 0:e.presetId)}get rules(){var e;return null==(e=this.data)?void 0:e.meta.rules.split("\n")}get tcfVendors(){var e;return(null==(e=this.data)?void 0:e.meta.tcfVendors)?this.data.meta.tcfVendors.split(",").filter(Boolean).map(Number):[]}get tcfPurposes(){var e;return(null==(e=this.data)?void 0:e.meta.tcfPurposes)?this.data.meta.tcfPurposes.split(",").filter(Boolean).map(Number):[]}get services(){var e;return null==(e=this.data)?void 0:e.meta.services.split(",").filter(Boolean).map(Number)}get rootStore(){return this.collection.store.rootStore}get isUpdateAvailable(){for(const{post_id:e}of this.rootStore.optionStore.templateNeedsUpdate)if(e===this.data.id)return!0;return!1}constructor(e,t={}){super(e,t),(0,M.reaction)((()=>{var e;return null==(e=this.data)?void 0:e.usedTemplate}),(e=>(0,M.runInAction)((()=>{e&&rt.get.cookieStore.addBlockerTemplates([e])}))),{fireImmediately:!0})}setName(e){this.data.title.raw=e}setStatus(e){this.data.status=e}setDescription(e){this.data.content.raw=e}setMeta(e){this.data.meta=e}transformDataForPatch(){const e=super.transformDataForPatch();return{title:e.title,content:e.content,status:e.status,meta:e.meta,slug:e.title}}afterPatch(){this.collection.store.blockers.store.rootStore.optionStore.fetchCurrentRevision()}afterDelete(){this.collection.store.blockers.store.rootStore.optionStore.fetchCurrentRevision()}afterPersist(){this.collection.store.blockers.store.rootStore.optionStore.fetchCurrentRevision()}}(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Z.prototype,"templateModel",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Z.prototype,"rules",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Z.prototype,"tcfVendors",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Z.prototype,"tcfPurposes",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Z.prototype,"services",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Z.prototype,"rootStore",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Z.prototype,"isUpdateAvailable",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[String]),(0,E.Sn)("design:returntype",void 0)],Z.prototype,"setName",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof PostStatus?Object:PostStatus]),(0,E.Sn)("design:returntype",void 0)],Z.prototype,"setStatus",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[String]),(0,E.Sn)("design:returntype",void 0)],Z.prototype,"setDescription",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object]),(0,E.Sn)("design:returntype",void 0)],Z.prototype,"setMeta",null),Z=(0,E.Cg)([s.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:H,create:{path:"/rcb-blocker"},patch:{path:"/rcb-blocker/:id"},delete:{path:"/rcb-blocker/:id"}}),(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object,"undefined"==typeof Partial?Object:Partial])],Z);class Q extends s.AbstractPostCollection{constructor(e){super(),this.store=e}get sortedBlockers(){const e=Array.from(this.entries.values());return e.sort(((e,t)=>e.data.menu_order<t.data.menu_order?-1:e.data.menu_order>t.data.menu_order||e.key<t.key?1:e.key>t.key?-1:0)),e}instance(e){return new Z(this).fromResponse(e)}}(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Q.prototype,"sortedBlockers",null),Q=(0,E.Cg)([s.ClientCollection.annotate({path:"/rcb-blocker",singlePath:"/rcb-blocker/:id",namespace:"wp/v2",methods:[s.RouteHttpVerb.GET],request:H}),(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof CookieStore?Object:CookieStore])],Q);const ee={path:"/templates/blocker/:identifier",method:s.RouteHttpVerb.GET,obfuscatePath:"full"};class te{constructor(e,t){this.busy=!1,this.fetchUse=(0,M.flow)((function*(){try{this.busy=!0;const e=yield H({location:ee,params:{identifier:this.data.identifier}});return this.use=e,this.store.addServiceTemplates(e.consumerData.serviceTemplates),this.use}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,M.runInAction)((()=>{this.data=e})),this.store=t}}(0,E.Cg)([M.observable],te.prototype,"busy",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],te.prototype,"data",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type","undefined"==typeof ResponseRouteTemplatesBlockerUseGet?Object:ResponseRouteTemplatesBlockerUseGet)],te.prototype,"use",void 0);class oe extends s.AbstractPost{get templateModel(){var e;return rt.get.cookieStore.templatesServices.get(null==(e=this.data.meta)?void 0:e.presetId)}get rootStore(){return this.collection.store.collection.store.rootStore}get technicalDefinitions(){return JSON.parse(this.data.meta.technicalDefinitions||"[]")}get googleConsentModeConsentTypes(){return JSON.parse(this.data.meta.googleConsentModeConsentTypes||"[]")}get dataProcessingInCountries(){return JSON.parse(this.data.meta.dataProcessingInCountries||"[]")}get dataProcessingInCountriesSpecialTreatments(){return JSON.parse(this.data.meta.dataProcessingInCountriesSpecialTreatments||"[]")}get isUpdateAvailable(){for(const{post_id:e}of this.rootStore.optionStore.templateNeedsUpdate)if(e===this.data.id)return!0;return!1}get codeDynamics(){return JSON.parse(this.data.meta.codeDynamics||"{}")}constructor(e,t={}){super(e,t),(0,M.reaction)((()=>{var e;return null==(e=this.data)?void 0:e.usedTemplate}),(e=>(0,M.runInAction)((()=>{e&&rt.get.cookieStore.addServiceTemplates([e])}))),{fireImmediately:!0})}afterPatch(){const e=this.collection.store.collection,[t]=this.data["rcb-cookie-group"];e.entries.forEach((e=>{t!==e.key?e.cookies.entries.delete(this.key):e.cookies.entries.set(this.key,this)})),this.rootStore.optionStore.fetchCurrentRevision(),this.rootStore.cookieStore.unassignedCookies.delete(this.key)}setOrder(e){this.data.menu_order=e}setName(e){this.data.title.raw=e}setStatus(e){this.data.status=e}setPurpose(e){this.data.content.raw=e}setGroup(e){this.data["rcb-cookie-group"]=[e]}setMeta(e){this.data.meta=e}transformDataForPersist(){return{...super.transformDataForPersist(),"rcb-cookie-group":[this.collection.store.key]}}transformDataForPatch(){const e=super.transformDataForPatch();return{title:e.title,content:e.content,status:e.status,meta:e.meta,menu_order:e.menu_order,"rcb-cookie-group":this.data["rcb-cookie-group"],slug:e.title}}afterDelete(){this.collection.store.cookies.store.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterPersist(){const{optionStore:e,checklistStore:t}=this.collection.store.cookies.store.collection.store.rootStore;e.fetchCurrentRevision(),t.probablyFetchByChangedItem("add-cookie")}}(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],oe.prototype,"templateModel",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],oe.prototype,"rootStore",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",Array)],oe.prototype,"technicalDefinitions",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",Array)],oe.prototype,"googleConsentModeConsentTypes",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",Object)],oe.prototype,"dataProcessingInCountries",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",Object)],oe.prototype,"dataProcessingInCountriesSpecialTreatments",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],oe.prototype,"isUpdateAvailable",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype","undefined"==typeof Record?Object:Record)],oe.prototype,"codeDynamics",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],oe.prototype,"afterPatch",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Number]),(0,E.Sn)("design:returntype",void 0)],oe.prototype,"setOrder",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[String]),(0,E.Sn)("design:returntype",void 0)],oe.prototype,"setName",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof PostStatus?Object:PostStatus]),(0,E.Sn)("design:returntype",void 0)],oe.prototype,"setStatus",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[String]),(0,E.Sn)("design:returntype",void 0)],oe.prototype,"setPurpose",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Number]),(0,E.Sn)("design:returntype",void 0)],oe.prototype,"setGroup",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object]),(0,E.Sn)("design:returntype",void 0)],oe.prototype,"setMeta",null),oe=(0,E.Cg)([s.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:H,create:{path:"/rcb-cookie"},patch:{path:"/rcb-cookie/:id"},delete:{path:"/rcb-cookie/:id"}}),(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object,"undefined"==typeof Partial?Object:Partial])],oe);const ne={path:"/cookies/order",method:s.RouteHttpVerb.PUT};class se extends s.AbstractPostCollection{get sortedCookies(){const e=Array.from(this.entries.values());return e.sort(((e,t)=>e.data.menu_order<t.data.menu_order?-1:e.data.menu_order>t.data.menu_order||e.key<t.key?1:e.key>t.key?-1:0)),e}constructor(e){super(),this.orderCookies=(0,M.flow)((function*(e){this.busy=!0;try{yield H({location:ne,request:{ids:e}});let t=0;for(const o of e)this.entries.get(o).setOrder(t),t++}catch(e){throw console.log(e),e}finally{this.busy=!1}})),this.store=e}instance(e){return new oe(this).fromResponse(e)}}(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],se.prototype,"sortedCookies",null),se=(0,E.Cg)([s.ClientCollection.annotate({path:"/rcb-cookie",singlePath:"/rcb-cookie/:id",namespace:"wp/v2",methods:[s.RouteHttpVerb.GET],request:H}),(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof CookieGroupModel?Object:CookieGroupModel])],se);class ie extends s.AbstractCategory{get cookiesCount(){return this.fetchedAllCookies?this.cookies.entries.size:this.data.count}constructor(e,t={}){super(e,t),this.fetchedAllCookies=!1,this.fetchCookies=(0,M.flow)((function*(){yield this.cookies.get({request:{status:["draft","publish","private"]},params:{per_page:100,"rcb-cookie-group":this.key,context:"edit"}}),this.fetchedAllCookies=!0})),(0,M.runInAction)((()=>{this.cookies=new se(this)}))}setName(e){this.data.name=e}setDescription(e){this.data.description=e}setOrder(e){this.data.meta.order=e}afterDelete(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterPatch(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterPersist(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}}(0,E.Cg)([M.observable,(0,E.Sn)("design:type",void 0===se?Object:se)],ie.prototype,"cookies",void 0),(0,E.Cg)([M.observable],ie.prototype,"fetchedAllCookies",void 0),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],ie.prototype,"cookiesCount",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[String]),(0,E.Sn)("design:returntype",void 0)],ie.prototype,"setName",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[String]),(0,E.Sn)("design:returntype",void 0)],ie.prototype,"setDescription",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Number]),(0,E.Sn)("design:returntype",void 0)],ie.prototype,"setOrder",null),ie=(0,E.Cg)([s.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:H,create:{path:"/rcb-cookie-group"},patch:{path:"/rcb-cookie-group/:id"},delete:{path:"/rcb-cookie-group/:id"}}),(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof CookieGroupCollection?Object:CookieGroupCollection,"undefined"==typeof Partial?Object:Partial])],ie);const re={path:"/cookie-groups/order",method:s.RouteHttpVerb.PUT};class ae extends s.AbstractCategoryCollection{get sortedGroups(){const e=Array.from(this.entries.values());return e.sort(((e,t)=>e.data.meta.order<t.data.meta.order?-1:e.data.meta.order>t.data.meta.order?1:0)),e}constructor(e){super(),this.orderCookieGroups=(0,M.flow)((function*(e){this.busy=!0;try{yield H({location:re,request:{ids:e}});let t=0;for(const o of e)this.entries.get(o).setOrder(t),t++}catch(e){throw console.log(e),e}finally{this.busy=!1}})),this.store=e}instance(e){return new ie(this).fromResponse(e)}}(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],ae.prototype,"sortedGroups",null),ae=(0,E.Cg)([s.ClientCollection.annotate({path:"/rcb-cookie-group",singlePath:"/rcb-cookie-group/:id",namespace:"wp/v2",methods:[s.RouteHttpVerb.GET],request:H}),(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof CookieStore?Object:CookieStore])],ae);const ce={path:"/templates/services/:identifier",method:s.RouteHttpVerb.GET,obfuscatePath:"full"};class le{constructor(e,t){this.busy=!1,this.fetchUse=(0,M.flow)((function*(){try{this.busy=!0;const e=yield H({location:ce,params:{identifier:this.data.identifier}});return this.use=e,this.use}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,M.runInAction)((()=>{this.data=e})),this.store=t}}(0,E.Cg)([M.observable],le.prototype,"busy",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],le.prototype,"data",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type","undefined"==typeof ResponseRouteTemplatesServiceUseGet?Object:ResponseRouteTemplatesServiceUseGet)],le.prototype,"use",void 0);const de={path:"/cookies/unassigned",method:s.RouteHttpVerb.GET},pe={path:"/templates/blocker",method:s.RouteHttpVerb.GET},ue={path:"/templates/services",method:s.RouteHttpVerb.GET};class ge{get blockersCount(){return this.fetchedAllBlockers?this.blockers.entries.size:this.rootStore.optionStore.allBlockerCount}get cookiesCount(){return Array.from(this.groups.entries.values()).map((e=>{let{cookiesCount:t}=e;return t})).reduce(((e,t)=>e+t),0)}constructor(e){this.busy=!1,this.unassignedCookies=new Map,this.templatesBlocker=new Map,this.busyTemplatesBlocker=!1,this.templatesServices=new Map,this.busyTemplatesServices=!1,this.fetchedAllBlockers=!1,this.fetchGroups=(0,M.flow)((function*(){yield this.groups.get({params:{per_page:100}}),yield this.fetchUnassignedCookies()})),this.fetchUnassignedCookies=(0,M.flow)((function*(){try{const e=yield H({location:de});for(const t of Object.values(e))this.unassignedCookies.set(t.id,t)}catch(e){throw console.log(e),e}})),this.fetchBlockers=(0,M.flow)((function*(){yield this.blockers.get({request:{status:["draft","publish","private"]},params:{per_page:100,context:"edit"}}),this.fetchedAllBlockers=!0})),this.fetchTemplatesBlocker=(0,M.flow)((function*(e){this.busyTemplatesBlocker=!0;try{const{items:t}=yield H({location:pe,params:e});this.templatesBlocker.clear(),this.addBlockerTemplates(t)}catch(e){throw console.log(e),e}finally{this.busyTemplatesBlocker=!1}})),this.fetchTemplatesServices=(0,M.flow)((function*(e){this.busyTemplatesServices=!0;try{const{items:t}=yield H({location:ue,params:e});if(["redownload","invalidate"].indexOf(null==e?void 0:e.storage)>-1){const{activeLanguages:t,currentLanguage:o}=this.rootStore.optionStore.others;for(const n of t)n!==o&&(yield H({location:ue,params:{...e,_dataLocale:n}}))}this.templatesServices.clear(),this.addServiceTemplates(t)}catch(e){throw console.log(e),e}finally{this.busyTemplatesServices=!1}})),this.rootStore=e,(0,M.runInAction)((()=>{this.groups=new ae(this),this.blockers=new Q(this)}))}get essentialGroup(){if(0===this.groups.entries.size)return;const e=this.groups.entries.values();let t;for(;(t=e.next().value)&&!t.data.meta.isEssential;);return t}addBlockerTemplates(e){for(const t of e)this.templatesBlocker.set(t.identifier,new te(t,this))}addServiceTemplates(e){for(const t of e)this.templatesServices.set(t.identifier,new le(t,this))}}(0,E.Cg)([M.observable],ge.prototype,"busy",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",void 0===ae?Object:ae)],ge.prototype,"groups",void 0),(0,E.Cg)([M.observable],ge.prototype,"unassignedCookies",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",void 0===Q?Object:Q)],ge.prototype,"blockers",void 0),(0,E.Cg)([M.observable],ge.prototype,"templatesBlocker",void 0),(0,E.Cg)([M.observable],ge.prototype,"busyTemplatesBlocker",void 0),(0,E.Cg)([M.observable],ge.prototype,"templatesServices",void 0),(0,E.Cg)([M.observable],ge.prototype,"busyTemplatesServices",void 0),(0,E.Cg)([M.observable],ge.prototype,"fetchedAllBlockers",void 0),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],ge.prototype,"blockersCount",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],ge.prototype,"cookiesCount",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],ge.prototype,"essentialGroup",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Array]),(0,E.Sn)("design:returntype",void 0)],ge.prototype,"addBlockerTemplates",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Array]),(0,E.Sn)("design:returntype",void 0)],ge.prototype,"addServiceTemplates",null);class he{constructor(e,t){(0,M.runInAction)((()=>(0,M.set)(this,e))),this.store=t}static getIframeStore(){try{return document.querySelector("#customize-preview > iframe").contentWindow.realCookieBanner_customize_banner.RootStore.get}catch(e){return}}applyInUi(){if(!this.store.rootStore.optionStore.others.isPro&&this.needsPro)return!1;const e=(0,i.getSidebarCustomize)();return this.previewInUi(),setTimeout((()=>{this.store.presetDefaults.forEach(((t,o)=>{e(o).set(void 0===this.settings[o]?t:this.settings[o])}))}),100),!0}previewInUi(){const{presetDefaults:e}=this.store,{settings:t}=this.store.rootStore.optionStore.others.customizeIdsBanner,o=[];this.resetPreviewInUiSettings={};for(const n of Object.keys(t)){const s=t[n];for(const t of Object.keys(s)){const r=s[t];if(!e.has(r))continue;const a=(0,i.getSanitizedControlValue)(r,e.get(r));if(null!==a){this.resetPreviewInUiSettings[r]=[n,t,a];const s=Object.prototype.hasOwnProperty.call(this.settings,r)?this.settings[r]:e.get(r);o.push([n,t,s])}}}he.getIframeStore().customizeBannerStore.setBannerFromPreset(o)}resetPreviewInUi(){this.resetPreviewInUiSettings&&(he.getIframeStore().customizeBannerStore.setBannerFromPreset(Object.values(this.resetPreviewInUiSettings)),this.resetPreviewInUiSettings={})}}(0,E.Cg)([M.observable,(0,E.Sn)("design:type",String)],he.prototype,"id",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",String)],he.prototype,"name",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Boolean)],he.prototype,"needsPro",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",String)],he.prototype,"description",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type","undefined"==typeof Array?Object:Array)],he.prototype,"tags",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],he.prototype,"settings",void 0);const ye={path:"/presets/banner",method:s.RouteHttpVerb.GET};class be{constructor(e){this.visible=!1,this.individualPrivacyOpen=!1,this.previewCheckboxActiveState=!1,this.previewStickyMenuOpenState=!1,this.busyPresets=!1,this.presets=new Map,this.presetConstants=new Map,this.presetDefaults=new Map,this.debounceFromCustomize={},this.fetchPresets=(0,M.flow)((function*(){this.busyPresets=!0;try{const{defaults:e,constants:t,items:o}=yield H({location:ye});for(const t of Object.keys(e))this.presetDefaults.set(t,e[t]);for(const e of Object.keys(t))this.presetConstants.set(e,t[e]);for(const e of Object.keys(o))this.presets.set(e,new he({id:e,...o[e]},this))}catch(e){throw console.log(e),e}finally{this.busyPresets=!1}})),this.rootStore=e}setBannerFromCustomize(e,t,o,n){void 0===n&&(n=!0);const{customizeValuesBanner:s}=this.rootStore.optionStore.others,i=t.toString();if(n&&["css","animationInDuration","animationOutDuration"].indexOf(i)>-1)clearTimeout(this.debounceFromCustomize[i]),this.debounceFromCustomize[i]=setTimeout((()=>this.setBannerFromCustomize(e,t,o,!1)),500);else{const n=s[e][t];s[e][t]=o,i.startsWith("animationOut")&&n!==o&&this.forceAnimationOutSimulation()}}setBannerFromPreset(e){for(const t of e){const[e,o,n]=t;this.rootStore.optionStore.others.customizeValuesBanner[e][o]=n}}forceAnimationOutSimulation(){const{customizeValuesBanner:e}=this.rootStore.optionStore.others;"none"!==e.layout.animationOut&&(this.visible=!1,setTimeout((()=>(0,M.runInAction)((()=>{this.visible=!0}))),+e.layout.animationOutDuration+1e3))}setVisible(e){this.visible=e}setIndividualPrivacyOpen(e){this.individualPrivacyOpen=e}setPreviewCheckboxActiveState(e){this.previewCheckboxActiveState=e}setPreviewStickyMenuOpenState(e){this.previewStickyMenuOpenState=e}exportPhp(){const e={},t=(0,i.getSidebarCustomize)();return this.presetDefaults.forEach(((o,n)=>{let s=t(n).get();"boolean"==typeof o?s=!!+s:isNaN(s)||""===s||(s=+s),JSON.stringify(o)!==JSON.stringify(s)&&(e[this.presetConstants.get(n)]=s)})),this.jsonToPHPArray(e)}jsonToPHPArray(e){const t=JSON.stringify(e,null,4).split("\n");return t.shift(),t.pop(),t.join("\n").replace(/^(\s+)"([A-Za-z\\]+::[A-Z_]+)"(:)/gm,"$1$2 =>").replace(/^(\s+)([A-Za-z\\]+)::/gm,((e,t,o)=>`${t}${o.replace(/\\\\/gm,"\\")}::`))}}(0,E.Cg)([M.observable],be.prototype,"visible",void 0),(0,E.Cg)([M.observable],be.prototype,"individualPrivacyOpen",void 0),(0,E.Cg)([M.observable],be.prototype,"previewCheckboxActiveState",void 0),(0,E.Cg)([M.observable],be.prototype,"previewStickyMenuOpenState",void 0),(0,E.Cg)([M.observable],be.prototype,"busyPresets",void 0),(0,E.Cg)([M.observable],be.prototype,"presets",void 0),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof T?Object:T,"undefined"==typeof P?Object:P,Object,void 0]),(0,E.Sn)("design:returntype",void 0)],be.prototype,"setBannerFromCustomize",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof Array?Object:Array]),(0,E.Sn)("design:returntype",void 0)],be.prototype,"setBannerFromPreset",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],be.prototype,"forceAnimationOutSimulation",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Boolean]),(0,E.Sn)("design:returntype",void 0)],be.prototype,"setVisible",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Boolean]),(0,E.Sn)("design:returntype",void 0)],be.prototype,"setIndividualPrivacyOpen",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Boolean]),(0,E.Sn)("design:returntype",void 0)],be.prototype,"setPreviewCheckboxActiveState",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Boolean]),(0,E.Sn)("design:returntype",void 0)],be.prototype,"setPreviewStickyMenuOpenState",null);class ve extends s.AbstractPost{get rootStore(){return this.collection.store.rootStore}constructor(e,t={}){super(e,t)}setOrder(e){this.data.menu_order=e}setLabel(e){this.data.title.raw=e}setStatus(e){this.data.status=e}setMeta(e){this.data.meta=e}transformDataForPatch(){const e=super.transformDataForPatch();return{title:e.title,content:"",status:e.status,meta:e.meta,menu_order:e.menu_order,slug:e.title}}}(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],ve.prototype,"rootStore",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Number]),(0,E.Sn)("design:returntype",void 0)],ve.prototype,"setOrder",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[String]),(0,E.Sn)("design:returntype",void 0)],ve.prototype,"setLabel",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof PostStatus?Object:PostStatus]),(0,E.Sn)("design:returntype",void 0)],ve.prototype,"setStatus",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object]),(0,E.Sn)("design:returntype",void 0)],ve.prototype,"setMeta",null),ve=(0,E.Cg)([s.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:H,create:{path:"/rcb-banner-link"},patch:{path:"/rcb-banner-link/:id"},delete:{path:"/rcb-banner-link/:id"}}),(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object,"undefined"==typeof Partial?Object:Partial])],ve);const me={path:"/banner-links/order",method:s.RouteHttpVerb.PUT};class fe extends s.AbstractPostCollection{get sortedBannerLinks(){const e=Array.from(this.entries.values());return e.sort(((e,t)=>e.data.menu_order<t.data.menu_order?-1:e.data.menu_order>t.data.menu_order||e.key<t.key?1:e.key>t.key?-1:0)),e}constructor(e){super(),this.orderCookies=(0,M.flow)((function*(e){this.busy=!0;try{yield H({location:me,request:{ids:e}});let t=0;for(const o of e)this.entries.get(o).setOrder(t),t++}catch(e){throw console.log(e),e}finally{this.busy=!1}})),this.store=e}instance(e){return new ve(this).fromResponse(e)}}(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],fe.prototype,"sortedBannerLinks",null),fe=(0,E.Cg)([s.ClientCollection.annotate({path:"/rcb-banner-link",singlePath:"/rcb-banner-link/:id",namespace:"wp/v2",methods:[s.RouteHttpVerb.GET],request:H}),(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof OptionStore?Object:OptionStore])],fe);const Se={path:"/country-bypass/database",method:s.RouteHttpVerb.PUT},Ce={path:"/migration/:migration",method:s.RouteHttpVerb.DELETE},ke={path:"/nav-menu/add-links",method:s.RouteHttpVerb.POST},we={path:"/revision/current",method:s.RouteHttpVerb.GET},xe={path:"/revision/current",method:s.RouteHttpVerb.PUT},je={path:"/settings",method:s.RouteHttpVerb.GET},Oe={path:"/settings",method:s.RouteHttpVerb.PATCH};class Ae extends s.BaseOptions{get isOnlyRcbCookieCreated(){return!(1!==this.allCookieCount||this.isTcf&&this.allTcfVendorConfigurationCount)}get areSettingsFetched(){return void 0!==this.isRespectDoNotTrack}constructor(e){super(),this.busySettings=!1,this.busyCountryBypassUpdate=!1,this.busyAddLinksToNavigationMenu=!1,this.needsRevisionRetrigger=!1,this.fetchedBannerLinks=!1,this.publicCookieCount=0,this.allCookieCount=0,this.allBlockerCount=0,this.allTcfVendorConfigurationCount=0,this.allScannerResultTemplatesCount=0,this.allScannerResultExternalUrlsCount=0,this.cookieCounts={draft:0,private:0,publish:0},this.cloudReleaseInfo={blocker:null,service:null},this.navMenus=[],this.templateNeedsUpdate=[],this.googleConsentModeNoticesHtml=[],this.servicesDataProcessingInUnsafeCountriesNoticeHtml="",this.servicesWithEmptyPrivacyPolicyNoticeHtml="",this.createdTagManagers={gtm:[],mtm:[]},this.contexts={"":""},this.isBannerActive=!1,this.isBlockerActive=!1,this.hidePageIds=[],this.forwardTo=[],this.countryBypassCountries=[],this.isTcf=!1,this.isGcm=!1,this.isGcmShowRecommandationsWithoutConsent=!1,this.isGcmCollectAdditionalDataViaUrlParameters=!1,this.isGcmRedactAdsDataWithoutConsent=!0,this.isGcmListPurposes=!0,this.bannerlessConsentChecks={essential:[],legalBasisConsentWithoutVisualContentBlocker:[],legalBasisLegitimateInterest:[]},this.isBannerStickyLinksEnabled=!1,this.fetchSettings=(0,M.flow)((function*(e){this.busySettings=!0;try{const t=e||(yield H({location:je}));this.isBannerActive=t["rcb-banner-active"],this.isBlockerActive=t["rcb-blocker-active"],this.hidePageIds=(t["rcb-hide-page-ids"]||"").split(",").map(Number).filter(Boolean),this.setCookiesViaManager=t["rcb-set-cookies-via-manager"]||"none",this.operatorCountry=t["rcb-operator-country"],this.operatorContactAddress=t["rcb-operator-contact-address"],this.operatorContactPhone=t["rcb-operator-contact-phone"],this.operatorContactEmail=t["rcb-operator-contact-email"],this.operatorContactFormId=t["rcb-operator-contact-form-id"],this.cookiePolicyId=t["rcb-cookie-policy-id"],this.territorialLegalBasis=t["rcb-territorial-legal-basis"].split(","),this.isAcceptAllForBots=t["rcb-accept-all-for-bots"],this.isRespectDoNotTrack=t["rcb-respect-do-not-track"],this.isBannerLessConsent=t["rcb-banner-less-consent"],this.bannerLessConsentShowOnPageIds=(t["rcb-banner-less-show-on-page-ids"]||"").split(",").map(Number).filter(Boolean),this.cookieDuration=t["rcb-cookie-duration"],this.failedConsentDocumentationHandling=t["rcb-failed-consent-documentation-handling"],this.isSaveIp=t["rcb-save-ip"],this.isDataProcessingInUnsafeCountries=t["rcb-data-processing-in-unsafe-countries"],this.isAgeNotice=t["rcb-age-notice"],this.ageNoticeAgeLimit=t["rcb-age-notice-age-limit"],this.isListServicesNotice=t["rcb-list-services-notice"],this.isConsentForwarding=t["rcb-consent-forwarding"]||!1,this.forwardTo=(t["rcb-forward-to"]||"").split("|").filter(Boolean),this.crossDomains=t["rcb-cross-domains"]||"",this.isCountryBypass=t["rcb-country-bypass"],this.countryBypassCountries=(t["rcb-country-bypass-countries"]||"").split(",").filter(Boolean),this.countryBypassType=t["rcb-country-bypass-type"],this.countryBypassDbDownloadTime=t["rcb-country-bypass-db-download-time"],this.isTcf=t["rcb-tcf"],this.tcfAcceptedTime=t["rcb-tcf-accepted-time"],this.tcfGvlDownloadTime=t["rcb-tcf-gvl-download-time"],this.consentDuration=t["rcb-consent-duration"],yield this.fetchCurrentRevision()}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.fetchBannerLinks=(0,M.flow)((function*(){yield this.bannerLinks.get({request:{status:["draft","publish","private"]},params:{per_page:100,context:"edit"}}),this.fetchedBannerLinks=!0})),this.updateSettings=(0,M.flow)((function*(e,t){let{isBannerActive:o,isBlockerActive:n,hidePageIds:s,setCookiesViaManager:i,operatorCountry:r,operatorContactAddress:a,operatorContactEmail:c,operatorContactFormId:l,operatorContactPhone:d,cookiePolicyId:p,territorialLegalBasis:u,isAcceptAllForBots:g,isRespectDoNotTrack:h,cookieDuration:y,failedConsentDocumentationHandling:b,isSaveIp:v,isDataProcessingInUnsafeCountries:m,isAgeNotice:f,isBannerLessConsent:S,bannerLessConsentShowOnPageIds:C,ageNoticeAgeLimit:k,isListServicesNotice:w,isConsentForwarding:x,forwardTo:j,crossDomains:O,affiliateLink:A,affiliateLabelBehind:P,affiliateLabelDescription:F,isCountryBypass:B,countryBypassCountries:T,countryBypassType:I,isTcf:R,isGcm:_,isGcmShowRecommandationsWithoutConsent:E,isGcmCollectAdditionalDataViaUrlParameters:M,isGcmRedactAdsDataWithoutConsent:D,isGcmListPurposes:U,consentDuration:N,isBannerStickyLinksEnabled:L}=e;this.busySettings=!0;try{const e=yield H({location:Oe,request:{...void 0===o?{}:{"rcb-banner-active":o},...void 0===n?{}:{"rcb-blocker-active":n},...void 0===s?{}:{"rcb-hide-page-ids":s.join(",")},...void 0===i?{}:{"rcb-set-cookies-via-manager":i},...void 0===r?{}:{"rcb-operator-country":r},...void 0===a?{}:{"rcb-operator-contact-address":a},...void 0===d?{}:{"rcb-operator-contact-phone":d},...void 0===c?{}:{"rcb-operator-contact-email":c},...void 0===l?{}:{"rcb-operator-contact-form-id":l},...void 0===p?{}:{"rcb-cookie-policy-id":p},...void 0===u?{}:{"rcb-territorial-legal-basis":u.join(",")},...void 0===g?{}:{"rcb-accept-all-for-bots":g},...void 0===S?{}:{"rcb-banner-less-consent":S},...void 0===C?{}:{"rcb-banner-less-show-on-page-ids":C.join(",")},...void 0===h?{}:{"rcb-respect-do-not-track":h},...void 0===y?{}:{"rcb-cookie-duration":y},...void 0===b?{}:{"rcb-failed-consent-documentation-handling":b},...void 0===v?{}:{"rcb-save-ip":v},...void 0===m?{}:{"rcb-data-processing-in-unsafe-countries":m},...void 0===f?{}:{"rcb-age-notice":f},...void 0===k?{}:{"rcb-age-notice-age-limit":k},...void 0===w?{}:{"rcb-list-services-notice":w},...void 0===x?{}:{"rcb-consent-forwarding":x},...void 0===j?{}:{"rcb-forward-to":j.join("|")},...void 0===O?{}:{"rcb-cross-domains":O},...void 0===B?{}:{"rcb-country-bypass":B},...void 0===T?{}:{"rcb-country-bypass-countries":T.join(",")},...void 0===I?{}:{"rcb-country-bypass-type":I},...void 0===R?{}:{"rcb-tcf":R},...void 0===N?{}:{"rcb-consent-duration":N}}});if(this.fetchedBannerLinks&&t){const e=this.bannerLinks.sortedBannerLinks;for(const o of e)t.find((e=>{let{id:t}=e;return o.data.id===t}))||(yield o.delete());for(let o=0;o<t.length;o++){const{isExternalUrl:n,label:s,pageType:i,externalUrl:r,hideCookieBanner:a,id:c,pageId:l,isTargetBlank:d}=t[o],p={isExternalUrl:n,pageType:i,externalUrl:r,hideCookieBanner:a,pageId:l,isTargetBlank:d};if(c){const t=e.find((e=>{let{data:{id:t}}=e;return c===t}));if(t){const{data:{title:{raw:e},menu_order:c,meta:{isExternalUrl:d,pageType:u,externalUrl:g,hideCookieBanner:h,pageId:y}}}=t;e===s&&c===o&&d===n&&u===i&&y===l&&g===r&&h===a||(t.setLabel(s),t.setOrder(o),t.setMeta(p),yield t.patch())}}else{const e=new ve(this.bannerLinks,{title:{raw:s},content:{raw:"",protected:!1},status:"publish",menu_order:o,meta:p});yield e.persist()}}}this.fetchSettings(e),this.rootStore.checklistStore.fetchChecklist()}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.fetchCurrentRevision=(0,M.flow)((function*(){this.busySettings=!0;try{this.setFromCurrentRevision(yield H({location:we}))}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.updateCurrentRevision=(0,M.flow)((function*(e){this.busySettings=!0;try{this.setFromCurrentRevision(yield H({location:xe,request:e}))}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.setModalHintSeen=(0,M.flow)((function*(e){this.busySettings=!0;try{this.others.modalHints.push(e),yield H({location:s.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:`modal-hint-${e}`},request:{value:!0}})}catch(e){throw console.log(e),e}finally{this.busySettings=!1}})),this.dismissConfigProNotice=(0,M.flow)((function*(){try{this.others.isConfigProNoticeVisible=!1,yield H({location:s.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:"get-pro-main-button"},request:{value:!0}})}catch(e){throw console.log(e),e}})),this.dismissServiceDataProcessingInUnsafeCountriesNotice=(0,M.flow)((function*(){try{this.servicesDataProcessingInUnsafeCountriesNoticeHtml="",yield H({location:s.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:"service-data-processing-in-unsafe-countries"},request:{value:!1}})}catch(e){throw console.log(e),e}})),this.dismissBannerlessConsentLegitimateServicesNotice=(0,M.flow)((function*(){try{yield H({location:s.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:"dismissed-bannerless-consent-legint-services"},request:{value:!1}}),yield this.fetchCurrentRevision()}catch(e){throw console.log(e),e}})),this.dismissBannerlessConsentServicesWithoutVisualContentBlockerNotice=(0,M.flow)((function*(){try{yield H({location:s.locationRestKeyValueMapPatch,params:{name:"rcb-notice-states",key:"dismissed-bannerless-consent-services-without-visual-content-blocker"},request:{value:!1}}),yield this.fetchCurrentRevision()}catch(e){throw console.log(e),e}})),this.dismissMigration=(0,M.flow)((function*(){try{const{id:e}=this.dashboardMigration;this.dashboardMigration=void 0,yield H({location:Ce,params:{migration:e}})}catch(e){throw console.log(e),e}})),this.addLinksToNavigationMenu=(0,M.flow)((function*(e){this.busyAddLinksToNavigationMenu=!0;try{const{success:t}=yield H({location:ke,request:{id:e}});return t&&(this.rootStore.checklistStore.fetchChecklist(),yield this.fetchCurrentRevision()),t}catch(e){throw console.log(e),e}finally{this.busyAddLinksToNavigationMenu=!1}})),this.updateCountryBypassDatabase=(0,M.flow)((function*(){this.busyCountryBypassUpdate=!0;try{const{dbDownloadTime:e}=yield H({location:Se});this.countryBypassDbDownloadTime=e}catch(e){throw console.log(e),e}finally{this.busyCountryBypassUpdate=!1}})),this.rootStore=e,this.pureSlug=s.BaseOptions.getPureSlug("real-cookie-banner"),this.pureSlugCamelCased=s.BaseOptions.getPureSlug("real-cookie-banner",!0),(0,M.runInAction)((()=>{Object.assign(this,window[this.pureSlugCamelCased]),this.bannerLinks=new fe(this),this.fomoCoupon=this.others.fomoCoupon}))}setFromCurrentRevision(e){let{contexts:t,created_tag_managers:o,needs_retrigger:n,public_cookie_count:s,all_cookie_count:i,all_blocker_count:r,all_tcf_vendor_configuration_count:a,all_scanner_result_templates_count:c,all_scanner_result_external_urls_count:l,cookie_counts:d,cloud_release_info:p,consents_deleted_at:u,bannerless_consent_checks:g,nav_menus:h,tcf_vendor_configuration_counts:y,dashboard_migration:b,fomo_coupon:v,template_needs_update:m,check_saving_consent_via_rest_api_endpoint_working_html:f,template_update_notice_html:S,template_successors_notice_html:C,google_consent_mode_notices_html:k,services_data_processing_in_unsafe_countries_notice_html:w,services_with_empty_privacy_policy_notice_html:x}=e;this.createdTagManagers=o,this.needsRevisionRetrigger=n,this.publicCookieCount=s,this.allCookieCount=i,this.allBlockerCount=r,this.allTcfVendorConfigurationCount=a,this.allScannerResultTemplatesCount=c,this.allScannerResultExternalUrlsCount=l,this.templateNeedsUpdate=m,this.templateUpdateNoticeHtml=S,this.checkSavingConsentViaRestApiEndpointWorkingHtml=f,this.templateSuccessorsNoticeHtml=C,this.googleConsentModeNoticesHtml=k,this.servicesDataProcessingInUnsafeCountriesNoticeHtml=w,this.servicesWithEmptyPrivacyPolicyNoticeHtml=x,this.cookieCounts=d,this.cloudReleaseInfo=p,this.consentsDeletedAt=u,this.bannerlessConsentChecks=g,this.navMenus=h,this.tcfVendorConfigurationCounts=y,this.contexts=t,this.dashboardMigration=b,this.fomoCoupon=v}setShowLicenseFormImmediate(e,t){this.others.showLicenseFormImmediate=e,this.others.isLicensed=t}}let Pe;function Fe(){return Pe||(Pe=(0,s.createLocalizationFactory)(s.BaseOptions.getPureSlug("real-cookie-banner")))}(0,E.Cg)([M.observable],Ae.prototype,"busySettings",void 0),(0,E.Cg)([M.observable],Ae.prototype,"busyCountryBypassUpdate",void 0),(0,E.Cg)([M.observable],Ae.prototype,"busyAddLinksToNavigationMenu",void 0),(0,E.Cg)([M.observable],Ae.prototype,"needsRevisionRetrigger",void 0),(0,E.Cg)([M.observable],Ae.prototype,"fetchedBannerLinks",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",void 0===fe?Object:fe)],Ae.prototype,"bannerLinks",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"publicCookieCount",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"allCookieCount",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"allBlockerCount",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"allTcfVendorConfigurationCount",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"allScannerResultTemplatesCount",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"allScannerResultExternalUrlsCount",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"cookieCounts",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"cloudReleaseInfo",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"navMenus",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"tcfVendorConfigurationCounts",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"templateNeedsUpdate",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"checkSavingConsentViaRestApiEndpointWorkingHtml",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"templateUpdateNoticeHtml",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"templateSuccessorsNoticeHtml",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"googleConsentModeNoticesHtml",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"servicesDataProcessingInUnsafeCountriesNoticeHtml",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"servicesWithEmptyPrivacyPolicyNoticeHtml",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"createdTagManagers",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"contexts",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"dashboardMigration",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"fomoCoupon",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isBannerActive",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isBlockerActive",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"hidePageIds",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"setCookiesViaManager",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"operatorCountry",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"operatorContactAddress",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"operatorContactPhone",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"operatorContactEmail",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"operatorContactFormId",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"cookiePolicyId",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"territorialLegalBasis",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isAcceptAllForBots",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isRespectDoNotTrack",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isBannerLessConsent",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"bannerLessConsentShowOnPageIds",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"cookieDuration",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"failedConsentDocumentationHandling",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isSaveIp",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isDataProcessingInUnsafeCountries",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isAgeNotice",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"ageNoticeAgeLimit",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isListServicesNotice",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isConsentForwarding",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"forwardTo",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"crossDomains",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"affiliateLink",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"affiliateLabelBehind",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"affiliateLabelDescription",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isCountryBypass",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"countryBypassCountries",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"countryBypassType",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"countryBypassDbDownloadTime",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isTcf",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"tcfAcceptedTime",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"tcfGvlDownloadTime",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isGcm",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isGcmShowRecommandationsWithoutConsent",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isGcmCollectAdditionalDataViaUrlParameters",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isGcmRedactAdsDataWithoutConsent",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isGcmListPurposes",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"consentDuration",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"consentsDeletedAt",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"bannerlessConsentChecks",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ae.prototype,"isBannerStickyLinksEnabled",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type","undefined"==typeof OtherOptions?Object:OtherOptions)],Ae.prototype,"others",void 0),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ae.prototype,"isOnlyRcbCookieCreated",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ae.prototype,"areSettingsFetched",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof ResponseRouteRevisionCurrentGet?Object:ResponseRouteRevisionCurrentGet]),(0,E.Sn)("design:returntype",void 0)],Ae.prototype,"setFromCurrentRevision",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Boolean,Boolean]),(0,E.Sn)("design:returntype",void 0)],Ae.prototype,"setShowLicenseFormImmediate",null);const Be=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return Fe()._n(...t)},Te=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return Fe()._x(...t)},Ie=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return Fe().__(...t)},Re=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return Fe()._i(...t)},_e={path:"/scanner/result/ignore",method:s.RouteHttpVerb.POST};class Ee{get identifier(){return this.data.host}get inactive(){return"full"===this.blockedStatus||this.data.ignored}get blockedStatus(){const{foundCount:e,blockedCount:t}=this.data;return 0===t?"none":e===t?"full":"partial"}get blockedStatusText(){switch(this.blockedStatus){case"full":return Ie("Fully blocked");case"partial":return Ie("Partially blocked");default:return Ie("Not blocked")}}constructor(e,t){this.busy=!1,this.ignore=(0,M.flow)((function*(e){this.busy=!0;try{yield H({location:_e,request:{type:"host",value:this.data.host,ignored:e}}),this.data.ignored=e}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,M.runInAction)((()=>{this.data=e})),this.store=t}}function Me(e,t,o){if(void 0===t&&(t=50),void 0===o&&(o="..."),!e||e.length<=t)return e;const n=t-o.length,s=Math.ceil(n/2),i=Math.floor(n/2);return e.substr(0,s)+o+e.substr(e.length-i)}(0,E.Cg)([M.observable],Ee.prototype,"busy",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ee.prototype,"data",void 0),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ee.prototype,"identifier",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ee.prototype,"inactive",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ee.prototype,"blockedStatus",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ee.prototype,"blockedStatusText",null);class De{get markup(){return this.store.resultMarkup.get(this.data.id)}get blockedUrlTruncate(){return Me(this.data.blockedUrl,50,"[...]")}get sourceUrlTruncate(){return Me(this.data.sourceUrl,50,"[...]")}constructor(e,t){this.busy=!1,this.fetchMarkup=(0,M.flow)((function*(){yield this.store.fetchMarkup(this.data.id)})),(0,M.runInAction)((()=>{this.data=e})),this.store=t}}(0,E.Cg)([M.observable],De.prototype,"busy",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],De.prototype,"data",void 0),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],De.prototype,"markup",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],De.prototype,"blockedUrlTruncate",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],De.prototype,"sourceUrlTruncate",null);class He{get identifier(){return this.data.identifier}get type(){return this.templateModel instanceof le?"service":"blocker"}get inactive(){return this.data.consumerData.isCreated||this.data.consumerData.isIgnored}constructor(e,t){this.busy=!1,this.ignore=(0,M.flow)((function*(e){this.busy=!0;try{yield H({location:_e,request:{type:"template",value:this.identifier,ignored:e}}),this.data.consumerData.isIgnored=e}catch(e){throw console.log(e),e}finally{this.busy=!1}})),(0,M.runInAction)((()=>{this.data=e})),this.store=t;const{cookieStore:o}=t.rootStore;Object.hasOwn(e,"rules")?(o.addBlockerTemplates([e]),this.templateModel=o.templatesBlocker.get(e.identifier)):(o.addServiceTemplates([e]),this.templateModel=o.templatesServices.get(e.identifier))}}(0,E.Cg)([M.observable],He.prototype,"busy",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],He.prototype,"data",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],He.prototype,"templateModel",void 0),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],He.prototype,"identifier",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],He.prototype,"type",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],He.prototype,"inactive",null);const Ue={path:"/scanner/queue",method:s.RouteHttpVerb.POST},Ne={path:"/scanner/result/externals/:type/:identifier",method:s.RouteHttpVerb.GET,obfuscatePath:"full"},Le={path:"/scanner/result/externals",method:s.RouteHttpVerb.GET},Ve={path:"/scanner/result/markup/:id",method:s.RouteHttpVerb.GET},ze={path:"/scanner/result/templates",method:s.RouteHttpVerb.GET};class Ge{get sortedTemplates(){const e=Array.from(this.resultTemplates.values());return e.sort(((e,t)=>(e.data.consumerData.isIgnored,t.data.consumerData.isIgnored,e.data.headline.localeCompare(t.data.headline)))),e}get sortedExternalUrls(){const e=Array.from(this.resultExternalUrls.values());return e.sort(((e,t)=>e.inactive===t.inactive?0:e.inactive?1:-1)),e}get templatesCount(){return this.fetchedAllResultTemplates?this.resultTemplates.size:this.rootStore.optionStore.allScannerResultTemplatesCount}get externalUrlsCount(){return this.fetchedAllResultExternalUrls?this.resultExternalUrls.size:this.rootStore.optionStore.allScannerResultExternalUrlsCount}get canShowResults(){var e;return this.templatesCount+this.externalUrlsCount>0&&(null==(e=this.rootStore.checklistStore.checklist)?void 0:e.items.scanner.checked)&&this.rootStore.optionStore.others.isLicensed}get foundScanResultsCount(){return this.resultTemplates.size+this.resultExternalUrls.size}get needsAttentionCount(){return[...this.resultTemplates.values(),...this.resultExternalUrls.values()].filter((e=>{let{inactive:t}=e;return!t})).length}constructor(e){this.resultTemplates=new Map,this.busyResultTemplates=!1,this.fetchedAllResultTemplates=!1,this.resultExternalUrls=new Map,this.resultAllExternalUrls=new Map,this.busyExternalUrls=!1,this.fetchedAllResultExternalUrls=!1,this.busyMarkup=!1,this.resultMarkup=new Map,this.addUrlsToQueue=(0,M.flow)((function*(e){return yield H({location:Ue,request:e})})),this.fetchResultTemplates=(0,M.flow)((function*(){this.busyResultTemplates=!0;try{this.resultTemplatesFromResponse(yield H({location:ze})),this.fetchedAllResultTemplates=!0}catch(e){throw console.log(e),e}finally{this.busyResultTemplates=!1}})),this.fetchResultExternals=(0,M.flow)((function*(){this.busyExternalUrls=!0;try{this.resultExternalUrlsFromResponse(yield H({location:Le})),this.fetchedAllResultExternalUrls=!0}catch(e){throw console.log(e),e}finally{this.busyExternalUrls=!1}})),this.fetchResultAllExternals=(0,M.flow)((function*(e){const t=e instanceof Ee?"host":"template",{identifier:o}=e;e.busy=!0;try{const{items:e}=yield H({location:Ne,params:{type:t,identifier:"host"===t?o.replace(/\./g,"_"):o}});let n=this.resultAllExternalUrls.get(o);if(n){const t=e.map((e=>{let{id:t}=e;return t}));for(const e of n.keys())-1===t.indexOf(e)&&n.delete(e)}else n=new Map;for(const t of Object.values(e))n.set(t.id,new De(t,this)),this.resultAllExternalUrls.set(o,n)}catch(e){throw console.log(e),e}finally{e.busy=!1}})),this.fetchMarkup=(0,M.flow)((function*(e){this.busyMarkup=!0;try{const t=yield H({location:Ve,params:{id:e}});this.resultMarkup.set(e,t)}catch(e){throw console.log(e),e}finally{this.busyMarkup=!1}})),this.rootStore=e}resultTemplatesFromResponse(e){let{items:t}=e;const o=Object.keys(t);for(const e of this.resultTemplates.keys())-1===o.indexOf(e)&&this.resultTemplates.delete(e);for(const e of o)this.resultTemplates.set(e,new He(t[e],this))}resultExternalUrlsFromResponse(e){let{items:t}=e;const o=Object.keys(t);for(const e of this.resultExternalUrls.keys())-1===o.indexOf(e)&&this.resultExternalUrls.delete(e);for(const e of o){const o=this.resultExternalUrls.get(e);o?(0,M.set)(o,{data:t[e]}):this.resultExternalUrls.set(e,new Ee(t[e],this))}}}(0,E.Cg)([M.observable],Ge.prototype,"resultTemplates",void 0),(0,E.Cg)([M.observable],Ge.prototype,"busyResultTemplates",void 0),(0,E.Cg)([M.observable],Ge.prototype,"fetchedAllResultTemplates",void 0),(0,E.Cg)([M.observable],Ge.prototype,"resultExternalUrls",void 0),(0,E.Cg)([M.observable],Ge.prototype,"resultAllExternalUrls",void 0),(0,E.Cg)([M.observable],Ge.prototype,"busyExternalUrls",void 0),(0,E.Cg)([M.observable],Ge.prototype,"fetchedAllResultExternalUrls",void 0),(0,E.Cg)([M.observable],Ge.prototype,"busyMarkup",void 0),(0,E.Cg)([M.observable],Ge.prototype,"resultMarkup",void 0),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ge.prototype,"sortedTemplates",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ge.prototype,"sortedExternalUrls",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ge.prototype,"templatesCount",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ge.prototype,"externalUrlsCount",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ge.prototype,"canShowResults",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ge.prototype,"foundScanResultsCount",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Ge.prototype,"needsAttentionCount",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof ResponseRouteScannerResultTemplatesGet?Object:ResponseRouteScannerResultTemplatesGet]),(0,E.Sn)("design:returntype",void 0)],Ge.prototype,"resultTemplatesFromResponse",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof ResponseRouteScannerResultExternalsGet?Object:ResponseRouteScannerResultExternalsGet]),(0,E.Sn)("design:returntype",void 0)],Ge.prototype,"resultExternalUrlsFromResponse",null);const We=moment;var qe=o.n(We);class $e extends s.BaseOptions{constructor(e){super(),this.busyStats={main:!1,buttonClicked:!1,customBypass:!1},this.stats=M.observable.object({main:void 0,buttonsClicked:void 0,customBypass:void 0},{},{deep:!1}),this.filters=M.observable.object({dates:void 0,context:void 0},{},{deep:!1}),this.fetchMain=(0,M.flow)((function*(){throw new Error("This feature is not available in the free version.")})),this.fetchButtonsClicked=(0,M.flow)((function*(){throw new Error("This feature is not available in the free version.")})),this.fetchCustomBypass=(0,M.flow)((function*(){throw new Error("This feature is not available in the free version.")})),this.rootStore=e,(0,M.runInAction)((()=>{this.filters.dates=[qe()().subtract(30,"days"),qe()()],this.filters.context=this.rootStore.optionStore.others.context}))}applyDates(e){this.filters.dates=e}applyContext(e){this.filters.context=e}}(0,E.Cg)([M.observable],$e.prototype,"busyStats",void 0),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object]),(0,E.Sn)("design:returntype",void 0)],$e.prototype,"applyDates",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object]),(0,E.Sn)("design:returntype",void 0)],$e.prototype,"applyContext",null);class Je{constructor(e,t){(0,M.runInAction)((()=>{this.data=e})),this.store=t}}(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Je.prototype,"data",void 0);class Ye{constructor(e,t,o){(0,M.runInAction)((()=>{this.special=t,this.data=e})),this.store=o}}(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ye.prototype,"data",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Boolean)],Ye.prototype,"special",void 0);class Ke{constructor(e,t,o){(0,M.runInAction)((()=>{this.special=t,this.data=e})),this.store=o}}(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Ke.prototype,"data",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Boolean)],Ke.prototype,"special",void 0);class Xe{get vendorConfiguration(){for(const t of this.store.vendorConfigurations.entries.values()){var e;if((null==(e=t.vendorModel)?void 0:e.data.id)===this.data.id)return t}}get restrictivePurposes(){const e={normal:{}};for(const t of[...this.legIntPurposes,...this.purposes])e.normal[t.data.id.toString()]={enabled:!0,legInt:this.legIntPurposes.indexOf(t)>-1&&!t.special?"yes":"no"};return e}get purposes(){var e;return null==(e=this.data)?void 0:e.purposes.map((e=>this.store.purposes.get(`${e}`)))}get legIntPurposes(){var e;return null==(e=this.data)?void 0:e.legIntPurposes.map((e=>this.store.purposes.get(`${e}`)))}get specialPurposes(){var e;return null==(e=this.data)?void 0:e.specialPurposes.map((e=>this.store.specialPurposes.get(`${e}`)))}get features(){var e;return null==(e=this.data)?void 0:e.features.map((e=>this.store.features.get(`${e}`)))}get specialFeatures(){var e;return null==(e=this.data)?void 0:e.specialFeatures.map((e=>this.store.specialFeatures.get(`${e}`)))}get dataCategories(){var e;return null==(e=this.data)?void 0:e.dataDeclaration.map((e=>this.store.dataCategories.get(`${e}`)))}constructor(e,t){(0,M.runInAction)((()=>{this.data=e})),this.store=t}}(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],Xe.prototype,"data",void 0),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Xe.prototype,"vendorConfiguration",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Xe.prototype,"restrictivePurposes",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Xe.prototype,"purposes",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Xe.prototype,"legIntPurposes",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Xe.prototype,"specialPurposes",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Xe.prototype,"features",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Xe.prototype,"specialFeatures",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],Xe.prototype,"dataCategories",null);const Ze=jQuery;var Qe=o.n(Ze);class et extends s.AbstractPost{get restrictivePurposes(){var e;const t=JSON.parse(this.data.meta.restrictivePurposes);return Qe().extend(!0,{},(null==(e=this.vendorModel)?void 0:e.restrictivePurposes)||{},t)}get dataProcessingInCountries(){return JSON.parse(this.data.meta.dataProcessingInCountries||"[]")}get dataProcessingInCountriesSpecialTreatments(){return JSON.parse(this.data.meta.dataProcessingInCountriesSpecialTreatments||"[]")}constructor(e,t={}){super(e,t),(0,M.reaction)((()=>this.data.vendor),(e=>(0,M.runInAction)((()=>{if(e){const{vendors:t}=this.collection.store,o=e.id.toString();let n=t.get(o);n||(n=new Xe(e,this.collection.store),t.set(o,n)),this.vendorModel=n}}))),{fireImmediately:!0}),(0,M.reaction)((()=>{var e;return null==(e=this.data.meta)?void 0:e.vendorId}),(e=>{e&&(this.vendorModel=this.collection.store.vendors.get(e.toString()))}),{fireImmediately:!0})}setStatus(e){this.data.status=e}setMeta(e){this.data.meta=e}transformDataForPatch(){const e=super.transformDataForPatch();return{status:e.status,meta:e.meta}}afterPatch(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterDelete(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}afterPersist(){this.collection.store.rootStore.optionStore.fetchCurrentRevision()}}(0,E.Cg)([M.observable,(0,E.Sn)("design:type",void 0===Xe?Object:Xe)],et.prototype,"vendorModel",void 0),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype","undefined"==typeof TcfVendorConfigurationRestrictivePurposes?Object:TcfVendorConfigurationRestrictivePurposes)],et.prototype,"restrictivePurposes",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",Object)],et.prototype,"dataProcessingInCountries",null),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",Object)],et.prototype,"dataProcessingInCountriesSpecialTreatments",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof PostStatus?Object:PostStatus]),(0,E.Sn)("design:returntype",void 0)],et.prototype,"setStatus",null),(0,E.Cg)([M.action,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object]),(0,E.Sn)("design:returntype",void 0)],et.prototype,"setMeta",null),et=(0,E.Cg)([s.ClientModel.annotate({keyId:"id",namespace:"wp/v2",request:H,create:{path:"/rcb-tcf-vendor-conf"},patch:{path:"/rcb-tcf-vendor-conf/:id"},delete:{path:"/rcb-tcf-vendor-conf/:id"}}),(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[Object,"undefined"==typeof Partial?Object:Partial])],et);class tt extends s.AbstractPostCollection{constructor(e){super(),this.store=e}instance(e){return new et(this).fromResponse(e)}}tt=(0,E.Cg)([s.ClientCollection.annotate({path:"/rcb-tcf-vendor-conf",singlePath:"/rcb-tcf-vendor-conf/:id",namespace:"wp/v2",methods:[s.RouteHttpVerb.GET],request:H}),(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",["undefined"==typeof TcfStore?Object:TcfStore])],tt);const ot={path:"/tcf/declarations",method:s.RouteHttpVerb.GET},nt={path:"/tcf/gvl",method:s.RouteHttpVerb.PUT},st={path:"/tcf/vendors",method:s.RouteHttpVerb.GET};class it extends s.BaseOptions{get vendorConfigurationCount(){return this.fetchedAllVendorConfigurations?this.vendorConfigurations.entries.size:this.rootStore.optionStore.allTcfVendorConfigurationCount}constructor(e){super(),this.busyGvl=!1,this.busyVendors=!1,this.busyDeclarations=!1,this.fetchedAllVendorConfigurations=!1,this.vendors=new Map,this.purposes=new Map,this.specialPurposes=new Map,this.features=new Map,this.specialFeatures=new Map,this.dataCategories=new Map,this.fetchVendorConfigurations=(0,M.flow)((function*(){const e=Math.ceil(this.vendorConfigurationCount/100);for(let t=0;t<e;t++)yield this.vendorConfigurations.get({request:{status:["draft","publish","private"]},params:{offset:100*t,per_page:100,context:"edit"}});this.fetchedAllVendorConfigurations=!0})),this.fetchVendors=(0,M.flow)((function*(){this.busyVendors=!0;try{const{vendorListVersion:e,vendors:t}=yield H({location:st});for(const e of Object.keys(t))this.vendors.set(e,new Xe(t[e],this));this.vendorListVersion=e}catch(e){throw console.log(e),e}finally{this.busyVendors=!1}})),this.fetchDeclarations=(0,M.flow)((function*(){this.busyDeclarations=!0;try{const{gvlSpecificationVersion:e,tcfPolicyVersion:t,purposes:o,specialPurposes:n,features:s,specialFeatures:i,dataCategories:r}=yield H({location:ot});for(const e of Object.keys(o))this.purposes.set(e,new Ke(o[e],!1,this));for(const e of Object.keys(n))this.specialPurposes.set(e,new Ke(n[e],!0,this));for(const e of Object.keys(s))this.features.set(e,new Ye(s[e],!1,this));for(const e of Object.keys(i))this.specialFeatures.set(e,new Ye(i[e],!0,this));for(const e of Object.keys(r))this.dataCategories.set(e,new Je(r[e],this));this.declarations={purposes:o,specialPurposes:n,features:s,specialFeatures:i,dataCategories:r},this.gvlSpecificationVersion=e,this.tcfPolicyVersion=t}catch(e){throw console.log(e),e}finally{this.busyDeclarations=!1}})),this.updateGvl=(0,M.flow)((function*(){this.busyGvl=!0;try{const{gvlDownloadTime:e}=yield H({location:nt});this.rootStore.optionStore.tcfGvlDownloadTime=e}catch(e){throw console.log(e),e}finally{this.busyGvl=!1}})),this.rootStore=e,(0,M.runInAction)((()=>{this.vendorConfigurations=new tt(this)}))}}(0,E.Cg)([M.observable],it.prototype,"busyGvl",void 0),(0,E.Cg)([M.observable],it.prototype,"busyVendors",void 0),(0,E.Cg)([M.observable],it.prototype,"busyDeclarations",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",void 0===tt?Object:tt)],it.prototype,"vendorConfigurations",void 0),(0,E.Cg)([M.observable],it.prototype,"fetchedAllVendorConfigurations",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],it.prototype,"vendorListVersion",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],it.prototype,"gvlSpecificationVersion",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type",Object)],it.prototype,"tcfPolicyVersion",void 0),(0,E.Cg)([M.observable,(0,E.Sn)("design:type","undefined"==typeof Omit?Object:Omit)],it.prototype,"declarations",void 0),(0,E.Cg)([M.observable],it.prototype,"vendors",void 0),(0,E.Cg)([M.observable],it.prototype,"purposes",void 0),(0,E.Cg)([M.observable],it.prototype,"specialPurposes",void 0),(0,E.Cg)([M.observable],it.prototype,"features",void 0),(0,E.Cg)([M.observable],it.prototype,"specialFeatures",void 0),(0,E.Cg)([M.observable],it.prototype,"dataCategories",void 0),(0,E.Cg)([M.computed,(0,E.Sn)("design:type",Function),(0,E.Sn)("design:paramtypes",[]),(0,E.Sn)("design:returntype",void 0)],it.prototype,"vendorConfigurationCount",null);class rt{get context(){return this.contextMemo?this.contextMemo:this.contextMemo=(0,s.createContextFactory)(this)}constructor(){this.optionStore=new Ae(this),this.customizeBannerStore=new be(this),this.cookieStore=new ge(this),this.consentStore=new X(this),this.statsStore=new $e(this),this.checklistStore=new L(this),this.tcfStore=new it(this),this.scannerStore=new Ge(this)}static get StoreProvider(){return rt.get.context.StoreProvider}static get get(){return rt.me?rt.me:rt.me=new rt}}const at=()=>rt.get.context.useStores();var ct=o(19327),lt=o(65666);const dt=e=>{let{children:t,configProvider:o={},app:s={}}=e;return(0,n.jsx)(lt.Ay,{prefixCls:"rcb-antd",iconPrefixCls:"rcb-antd-anticon",theme:{token:{colorPrimary:"#2271b1",borderRadius:3}},...o,children:(0,n.jsx)(ct.Z_3,{value:{prefixCls:"rcb-antd-anticon"},children:(0,n.jsx)(u.A,{message:{top:50},...s,children:(0,n.jsx)(rt.StoreProvider,{children:t})})})})},pt=e=>{let{children:t}=e;const{optionStore:{others:{isPro:o,isLicensed:s,hints:i,proUrl:r,isDemoEnv:a},fomoCoupon:c}}=at(),[l,d]=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return f(S,...t)}({__:Ie,_i:Re,_n:Be,_x:Te}),[p,u]=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return f(k,...t)}({isPro:o,isLicensed:s,isDemoEnv:a,hint:null==i?void 0:i.proDialog,proUrl:r,fomoCoupon:c},{},{inherit:["isLicensed","fomoCoupon"]});return(0,n.jsx)(l,{value:d,children:(0,n.jsx)(p,{value:u,children:t})})},ut=e=>{let{children:t}=e;return(0,n.jsx)(dt,{children:(0,n.jsx)(pt,{children:t})})};function gt(e,t){(0,i.getSidebarCustomize)().control(e,(()=>{const o=document.querySelector(`label[for="_customize-input-${e}"]`),i=document.createElement("span");o.appendChild(i),(0,s.createRoot)(i).render((0,n.jsx)(ut,{children:(0,n.jsx)(_,{...t,wrapperAttributes:{style:{marginLeft:5}}})}))}))}const ht=e=>{let{settings:t}=e;const o=(0,i.useCustomizeValues)(t),s=(0,c.useMemo)((()=>{const{[t[0]]:{value:e},[t[1]]:{value:n},[t[2]]:{value:s}}=o;return"banner"!==e&&(!n||s<50)}),[JSON.stringify(o)]);return(0,i.useA11yDispatcher)({setting:t[1],valid:!s}),!1===s?null:(0,n.jsx)(i.A11yNotice,{severity:"warning",text:Ie('We recommend to show an overlay until consent and an overlay opacity over 50 %% to comply with the {{aPerc}}"perceptibility" of the WCAG 2.2 standard{{/aPerc}} and thereby meet the {{aEuMinimum}}EU\'s minimum legal requirements for accessibility{{/aEuMinimum}}.')})};var yt=o(14322),bt=o(17989),vt=o(55221),mt=o(57922);function ft(e,t){const o={},n=[];for(const s in e){const i=e[s],{breadcrumb:r,valid:a}=i;if(a&&!t)continue;const c=r.split(" ▸ ").map((e=>e.trim()));let l=o;for(const e of c)l[e]||(l[e]={__:i},n.push(l[e])),l=l[e]}for(const e of n){const{__:t,...o}=e;Object.keys(o).length>0&&delete e.__}return o}const St=e=>{let{node:t}=e;return 0===Object.keys(t).length?null:(0,n.jsx)("ul",{style:{listStyle:"disc",paddingLeft:20},children:Object.keys(t).map((e=>{const{__:o}=t[e];return(0,n.jsx)("li",{style:o?{listStyle:"none",marginLeft:-20}:void 0,children:o?(0,n.jsxs)(n.Fragment,{children:[o.valid?(0,n.jsx)(yt.A,{style:{color:"green"}}):(0,n.jsx)(bt.A,{style:{color:"#df3b3b"}})," ",(0,n.jsx)("a",{href:"#",onClick:e=>{o.instance.focus(),e.preventDefault()},children:e})]}):(0,n.jsxs)(n.Fragment,{children:[e,(0,n.jsx)(St,{node:t[e]})]})},e)}))})},Ct=(0,mt.PA)((()=>{const[e,t]=(0,c.useState)(!1),[o,s]=(0,c.useState)({}),[r,l]=(0,c.useState)(!1);(0,c.useEffect)((()=>{window.location.href.indexOf("customAutofocus[rcb-a11y-score]=1")>-1&&t(!0)}),[]),(0,c.useEffect)((()=>{document.addEventListener(i.SETTING_ACCESSIBILITY_CHANGED_EVENT,(e=>{let{detail:t}=e;t.setting.startsWith("rcb-")&&s((e=>({...e,[t.setting]:t})))}))}),[]);const d=Object.values(o).filter((e=>{let{visible:t}=e;return t}));d.sort(((e,t)=>{let{breadcrumb:o}=e,{breadcrumb:n}=t;return o.localeCompare(n)}));const{score:p,max:u}=d.reduce(((e,t)=>{let{valid:o}=t;return e.max++,o&&e.score++,e}),{max:0,score:0}),g=p/u*100,h=g>=95?"green":g>=50?"orange":"red";return(0,n.jsxs)(i.SameSectionAccordion,{state:e,onToggle:()=>t(!e),title:(0,n.jsxs)(n.Fragment,{children:[Ie("Accessibility score")," ",(0,n.jsxs)(a.A,{color:h,children:[g.toFixed(0)," %"]})]}),children:[(0,n.jsx)("p",{className:"description",children:Re(Ie("Reach 100 points to meet all of Real Cookie Banner's recommendations for making your cookie banner be easily accessible to people with disabilities on the basis of the {{a}}WCAG Standard 2.2{{/a}}. You can find out which sections of your cookie banner need to be changed and which improvements we suggest under the individual options in the customizer."),{a:(0,n.jsx)("a",{href:Ie("https://www.w3.org/TR/WCAG22/"),target:"_blank",rel:"noreferrer"})})}),100===g?(0,n.jsx)(vt.Ay,{status:"success",subTitle:Ie("Congratulations, your Cookie Banner is accessible! Thank you for making your cookie banner easily accessible to people with disabilities.")}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{className:"description",children:Ie("The following configurations do not comply with the requirements:")}),(0,n.jsx)("p",{style:{textAlign:"right"},children:(0,n.jsx)("a",{onClick:()=>l(!r),children:Ie(r?"Hide passed rules":"Show passed rules")})}),(0,n.jsx)(St,{node:ft(d,r)})]})]})})),kt=e=>{let{decision:t}=e;const{backboneView:o}=(0,i.useCustomControl)(),s=(0,i.getSidebarCustomize)()(rt.get.optionStore.others.customizeIdsBanner.settings.decision[t]),r=s.get(),a={button:Ie("Button"),link:Ie("Link"),hide:Ie("Hide")};let l=!1;(0,c.useEffect)((()=>s.bind("change",(()=>!l&&o.renderContent()))),[]);const d=(0,c.useCallback)((e=>{l=!0,s.set(e.target.value),l=!1,o.renderContent()}),[s]);return(0,n.jsx)("select",{value:r,onChange:d,children:Object.keys(a).map((e=>(0,n.jsx)("option",{value:e,children:a[e]},e)))})},wt=()=>{const{value:e,setValue:t}=(0,i.useCustomControl)(),{isPro:o,modal:s,tag:r}=R({title:Ie("Want to have a choice in the first view?"),testDrive:!0,feature:"groups-first-view",assetName:Ie("pro-modal/groups-first-view.png"),description:Ie("Visitors of your website can choose which service groups they want to accept immediately after loading the cookie banner, without the need to go into the individual privacy settings.")});return(0,n.jsxs)(n.Fragment,{children:[s,(0,n.jsxs)("label",{children:[(0,n.jsx)("input",{className:i.CUSTOM_CONTROL_INPUT_CHECKBOX_CLASS_NAME,type:"checkbox",disabled:!o,value:"1",checked:!!o&&e,onChange:()=>t(!e)}),Ie("Allow to choose service groups directly in the first view of the cookie banner")," ",r]})]})};let xt=0;const jt=()=>{const{optionStore:{others:{customizeIdsBanner:{sections:{mobile:e}}}}}=at(),{value:t,setValue:o}=(0,i.useCustomControl)(),[s,r]=(0,c.useState)("375,812"),[a,l]=(0,c.useState)(!1),{isPro:d,modal:p,tag:u}=R({title:Ie("Want to optimize the cookie banner for mobile users?"),testDrive:!0,feature:"mobile-experience",assetName:Ie("pro-modal/mobile-optimization.png"),description:Ie("Cookie banners are a necessary evil that takes up a lot of space, especially on smartphones. With mobile optimization you can customize the cookie banner so that it is more discreet and at the same time easy to read on smartphones.")});(0,c.useEffect)((()=>{(0,i.listenPanelExpanded)(e,l)}),[]);const[g,h]=s.split(",").map(Number);return function(e,t,o){const n=(0,c.useMemo)((()=>`react-use-plain-css-${(xt++).toString(36)}`),[]);(0,c.useLayoutEffect)((()=>{let t=document.getElementById(n);return t||(t=document.createElement("style"),t.style.type="text/css",t.id=n,t.setAttribute("skip-rucss","true"),document.getElementsByTagName("head")[0].appendChild(t)),t.innerHTML=e,()=>{var e;null==(e=t.parentNode)||e.removeChild(t)}}),[e,o])}(t&&a?`#customize-preview {\n    width: ${g}px !important;\n    height: ${h}px !important;\n    margin: auto 0 auto calc(${g}px / 2 * -1) !important;\n    max-height: 100%;\n    max-width: 100%;\n    left: 50%;\n}\n\n#customize-footer-actions .devices-wrapper {\n    display: none;\n}`:""),(0,n.jsxs)(n.Fragment,{children:[p,(0,n.jsxs)("label",{children:[(0,n.jsx)("input",{className:i.CUSTOM_CONTROL_INPUT_CHECKBOX_CLASS_NAME,type:"checkbox",disabled:!d,value:"1",checked:!!d&&t,onChange:()=>o(!t)}),Ie("Enable mobile optimization")," ",u]}),(0,n.jsx)("span",{className:"description customize-control-description",style:{marginTop:5},children:Ie("To ensure that the cookie banner does not take up too much space on mobile devices, its display can be reduced for this class of device.")}),t&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{className:"customize-control-title",style:{marginTop:10},children:Ie("Simulate device in preview")}),(0,n.jsxs)("select",{value:s,onChange:e=>r(e.target.value),children:[(0,n.jsx)("option",{value:"411,731",children:"Pixel 2"}),(0,n.jsx)("option",{value:"411,823",children:"Pixel 2 XL"}),(0,n.jsx)("option",{value:"320,568",children:"iPhone 5/SE"}),(0,n.jsx)("option",{value:"375,667",children:"iPhone 6/7/8"}),(0,n.jsx)("option",{value:"414,736",children:"iPhone 6/7/8 Plus"}),(0,n.jsx)("option",{value:"375,812",children:"iPhone X"})]})]})]})},Ot=()=>{const{value:e,setValue:t}=(0,i.useCustomControl)(),{isPro:o,modal:s,tag:r}=R({title:Ie("Want to show legal links with a sticky icon?"),testDrive:!0,feature:"sticky-links",assetName:Ie("pro-modal/sticky-legal-links.webm"),description:Ie("Display a sticky icon on your website that makes the legal links easily accessible on every subpage. Not only does it look fancy, it also boosts your data protection!")});return(0,n.jsxs)(n.Fragment,{children:[s,(0,n.jsxs)("label",{children:[(0,n.jsx)("input",{className:i.CUSTOM_CONTROL_INPUT_CHECKBOX_CLASS_NAME,type:"checkbox",disabled:!o,value:"1",checked:!!o&&e,onChange:()=>t(!e)}),Ie("Enable sticky links widget")," ",r]})]})};let At,Pt;const Ft=(0,mt.PA)((e=>{let{preset:t,onApply:o}=e;var s;const[i,r]=(0,c.useState)(!1),{isPro:l,open:d,modal:p}=R({title:Ie("Want to use %s preset?",t.name),testDrive:!0,feature:"customize-preset",description:Ie("Only a limited number of presets are available for the free version of Real Cookie Banner. Start now with the PRO version and create a design from a preset with just one click.")}),u=he.getIframeStore().customizeBannerStore,g=(0,c.useCallback)((()=>{clearTimeout(At),clearInterval(Pt),At=setTimeout((()=>{r(!1),t.previewInUi(),Pt=setInterval((()=>{u.setIndividualPrivacyOpen(!u.individualPrivacyOpen)}),1e3*Bt)}),100)}),[r,t,u]),h=(0,c.useCallback)((()=>{clearTimeout(At),clearInterval(Pt),u.setIndividualPrivacyOpen(!1),i||(At=setTimeout((()=>{t.resetPreviewInUi()}),100))}),[t,i]),y=(0,c.useCallback)((()=>{clearInterval(Pt),t.applyInUi()?(r(!0),null==o||o()):d()}),[r,t]);return(0,n.jsxs)("div",{className:"customize-rcb-preset",onMouseEnter:g,onMouseLeave:h,children:[p,(0,n.jsxs)("strong",{children:[t.name," ",!l&&t.needsPro&&(0,n.jsx)(a.A,{color:I,children:"PRO"}),null==(s=t.tags)?void 0:s.map((e=>{let[t,o]=e;return(0,n.jsx)(a.A,{color:t,children:o},o)}))]}),(0,n.jsx)("span",{children:t.description}),(0,n.jsx)("div",{className:"btn-apply hidden",onClick:y,children:Ie(i?"Applied!":"Apply")})]})})),Bt=5,Tt=(0,mt.PA)((()=>{const[e,t]=(0,c.useState)(!1),[o,s]=(0,c.useState)(!1),{customizeBannerStore:r}=at(),{busyPresets:a,presets:l}=r,d=e=>{e&&!o&&(r.fetchPresets(),s(!0)),t(e)},p=()=>{d(!1)};return(0,c.useEffect)((()=>{window.location.href.indexOf("customAutofocus[rcb-presets]=1")>-1&&async function(e,t,o){void 0===t&&(t=500),void 0===o&&(o=0);let n=0;for(;!e();){if(o>0&&n>=o)return;await new Promise((e=>setTimeout(e,t))),n++}return e()}((()=>he.getIframeStore())).then((()=>d(!0)))}),[]),(0,n.jsxs)(i.SameSectionAccordion,{state:e,onToggle:d,title:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{className:a?"spinner is-active":"",style:{margin:0}})," ",Ie("Presets")]}),grouped:!0,children:[(0,n.jsx)("p",{className:"description",children:Ie("You can choose between different presets. Presets only change the style, but no texts or content. Tip: Hover the mouse over a preset to see a real-time preview. Wait %d seconds, and you will see a preview of the individual privacy settings screen.",Bt)}),(0,n.jsx)("br",{}),Array.from(l.values()).map((e=>(0,n.jsx)(Ft,{preset:e,onApply:p},e.id)))]})}));!function(){const{optionStore:{others:{isPro:e,customizeIdsBanner:t,customizeDefaultsBanner:o,isPoweredByLinkDisabledByException:r,activeLanguages:a,frontend:{isDataProcessingInUnsafeCountries:c,isAgeNotice:l,isListServicesNotice:d,isTcf:p,isGcm:u}}}}=rt.get,{panel:g,headlines:h,others:{customHtmlDecisionButtonTypeDiffers:y,customHtmlDecisionLegalNoticeAll:b,customHtmlDecisionLegalNoticeEssentials:v,customHtmlDecisionLegalNoticeIndividual:m,customHtmlMobileHideHeaderIgnoreDueCloseIcon:f,customHtmlLayoutMaxHeightNotice:S,customHtmlLayoutAnimationInClsNotice:C},sections:k,settings:{layout:w,decision:x,design:j,headerDesign:O,bodyDesign:A,footerDesign:P,texts:F,individualLayout:B,group:T,saveButton:I,mobile:R,sticky:_}}=t,E=(0,i.getSidebarCustomize)(),M=e=>!!e,D=t=>M(t)&&e,H=e=>e>0,U=e=>"hide"!==e,N=e=>"hide"===e,L=e=>"none"!==e,V=()=>c||l||d,z=()=>!p,G=()=>a.length>1,W=()=>{const e=E(w.type).get(),t=E(w.maxHeight).get();return"dialog"===e&&t<740||"banner"===e&&t<500},q=()=>E(x.acceptAll).get()!==E(x.acceptEssentials).get(),$=()=>!q(),J=()=>!E(A.acceptEssentialsUseAcceptAll).get()||q(),Y={[A.acceptEssentialsUseAcceptAll]:J,[x.acceptAll]:J,[x.acceptEssentials]:J},K=()=>E(I.type).get()!==E(x.acceptAll).get(),X=()=>!K(),Z=()=>!E(I.useAcceptAll).get()||K(),Q={[I.useAcceptAll]:Z,[x.acceptAll]:Z,[I.type]:Z},ee={...Q,[I.type]:e=>Z()&&"button"===e};(0,i.sidebarFactory)({panel:g,ids:t,defaults:o,contentBesideSection:{[k.design]:()=>(0,n.jsx)(i.HeadlineBeforeSection,{children:Ie("Cookie banner/dialog design")}),[k.mobile]:()=>(0,n.jsx)(i.HeadlineBeforeSection,{children:Ie("Miscellaneous")}),[k.individualLayout]:()=>(0,n.jsx)(i.HeadlineBeforeSection,{children:Ie("Individual privacy settings")}),[k.layout]:()=>(0,n.jsxs)(ut,{children:[(0,n.jsx)(Tt,{}),(0,n.jsx)(Ct,{})]}),[k.customCss]:{render:()=>(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.HeadlineBeforeSection,{children:Ie("Useful links")}),(0,n.jsxs)("ul",{style:{listStyle:"initial",padding:"10px 15px 10px 30px"},children:[(0,n.jsx)("li",{children:Re(Ie("Avoid popular mistakes when creating a cookie banner. {{a}}Learn more!{{/a}}"),{a:(0,n.jsx)("a",{href:Ie("https://devowl.io/cookie-banner/common-mistakes/"),rel:"noreferrer",target:"_blank"})})}),(0,n.jsx)("li",{children:Re(Ie("Find out what you should pay attention to in the {{a}}wording of your cookie banner{{/a}}!"),{a:(0,n.jsx)("a",{href:Ie("https://devowl.io/cookie-banner/text/"),rel:"noreferrer",target:"_blank"})})}),(0,n.jsx)("li",{children:Re(Ie("Learn what to look for when {{a}}designing your cookie banner{{/a}}!"),{a:(0,n.jsx)("a",{href:Ie("https://devowl.io/cookie-banner/design/"),rel:"noreferrer",target:"_blank"})})}),(0,n.jsx)("li",{children:Re(Ie("Learn more about {{a}}accessibility and the legal conditions!{{/a}}"),{a:(0,n.jsx)("a",{href:Ie("https://devowl.io/knowledge-base/accessibility-of-real-cookie-banner/"),rel:"noreferrer",target:"_blank"})})})]})]}),type:"after"}},controls:{acceptAllButtonType:()=>(0,n.jsx)(kt,{decision:"acceptAll"}),acceptEssentialsButtonType:()=>(0,n.jsx)(kt,{decision:"acceptEssentials"}),acceptIndividualButtonType:()=>(0,n.jsx)(kt,{decision:"acceptIndividual"}),rcbGroupsFirstView:()=>(0,n.jsx)(ut,{children:(0,n.jsx)(wt,{})}),mobileSwitcher:()=>(0,n.jsx)(ut,{children:(0,n.jsx)(jt,{})}),stickyLinksSwitcher:()=>(0,n.jsx)(ut,{children:(0,n.jsx)(Ot,{})})},conditionals:{[S]:{[w.maxHeightEnabled]:!0,[w.maxHeight]:W,[w.type]:W},[w.maxHeight]:{[w.maxHeightEnabled]:!0},[w.dialogMaxWidth]:{[w.type]:"dialog"},[w.dialogMargin]:{[w.type]:"dialog",[w.dialogPosition]:e=>"middleCenter"!==e},[w.dialogPosition]:{[w.type]:"dialog"},[w.bannerPosition]:{[w.type]:"banner"},[w.bannerMaxWidth]:{[w.type]:"banner"},[w.animationInDuration]:{[w.animationIn]:L},[w.animationInOnlyMobile]:{[w.animationIn]:L},[w.animationOutDuration]:{[w.animationOut]:L},[w.animationOutOnlyMobile]:{[w.animationOut]:L},[w.dialogBorderRadius]:{[w.type]:"dialog"},[w.overlayBg]:{[w.overlay]:!0},[w.overlayBgAlpha]:{[w.overlay]:!0},[w.overlayBlur]:{[w.overlay]:!0},[C]:{[w.animationIn]:e=>["none","fadeIn"].concat([]).indexOf(e)>-1},[b]:{[x.acceptAll]:N},[y]:{[x.acceptAll]:q,[x.acceptEssentials]:q,...Y},[v]:{[x.acceptEssentials]:N,...Y},[m]:{[x.acceptIndividual]:N},[x.groupsFirstView]:{[x.groupsFirstView]:z,[x.showGroups]:!0},[x.saveButton]:{[x.groupsFirstView]:!0,[x.showGroups]:!0},[j.borderColor]:{[j.borderWidth]:H},[j.fontFamily]:{[j.fontInheritFamily]:!1},[j.boxShadowOffsetX]:{[j.boxShadowEnabled]:!0},[j.boxShadowOffsetY]:{[j.boxShadowEnabled]:!0},[j.boxShadowBlurRadius]:{[j.boxShadowEnabled]:!0},[j.boxShadowSpreadRadius]:{[j.boxShadowEnabled]:!0},[j.boxShadowColor]:{[j.boxShadowEnabled]:!0},[j.boxShadowColorAlpha]:{[j.boxShadowEnabled]:!0},[O.bg]:{[O.inheritBg]:!1},[O.textAlign]:{[O.inheritTextAlign]:!1},[O.logoRetina]:{[O.logo]:e=>e&&!e.endsWith(".svg")},[O.logoMaxHeight]:{[O.logo]:M},[O.logoPosition]:{[O.logo]:M,[F.headline]:M},[O.logoMargin]:{[O.logo]:M},[O.fontFamily]:{[O.fontInheritFamily]:!1},[O.fontSize]:{[F.headline]:M},[O.fontFamily]:{[O.fontInheritFamily]:!1},[O.borderColor]:{[O.borderWidth]:H},[A.descriptionFontSize]:{[A.descriptionInheritFontSize]:!1},[A.dottedGroupsInheritFontSize]:{[A.dottedGroupsInheritFontSize]:z},[A.dottedGroupsFontSize]:{[A.dottedGroupsInheritFontSize]:!1,[A.dottedGroupsFontSize]:z},[A.dottedGroupsBulletColor]:{[A.dottedGroupsBulletColor]:z,[x.showGroups]:!0},[h.bodyDesignTeachings]:{[h.bodyDesignTeachings]:V},[h.bodyDesignTeachingsFont]:{[h.bodyDesignTeachingsFont]:V},[h.bodyDesignTeachingsSeparator]:{[A.teachingsSeparatorActive]:!0,[h.bodyDesignTeachingsSeparator]:V},[A.teachingsInheritTextAlign]:{[A.teachingsInheritTextAlign]:V},[A.teachingsTextAlign]:{[A.teachingsInheritTextAlign]:!1,[A.teachingsTextAlign]:V},[A.teachingsInheritFontSize]:{[A.teachingsInheritFontSize]:V},[A.teachingsFontSize]:{[A.teachingsInheritFontSize]:!1,[A.teachingsFontSize]:V},[A.teachingsInheritTextAlign]:{[A.teachingsInheritTextAlign]:V},[A.teachingsFontColor]:{[A.teachingsInheritFontColor]:!1,[A.teachingsFontColor]:V},[A.teachingsSeparatorWidth]:{[A.teachingsSeparatorActive]:!0,[A.teachingsSeparatorWidth]:V},[A.teachingsSeparatorHeight]:{[A.teachingsSeparatorActive]:!0,[A.teachingsSeparatorHeight]:V},[A.teachingsSeparatorColor]:{[A.teachingsSeparatorActive]:!0,[A.teachingsSeparatorColor]:V},[A.acceptAllOneRowLayout]:{[x.acceptAll]:U,[x.acceptEssentials]:U},[A.acceptAllFontSize]:{[x.acceptAll]:U},[A.acceptAllPadding]:{[x.acceptAll]:U},[A.acceptAllBg]:{[x.acceptAll]:"button"},[A.acceptAllTextAlign]:{[x.acceptAll]:U},[A.acceptAllBorderWidth]:{[x.acceptAll]:"button"},[A.acceptAllBorderColor]:{[x.acceptAll]:"button",[A.acceptAllBorderWidth]:H},[A.acceptAllFontColor]:{[x.acceptAll]:U},[A.acceptAllFontWeight]:{[x.acceptAll]:U},[A.acceptAllHoverBg]:{[x.acceptAll]:"button"},[A.acceptAllHoverFontColor]:{[x.acceptAll]:U},[A.acceptAllHoverBorderColor]:{[x.acceptAll]:"button",[A.acceptAllBorderWidth]:H},[h.bodyDesignAcceptAllBorder]:{[x.acceptAll]:"button"},[h.bodyDesignAcceptAllFont]:{[x.acceptAll]:U},[h.bodyDesignAcceptAllHover]:{[x.acceptAll]:U},[A.acceptEssentialsUseAcceptAll]:{[x.acceptAll]:$,[x.acceptEssentials]:$},[A.acceptEssentialsFontSize]:{[x.acceptEssentials]:U,[A.acceptAllOneRowLayout]:!1,[A.acceptEssentialsFontSize]:z,...Y},[A.acceptEssentialsPadding]:{[x.acceptEssentials]:U,[A.acceptAllOneRowLayout]:!1,...Y},[A.acceptEssentialsBg]:{[x.acceptEssentials]:"button",...Y},[A.acceptEssentialsTextAlign]:{[x.acceptEssentials]:U,...Y},[A.acceptEssentialsBorderWidth]:{[x.acceptEssentials]:"button",...Y},[A.acceptEssentialsBorderColor]:{[x.acceptEssentials]:"button",[A.acceptEssentialsBorderWidth]:H,...Y},[A.acceptEssentialsFontColor]:{[x.acceptEssentials]:U,...Y},[A.acceptEssentialsFontWeight]:{[x.acceptEssentials]:U,[A.acceptEssentialsFontWeight]:z,...Y},[A.acceptEssentialsHoverBg]:{[x.acceptEssentials]:"button",...Y},[A.acceptEssentialsHoverFontColor]:{[x.acceptEssentials]:U,...Y},[A.acceptEssentialsHoverBorderColor]:{[x.acceptEssentials]:"button",[A.acceptEssentialsBorderWidth]:H,...Y},[h.bodyDesignAcceptEssentialsBorder]:{[x.acceptEssentials]:"button",...Y},[h.bodyDesignAcceptEssentialsFont]:{[x.acceptEssentials]:U,...Y},[h.bodyDesignAcceptEssentialsHover]:{[x.acceptEssentials]:U,...Y},[A.acceptIndividualFontSize]:{[x.acceptIndividual]:U},[A.acceptIndividualPadding]:{[x.acceptIndividual]:U},[A.acceptIndividualBg]:{[x.acceptIndividual]:"button"},[A.acceptIndividualTextAlign]:{[x.acceptIndividual]:U},[A.acceptIndividualBorderWidth]:{[x.acceptIndividual]:"button"},[A.acceptIndividualBorderColor]:{[x.acceptIndividual]:"button",[A.acceptIndividualBorderWidth]:H},[A.acceptIndividualFontColor]:{[x.acceptIndividual]:U},[A.acceptIndividualFontWeight]:{[x.acceptIndividual]:U},[A.acceptIndividualHoverBg]:{[x.acceptIndividual]:"button"},[A.acceptIndividualHoverFontColor]:{[x.acceptIndividual]:U},[A.acceptIndividualHoverBorderColor]:{[x.acceptIndividual]:"button",[A.acceptIndividualBorderWidth]:H},[h.bodyDesignAcceptIndividualBorder]:{[x.acceptIndividual]:"button"},[h.bodyDesignAcceptIndividualFont]:{[x.acceptIndividual]:U},[h.bodyDesignAcceptIndividualHover]:{[x.acceptIndividual]:U},[P.poweredByLink]:{[P.poweredByLink]:()=>!r},[P.bg]:{[P.inheritBg]:!1},[P.textAlign]:{[P.inheritTextAlign]:!1},[P.fontFamily]:{[j.fontInheritFamily]:!1},[P.fontSize]:{[F.headline]:M},[P.fontFamily]:{[P.fontInheritFamily]:!1},[P.borderColor]:{[P.borderWidth]:H},[h.footerLanguageSwitcher]:{[h.footerLanguageSwitcher]:G},[P.languageSwitcher]:{[P.languageSwitcher]:G},[F.acceptAll]:{[x.acceptAll]:U},[F.acceptEssentials]:{[x.acceptEssentials]:U},[F.acceptIndividual]:{[x.acceptIndividual]:U},[F.poweredBy]:{[P.poweredByLink]:!0},[B.inheritDialogMaxWidth]:{[w.type]:"dialog"},[B.dialogMaxWidth]:{[w.type]:"dialog",[B.inheritDialogMaxWidth]:!1},[B.inheritBannerMaxWidth]:{[w.type]:"banner"},[B.bannerMaxWidth]:{[w.type]:"banner",[B.inheritBannerMaxWidth]:!1},[T.checkboxBorderColor]:{[T.checkboxBorderWidth]:H},[T.checkboxActiveBorderColor]:{[T.checkboxBorderWidth]:H},[T.groupBg]:{[T.groupInheritBg]:!1},[T.groupBorderColor]:{[T.groupBorderWidth]:H},[I.useAcceptAll]:{[x.acceptAll]:X,[I.type]:X},[I.type]:Q,[I.padding]:Q,[I.bg]:ee,[I.textAlign]:Q,[h.saveButtonFont]:Q,[I.fontColor]:Q,[I.fontSize]:{[I.fontSize]:z,...Q},[I.fontWeight]:{[I.fontWeight]:z,...Q},[I.borderWidth]:ee,[I.borderColor]:{[I.borderWidth]:H,...ee},[h.saveButtonHover]:Q,[I.hoverBg]:ee,[I.hoverFontColor]:Q,[I.hoverBorderColor]:{[I.borderWidth]:H,...ee},[h.saveButtonBorder]:ee,[R.hideHeader]:{[R.enabled]:D},[R.maxHeight]:{[R.enabled]:D},[R.alignment]:{[R.enabled]:D},[R.scalePercent]:{[R.enabled]:D},[R.scalePercentVertical]:{[R.enabled]:D},[f]:{[R.hideHeader]:!0,[x.showCloseIcon]:!0,[x.acceptEssentials]:N},[_.bubbleBorderColor]:{[_.bubbleBorderWidth]:H,[_.enabled]:D},[_.bubbleHoverBorderColor]:{[_.bubbleBorderWidth]:H,[_.enabled]:D},[_.boxShadowOffsetX]:{[_.boxShadowEnabled]:!0,[_.enabled]:D},[_.boxShadowOffsetY]:{[_.boxShadowEnabled]:!0,[_.enabled]:D},[_.boxShadowBlurRadius]:{[_.boxShadowEnabled]:!0,[_.enabled]:D},[_.boxShadowSpreadRadius]:{[_.boxShadowEnabled]:!0,[_.enabled]:D},[_.boxShadowColor]:{[_.boxShadowEnabled]:!0,[_.enabled]:D},[_.boxShadowColorAlpha]:{[_.boxShadowEnabled]:!0,[_.enabled]:D},[_.animationsEnabled]:{[_.enabled]:D},[_.alignment]:{[_.enabled]:D},[_.bubbleBorderRadius]:{[_.enabled]:D},[_.icon]:{[_.enabled]:D},[_.iconCustom]:{[_.enabled]:D,[_.icon]:e=>"custom"===e},[_.iconCustomRetina]:{[_.enabled]:D,[_.iconCustom]:e=>e&&!e.endsWith(".svg"),[_.icon]:e=>"custom"===e},[_.iconSize]:{[_.enabled]:D},[_.iconColor]:{[_.enabled]:D},[_.bubbleMargin]:{[_.enabled]:D},[_.bubblePadding]:{[_.enabled]:D},[_.bubbleBg]:{[_.enabled]:D},[_.bubbleBorderWidth]:{[_.enabled]:D},[_.boxShadowEnabled]:{[_.enabled]:D},[_.bubbleHoverBg]:{[_.enabled]:D},[_.bubbleHoverBorderColor]:{[_.enabled]:D},[_.hoverIconColor]:{[_.enabled]:D},[_.hoverIconCustom]:{[_.enabled]:D,[_.icon]:e=>"custom"===e},[_.hoverIconCustomRetina]:{[_.enabled]:D,[_.hoverIconCustom]:e=>e&&!e.endsWith(".svg"),[_.icon]:e=>"custom"===e},[_.menuFontSize]:{[_.enabled]:D},[_.menuBorderRadius]:{[_.enabled]:D},[_.menuItemSpacing]:{[_.enabled]:D},[_.menuItemPadding]:{[_.enabled]:D},[h.stickyIcon]:{[_.enabled]:D},[h.stickyBorder]:{[_.enabled]:D},[h.stickyBoxShadow]:{[_.enabled]:D},[h.stickyHover]:{[_.enabled]:D},[h.stickyMenu]:{[_.enabled]:D},[h.textsSticky]:{[_.enabled]:D},[F.stickyChange]:{[_.enabled]:D},[F.stickyHistory]:{[_.enabled]:D},[F.stickyRevoke]:{[_.enabled]:D},[F.stickyRevokeSuccessMessage]:{[_.enabled]:D}},onReady:()=>{gt(w.overlayBlur,{title:Ie("Want to stand out from the crowd?"),testDrive:!0,feature:"blur",assetName:Ie("pro-modal/blur.png"),description:Ie("Blur the background and set the focus on the cookie banner so that your visitors agree to as many services and cookies as possible.")}),gt(w.animationIn,{title:Ie("Want to have cool animations?"),testDrive:!0,feature:"animations-in",assetName:Ie("pro-modal/animations-in.png"),description:Ie("The cookie banner can be displayed with an animation. Choose from over 40 animations the most suitable!"),tagText:Ie("Unlock animations")}),gt(w.animationOut,{title:Ie("Want to have cool animations?"),testDrive:!0,feature:"animations-out",assetName:Ie("pro-modal/animations-in.png"),description:Ie("The cookie banner can be hidden with an animation. Choose from over 40 animations the most suitable!"),tagText:Ie("Unlock animations")});const e=e=>(0,i.a11yConstrastRatio)({...e,minimumRatio:p?5:4.5,severity:p?"error":"warning",renderNotice:p?()=>Ie("You need to use a minimum contrast ratio between background and font color of %d to be compliant with TCF!",5):void 0}),t=e=>(0,i.a11yConstrastRatio)({...e,minimumRatio:4.5,severity:"warning"});t({settings:[j.bg,j.fontColor]}),t({settings:[O.bg,O.fontColor,O.inheritBg,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return[s?i:o,n]}}),t({settings:[j.bg,A.teachingsFontColor]}),t({settings:[P.bg,P.fontColor,P.inheritBg,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return[s?i:o,n]}}),t({settings:[P.bg,P.hoverFontColor,P.inheritBg,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return[s?i:o,n]}}),t({settings:[T.checkboxActiveBg,T.checkboxActiveColor]}),t({settings:[T.groupBg,T.headlineFontColor,T.groupInheritBg,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return[s?i:o,n]}}),t({settings:[T.groupBg,T.descriptionFontColor,T.groupInheritBg,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return[s?i:o,n]}}),t({settings:[T.groupBg,T.linkColor,T.groupInheritBg,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return[s?i:o,n]}}),t({settings:[T.groupBg,T.linkHoverColor,T.groupInheritBg,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return[s?i:o,n]}}),e({settings:[A.acceptAllBg,A.acceptAllFontColor,x.acceptAll,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return["link"===s?i:o,n]}}),e({settings:[A.acceptAllHoverBg,A.acceptAllHoverFontColor,x.acceptAll,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return["link"===s?i:o,n]}}),e({settings:[A.acceptEssentialsBg,A.acceptEssentialsFontColor,x.acceptEssentials,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return["link"===s?i:o,n]}}),e({settings:[A.acceptEssentialsHoverBg,A.acceptEssentialsHoverFontColor,x.acceptEssentials,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return["link"===s?i:o,n]}}),e({settings:[I.bg,I.fontColor,I.type,I.useAcceptAll,x.acceptAll,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i,r,a]=t;return["link"===(i?r:s)?a:o,n]}}),e({settings:[I.hoverBg,I.hoverFontColor,I.type,I.useAcceptAll,x.acceptAll,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i,r,a]=t;return["link"===(i?r:s)?a:o,n]}}),e({settings:[A.acceptIndividualBg,A.acceptIndividualFontColor,x.acceptIndividual,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return["link"===s?i:o,n]}}),e({settings:[A.acceptIndividualHoverBg,A.acceptIndividualHoverFontColor,x.acceptIndividual,j.bg],modifier:(e,t)=>{let[o,n]=e,[s,i]=t;return["link"===s?i:o,n]}});const o=e=>(0,i.a11yFontWeight)({...e,notAccessible:["lighter"],severity:"warning"});o({settings:[j.fontWeight]}),o({settings:[O.fontWeight]}),o({settings:[A.acceptAllFontWeight]}),o({settings:[A.acceptEssentialsFontWeight]}),o({settings:[A.acceptIndividualFontWeight]}),o({settings:[P.fontWeight]}),o({settings:[T.headlineFontWeight]}),o({settings:[I.fontWeight]});const r=[],a=e=>{r.push(e.settings[0]),(0,i.a11yFontSize)({...e,severity:"warning"})};a({settings:[j.fontSize],minimumPx:12}),a({settings:[O.fontSize],minimumPx:16}),a({settings:[A.descriptionFontSize],minimumPx:12}),a({settings:[A.dottedGroupsFontSize],minimumPx:12}),a({settings:[A.teachingsFontSize],minimumPx:12}),a({settings:[A.acceptAllFontSize],minimumPx:16}),a({settings:[A.acceptEssentialsFontSize],minimumPx:16}),a({settings:[A.acceptIndividualFontSize],minimumPx:16}),a({settings:[T.headlineFontSize],minimumPx:14}),a({settings:[T.descriptionFontSize],minimumPx:12}),a({settings:[I.fontSize],minimumPx:16}),(0,i.a11yTextDecoration)({settings:[j.linkTextDecoration],notAccessible:["none"],severity:"warning"});const c=[w.type,w.overlay,w.overlayBgAlpha];(0,i.getSidebarCustomize)().control(c[1],(e=>{let{container:t}=e;(0,s.createRoot)(jQuery("<div />").addClass("customize-control-a11y-overlay").appendTo(t).get(0)).render((0,n.jsx)(ht,{settings:c}))}))}})}()},41594:e=>{e.exports=React},75206:e=>{e.exports=ReactDOM},44497:e=>{e.exports=mobx}},o={};function n(e){var s=o[e];if(void 0!==s)return s.exports;var i=o[e]={exports:{}};return t[e](i,i.exports,n),i.exports}n.m=t,e=[],n.O=(t,o,s,i)=>{if(!o){var r=1/0;for(d=0;d<e.length;d++){for(var[o,s,i]=e[d],a=!0,c=0;c<o.length;c++)(!1&i||r>=i)&&Object.keys(n.O).every((e=>n.O[e](o[c])))?o.splice(c--,1):(a=!1,i<r&&(r=i));if(a){e.splice(d--,1);var l=s();void 0!==l&&(t=l)}}return t}i=i||0;for(var d=e.length;d>0&&e[d-1][2]>i;d--)e[d]=e[d-1];e[d]=[o,s,i]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e={904:0};n.O.j=t=>0===e[t];var t=(t,o)=>{var s,i,[r,a,c]=o,l=0;if(r.some((t=>0!==e[t]))){for(s in a)n.o(a,s)&&(n.m[s]=a[s]);if(c)var d=c(n)}for(t&&t(o);l<r.length;l++)i=r[l],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return n.O(d)},o=self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var s=n.O(void 0,[683],(()=>n(98741)));s=n.O(s),realCookieBanner_customize=s})();
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.19/d1903f7a609ffbf15837e749eacbf34e/customize.lite.js.map
