"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[655],{23708:(e,t,r)=>{r.r(t),r.d(t,{ImportExportCards:()=>q});var n=r(3713),s=r(6099),a=r(92453),l=r(43799),o=r(57922),i=r(19117),c=r(91386),d=r(18197),u=r(45854),h=r(19991),p=r(9551),_=r(41594),m=r(97745),x=r(55924),j=r(71951),g=r(30617),y=r(42090),k=r(62434);const A={labelCol:{span:24},wrapperCol:{span:24}},b=(0,o.PA)((()=>{const{message:e}=i.A.useApp(),{optionStore:{isTcf:t,others:{hints:{export:r}}}}=(0,j.g)(),[s]=c.A.useForm(),[a,l]=(0,_.useState)(!1),[o,b]=(0,_.useState)(""),f=(0,_.useCallback)((async e=>{l(!0);try{b(JSON.stringify(await(0,y.E)({location:k.A,params:e})))}finally{l(!1)}}),[]),v=(0,_.useCallback)((()=>{(0,m.l)(o),e.success((0,g.__)("Export successfully copied to the clipboard."))}),[o]),S=(0,_.useCallback)((e=>((async()=>{l(!0),window.location.href=(0,y.T)({location:k.A,params:{...s.getFieldsValue(),download:!0}}),l(!1)})(),e.preventDefault(),!1)),[l,s]);return(0,n.jsx)(d.A,{spinning:a,children:(0,n.jsxs)(c.A,{name:"export",form:s,...A,initialValues:{settings:!0,cookieGroups:!0,cookies:!0,blocker:!0,tcfVendorConfigurations:!0,customizeBanner:!0},onFinish:f,labelWrap:!0,children:[(0,n.jsxs)(c.A.Item,{label:(0,g.__)("Content to export"),children:[(0,n.jsx)("div",{children:(0,n.jsx)(c.A.Item,{name:"settings",noStyle:!0,valuePropName:"checked",children:(0,n.jsx)(u.A,{children:(0,g.__)("Settings")})})}),(0,n.jsx)("div",{children:(0,n.jsx)(c.A.Item,{name:"cookieGroups",noStyle:!0,valuePropName:"checked",children:(0,n.jsx)(u.A,{children:(0,g.__)("Service groups")})})}),(0,n.jsx)("div",{children:(0,n.jsx)(c.A.Item,{name:"cookies",noStyle:!0,valuePropName:"checked",children:(0,n.jsx)(u.A,{children:(0,g.__)("Services")})})}),(0,n.jsx)("div",{children:(0,n.jsx)(c.A.Item,{name:"blocker",noStyle:!0,valuePropName:"checked",children:(0,n.jsx)(u.A,{children:(0,g.__)("Content Blocker")})})}),t&&(0,n.jsx)("div",{children:(0,n.jsx)(c.A.Item,{name:"tcfVendorConfigurations",noStyle:!0,valuePropName:"checked",children:(0,n.jsx)(u.A,{children:(0,g.__)("TCF Vendor configurations")})})}),(0,n.jsx)("div",{children:(0,n.jsx)(c.A.Item,{name:"customizeBanner",noStyle:!0,valuePropName:"checked",children:(0,n.jsx)(u.A,{children:(0,g.__)("Cookie banner customization")})})})]}),(0,n.jsxs)(c.A.Item,{children:[(0,n.jsx)("input",{type:"submit",className:"button button-primary",style:{marginTop:10},value:(0,g.__)("Export")}),(0,n.jsx)("input",{onClick:S,type:"submit",className:"button",style:{margin:"10px 0 0 10px"},value:(0,g.__)("Download JSON")}),r.length>0&&(0,n.jsx)("div",{className:"notice notice-info inline below-h2 notice-alt",style:{margin:"10px 0 0 0"},children:(0,n.jsx)("p",{children:(0,x.g)(r.join("\n\n"))})})]}),(0,n.jsxs)(c.A.Item,{style:{display:o?"block":"none"},children:[(0,n.jsxs)(c.A.Item,{noStyle:!0,children:[(0,n.jsx)(h.A,{style:{marginTop:0},children:(0,g.__)("Result")}),(0,n.jsx)(p.A.TextArea,{onClick:v,value:o,readOnly:!0,rows:5})]}),(0,n.jsx)("p",{className:"description",children:(0,g.__)('Copy the exported content and paste it into the "Import" text area of your target WordPress installation.')})]})]})})}));var f=r(81533),v=r(38123),S=r.n(v),C=r(52135),I=r(52624);const F={labelCol:{span:24},wrapperCol:{span:24}},w=(0,o.PA)((()=>{const[e]=c.A.useForm(),[t,r]=(0,_.useState)(!1),[s,a]=(0,_.useState)(!1),l=(0,_.useCallback)((()=>{(async()=>{r(!0),a(!0);const{uuid:t,date:n}=e.getFieldsValue(),s=(0,y.T)({location:C.R,params:{uuid:t||"",from:(null==n?void 0:n[0].format("YYYY-MM-DD"))||"",to:(null==n?void 0:n[1].format("YYYY-MM-DD"))||""}});window.open(s,"_blank"),r(!1)})()}),[r,a,e]);return(0,n.jsx)(d.A,{spinning:t,children:(0,n.jsxs)(c.A,{name:"export",form:e,...F,initialValues:{by:"",date:[],uuid:""},onFinish:l,labelWrap:!0,children:[(0,n.jsx)(c.A.Item,{label:(0,g.__)("Export by"),required:!0,children:(0,n.jsx)(c.A.Item,{name:"by",noStyle:!0,rules:[{required:!0,message:(0,g.__)("Please select an option!")}],children:(0,n.jsxs)(f.Ay.Group,{children:[(0,n.jsx)(f.Ay.Button,{value:"date",children:(0,g.__)("Date range")}),(0,n.jsx)(f.Ay.Button,{value:"uuid",children:(0,g.__)("UUID")})]})})}),(0,n.jsx)(c.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.by!==t.by,children:e=>{let{getFieldValue:t}=e;return"date"===t("by")&&(0,n.jsx)(c.A.Item,{name:"date",label:(0,g.__)("Date range"),required:!0,rules:[{type:"array",required:!0,message:(0,g.__)("Please provide a valid date range!")}],children:(0,n.jsx)(I.U,{disabledDate:e=>!e||e.isAfter(S()())})})}}),(0,n.jsx)(c.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.by!==t.by,children:e=>{let{getFieldValue:t}=e;return"uuid"===t("by")&&(0,n.jsx)(c.A.Item,{name:"uuid",label:(0,g.__)("UUID"),required:!0,rules:[{required:!0,pattern:/^\w{8}-\w{4}-\w{4}-\w{4}-\w{12}$/,message:(0,g.__)("Please provide a valid UUID!")}],children:(0,n.jsx)(p.A,{})})}}),(0,n.jsx)(c.A.Item,{children:(0,n.jsx)("input",{type:"submit",className:"button button-primary",style:{marginTop:10},value:(0,g.__)("Download CSV")})}),s&&(0,n.jsx)("div",{className:"notice notice-info inline below-h2 notice-alt",style:{margin:"10px 0 0 0"},children:(0,n.jsx)("p",{children:(0,g.__)("The CSV file can be very large because data in this format is redundant per line. In your WordPress database the consents are much smaller.")})})]})})}));var D=r(10099),P=r(50777),N=r(6196),Y=r(19393);const T=(0,o.PA)((e=>{let{result:t}=e;const r=(0,_.useCallback)((e=>{let{fix:t,settingsTab:r,cookieDuplicate:s,cookie:a,blockerDuplicate:l,blocker:o,href:i,linkText:c}=e;switch(t){case"settings":return(0,n.jsx)("a",{href:`#/settings/${r}`,target:"_blank",rel:"noreferrer",children:(0,g.__)("Set manually")});case"cookieDuplicate":{const{original:[e,t],duplicate:[r,a]}=s;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("a",{href:`#/cookies/${e}/edit/${t}`,target:"_blank",rel:"noreferrer",children:(0,g.__)("Open original")})," ","•"," ",(0,n.jsx)("a",{href:`#/cookies/${r}/edit/${a}`,target:"_blank",rel:"noreferrer",children:(0,g.__)("Open newly created")})]})}case"cookie":{const[e,t]=a;return(0,n.jsx)("a",{href:`#/cookies/${e}/edit/${t}`,target:"_blank",rel:"noreferrer",children:(0,g.__)("Check manually")})}case"blockerDuplicate":{const{original:e,duplicate:t}=l;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("a",{href:`#/blocker/edit/${e}`,target:"_blank",rel:"noreferrer",children:(0,g.__)("Open original")})," ","•"," ",(0,n.jsx)("a",{href:`#/blocker/edit/${t}`,target:"_blank",rel:"noreferrer",children:(0,g.__)("Open newly created")})]})}case"blocker":return(0,n.jsx)("a",{href:`#/blocker/edit/${o}`,target:"_blank",rel:"noreferrer",children:(0,g.__)("Check manually")});case"link":return(0,n.jsx)("a",{href:i,rel:"noreferrer",children:c||(0,g.__)("Set manually")});default:return null}}),[]);return t?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(h.A,{style:{marginTop:0},children:(0,g.__)("Result")}),t.messages.map(((e,t)=>{let{message:s,severity:a,...l}=e;return(0,n.jsx)("div",{className:`notice notice-${a} inline below-h2 notice-alt`,style:{margin:"10px 0 0 0"},children:(0,n.jsxs)("p",{children:[(0,n.jsx)("span",{dangerouslySetInnerHTML:{__html:s}}),!!l.fix&&(0,n.jsxs)(n.Fragment,{children:[" • ",r(l)]})]})},t)}))]}):null}));var V=r(60789);const B={labelCol:{span:24},wrapperCol:{span:24}},E=(0,o.PA)((()=>{var e;const{message:t}=i.A.useApp(),{cookieStore:r,optionStore:s}=(0,j.g)(),{groups:a,busy:l}=r,[o]=c.A.useForm(),[u,h]=(0,_.useState)(!1),[m,x]=(0,_.useState)(void 0);(0,_.useEffect)((()=>{r.fetchGroups()}),[]);const k=(0,_.useCallback)((async e=>{h(!0);try{x(await(0,y.E)({location:V.h,request:{cookieGroup:0,cookieStatus:"keep",cookieSkipExisting:!0,blockerStatus:"keep",blockerSkipExisting:!0,tcfVendorConfigurationStatus:"keep",...e}})),s.fetchCurrentRevision(),s.fetchSettings(),s.fetchBannerLinks(),t.success((0,g.__)("Successfully imported!"))}catch(e){t.error(e.responseJSON.message)}finally{h(!1)}}),[]),A=(0,_.useCallback)((e=>{const t=new FileReader;return t.readAsText(e,"UTF-8"),t.onload=e=>o.setFieldsValue({json:e.target.result}),t.onerror=()=>o.setFieldsValue({json:(0,g.__)("File could not be read.")}),!1}),[o]),b=(0,_.useCallback)(((e,t)=>{try{const r=JSON.parse(e);return!t||!!r[t]}catch(e){return!1}}),[]);return(0,n.jsx)(d.A,{spinning:u||l,children:(0,n.jsxs)(c.A,{name:"import",form:o,...B,initialValues:{json:"",cookieStatus:"keep",cookieSkipExisting:!0,blockerStatus:"keep",blockerSkipExisting:!0,tcfVendorConfigurationStatus:"keep"},onFinish:k,labelWrap:!0,children:[(0,n.jsxs)(c.A.Item,{label:(0,g.__)("Content to import"),required:!0,children:[(0,n.jsx)(c.A.Item,{name:"json",rules:[{required:!0,message:(0,g.__)("Please provide a value!")}],extra:(0,g.__)("You can get the settings as JSON file or text if you export the settings in this or another WordPress installation."),children:(0,n.jsx)(p.A.TextArea,{rows:5})}),(0,n.jsx)("p",{className:"description",children:(0,g.__)("or select a file to upload:")}),(0,n.jsx)(P.A,{accept:"application/json",showUploadList:!1,beforeUpload:A,children:(0,n.jsxs)("a",{className:"button",children:[(0,n.jsx)(D.A,{})," ",(0,g.__)("Select File")]})})]}),(0,n.jsx)(c.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.json!==t.json,children:e=>{let{getFieldValue:t}=e;return b(t("json"),"cookies")&&(0,n.jsxs)(c.A.Item,{label:(0,g.__)("Fallback service group"),required:!0,children:[(0,n.jsx)(c.A.Item,{name:"cookieGroup",noStyle:!0,rules:[{required:!0,message:(0,g.__)("Please provide a group!")}],children:(0,n.jsx)(N.A,{style:{width:"70%"},children:a.sortedGroups.map((e=>{let{data:{id:t,name:r}}=e;return(0,n.jsx)(N.A.Option,{value:t,children:r},t)}))})}),(0,n.jsx)("p",{className:"description",children:(0,g.__)("Select an alternative group to assign the service to if an imported service cannot be assigned to its original group.")})]})}}),(0,n.jsx)(c.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.json!==t.json,children:e=>{let{getFieldValue:t}=e;return b(t("json"),"cookies")&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.A.Item,{children:(0,n.jsxs)("span",{children:[(0,n.jsx)(c.A.Item,{name:"cookieSkipExisting",valuePropName:"checked",noStyle:!0,children:(0,n.jsx)(Y.A,{})}),(0,n.jsxs)("span",{children:["  ",(0,g.__)("Skip already existing services")]})]})}),(0,n.jsx)(c.A.Item,{label:(0,g.__)("Set service status"),name:"cookieStatus",rules:[{required:!0,message:(0,g.__)("Please choose a status!")}],children:(0,n.jsxs)(f.Ay.Group,{children:[(0,n.jsx)(f.Ay.Button,{value:"keep",children:(0,g.__)("Keep")}),(0,n.jsx)(f.Ay.Button,{value:"publish",children:(0,g.__)("Enabled")}),(0,n.jsx)(f.Ay.Button,{value:"private",children:(0,g.__)("Disabled")}),(0,n.jsx)(f.Ay.Button,{value:"draft",children:(0,g.__)("Draft")})]})})]})}}),(0,n.jsx)(c.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.json!==t.json,children:e=>{let{getFieldValue:t}=e;return b(t("json"),"blocker")&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.A.Item,{children:(0,n.jsxs)("span",{children:[(0,n.jsx)(c.A.Item,{name:"blockerSkipExisting",valuePropName:"checked",noStyle:!0,children:(0,n.jsx)(Y.A,{})}),(0,n.jsxs)("span",{children:["  ",(0,g.__)("Skip already existing content blocker")]})]})}),(0,n.jsx)(c.A.Item,{label:(0,g.__)("Set content blocker status"),name:"blockerStatus",rules:[{required:!0,message:(0,g.__)("Please choose a status!")}],children:(0,n.jsxs)(f.Ay.Group,{children:[(0,n.jsx)(f.Ay.Button,{value:"keep",children:(0,g.__)("Keep")}),(0,n.jsx)(f.Ay.Button,{value:"publish",children:(0,g.__)("Enabled")}),(0,n.jsx)(f.Ay.Button,{value:"private",children:(0,g.__)("Disabled")}),(0,n.jsx)(f.Ay.Button,{value:"draft",children:(0,g.__)("Draft")})]})})]})}}),(0,n.jsx)(c.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.json!==t.json,children:e=>{let{getFieldValue:t}=e;return b(t("json"),"tcfVendorConfigurations")&&(0,n.jsx)(c.A.Item,{label:(0,g.__)("Set TCF Vendor configuration status"),name:"tcfVendorConfigurationStatus",rules:[{required:!0,message:(0,g.__)("Please choose a status!")}],children:(0,n.jsxs)(f.Ay.Group,{children:[(0,n.jsx)(f.Ay.Button,{value:"keep",children:(0,g.__)("Keep")}),(0,n.jsx)(f.Ay.Button,{value:"publish",children:(0,g.__)("Enabled")}),(0,n.jsx)(f.Ay.Button,{value:"private",children:(0,g.__)("Disabled")}),(0,n.jsx)(f.Ay.Button,{value:"draft",children:(0,g.__)("Draft")})]})})}}),(0,n.jsx)(c.A.Item,{children:(0,n.jsx)("input",{type:"submit",className:"button button-primary",style:{marginTop:10},value:(0,g.__)("Import")})}),(0,n.jsx)(c.A.Item,{style:{display:(null==m||null==(e=m.messages)?void 0:e.length)?"block":"none"},children:(0,n.jsx)(T,{result:m})})]})})}));var U=r(53603),L=r(89657);const q=(0,o.PA)((()=>{const e=(0,U.m)("import");return(0,n.jsxs)(s.A,{children:[(0,n.jsx)(a.A,{xl:16,sm:16,xs:24,children:(0,n.jsx)(l.A,{style:{margin:10},title:(0,g.__)("Import"),children:(0,n.jsx)(E,{})})}),(0,n.jsxs)(a.A,{xl:8,sm:8,xs:24,children:[(0,n.jsx)(l.A,{style:{margin:10},title:(0,g.__)("Export"),children:(0,n.jsx)(b,{})}),(0,n.jsx)(l.A,{style:{margin:10},title:(0,g.__)("Export consents"),children:(0,n.jsx)(w,{})})]}),(0,n.jsx)("p",{className:"description",style:{maxWidth:800,margin:"30px auto 0",textAlign:"center"},children:e}),(0,n.jsx)(L.b,{identifier:"import"})]})}))},52624:(e,t,r)=>{r.d(t,{U:()=>u});var n=r(3713),s=r(38123),a=r.n(s),l=r(32386),o=r(16983);const i=l.A.generatePicker(o.A);var c=r(30617);const{RangePicker:d}=i,u=e=>{const t=a().localeData();return(0,n.jsx)(d,{locale:{lang:{locale:a().locale(),placeholder:(0,c.__)("Select date"),rangePlaceholder:[(0,c.__)("Start date"),(0,c.__)("End date")],today:(0,c.__)("Today"),now:(0,c.__)("Now"),backToToday:(0,c.__)("Back to today"),ok:(0,c.__)("OK"),clear:(0,c.__)("Clear"),month:(0,c.__)("Month"),year:(0,c.__)("Year"),timeSelect:(0,c.__)("Select time"),dateSelect:(0,c.__)("Select date"),monthSelect:(0,c.__)("Choose a month"),yearSelect:(0,c.__)("Choose a year"),decadeSelect:(0,c.__)("Choose a decade"),yearFormat:"YYYY",dateFormat:t.longDateFormat("LL"),dayFormat:"D",dateTimeFormat:t.longDateFormat("LLL"),monthFormat:"MMMM",monthBeforeYear:!0,previousMonth:(0,c.__)("Previous month (PageUp)"),nextMonth:(0,c.__)("Next month (PageDown)"),previousYear:(0,c.__)("Last year (Control + left)"),nextYear:(0,c.__)("Next year (Control + right)"),previousDecade:(0,c.__)("Last decade"),nextDecade:(0,c.__)("Next decade"),previousCentury:(0,c.__)("Last century"),nextCentury:(0,c.__)("Next century")},timePickerLocale:{placeholder:(0,c.__)("Select time")},dateFormat:t.longDateFormat("LL"),dateTimeFormat:t.longDateFormat("LLL"),weekFormat:"YYYY-wo",monthFormat:"YYYY-MM"},...e})}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/24ff9b340dde82b4e71753b1e19cdc07/chunk-config-tab-import.lite.js.map
