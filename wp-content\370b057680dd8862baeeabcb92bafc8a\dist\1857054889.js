"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[108],{77421:(e,t,n)=>{var s,i,r,a,o,l;n.d(t,{FX:()=>r}),function(e){e.Append="append",e.Overwrite="overwrite",e.Clear="clear",e.Keep="keep"}(s||(s={})),function(e){e.Draft="draft",e.Published="published"}(i||(i={})),function(e){e.Free="free",e.Pro="pro"}(r||(r={})),function(e){e.IsWordPressPluginOrThemeActive="is-wordpress-plugin-or-theme-active"}(a||(a={})),function(e){e.Initial="initial",e.Update="update",e.Notify="notify",e.Removed="removed",e.NeverReleased="never-released"}(o||(o={})),function(e){e.CreatedAt="createdAt",e.EnabledWhenOneOf="enabledWhenOneOf",e.Extends="extends",e.ExtendedMergeStrategies="extendedMergeStrategies",e.ExtendedTemplateId="extendedTemplateId",e.ExtendsId="extendsId",e.ExtendsIdentifier="extendsIdentifier",e.Headline="headline",e.Id="id",e.Identifier="identifier",e.IsDeleted="isDeleted",e.IsExtendingMetaData="isExtendingMetaData",e.IsFavourite="isFavourite",e.MachineTranslationStatus="machineTranslationStatus",e.IsHidden="isHidden",e.LastEditedBy="lastEditedBy",e.Logo="logo",e.LogoId="logoId",e.MinRequiredRcbVersion="minRequiredRcbVersion",e.Name="name",e.NameTranslationFlags="nameTranslationFlags",e.Next="next",e.NextId="nextId",e.Pre="pre",e.PreId="preId",e.RecommendedWhenOneOf="recommendedWhenOneOf",e.ReleaseStatus="releaseStatus",e.SubHeadline="subHeadline",e.Status="status",e.SuccessorOfIdentifier="successorOfIdentifier",e.SuccessorOfIdentifierInfo="successorOfIdentifierInfo",e.Tier="tier",e.TranslationInfo="translationInfo",e.Version="version"}(l||(l={}))},13125:(e,t,n)=>{n.d(t,{T:()=>R,R:()=>F});var s=n(3713),i=n(91386),r=n(79955),a=n(59726),o=n(19393),l=n(41594),c=n(42396),d=n(18137);const u=()=>{const{__:e}=(0,a.s)(),[t,n]=(0,l.useState)(),r=e("This service does not set any technical cookies on the client of the visitor, but e.g. integrates a script.");return(0,s.jsxs)(i.A.Item,{required:!0,wrapperCol:{offset:c.sN.labelCol.span,span:c.sN.wrapperCol.span},children:[(0,s.jsx)(i.A.Item,{name:"isEmbeddingOnlyExternalResources",valuePropName:"checked",noStyle:!0,children:(0,s.jsx)(o.A,{})}),(0,s.jsxs)("span",{children:["  ",r]}),(0,s.jsx)("div",{style:{marginTop:10},ref:n,children:(0,s.jsx)(d.E,{form:"service",valueName:"isEmbeddingOnlyExternalResources",widthOfRef:t,renderDiff:e=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{disabled:!0,checked:e}),(0,s.jsxs)("span",{children:["  ",r]})]}),noBr:!0})})]})};var h=n(25330),p=n(33210),m=n(96431),g=n(64715),f=n(86310),v=n(45854),y=n(9551),x=n(6196);const j=e=>{let{field:t,readOnly:n}=e;const{__:r}=(0,a.s)(),o=(0,l.useMemo)((()=>({s:r("second(s)"),m:r("minute(s)"),h:r("hour(s)"),d:r("day(s)"),mo:r("month(s)"),y:r("year(s)")})),[r]);return(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,n)=>{var s,i,r,a;return(null==(s=e.technicalDefinitions[t.name])?void 0:s.type)!==(null==(i=n.technicalDefinitions[t.name])?void 0:i.type)||(null==(r=e.technicalDefinitions[t.name])?void 0:r.isSessionDuration)!==(null==(a=n.technicalDefinitions[t.name])?void 0:a.isSessionDuration)},children:e=>{let{getFieldValue:a}=e;const l=a(["technicalDefinitions",t.name,"type"]),c=a(["technicalDefinitions",t.name,"isSessionDuration"]);return["local","session","indexedDb","flash"].indexOf(l)>-1?null:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A.Item,{name:[t.name,"isSessionDuration"],noStyle:!0,valuePropName:"checked",children:(0,s.jsx)(v.A,{style:{float:"left",marginTop:5},disabled:n,children:(0,s.jsx)(g.A,{title:r("This cookie is active as long as the session is active"),children:(0,s.jsx)("span",{children:r("Session")})})})}),(0,s.jsx)(i.A.Item,{...P,name:[t.name,"duration"],rules:[{required:!c,message:r("Please provide a valid duration!")}],dependencies:[["technicalDefinitions",t.name,"isSessionDuration"]],children:(0,s.jsx)(y.A,{disabled:n,min:"0",addonAfter:(0,s.jsx)(i.A.Item,{name:[t.name,"durationUnit"],noStyle:!0,rules:[{required:!c,message:r("Please provide an unit!")}],children:(0,s.jsx)(x.A,{disabled:n,children:Object.keys(o).map((e=>(0,s.jsx)(x.A.Option,{value:e,children:o[e]},e)))})}),type:"number",style:{maxWidth:200,display:c?"none":void 0}})})]})}})};var b=n(84200),C=n(24513),k=n(39795);const w=e=>{let{field:t,readOnly:n}=e;const{__:r,_i:o}=(0,a.s)();return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,n)=>{var s,i,r,a;return(null==(s=e.technicalDefinitions[t.name])?void 0:s.type)!==(null==(i=n.technicalDefinitions[t.name])?void 0:i.type)||(null==(r=e.technicalDefinitions[t.name])?void 0:r.host)!==(null==(a=n.technicalDefinitions[t.name])?void 0:a.host)},children:e=>{let{getFieldValue:a}=e;const l=a(["technicalDefinitions",t.name,"type"]),c=a(["technicalDefinitions",t.name,"host"]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A.Item,{...P,name:[t.name,"host"],rules:[{validator:(e,t)=>(["local","session","indexedDb"].indexOf(l)>-1?(0,b.g)(t):(0,C.j)(t))||""===t&&"http"!==l?Promise.resolve():Promise.reject(r("Please provide a valid hostname!"))}],dependencies:[["technicalDefinitions",t.name,"type"]],children:(0,s.jsx)(y.A,{disabled:n})}),c.startsWith("*.")&&c.length>2&&(0,s.jsx)(k.q,{notices:[{message:o(r("You are using an invalid wildcard (placeholder) syntax {{code}}*.{{/code}} to match subdomains. Use {{code}}.%s{{/code}} to include subdomains.",c.substr(2)),{code:(0,s.jsx)("code",{})}),severity:"warning"}]})]})}})})},A=e=>{let{field:t,readOnly:n}=e;const{__:r,_i:o}=(0,a.s)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A.Item,{...P,name:[t.name,"name"],rules:[{required:!0,message:r("Please provide a technical cookie name!")}],children:(0,s.jsx)(y.A,{disabled:n})}),(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,n)=>{var s,i;return(null==(s=e.technicalDefinitions[t.name])?void 0:s.name)!==(null==(i=n.technicalDefinitions[t.name])?void 0:i.name)},children:e=>{let{getFieldValue:n}=e;const i=n(["technicalDefinitions",t.name,"name"]);return[/\[/gm,[/([{]+)/gm,e=>1===e.length],/\(/gm].filter((e=>Array.isArray(e)?e[0].test(i)&&i.match(e[0]).filter(e[1]).length>0:e.test(i))).length>0&&(0,s.jsx)(k.q,{notices:[{message:o(r("Are you sure this is the name of the cookie? It seems like you are using a placeholder that is not supported. Please use an asterisk ({{code}}*{{/code}}) as a placeholder if the cookie name is dynamically composed."),{code:(0,s.jsx)("code",{})}),severity:"warning"}]})}})]})},I=e=>{let{field:t,readOnly:n}=e;return(0,s.jsx)(i.A.Item,{noStyle:!0,name:[t.name,"purpose"],children:(0,s.jsx)(y.A.TextArea,{disabled:n,rows:1,autoSize:{maxRows:3}})})};var O=n(43181);const S=e=>{let{field:t,readOnly:n=!1}=e;const{__:r}=(0,a.s)(),o=(0,O.G)();return(0,s.jsx)(i.A.Item,{name:[t.name,"type"],noStyle:!0,rules:[{required:!0,message:r("Please provide a cookie type!")}],children:(0,s.jsx)(x.A,{disabled:n,children:Object.keys(o).map((e=>(0,s.jsx)(x.A.Option,{value:e,style:{display:"flash"===e?"none":void 0},children:o[e].name},e)))})})};var T=n(60111);const P={labelCol:{span:0},wrapperCol:{span:24},style:{margin:0}},N=e=>{let{technicalDefinitions:t}=e;const[n]=i.A.useForm();return(0,l.useEffect)((()=>{n.setFieldsValue({technicalDefinitions:t})}),[t]),(0,s.jsx)(i.A,{form:n,initialValues:{isEmbeddingOnlyExternalResources:!1,technicalDefinitions:t},children:(0,s.jsx)(E,{isDiff:!0})})},D=(e,t)=>{const n=[],s=e.reduce(((e,t)=>{let{name:s,host:i,duration:r,durationUnit:a,isSessionDuration:o,type:l,purpose:c}=t;const d=`${l}/${s}/${i}`;return e[d]=`${r}/${a}/${o}/${l}/${c||""}`,n.push(d),e}),{}),i=t.map((e=>{const{name:t,host:i,duration:r,durationUnit:a,isSessionDuration:o,type:l,purpose:c}=e,d=`${l}/${t}/${i}`,u=s[d];return void 0===u||u===`${r}/${a}/${o}/${l}/${c||""}`?void 0:{...e,position:n.indexOf(d)}})).filter(Boolean),r=e.map((e=>{let{type:t,name:n,host:s}=e;return`${t}/${n}/${s}`})),a=t.map(((e,t)=>{const{type:n,name:s,host:i}=e;return r.includes(`${n}/${s}/${i}`)?void 0:{...e,position:t}})).filter(Boolean);return a.length||i.length?{added:a,modified:i}:void 0},E=e=>{let{isDiff:t=!1}=e;const{__:n,_i:r}=(0,a.s)(),{SortableContext:o,SortableRow:c,DragHandle:u}=(0,T.E)(),v=function(){const[e,t]=(0,l.useState)({}),n=(0,l.useRef)([]);return(0,l.useEffect)((()=>{let e;for(;e=n.current.pop();)e()}),[e]),(0,l.useCallback)((e=>{n.current.push(e),t({})}),[])}(),[y,x]=(0,l.useState)({current:1,pageSize:10});return(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,n)=>e.isEmbeddingOnlyExternalResources!==n.isEmbeddingOnlyExternalResources||(t?JSON.stringify(e.technicalDefinitions)!==JSON.stringify(n.technicalDefinitions):e.technicalDefinitions.length!==n.technicalDefinitions.length),children:e=>{let{getFieldValue:a,validateFields:l,getFieldsValue:b}=e;return a("isEmbeddingOnlyExternalResources")?null:(0,s.jsx)(i.A.List,{name:"technicalDefinitions",children:(e,i)=>{let{add:a,remove:C,move:k}=i;const{current:O,pageSize:T}=y,P=(O-1)*T,E=O*T,B=Math.ceil(e.length/T);return(0,s.jsxs)("table",{className:"wp-list-table widefat fixed striped table-view-list",style:{marginBottom:25},children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{width:45,align:"right",children:" "}),(0,s.jsx)("td",{width:150,children:n("Cookie type")}),(0,s.jsx)("td",{children:(0,s.jsx)(g.A,{title:r(n("Every cookie has a technical name, which you must provide. If a cookie name is dynamically composed, please use an asterisk ({{code}}*{{/code}}) as a wildcard (placeholder)."),{code:(0,s.jsx)("code",{})}),children:(0,s.jsxs)("span",{children:[n("Technical cookie name")," ",(0,s.jsx)(h.A,{})]})})}),(0,s.jsx)("td",{children:(0,s.jsx)(g.A,{title:n("Every cookie is associated to a domain or hostname."),children:(0,s.jsxs)("span",{children:[n("Technical cookie host")," ",(0,s.jsx)(h.A,{})]})})}),(0,s.jsx)("td",{width:290,children:(0,s.jsx)(g.A,{title:n("A HTTP cookie is only valid for a certain time, which is defined when the cookie is set."),children:(0,s.jsxs)("span",{children:[n("Cookie duration")," ",(0,s.jsx)(h.A,{})]})})}),(0,s.jsx)("td",{children:(0,s.jsx)(g.A,{title:n("Each cookie serves a purpose (e.g. user identification for tracking), which should be explained."),children:(0,s.jsxs)("span",{children:[n("Purpose")," ",(0,s.jsx)(h.A,{})]})})}),(0,s.jsx)("td",{width:70,align:"right",children:" "})]})}),(0,s.jsx)("tbody",{children:(0,s.jsx)(o,{items:e.map((e=>{let{key:t}=e;return`${t}`})),onDragEnd:t=>{let{active:n,over:s}=t;const i=e.findIndex((e=>e.key===+n.id)),r=e.findIndex((e=>e.key===+(null==s?void 0:s.id)));k(i,r)},children:e.map(((n,i)=>{const r=i+1,a=r>P&&r<=E;return(0,s.jsxs)(c,{"data-row-key":`${n.key}`,style:{display:a?void 0:"none"},children:[(0,s.jsx)("td",{children:e.length>1&&!t?(0,s.jsx)(u,{}):void 0}),(0,s.jsx)("td",{children:(0,s.jsx)(S,{field:n,readOnly:t})}),(0,s.jsx)("td",{children:(0,s.jsx)(A,{field:n,readOnly:t})}),(0,s.jsx)("td",{children:(0,s.jsx)(w,{field:n,readOnly:t})}),(0,s.jsx)("td",{children:(0,s.jsx)(j,{field:n,readOnly:t})}),(0,s.jsx)("td",{children:(0,s.jsx)(I,{field:n,readOnly:t})}),(0,s.jsx)("td",{children:e.length>1&&!t?(0,s.jsx)("a",{className:"button button-small",onClick:()=>{C(n.name),O>1&&O===B&&e.length%T==1&&x({current:O-1,pageSize:T})},children:(0,s.jsx)(p.A,{})}):null})]},n.key)}))})}),!t&&(0,s.jsx)("tfoot",{children:(0,s.jsx)("tr",{children:(0,s.jsxs)("td",{colSpan:7,align:"right",children:[(0,s.jsxs)("a",{className:"button button-primary alignright",onClick:()=>{a(F);const t=B+(e.length>0&&e.length%T==0?1:0);O!==t&&x({current:t,pageSize:T})},children:[(0,s.jsx)(m.A,{})," ",n("Add another cookie definition")]}),(0,s.jsx)(f.A,{onChange:(e,t)=>{const n=y.pageSize!==t?1:e;x({current:n,pageSize:t});const s=(n-1)*t,i=n*t,r=b(["technicalDefinitions"]).technicalDefinitions.map(((e,t)=>{const n=t+1;return n>s&&n<=i&&Object.keys(e).map((e=>["technicalDefinitions",t,e]))})).filter(Boolean).flat();v((()=>l(r)))},...y,showLessItems:!0,showTotal:e=>n("Total %d items",e),size:"small",total:e.length,className:"alignright",pageSizeOptions:[10,20,50,100,1e3],style:{marginRight:10},showSizeChanger:!0}),(0,s.jsx)(d.E,{useModal:!0,form:"service",valueName:"technicalDefinitions",noBr:!0,style:{marginTop:4},difference:D,apply:(e,t,n)=>{let{added:s,modified:i}=e,r=n;i.forEach((e=>{let{position:t,...n}=e;r[t]=n})),s.forEach((e=>{let{position:t,...n}=e;r.splice(t,0,n)})),r=r.filter((e=>{let{host:t,name:n,duration:s}=e;return""!==t&&""!==n&&void 0!==s})),r.forEach((e=>{const{type:t,host:n,name:s,_duplicate:i}=e;if(!i){const i=r.find((i=>{const{type:r,host:a,name:o}=i;return e!==i&&t===r&&n===a&&s===o}));i&&(i._duplicate=!0)}})),r=r.filter((e=>{let{_duplicate:t}=e;return!t})),t({technicalDefinitions:r})},newValueText:!1,renderDiff:(e,t)=>{let{added:i,modified:r}=t;return(0,s.jsxs)(s.Fragment,{children:[i.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:n("Missing entries:")})}),(0,s.jsx)(N,{technicalDefinitions:i})]}),r.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:n("Modified entries:")})}),(0,s.jsx)(N,{technicalDefinitions:r})]})]})}})]})})})]})}})}})};var B=n(38994);const _=()=>{const{__:e}=(0,a.s)(),[t,n]=(0,l.useState)(),r=e("Delete all first-party cookies after opt-out. First-party cookies are only cookies that are set by or for this domain.");return(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isEmbeddingOnlyExternalResources!==t.isEmbeddingOnlyExternalResources,children:e=>{let{getFieldValue:a}=e;return a("isEmbeddingOnlyExternalResources")?null:(0,s.jsxs)(i.A.Item,{required:!0,wrapperCol:{offset:c.sN.labelCol.span,span:c.sN.wrapperCol.span},children:[(0,s.jsx)(i.A.Item,{name:"deleteTechnicalDefinitionsAfterOptOut",valuePropName:"checked",noStyle:!0,children:(0,s.jsx)(o.A,{})}),(0,s.jsxs)("span",{children:["  ",r]}),(0,s.jsx)("div",{style:{marginTop:10},ref:n,children:(0,s.jsx)(d.E,{form:"service",valueName:"deleteTechnicalDefinitionsAfterOptOut",widthOfRef:t,renderDiff:e=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{disabled:!0,checked:e}),(0,s.jsxs)("span",{children:["  ",r]})]}),noBr:!0})})]})}})},F={type:"http",name:"",host:"",duration:void 0,durationUnit:"y",isSessionDuration:!1,purpose:""},R=()=>{const{__:e,_i:t}=(0,a.s)(),{essentialGroupId:n}=(0,r.X)();return(0,s.jsx)(i.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.group!==t.group,children:i=>{let{getFieldValue:r}=i;const a=r("group")!==n;return(0,s.jsxs)("div",{children:[(0,s.jsx)(B.r,{offset:c.sN.labelCol.span,description:t(e("For each service you use, you should specify all cookies that are used by it. You can find this out in the developer console of your browser. Please note that there are several types of cookies and according to {{aEprivacy}}ePrivacy Directive (Directive 2009/136/EC) Recital 66{{/aEprivacy}} you have to inform your visitors not only about (HTTP) cookies, but also about cookie-like information."),{aEprivacy:(0,s.jsx)("a",{href:e("https://devowl.io/go/eu-directive-2009-136-ec"),target:"_blank",rel:"noreferrer"})}),children:e("Technical cookie information")}),(0,s.jsx)(u,{}),a&&(0,s.jsx)(_,{}),(0,s.jsx)(E,{})]})}})}},92454:(e,t,n)=>{n.d(t,{h:()=>d});var s=n(3713),i=n(91386),r=n(45854),a=n(42396),o=n(20931),l=n(79955),c=n(59726);const d=e=>{let{type:t}=e;const{__:n}=(0,c.s)(),{template:d,isEdit:u}="service"===t?(0,l.X)():(0,o.j)();return(null==d?void 0:d.consumerData.successorOf.length)>0&&!u&&(0,s.jsx)(i.A.Item,{name:"succeessorDeletionCheck",valuePropName:"checked",wrapperCol:{offset:a.sN.labelCol.span},children:(0,s.jsx)(r.A,{children:n("service"===t?"Replace the previous service with this one. The old one should be deleted when this one is saved.":"Replace the previous content blocker with this one. The old one should be deleted when this one is saved.")})})}},14676:(e,t,n)=>{n.d(t,{o:()=>m});var s,i=n(3713),r=n(36086),a=n(91386),o=n(41594);!function(e){e.NoTranslation="no-translation",e.Full="full",e.Partly="partly"}(s||(s={}));var l=n(85360),c=n(42396),d=n(20931),u=n(79955),h=n(59726),p=n(39795);const m=e=>{let{form:t}=e;const{__:n,_i:m}=(0,h.s)(),{template:g}="service"===t?(0,u.X)():(0,d.j)(),{languagesCount:f,untranslatedItems:v,translatedItems:y,machineTranslatedItems:x}=(0,o.useMemo)((()=>{var e;return Object.values((null==g||null==(e=g.consumerData)?void 0:e.translations)||{}).reduce(((e,t)=>(t.isUntranslated?e.untranslatedItems.push(t):t.machineTranslationStatus===s.Full?e.machineTranslatedItems.push(t):e.translatedItems.push(t),e.languagesCount+=1,e)),{languagesCount:0,untranslatedItems:[],translatedItems:[],machineTranslatedItems:[]})}),[g]),j=(0,o.useCallback)((e=>(0,l.i)(e.map((e=>{let{flag:t,language:n,name:s}=e;return(0,i.jsxs)(i.Fragment,{children:[t&&(0,i.jsx)(r.A,{src:t,size:13,shape:"square",style:{display:"inline-block",width:"auto",borderRadius:5,marginRight:4},children:n.toUpperCase()},n),s||n.toUpperCase()]})})),((e,t)=>e===t-1?n(" and "):", "))),[]);return v.length>0||x.length>0?(0,i.jsx)(a.A.Item,{wrapperCol:{offset:c.sN.labelCol.span,span:c.sN.wrapperCol.span},children:(0,i.jsx)(p.q,{notices:[{nop:!0,severity:"info",message:(0,i.jsxs)(i.Fragment,{children:[v.length>0&&(0,i.jsx)("p",{children:m(n("This template is {{strong}}not yet translated{{/strong}} into {{languages/}}. Feel free to translate it yourself!"),{strong:(0,i.jsx)("strong",{}),languages:j(v)})}),x.length>0&&(0,i.jsx)("p",{children:m(1===f?n("This template has been automatically machine translated into {{machineTranslatedLanguages/}}. Please check all texts carefully!"):y.length>1?n("This template has been translated into {{humanTranslatedLanguages/}} by humans. However, translations in {{machineTranslatedLanguages/}} have so far only been machine translated and not checked by a human translator. Please check the text in the mentioned languages carefully!"):n("This template has so far only been machine translated in {{machineTranslatedLanguages/}} and has not been checked by a human translator. Please check the text carefully in the languages mentioned!"),{strong:(0,i.jsx)("strong",{}),machineTranslatedLanguages:j(x),humanTranslatedLanguages:j(y)})})]})}]})}):null}},74865:(e,t,n)=>{n.d(t,{B:()=>m,g:()=>g});var s=n(3713),i=n(91386),r=n(73491),a=n(41594),o=n(7533),l=n(42396),c=n(18137),d=n(20931),u=n(79955),h=n(59726),p=n(39795);const m="rcb-value-differ-from-template-pseudo",g=e=>{let{containerRef:t,resetButton:n,resetButtonEvent:g="click",form:f}=e;const{__:v,_i:y}=(0,h.s)(),{isTemplateUpdate:x}="service"===f?(0,u.X)():(0,d.j)(),[j,b]=(0,a.useState)([]),[C,k]=(0,a.useState)(!1),w=(0,a.useCallback)((()=>{if(t.current){const e=[...t.current.querySelectorAll(`.${m}`)];return b(e),e}return[]}),[C]);(0,a.useLayoutEffect)((()=>{const e=setInterval(w,800);return(0,o.x)((()=>t.current),100).then(w),document.addEventListener(c.sr,w),()=>{clearInterval(e),document.removeEventListener(c.sr,w)}}),[]);const A=()=>{const e=w();e.forEach((e=>e.click())),b([]),k(!0),e.length>0&&setTimeout(A,0)};return(0,a.useEffect)((()=>{if(null==n?void 0:n.current){const{current:e}=n;return j.length>0?e.style.removeProperty("display"):e.style.display="none",e.addEventListener(g,A),()=>e.removeEventListener(g,A)}return()=>{}}),[n,g,j.length]),x&&(j.length>0||C)?(0,s.jsx)(i.A.Item,{wrapperCol:{offset:l.sN.labelCol.span,span:l.sN.wrapperCol.span},style:{marginBottom:0},children:(0,s.jsx)(p.q,{notices:[{severity:"info",message:(0,s.jsxs)(s.Fragment,{children:[y(v("The template has been updated to provide current legal and technical information. Fields with outdated values or values you manually adjusted are marked with {{tag/}}. You can apply all changes with one click!"),{tag:(0,s.jsx)(r.A,{color:"blue",children:v("Differing from template")})}),(0,s.jsx)("br",{}),(0,s.jsx)("a",{className:"button "+(0===j.length&&C?"button-disabled":""),onClick:A,style:{marginTop:5},children:0===j.length&&C?v("Applied!"):v("Use default values")})]})}]})}):null}},93842:(e,t,n)=>{n.d(t,{T:()=>o,U:()=>a});var s=n(3713),i=n(24262),r=n(59726);const a="confirm:popconfirm-gateway",o=e=>{let{anchorRef:t}=e;const{__:n}=(0,r.s)();return(0,s.jsx)(i.A,{title:n("Do you really want to irrevocably overwrite all changes to this form with the defaults from the latest version of the template?"),cancelText:n("Cancel"),okText:n("Reset now"),overlayStyle:{maxWidth:450},onConfirm:()=>t.current.dispatchEvent(new CustomEvent(a)),placement:"topLeft",children:(0,s.jsx)("a",{style:{display:"none"},ref:t,children:n("Reset to latest template version")})})}},18137:(e,t,n)=>{n.d(t,{E:()=>C,MA:()=>j,sr:()=>y});var s=n(3713),i=n(6196),r=n(91386),a=n(73491),o=n(78915),l=n(30338),c=n(41594),d=n(53668),u=n.n(d),h=n(74865),p=n(20931),m=n(79955),g=n(59726),f=n(27114),v=n(75432);const y="RCB/FormValueDifferFromTemplateTag/Apply",x=(e,t)=>t instanceof Object&&!(t instanceof Array)?Object.keys(t).sort().reduce(((e,n)=>(e[n]=t[n],e)),{}):t,j=(e,t)=>JSON.stringify(e,x)!==JSON.stringify(t,x);function b(e){let{form:t,difference:n,newValueText:r,renderDiff:a,valueName:o,currentValue:l,splitView:h,useModal:y}=e;const{__:x}=(0,g.s)(),{template:j}="service"===t?(0,m.X)():(0,p.j)(),b="service"===t&&(0,m.X)().groups||[],C="function"!=typeof a,[k,w]=(0,c.useState)(j.version),{lang:A}=document.documentElement,I=(0,c.useMemo)((()=>[j,...j.consumerData.versions].map((e=>({..."service"===t?(0,v.r)(e,{groups:b}):(0,f.J)(e),version:e.version,versionOutput:new Date(e.createdAt).toLocaleDateString(A)})))),[j,b]),O=(0,c.useMemo)((()=>{const e=e=>I.filter((t=>{let{version:n}=t;return n===e}))[0];return I.map(((t,n)=>{let{version:s,versionOutput:i}=t,r="currentValue",a=e(s),o=s===j.version?x("Version %s (latest)",i):x("Version %s",i);if(C)if(0===n)o=`${x("Your value")} → ${o}`;else{const{version:t,versionOutput:i}=I[n-1];o=t===j.version?`${o} → ${x("Version %s (latest)",i)}`:`${o} → ${x("Version %s",i)}`,r=e(s),a=e(t)}return{version:s,label:o,leftVersion:r,rightVersion:a}}))}),[j.version,I,C,x]),S=(0,c.useMemo)((()=>O.filter((e=>{let{version:t}=e;return t===k}))[0]),[O,k]),[T,P]=(0,c.useMemo)((()=>{const{leftVersion:e,rightVersion:t}=S;return["currentValue"===e?l:e[o],t[o]]}),[l,I,S]),N=n(T,P);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.A,{style:{float:"right",width:300,marginRight:y?15:0},value:k,onChange:w,size:"small",children:[O.map((e=>{let{version:t,label:n}=e;return(0,s.jsx)(i.A.Option,{value:t,children:n},t)})),1!==O[O.length-1].version&&(0,s.jsx)(i.A.Option,{value:0,disabled:!0,children:(0,s.jsx)("i",{children:x("Older versions not available")})},0)]}),void 0===N?x("The version you selected does not show any difference to your current form value."):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{className:"description",style:{marginBottom:10},children:x("The value you entered is different from the value in the template.")}),!1!==r&&(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:r||x("Default value:")})}),C?(0,s.jsx)("div",{style:{maxHeight:200,overflow:"auto"},children:(0,s.jsx)(u(),{styles:{splitView:{fontSize:11},contentText:{lineHeight:"16px !important"},wordDiff:{wordBreak:"break-word !important",padding:0},gutter:{minWidth:"auto !important"},marker:{paddingLeft:"3px !important",paddingRight:"3px !important"}},oldValue:T,newValue:P,compareMethod:d.DiffMethod.WORDS,splitView:"boolean"!=typeof h||h,showDiffOnly:!1})}):a(P,N)]})]})}function C(e){const{useModal:t,form:n,valueName:i,difference:d,apply:u,widthOfRef:f,placement:v="bottomLeft",noBr:x=!1,className:j,style:C,ignoreEmptyDefaultValue:k,popoverProps:w}=e,{__:A}=(0,g.s)(),{template:I,defaultTemplateValues:{[i]:O}}="service"===n?(0,m.X)():(0,p.j)(),[S,T]=(0,c.useState)(!1),P=(0,c.useRef)(),N=(0,c.useCallback)(((e,t)=>d?d(e,t):e!==t||void 0),[d]);return k&&"string"==typeof O&&""===O||["undefined","null","[]","{}"].indexOf(JSON.stringify(O))>-1||"object"==typeof O&&0===Object.values(O).filter((e=>""!==e)).length?null:void 0!==O&&I&&(0,s.jsx)(r.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e[i]!==t[i]||P.current!==(null==f?void 0:f.clientWidth),children:n=>{let{getFieldValue:r,setFieldsValue:c}=n;const d=r(i),p=N(d,O);if(!p)return null;P.current=(null==f?void 0:f.clientWidth)||P.current;const m=()=>{"function"==typeof u?u(p,c,d):c({[i]:O}),document.dispatchEvent(new CustomEvent(y)),T(!1)},g=(0,s.jsx)(b,{...e,currentValue:d,difference:N}),k=(0,s.jsxs)(a.A,{color:"blue",style:{cursor:"pointer",...C},className:j,onClick:()=>t&&T(!0),children:[A("Differing from template"),(0,s.jsx)("span",{className:h.B,style:{display:"none"},onClick:e=>{m(),e.stopPropagation()}})]});return(0,s.jsxs)(s.Fragment,{children:[t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{open:S,onOk:m,onCancel:()=>T(!1),okText:A("Use default value"),cancelText:A("Cancel"),width:"calc(100% - 50px)",children:g}),k]}):(0,s.jsx)(l.A,{destroyTooltipOnHide:!0,overlayStyle:{width:P.current},content:(0,s.jsxs)("div",{className:"wp-clearfix",children:[g,(0,s.jsx)("a",{className:"button button-primary alignright",onClick:m,style:{marginTop:10},children:A("Use default value")})]}),title:void 0,placement:v,...w,children:k}),!x&&(0,s.jsx)("br",{})]})}})}},58025:(e,t,n)=>{n.d(t,{X:()=>x});var s=n(3713),i=n(18197),r=n(41594),a=n(43799),o=n(19117),l=n(19991),c=n(27465),d=n(64715),u=n(33146),h=n(73491),p=n(77421),m=n(59726),g=n(24325),f=n(12719);const{Meta:v}=a.A;function y(e){let{template:t,renderActions:n,renderTags:i,onSelect:r,grayOutAlreadyExisting:y,onAcknowledgementClick:x}=e;const{__:j,_i:b}=(0,m.s)(),{modal:C}=o.A.useApp(),{isPro:k,isDemoEnv:w}=(0,g.J)(),{headline:A,subHeadline:I,logoUrl:O,tier:S,consumerData:{tags:T,isDisabled:P,isCreated:N,isIgnored:D,acknowledgement:E}}=t,{technicalHandlingIntegration:B}=t.consumerData,_=j("Disabled"),F=S===p.FX.Pro&&(!k||w),{open:R,modal:M}=(0,f.WH)({title:j("Want to use %s template?",A),feature:"preset",description:`${j("Only a limited number of templates for services and content blockers are available in the %s version of Real Cookie Banner. Get the PRO version now and create a service or content blocker from this template with just one click!",j(w?"Demo":"Free").toLowerCase())}${w?"":`\n\n${j("You can create this service yourself in the free version without any restrictions and research the necessary information.")}`}`},!w&&void 0),V=e=>{if(e.target.closest(".rcb-antd-card,.rcb-template-card-create-link"))if(E){const{paragraphs:e,accordion:n,buttonAction:i,buttonLabel:r}=E,a=C.info({icon:null,width:800,closable:!0,okButtonProps:{style:{display:"none"}},content:(0,s.jsxs)("div",{style:{textAlign:"center"},children:[(0,s.jsx)("img",{src:t.logoUrl,style:{display:"block",paddingTop:15,margin:"auto",height:176}}),(0,s.jsx)("h3",{style:{margin:"10px 0 0"},children:t.headline}),(0,s.jsx)("p",{style:{marginTop:0},children:t.subHeadline}),(0,s.jsx)(l.A,{children:j("Special circumstances for this service")}),e.map((e=>(0,s.jsx)("p",{style:{textAlign:"left"},children:b(e,{strong:(0,s.jsx)("strong",{}),u:(0,s.jsx)("u",{style:{textDecorationStyle:"dashed"}})})},e))),n&&(0,s.jsx)(c.A,{style:{textAlign:"left"},defaultActiveKey:Object.keys(n)[0],items:Object.keys(n).map((e=>({key:e,label:e,children:(0,s.jsx)("p",{style:{margin:0},dangerouslySetInnerHTML:{__html:n[e]}})})))}),x&&(0,s.jsx)("div",{style:{marginTop:10},children:(0,s.jsx)("button",{className:"button button-large button-primary",onClick:()=>{a.destroy(),null==x||x(i,t)},children:r})})]})})}else F?R():P||null==r||r(t)},L=(0,s.jsx)("img",{style:{width:"90%"},src:O}),G=i?i({...T},t):T;return(0,s.jsxs)(s.Fragment,{children:[M,(0,s.jsx)(d.A,{title:P?(0,s.jsx)("span",{dangerouslySetInnerHTML:{__html:T[_]}}):void 0,children:(0,s.jsx)(a.A,{className:"rcb-antd-template-card",hoverable:!P,style:{opacity:P||y&&(N||D)?.6:1},onClick:V,cover:B?(0,s.jsx)(u.A.Ribbon,{text:(0,s.jsx)(d.A,{title:b(j("The {{strong}}%s{{/strong}} plugin is recommending this service.",B.name),{strong:(0,s.jsx)("strong",{})}),children:(0,s.jsx)("div",{children:j("Integration")})}),children:L}):L,actions:n?n(t,V):[],children:(0,s.jsx)(v,{title:(0,s.jsxs)("span",{children:[F&&(0,s.jsx)(h.A,{color:f.QB,children:"PRO"}),!!T&&Object.keys(G).map((e=>(0,s.jsx)(d.A,{title:e!==_&&T[e]?(0,s.jsx)("span",{dangerouslySetInnerHTML:{__html:T[e]}}):void 0,children:(0,s.jsx)(h.A,{children:e})},e))),(0,s.jsx)("br",{}),A]}),description:I||(0,s.jsx)("i",{children:j("No description")})})})})]})}function x(e){let{templates:t,showHidden:n,showDisabled:a=!0,...o}=e;const[l,c]=(0,r.useState)(!1);return(0,r.useEffect)((()=>{!l&&t.length>0&&c(!0)}),[l,t.length]),t.length>0||l?(0,s.jsx)(s.Fragment,{children:t.filter((e=>{let{isHidden:t}=e;return!!n||!t})).filter((e=>{let{consumerData:{isDisabled:t}}=e;return!!a||!t})).map((e=>(0,s.jsx)(y,{template:e,...o},e.identifier)))}):(0,s.jsx)(i.A,{spinning:!0,style:{marginTop:10}})}},62789:(e,t,n)=>{n.d(t,{q:()=>O});var s=n(3713),i=n(18197),r=n(41594),a=n(9551),o=n(6099),l=n(92453),c=n(45854),d=n(19991),u=n(77421),h=n(85360),p=n(58025),m=n(59726),g=n(93859),f=n(24325);function v(e){let{children:t,cards:n,onFetched:i}=e;const[v,y]=(0,r.useState)(!1),{__:x,_i:j}=(0,m.s)(),{isPro:b,isLicensed:C}=(0,f.J)(),{fetchTemplates:k,enableLocalFilter:w}=(0,g.Z)(),[A,I]=(0,r.useState)(""),[O,S]=(0,r.useState)(!1);return(0,r.useEffect)((()=>{C&&(y(!0),k({term:A,isShowOnlyFree:O}).then((e=>{let t=e;w&&(t=t.filter((e=>{let{tier:t}=e;return!!b||!O||t!==u.FX.Pro})).filter((e=>{let{headline:t,subHeadline:n}=e;return!A.trim().length||A.split(" ").filter(Boolean).filter((e=>`${t} ${n||""}`.toLowerCase().indexOf(e.trim().toLowerCase())>-1)).length>0}))),i(t)})).finally((()=>{y(!1)})))}),[i,A,O,C]),(0,s.jsxs)("div",{style:{marginBottom:20},children:[t,C&&(0,s.jsx)(a.A.Search,{autoFocus:!0,loading:v,style:{maxWidth:400},placeholder:x("Search template by name..."),onChange:e=>I(e.target.value)}),(0,s.jsx)(o.A,{style:{marginTop:10},justify:"center",align:"middle",gutter:[10,16],children:(0,h.i)([!b&&(0,s.jsx)(l.A,{children:(0,s.jsx)(c.A,{onChange:()=>S(!O),disabled:!C,style:{marginRight:-8},children:x("Show only free templates")},"isShowOnlyFree")},"isShowOnlyFree")].filter(Boolean),(0,s.jsx)(l.A,{children:(0,s.jsx)(d.A,{type:"vertical"})}))}),A.length>0&&0===n.templates.length?(0,s.jsx)("div",{className:"notice notice-warning inline below-h2 notice-alt",style:{margin:"10px 0 0 0",maxWidth:400,display:"inline-block"},children:(0,s.jsx)("p",{children:j(x("{{strong}}No template found{{/strong}}. Please try to create the service yourself or {{a}}contact us{{/a}} and let us know for which service you need a template."),{strong:(0,s.jsx)("strong",{}),a:(0,s.jsx)("a",{href:x("https://devowl.io/support/"),target:"_blank",rel:"noreferrer"})})})}):C&&(0,s.jsx)(p.X,{...n})]})}var y=n(96431),x=n(37269),j=n(43799),b=n(32041);const{Meta:C}=j.A,k=e=>{let{onSelect:t}=e;const{__:n}=(0,m.s)(),{quickLinks:i}=(0,g.Z)(),{openDialog:r}=(0,b.g)();return(0,s.jsx)(s.Fragment,{children:i.map((e=>{if("string"==typeof e)switch(e){case"cookie-experts":return{id:"cookie-experts",cover:(0,s.jsx)("img",{src:b.$,style:{display:"block",paddingTop:15,margin:"auto",height:81}}),title:"Cookie Experts",description:n("help you with the setup"),onClick:r};case"service-individual":return{id:"from-scratch",cover:(0,s.jsx)(y.A,{style:{paddingTop:25,fontSize:70}}),title:n("Create from scratch"),description:n("an individual service"),onClick:()=>t(void 0)};case"blocker-individual":return{id:"from-scratch",cover:(0,s.jsx)(y.A,{style:{paddingTop:25,fontSize:70}}),title:n("Create from scratch"),description:n("an individual content blocker"),onClick:()=>t(void 0)};case"service-scanner":return{id:"scanner",cover:(0,s.jsx)(x.A,{style:{paddingTop:25,fontSize:70}}),title:n("Scan website"),description:n("and find used service"),onClick:()=>window.location.hash="#/scanner"}}return e})).map((e=>{let{id:t,cover:n,description:i,onClick:r,title:a}=e;return(0,s.jsx)(j.A,{hoverable:!0,style:{margin:5,width:240,display:"inline-block"},onClick:r,cover:n,children:(0,s.jsx)(C,{title:a,description:i})},t)}))})};var w=n(12719),A=n(67993),I=n(23291);function O(){const{__:e}=(0,m.s)(),[t,n]=(0,r.useState)(!1),{type:a,fetchUse:o,initialSelection:l,onSelect:c}=(0,g.Z)(),{isPro:d,isLicensed:u}=(0,f.J)(),[h,p]=(0,r.useState)(),[y,x]=(0,r.useState)([]);(0,r.useEffect)((()=>{l&&(async()=>{if("scratch"===l)c(void 0,!0);else{n(!0);try{const e=await o(l);c(e,!0)}finally{n(!1)}}})()}),[l]);const j=(0,r.useCallback)((async e=>{if(e){n(!0);try{const t=await o(e.identifier,e);c(t)}finally{n(!1)}}else c(void 0)}),[c]);return l?(0,s.jsx)("div",{style:{textAlign:"center"},children:(0,s.jsx)(i.A,{spinning:!0})}):(0,s.jsx)(i.A,{spinning:t,children:(0,s.jsxs)("div",{style:{textAlign:"center"},children:[(0,s.jsx)(k,{onSelect:j}),(0,s.jsx)("h2",{style:{margin:"20px 0",fontWeight:400,fontSize:"23px"},children:e("...or create it from one of our templates")}),(0,s.jsx)(v,{onFetched:x,cards:{templates:y,onSelect:j},children:"service"===a&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(A.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-find-all-services/"),label:e("How do I find all services (cookies) on my website?"),style:{marginBottom:10}}),(0,s.jsx)("br",{})]})}),!u&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(I.X,{title:e("service"===a?"Service templates with technical and legal details from the Service Cloud":"Content Blocker with suitable settings from the Service Cloud"),inContainer:!0,inContainerElement:h,mode:"license-activation",feature:"templates",description:e("service"===a?"Templates for hundreds of services that you could be running on your website will save you a lot of time and work when setting up your cookie banner. The current service templates database must be downloaded from Real Cookie Banner's Service Cloud. The data will be downloaded locally to your server, so your website visitors will not need to connect to the cloud.":"Templates to block for hundreds of services you might use on your website will save you a lot of time and work when setting up your cookie banner. The current service templates database must be downloaded from Real Cookie Banner's Service Cloud. The data will be downloaded locally to your server, so your website visitors will not need to connect to the cloud.")+(d?"":` ${e("We also offer you a lot of templates in the free version, you can activate your free licence at any time, without any costs.")}`),assetName:"service-cloud.svg",assetMaxHeight:200}),(0,s.jsx)("div",{ref:p,className:"rcb-antd-modal-mount",style:{height:1400,backgroundImage:`url('${w.sK}templates-blured.jpg')`,backgroundSize:"contain",backgroundRepeat:"no-repeat",backgroundPosition:"center top"}})]})]})})}},20931:(e,t,n)=>{n.d(t,{j:()=>r,k:()=>a});var s=n(52113);const i=Symbol(),r=()=>(0,s.NV)(i),a=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,s.gm)(i,...t)}},79955:(e,t,n)=>{n.d(t,{U:()=>a,X:()=>r});var s=n(52113);const i=Symbol(),r=()=>(0,s.NV)(i),a=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,s.gm)(i,...t)}},93859:(e,t,n)=>{n.d(t,{Z:()=>r,m:()=>a});var s=n(52113);const i=Symbol(),r=()=>(0,s.NV)(i),a=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,s.gm)(i,...t)}},60111:(e,t,n)=>{n.d(t,{E:()=>g});var s=n(3713),i=n(4509),r=n(57344),a=n(2986),o=n(22969),l=n(68959),c=n(57333),d=n(41594);let u,h;const p=()=>{const{setActivatorNodeRef:e,listeners:t}=(0,d.useContext)(u);return(0,s.jsx)(c.Ay,{type:"text",size:"small",icon:(0,s.jsx)(i.A,{}),style:{cursor:"move"},ref:e,...t})},m=e=>{const{elementType:t="tr"}=(0,d.useContext)(h),{attributes:n,listeners:i,setNodeRef:r,setActivatorNodeRef:a,transform:c,transition:p,isDragging:m}=(0,o.gl)({id:e["data-row-key"]}),g={...e.style,transform:l.Ks.Translate.toString(c),transition:p,...m?{position:"relative",zIndex:9999}:{}},f=(0,d.useMemo)((()=>({setActivatorNodeRef:a,listeners:i})),[a,i]),v={...e,ref:r,style:g,...n};return(0,s.jsx)(u.Provider,{value:f,children:"li"===t?(0,s.jsx)("li",{...v}):"div"===t?(0,s.jsx)("div",{...v}):(0,s.jsx)("tr",{...v})})};function g(){return u||(u=(0,d.createContext)({}),h=(0,d.createContext)({})),(0,d.useRef)({SortableContext:function(e){let{children:t,onDragEnd:n,items:i,...l}=e;return(0,s.jsx)(h.Provider,{value:l,children:(0,s.jsx)(r.Mp,{modifiers:[a.FN],onDragEnd:n,accessibility:{container:document.body},children:(0,s.jsx)(o.gB,{items:i,strategy:o._G,children:t})})})},SortableRow:m,DragHandle:p}).current}},27114:(e,t,n)=>{n.d(t,{J:()=>r,z:()=>a});var s=n(3820),i=n(59726);function r(e){const t={name:null==e?void 0:e.name,description:null==e?void 0:e.description,rules:null==e?void 0:e.consumerData.rules.join("\n"),services:null==e?void 0:e.consumerData.serviceTemplates.map((e=>{let{consumerData:{id:t}}=e;return t})).filter(Boolean),tcfVendors:null==e?void 0:e.consumerData.tcfVendorConfigurations.map((e=>{let{vendorConfigurationId:t}=e;return!1===t?void 0:t})).filter(Boolean),isVisual:null==e?void 0:e.isVisual,visualType:null==e?void 0:e.visualType,visualContentType:null==e?void 0:e.visualContentType,isVisualDarkMode:null==e?void 0:e.isVisualDarkMode,visualBlur:null==e?void 0:e.visualBlur,visualHeroButtonText:null==e?void 0:e.visualHeroButtonText,shouldForceToShowVisual:null==e?void 0:e.shouldForceToShowVisual};for(const e in t)void 0!==t[e]&&null!==t[e]||delete t[e];return t}function a(e){var t,n;const{__:a}=(0,i.s)(),{isTcf:o,isEdit:l,template:c,attributes:d}=e,u={name:"",status:"publish",description:"",rules:"",criteria:o&&(null==c?void 0:c.tcfVendorIds.length)?"tcfVendors":"services",tcfVendors:(null==d||null==(t=d.tcfVendors)?void 0:t.filter(Number))||[],tcfPurposes:(null==d||null==(n=d.tcfPurposes)?void 0:n.filter(Number))||[1],services:[],isVisual:!0,visualType:"default",visualMediaThumbnail:0,visualContentType:"generic",isVisualDarkMode:!1,visualBlur:0,visualDownloadThumbnail:void 0,visualHeroButtonText:"",shouldForceToShowVisual:!1,templateCheck:!c,succeessorDeletionCheck:(null==c?void 0:c.consumerData.successorOf.length)>0&&!l,...r(c),...d||{}},h=(0,s.S)({...e,defaultValues:u,i18n:{successMessage:a("You have successfully saved the content blocker."),validationError:a("The content blocker could not be saved due to missing/invalid form values."),unloadConfirm:a("You have unsaved changes. If you leave this page, your changes will be discarded."),unloadConfirmInitialActive:a("You have already created a service. Are you sure that you don't want to create the corresponding content blocker?")}}),{isTemplateUpdate:p,templateCheck:m}=h,g=(null==c?void 0:c.consumerData.serviceTemplates.filter((e=>{let{consumerData:{isCreated:t}}=e;return!t})))||[],f=(null==c?void 0:c.consumerData.tcfVendorConfigurations.filter((e=>{let{vendorConfigurationId:t}=e;return!1===t})))||[];return{...h,template:c,nonExistingServices:g,nonExistingTcfVendors:f,contextValue:{isTcf:o,isEdit:l,template:c,isTemplateUpdate:p,templateCheck:m,defaultTemplateValues:c?u:{}}}}},75432:(e,t,n)=>{n.d(t,{b:()=>d,r:()=>c});var s=n(41594),i=n(55285),r=n(14142),a=n(3820),o=n(13125),l=n(59726);function c(e,t){let{groups:n}=t;var s,i;const r=e?e.group?(null==(i=n.filter((t=>{let{name:n}=t;return n===e.group})))||null==(s=i[0])?void 0:s.id)||"preset-group-not-found":-1:void 0,a={name:null==e?void 0:e.name,group:"number"==typeof r?r:void 0,templateGroupNotFound:"preset-group-not-found"===r,purpose:null==e?void 0:e.purpose,isProviderCurrentWebsite:null==e?void 0:e.isProviderCurrentWebsite,provider:null==e?void 0:e.provider,providerContact:(null==e?void 0:e.providerContact)?{phone:"",email:"",link:"",...JSON.parse(JSON.stringify(e.providerContact))}:void 0,providerPrivacyPolicyUrl:null==e?void 0:e.providerPrivacyPolicyUrl,providerLegalNoticeUrl:null==e?void 0:e.providerLegalNoticeUrl,uniqueName:null==e?void 0:e.identifier,isEmbeddingOnlyExternalResources:null==e?void 0:e.isEmbeddingOnlyExternalResources,dataProcessingInCountries:(null==e?void 0:e.dataProcessingInCountries)?JSON.parse(JSON.stringify(e.dataProcessingInCountries)):void 0,dataProcessingInCountriesSpecialTreatments:(null==e?void 0:e.dataProcessingInCountriesSpecialTreatments)?JSON.parse(JSON.stringify(e.dataProcessingInCountriesSpecialTreatments)):void 0,legalBasis:null==e?void 0:e.legalBasis,technicalDefinitions:(null==e?void 0:e.technicalDefinitions)?JSON.parse(JSON.stringify(e.technicalDefinitions)):void 0,tagManagerOptInEventName:null==e?void 0:e.tagManagerOptInEventName,tagManagerOptOutEventName:null==e?void 0:e.tagManagerOptOutEventName,googleConsentModeConsentTypes:(null==e?void 0:e.googleConsentModeConsentTypes)?JSON.parse(JSON.stringify(e.googleConsentModeConsentTypes)):void 0,executePriority:(null==e?void 0:e.executePriority)||10,codeOptIn:null==e?void 0:e.codeOptIn,executeCodeOptInWhenNoTagManagerConsentIsGiven:null==e?void 0:e.executeCodeOptInWhenNoTagManagerConsentIsGiven,codeOptOut:null==e?void 0:e.codeOptOut,codeOnPageLoad:null==e?void 0:e.codeOnPageLoad,executeCodeOptOutWhenNoTagManagerConsentIsGiven:null==e?void 0:e.executeCodeOptOutWhenNoTagManagerConsentIsGiven,deleteTechnicalDefinitionsAfterOptOut:null==e?void 0:e.deleteTechnicalDefinitionsAfterOptOut};for(const e in a)-1!==["group"].indexOf(e)||void 0!==a[e]&&null!==a[e]||delete a[e];return a}function d(e){var t;const{__:n}=(0,l.s)(),{setCookiesViaManager:d,isGcm:u,territorialLegalBasis:h,selectedGroup:p,groups:m,allowContentBlockerCreation:g,shouldUncheckContentBlockerCheckbox:f,template:v,attributes:y,isEdit:x}=e,j=(null==v?void 0:v.consumerData.contentBlockerTemplates)||[],{templateGroupNotFound:b,...C}=c(v,{groups:m}),k={name:"",group:p||void 0,status:"publish",purpose:"",isProviderCurrentWebsite:!1,provider:"",providerContact:{phone:"",email:"",link:""},providerPrivacyPolicyUrl:"",providerLegalNoticeUrl:"",uniqueName:"",isEmbeddingOnlyExternalResources:!1,dataProcessingInCountries:[],dataProcessingInCountriesSpecialTreatments:[],legalBasis:i.iQ.Consent,technicalDefinitions:[o.R],codeDynamics:{},tagManagerOptInEventName:"",tagManagerOptOutEventName:"",googleConsentModeConsentTypes:[],codeOptIn:"",executeCodeOptInWhenNoTagManagerConsentIsGiven:!1,codeOptOut:"",codeOnPageLoad:"",executeCodeOptOutWhenNoTagManagerConsentIsGiven:!1,deleteTechnicalDefinitionsAfterOptOut:!1,createContentBlocker:!(null==v||null==(t=v.consumerData.technicalHandlingIntegration)?void 0:t.name)&&("boolean"==typeof(null==y?void 0:y.createContentBlocker)?y.createContentBlocker:j.length>0&&g&&!f),createContentBlockerId:null==y?void 0:y.createContentBlockerId,templateCheck:!v,succeessorDeletionCheck:(null==v?void 0:v.consumerData.successorOf.length)>0&&!x,...C,...y||{}};k.group=-1===k.group?void 0:k.group;const w=(0,a.S)({...e,defaultValues:k,i18n:{successMessage:n("You have successfully saved the service."),validationError:n("The service could not be saved due to missing/invalid form values."),unloadConfirm:n("You have unsaved changes. If you leave this page, your changes will be discarded.")}}),{form:A,setIsBusy:I,isTemplateUpdate:O,templateCheck:S}=w,T=(0,s.useCallback)((async e=>{const{uniqueName:t,name:n}=e;if(!t){I(!0),A.setFieldsValue({uniqueName:(0,r.Y)(n)});const{uniqueName:t}=await A.validateFields(["uniqueName"]);e.uniqueName=t}w.onFinish(e)}),[w.onFinish,A,I]);return{...w,template:v,onFinish:T,defaultValues:k,contextValue:{form:A,template:v,territorialLegalBasis:h,isEdit:x,isTemplateUpdate:O,templateCheck:S,setCookiesViaManager:d,isGcm:u,defaultTemplateValues:v?k:{},allowLegalBasisLegalRequirement:"real-cookie-banner"===(null==v?void 0:v.identifier),groups:m,preselectGroup:b?"preset-group-not-found":k.group,allowContentBlockerCreation:g,contentBlockerTemplates:j}}}},43181:(e,t,n)=>{function s(){return{http:{name:"HTTP Cookie",abbr:"HTTP",backgroundColor:"black"},local:{name:"Local Storage",abbr:"Local",backgroundColor:"#b3983c"},session:{name:"Session Storage",abbr:"Session",backgroundColor:"#3c99b3"},indexedDb:{name:"IndexedDB",abbr:"I-DB",backgroundColor:"#4ab33c"}}}n.d(t,{G:()=>s}),n(3713)},24513:(e,t,n)=>{function s(e){return/^\.?(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9])$/gm.test(e)}n.d(t,{j:()=>s})},84200:(e,t,n)=>{function s(e){return e.indexOf(".")>-1&&!!/^(?:(?:https?|ftp):\/\/)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:\/\S*)?$/.test(e)}n.d(t,{g:()=>s})},14142:(e,t,n)=>{n.d(t,{Y:()=>l});const s={},i="àáäâèéëêìíïîòóöôùúüûñç·/_,:;",r="aaaaeeeeiiiioooouuuunc------",a=i.replace(/\//g,"\\/"),o=new RegExp(`[${a}]`,"g");function l(e){if(s[e])return s[e];const t=e.trim().toLowerCase().replace(o,(e=>r.charAt(i.indexOf(e)))).replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-");return s[e]=t,t}},7533:(e,t,n)=>{async function s(e,t,n){void 0===t&&(t=500),void 0===n&&(n=0);let s=0;for(;!e();){if(n>0&&s>=n)return;await new Promise((e=>setTimeout(e,t))),s++}return e()}n.d(t,{x:()=>s})},95122:(e,t,n)=>{n.d(t,{r:()=>o});var s=n(3713),i=n(41594),r=n(43244),a=n.n(r);const o=e=>{let{settings:t={},value:n="",onChange:r}=e;const o=(0,i.useRef)(),l=(0,i.useRef)(),{codeEditor:c}=a();(0,i.useEffect)((()=>{if(c){const{codemirror:e}=c.initialize(o.current,t);l.current=e,e.on("change",(e=>{null==r||r(e.getValue())}))}}),[]);const d=(0,i.useCallback)((()=>{}),[]);return(0,i.useEffect)((()=>{"string"==typeof n&&o.current&&l.current&&o.current.value!==l.current.getValue()&&l.current.setValue(o.current.value)}),[n]),(0,s.jsx)("textarea",{ref:o,value:n,onChange:c?d:e=>{let{target:{value:t}}=e;return r(t)},style:{width:"100%"}})}},67120:(e,t,n)=>{n.r(t),n.d(t,{CookieEditForm:()=>Se});var s=n(3713),i=n(75792),r=n(18197),a=n(91386),o=n(57922),l=n(41594),c=n(27667),d=n(75432),u=n(79955),h=n(42396),p=n(92454),m=n(14676),g=n(74865),f=n(45854),v=n(6196),y=n(59726),x=n(39795);const j=()=>{const{__:e}=(0,y.s)(),{template:t,isEdit:n,essentialGroupId:i,allowContentBlockerCreation:r,contentBlockerTemplates:o}=(0,u.X)();return r&&!n&&(null==o?void 0:o.length)>0&&(0,s.jsxs)(a.A.Item,{wrapperCol:{offset:h.sN.labelCol.span},children:[(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.group!==t.group,children:t=>{let{getFieldValue:n}=t;return(0,s.jsx)(a.A.Item,{noStyle:!0,name:"createContentBlocker",valuePropName:"checked",dependencies:["group"],rules:[{validator:(t,s)=>n("group")===i&&s?Promise.reject(e('Services that should be associated with a content blocker cannot be in the "Essential" group, because it cannot be rejected.')):Promise.resolve()}],children:(0,s.jsx)(f.A,{children:e("Create content blocker for this service.")})})}}),(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.createContentBlocker!==t.createContentBlocker,children:n=>{let{getFieldValue:i}=n;return(0,s.jsxs)(s.Fragment,{children:[i("createContentBlocker")&&(0,s.jsx)(s.Fragment,{children:o.length>1&&(0,s.jsx)(a.A.Item,{noStyle:!0,name:"createContentBlockerId",rules:[{required:!0,message:e("Please select a template for the Content Blocker!")}],children:(0,s.jsx)(v.A,{style:{maxWidth:500,marginTop:10,display:"block"},placeholder:e("Select Content Blocker template..."),children:o.map((e=>{let{identifier:t,name:n,subHeadline:i}=e;return(0,s.jsx)(v.A.Option,{value:t,children:`${n}${i&&-1===n.indexOf(i)?` (${i})`:""}`},t)}))})})}),(0,s.jsx)(x.q,{notices:[{message:i("createContentBlocker")?e("Immediately after submitting this form you will be forwarded to another form that allows you to create the Content Blocker."):void 0,severity:"info"},{message:null==t?void 0:t.createContentBlockerNotice,severity:"info"}]})]})}})]})};var b=n(21917),C=n(18137);const k=()=>{const{__:e}=(0,y.s)(),{essentialGroupId:t,iso3166OneAlpha2:n,predefinedDataProcessingInSafeCountriesLists:i,territorialLegalBasis:r}=(0,u.X)(),[o,c]=(0,l.useState)();return(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.group!==t.group||e.isProviderCurrentWebsite!==t.isProviderCurrentWebsite||JSON.stringify(e.dataProcessingInCountries)!==JSON.stringify(t.dataProcessingInCountries)||JSON.stringify(e.dataProcessingInCountriesSpecialTreatments)!==JSON.stringify(t.dataProcessingInCountriesSpecialTreatments),children:l=>{let{getFieldValue:d}=l;const u=d("isProviderCurrentWebsite")?[]:(0,b.F)({predefinedDataProcessingInSafeCountriesLists:i,territorialLegalBasis:r,isDataProcessingInUnsafeCountries:!0,service:{dataProcessingInCountries:d("dataProcessingInCountries"),dataProcessingInCountriesSpecialTreatments:d("dataProcessingInCountriesSpecialTreatments")}}).filter((e=>e.startsWith("D")));return(0,s.jsxs)(a.A.Item,{label:e("Data processing in countries"),children:[(0,s.jsx)(a.A.Item,{name:"dataProcessingInCountries",noStyle:!0,children:(0,s.jsx)(v.A,{mode:"multiple",showSearch:!0,optionFilterProp:"children",maxTagCount:"responsive",allowClear:!0,children:Object.keys(n).map((e=>(0,s.jsx)(v.A.Option,{value:e,children:n[e]},e)))})}),(0,s.jsxs)("p",{className:"description",ref:c,children:[(0,s.jsx)(C.E,{form:"service",valueName:"dataProcessingInCountries",widthOfRef:o,difference:C.MA,renderDiff:e=>(0,s.jsx)(v.A,{mode:"multiple",disabled:!0,value:e,children:Object.keys(n).map((e=>(0,s.jsx)(v.A.Option,{value:e,children:n[e]},e)))})}),e("Data is usually processed in the countries where the service has its headquarter or servers. You should check with your service provider where your users' data is processed.")]}),u.length>0&&d("group")===t&&(0,s.jsx)(x.q,{notices:[{message:e("A transfer of personal data (e.g. IP address of the website visitor) to third countries that are unsafe in terms of data protection law (%s), is only permitted with prior consent. Consequently, such a service can never be essential under data protection law, since essential services are used on a different (in the case, non-existent) legal basis. In practice, however, it will not always be possible to avoid such use.",u.map((e=>n[e])).join(", ")),severity:"warning"}]})]})}})};var w=n(85360),A=n(94349),I=n(69810);const O=()=>{const{__:e,_i:t}=(0,y.s)(),{iso3166OneAlpha2:n,territorialLegalBasis:i,predefinedDataProcessingInSafeCountriesLists:r}=(0,u.X)(),[o,c]=(0,l.useState)(),d=(0,A.o)({__:e,_i:t,predefinedDataProcessingInSafeCountriesLists:r,iso3166OneAlpha2:n,territorialLegalBasis:i}),{dataProcessingInUnsafeCountriesArticles:h,dataProcessingInUnsafeCountriesArticlesLinks:p}=(0,I.j)({predefinedDataProcessingInSafeCountriesLists:r,iso3166OneAlpha2:n})(i);return(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>JSON.stringify(e.dataProcessingInCountries)!==JSON.stringify(t.dataProcessingInCountries)||JSON.stringify(e.dataProcessingInCountriesSpecialTreatments)!==JSON.stringify(t.dataProcessingInCountriesSpecialTreatments),children:l=>{let{getFieldValue:u}=l;const m=u("dataProcessingInCountries"),g=u("dataProcessingInCountriesSpecialTreatments"),{unsafeCountries:v,allowedSpecialTreatments:y}=(0,b.F)({territorialLegalBasis:i,predefinedDataProcessingInSafeCountriesLists:r,service:{dataProcessingInCountries:m,dataProcessingInCountriesSpecialTreatments:g}}),x=d.filter((e=>{let{value:t}=e;return y.indexOf(t)>-1}));return(0,s.jsxs)(a.A.Item,{label:e("Safety mechanisms for data transmission"),style:{display:0===y.length?"none":void 0},children:[(0,s.jsx)("p",{className:"description",ref:c,style:{margin:"0px 0px 10px 0px"},children:t(e("You have selected countries which are considered as unsafe (%s). If you activate one of this safety mechanisms, you do not ask for consent according to {{article/}}, if this kind of consent is activated at all in the settings.",v.map((e=>n[e])).join(", ")),{article:(0,w.i)(h.map(((e,t)=>(0,s.jsx)("a",{href:p[t],target:"_blank",rel:"noreferrer",children:e},e))),e(" or "))})}),(0,s.jsx)(a.A.Item,{name:"dataProcessingInCountriesSpecialTreatments",noStyle:!0,children:(0,s.jsx)(f.A.Group,{options:x})}),(0,s.jsx)(C.E,{form:"service",valueName:"dataProcessingInCountriesSpecialTreatments",widthOfRef:o,style:{marginTop:10},difference:(e,t)=>(0,C.MA)(e.filter((e=>y.indexOf(e)>-1)),t.filter((e=>y.indexOf(e)>-1))),renderDiff:e=>(0,s.jsx)(f.A.Group,{options:x,value:e,disabled:!0})})]})}})};var S=n(39555);const T=()=>{const{__:e}=(0,y.s)(),{template:t,setCookiesViaManager:n,essentialGroupId:i,groups:r,preselectGroup:o,isGcm:c}=(0,u.X)(),[d,h]=(0,l.useState)(),{managerLabel:p,serviceIsManager:m}=(0,S.XR)(n,{isGcm:c,presetId:null==t?void 0:t.identifier});return(0,s.jsxs)(a.A.Item,{label:e("Group"),required:!0,children:[(0,s.jsx)(a.A.Item,{name:"group",noStyle:!0,rules:[{required:!0,message:e("Please provide a group!")}],children:(0,s.jsx)(v.A,{children:r.map((e=>{let{id:t,name:n}=e;return(0,s.jsx)(v.A.Option,{value:t,children:n},t)}))})}),(0,s.jsxs)("p",{className:"description",ref:h,children:[(0,s.jsx)(C.E,{form:"service",valueName:"group",widthOfRef:d,style:{marginBottom:3},ignoreEmptyDefaultValue:!0,renderDiff:e=>(0,s.jsx)(v.A,{value:e,disabled:!0,children:r.map((e=>{let{id:t,name:n}=e;return(0,s.jsx)(v.A.Option,{value:t,children:n},t)}))})}),e("Each service must be assigned to a group. All services that do not belong to the group of essential services can (but do not have to) be accepted by visitors. According to the ePrivacy Directive, only services without which the website would not work are considered as essential services if the visitor has explicitly requested the service used. A possible economic interest of the website operator in using a tool, e.g. Google Analytics, is irrelevant.")]}),(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.group!==t.group,children:n=>{let{getFieldValue:r}=n;return(0,s.jsx)(x.q,{notices:[{message:m&&p&&r("group")!==i?e("You have defined %1$s in a non-essential service group. In our legal opinion, this is the only way to use %1$s legally. However, please note that if a user doesn't consent to load %1$s, opt-in and opt-out events will not be sent to %1$s. Real Cookie Banner offers you the possibility to specify a fallback HTML/JavaScript for this case.",p):void 0,severity:"warning"},{message:"preset-group-not-found"===o?e("The service group in which this service is normally placed (%s) has been deleted by you. Please group the service yourself.",t.group):void 0,severity:"warning"},{message:(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.group!==t.group,children:n=>{let{getFieldValue:i}=n;return(0,s.jsx)(x.q,{notices:[{message:t&&"number"==typeof o&&i("group")!==o?e("The groups specified in service templates were chosen wisely. Are you sure that this service should be assigned to another group for your case?"):void 0,severity:"warning"}]})}},"groupDiff")},{message:null==t?void 0:t.groupNotice,severity:"info"}]})}})]})};var P=n(81533),N=n(55285);const D=e=>{const{__:t}=(0,y.s)(),{allowLegalBasisLegalRequirement:n}=(0,u.X)();return(0,s.jsxs)(P.Ay.Group,{...e,value:e.overwriteValue||e.value,children:[(0,s.jsx)(P.Ay.Button,{value:N.iQ.Consent,children:t("Consent (Opt-in)")}),(0,s.jsx)(P.Ay.Button,{value:N.iQ.LegitimateInterest,children:t("Legitimate interest (Opt-out)")}),n&&(0,s.jsx)(P.Ay.Button,{value:N.iQ.LegalRequirement,children:t("Compliance with a legal obligation")})]})},E=()=>{const{__:e,_i:t}=(0,y.s)(),{allowLegalBasisLegalRequirement:n,essentialGroupId:i,template:r,isGcm:o,isGcmShowRecommandationsWithoutConsent:c,isBannerLessConsent:d}=(0,u.X)(),[h,p]=(0,l.useState)();return(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.group!==t.group||e.legalBasis!==t.legalBasis||`${e.codeOptIn}|${e.codeOptOut}|${e.codeOnPageLoad}`!=`${t.codeOptIn}|${t.codeOptOut}|${t.codeOnPageLoad}`,children:l=>{let{getFieldValue:u}=l;const m=u("group")===i,g=u("legalBasis");return(0,s.jsxs)(a.A.Item,{label:e("Legal basis"),required:!0,children:[(0,s.jsx)(a.A.Item,{name:"legalBasis",noStyle:!0,rules:[{required:!0}],children:(0,s.jsx)(D,{disabled:m,overwriteValue:m&&!n?N.iQ.LegitimateInterest:void 0})}),(0,s.jsxs)("p",{className:"description",ref:p,children:[(0,s.jsx)(C.E,{form:"service",valueName:"legalBasis",widthOfRef:h,style:{marginBottom:3},renderDiff:e=>(0,s.jsx)(D,{disabled:!0,value:e})}),t(e('Services can be used on various legal bases according to {{aGdpr}}Art. 6 GDPR{{/aGdpr}} for procession personal data and the national implementations of {{aEP}}ePrivacy Directive (Directive 2009/136/EC) Recital 66{{/aEP}} for storing or accessing cookies or cookie like information. "Consent" means that the user must explicitly agree to the service (opt-in). "Legitimate interest" means that your interest as a website operator prevails to use the service and the user must explicitly disagree (opt-out). Unless it is an essential service. Then an objection is not possible. {{strong}}A legitimate interest exists only in rare cases and only for data processing. If you are not sure, it is better to obtain consent to be on the safe side.{{/strong}}'),{strong:(0,s.jsx)("strong",{}),aGdpr:(0,s.jsx)("a",{href:e("https://gdpr-text.com/read/article-6/"),target:"_blank",rel:"noreferrer"}),aEP:(0,s.jsx)("a",{href:e("https://devowl.io/go/eu-directive-2009-136-ec"),target:"_blank",rel:"noreferrer"})})]}),(0,s.jsx)(x.q,{notices:[{message:m&&!n&&e('Your service is currently grouped as "Essential". This group implies the legitimate interest, with the only difference that this service cannot be opted out.'),severity:"info"},{message:null==r?void 0:r.legalBasisNotice,severity:"info"},{message:o&&c&&g===N.iQ.Consent&&/gtag\(|gtag=/.test(`${u("codeOptIn")}${u("codeOptOut")}${u("codeOnPageLoad")}`)&&t(e("You have activated Google Consent Mode and agreed to the recommendations for the use of Google services without consent. At the same time, you allow this service to be loaded only after consent has been obtained. Please check whether you wish to use these services on your website {{strong}}on the basis of a legitimate interest{{/strong}} (even before Google receiving consent into consent types accordance with Google Consent Mode and therefore e.g. not setting advertising cookies)!"),{strong:(0,s.jsx)("strong",{})}),severity:"warning"},{message:d&&!m&&g===N.iQ.LegitimateInterest&&t(e("You have decided to obtain consent without cookie banners (banner-less consent) on your website. This means that when your website visitors visit your website for the first time, they will not know that you use this service on the basis of a legitimate interest and can object to it. They will need to read your cookie policy or privacy policy to find out. Check whether there really is a legitimate interest in the legal sense according to {{a}}Art. 6 (1) (f) GDPR{{/a}}, which is only given in rare cases!"),{a:(0,s.jsx)("a",{href:e("https://gdpr-text.com/read/article-6/"),target:"_blank",rel:"noreferrer"})}),severity:"warning"}]})]})}})};var B=n(9551),_=n(14142);const F=()=>{const{__:e}=(0,y.s)(),{hasServiceByUniqueName:t}=(0,u.X)(),[n,i]=(0,l.useState)(),[o,c]=(0,l.useState)(!1),[d,h]=(0,l.useState)(!1),p=(0,l.useCallback)((async(n,s)=>{h(!0);try{const i=!!n&&await t(n);if(i){if(!s)throw e("A service with the same unique name already exists.");{const e=/^(.*)-(\d+)$/;let r=n,a=i;for(;a;)r=r.match(e)?r.replace(e,((e,t,n)=>`${t}-${+n+1}`)):`${r}-1`,a=await t(r);s(r)}}}finally{h(!1)}}),[t]);return(0,s.jsxs)(a.A.Item,{label:e("Name"),required:!0,children:[(0,s.jsx)(a.A.Item,{name:"name",noStyle:!0,rules:[{required:!0,message:e("Please provide a name!")}],children:(0,s.jsx)(B.A,{})}),(0,s.jsx)(r.A,{spinning:d,children:(0,s.jsx)("div",{onMouseEnter:()=>c(!0),onMouseLeave:()=>c(!1),style:{marginTop:"-1px",opacity:o?1:.7,transition:"opacity 200ms"},children:(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.name!==t.name,children:t=>{let{getFieldValue:n,setFieldsValue:i}=t;const r=n("name"),o=(0,_.Y)(r);return(0,s.jsx)(a.A.Item,{name:"uniqueName",noStyle:!0,validateTrigger:"onBlur",rules:[{validator:async(e,t)=>p(t,(e=>i({uniqueName:e})))}],children:(0,s.jsx)(B.A,{addonBefore:e("Unique identifier"),placeholder:o,size:"small"})})}})})}),(0,s.jsxs)("p",{className:"description",ref:i,children:[(0,s.jsx)(C.E,{form:"service",valueName:"name",widthOfRef:n,renderDiff:e=>(0,s.jsx)(B.A,{value:e,readOnly:!0})}),e('Each service used should have a descriptive name that is understandable to a non-professional user. Example: "Google Analytics". In addition, each service must have a unique identifier that you use for e.g. consent forwarding or the developer API (automatically generated if empty).')]})]})};var R=n(19393);const M=()=>{const{__:e,_i:t}=(0,y.s)(),{template:n}=(0,u.X)(),[i,r]=(0,l.useState)(),o=e("I as the website operator am the provider/responsible for data processing (usually for self-hosted services)");return(0,s.jsx)(a.A.Item,{label:e("Provider"),required:!0,children:(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isProviderCurrentWebsite!==t.isProviderCurrentWebsite,children:l=>{let{getFieldValue:c,setFieldValue:d}=l;const u=c("isProviderCurrentWebsite");return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{style:{marginTop:4},children:[(0,s.jsx)(a.A.Item,{name:"isProviderCurrentWebsite",valuePropName:"checked",noStyle:!0,children:(0,s.jsx)(R.A,{})}),(0,s.jsxs)("span",{children:["  ",o]})]}),(0,s.jsx)("div",{style:{marginTop:10,marginBottom:u?0:10},ref:r,children:(0,s.jsx)(C.E,{form:"service",valueName:"isProviderCurrentWebsite",widthOfRef:i,renderDiff:e=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(R.A,{disabled:!0,checked:e}),(0,s.jsxs)("span",{children:["  ",o]})]}),noBr:!0})}),(0,s.jsx)(a.A.Item,{name:"provider",noStyle:!0,rules:[{required:!u,message:e("Please name a provider!")}],dependencies:["isProviderCurrentWebsite"],children:(0,s.jsx)(B.A.TextArea,{autoSize:!0,style:{display:u?"none":void 0},placeholder:e("e.g. Global Co., 90210 Broadway Blvd., Nashville, TN 37011-5678"),onKeyPress:e=>{if(13===e.keyCode||13===e.which)return e.preventDefault(),!1},onPaste:e=>{setTimeout((()=>{const{value:t}=e.target;d("provider",t.replace(/\n+/gm,", "))}),0)}})}),(0,s.jsxs)("p",{className:"description",ref:r,children:[!u&&(0,s.jsx)(C.E,{form:"service",valueName:"provider",widthOfRef:i,renderDiff:e=>(0,s.jsx)(B.A,{value:e,readOnly:!0})}),t(e('Services have an "owner" who processes personal data or information saved in cookies etc. For self-hosted services, you are the data processor. Your contact details under {{a}}Settings > General > Website operator details{{/a}} are used. For other services, input the provider\'s contact details, often found in their privacy policy.'),{a:(0,s.jsx)("a",{href:"#/settings",target:"_blank",style:{fontStyle:"italic"}})})]}),(0,s.jsx)(x.q,{notices:[{message:null==n?void 0:n.providerNotice,severity:"info"}]})]})}})})};var V=n(6099),L=n(92453);const G=()=>{const{__:e}=(0,y.s)(),[t,n]=(0,l.useState)();return(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isProviderCurrentWebsite!==t.isProviderCurrentWebsite,children:i=>{let{getFieldValue:r}=i;return(0,s.jsx)(a.A.Item,{label:e("Provider contact"),style:{display:r("isProviderCurrentWebsite")?"none":void 0},children:(0,s.jsxs)("div",{ref:n,children:[(0,s.jsxs)(V.A,{gutter:[10,10],children:[(0,s.jsx)(L.A,{span:12,children:(0,s.jsx)(a.A.Item,{name:["providerContact","phone"],style:{margin:0},children:(0,s.jsx)(B.A,{addonBefore:e("Phone")})})}),(0,s.jsx)(L.A,{span:12,children:(0,s.jsx)(a.A.Item,{name:["providerContact","email"],rules:[{type:"email",message:e("Please provide a valid email!")}],style:{margin:0},children:(0,s.jsx)(B.A,{addonBefore:e("Email")})})}),(0,s.jsx)(L.A,{span:24,children:(0,s.jsx)(a.A.Item,{name:["providerContact","link"],rules:[{type:"url",message:e("Please provide a valid URL!")}],style:{margin:0},children:(0,s.jsx)(B.A,{addonBefore:e("Contact form")})})})]}),(0,s.jsx)(C.E,{form:"service",valueName:"providerContact",widthOfRef:t,style:{marginTop:3},difference:C.MA,ignoreEmptyDefaultValue:!0,renderDiff:t=>{let{email:n,phone:i,link:r}=t;return(0,s.jsxs)(V.A,{gutter:[10,10],children:[(0,s.jsx)(L.A,{span:12,children:(0,s.jsx)(B.A,{addonBefore:e("Phone"),value:i,readOnly:!0})}),(0,s.jsx)(L.A,{span:12,children:(0,s.jsx)(B.A,{addonBefore:e("Email"),value:n,readOnly:!0})}),(0,s.jsx)(L.A,{span:24,children:(0,s.jsx)(B.A,{addonBefore:e("Contact form"),value:r,readOnly:!0})})]})}})]})})}})},$=()=>{const{__:e}=(0,y.s)(),[t,n]=(0,l.useState)();return(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isProviderCurrentWebsite!==t.isProviderCurrentWebsite,children:i=>{let{getFieldValue:r}=i;return(0,s.jsxs)(a.A.Item,{label:e("Legal notice of the provider"),style:{display:r("isProviderCurrentWebsite")?"none":void 0},children:[(0,s.jsx)(a.A.Item,{name:"providerLegalNoticeUrl",noStyle:!0,rules:[{type:"url",message:e("Please provide a valid URL!")}],children:(0,s.jsx)(B.A,{})}),(0,s.jsxs)("p",{className:"description",ref:n,children:[(0,s.jsx)(C.E,{form:"service",valueName:"providerLegalNoticeUrl",widthOfRef:t,ignoreEmptyDefaultValue:!0,renderDiff:e=>(0,s.jsx)(B.A,{value:e,readOnly:!0})}),e("Provide a direct link to the legal notice or contact of the provider who operates this service (in the language of your website).")]})]})}})},U=()=>{const{__:e}=(0,y.s)(),[t,n]=(0,l.useState)();return(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isProviderCurrentWebsite!==t.isProviderCurrentWebsite,children:i=>{let{getFieldValue:r}=i;return(0,s.jsxs)(a.A.Item,{label:e("Privacy policy of the provider"),required:!0,style:{display:r("isProviderCurrentWebsite")?"none":void 0},children:[(0,s.jsx)(a.A.Item,{name:"providerPrivacyPolicyUrl",noStyle:!0,rules:[{required:!r("isProviderCurrentWebsite"),type:"url",message:e("Please provide a valid URL!")}],children:(0,s.jsx)(B.A,{})}),(0,s.jsxs)("p",{className:"description",ref:n,children:[(0,s.jsx)(C.E,{form:"service",valueName:"providerPrivacyPolicyUrl",widthOfRef:t,renderDiff:e=>(0,s.jsx)(B.A,{value:e,readOnly:!0})}),e("Provide a direct link to the privacy policy of the provider that runs this service (in the language of your website).")]})]})}})},q=()=>{const{__:e}=(0,y.s)(),[t,n]=(0,l.useState)();return(0,s.jsxs)(a.A.Item,{label:e("Purpose"),children:[(0,s.jsx)(a.A.Item,{name:"purpose",noStyle:!0,children:(0,s.jsx)(B.A.TextArea,{autoSize:{minRows:3}})}),(0,s.jsxs)("p",{className:"description",ref:n,children:[(0,s.jsx)(C.E,{form:"service",valueName:"purpose",widthOfRef:t}),e("The ePrivacy Directive and the GDPR require that a non-professional user understands the purpose of this service, how personal data is collected and how cookies are used for this purpose.")]})]})},W=()=>{const{__:e}=(0,y.s)();return(0,s.jsxs)(a.A.Item,{label:e("Status"),required:!0,children:[(0,s.jsx)(a.A.Item,{name:"status",noStyle:!0,rules:[{required:!0,message:e("Please choose an option!")}],children:(0,s.jsxs)(P.Ay.Group,{children:[(0,s.jsx)(P.Ay.Button,{value:"publish",children:e("Enabled")}),(0,s.jsx)(P.Ay.Button,{value:"private",children:e("Disabled")}),(0,s.jsx)(P.Ay.Button,{value:"draft",children:e("Draft")})]})}),(0,s.jsx)("p",{className:"description",children:e('Services with the status "Draft" or "Disabled" are not visible to the public. In addition, a draft will be highlighted in the service table so that you do not forget to configure it.')})]})};var J=n(67993),z=n(38994);const H=()=>{const{__:e}=(0,y.s)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(z.r,{offset:h.sN.labelCol.span,children:[e("General service configuration")," ",(0,s.jsx)(J.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-create-individual-cookie/")})]}),(0,s.jsx)(F,{}),(0,s.jsx)(W,{}),(0,s.jsx)(T,{}),(0,s.jsx)(M,{}),(0,s.jsx)(G,{}),(0,s.jsx)(q,{}),(0,s.jsx)(U,{}),(0,s.jsx)($,{}),(0,s.jsx)(E,{}),(0,s.jsx)(k,{}),(0,s.jsx)(O,{})]})};var X=n(96789),Y=n(30338),Z=n(61298),Q=n(57333);const K=e=>{let{name:t}=e;const{__:n,_i:i}=(0,y.s)(),{skipIfActiveComponents:r}=(0,u.X)(),o=(0,l.useMemo)((()=>Object.keys(r)),[r]);return(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,n)=>e[t]!==n[t],children:e=>{let{getFieldValue:a}=e;const l=[...a(t).matchAll(/\s+(skip-if-active=")([^"]+)(")/gm)].map((e=>{let[,,t]=e;return t.split(",").filter((e=>o.indexOf(e)>-1))})).flat(),c=l.filter(((e,t)=>l.indexOf(e)===t));return 0===c.length?null:(0,s.jsx)("div",{style:{marginTop:10},children:(0,s.jsx)(x.q,{notices:[{message:i(n("The code above contains HTML tags that are skipped when one of the following plugins is active: {{strong/}}. {{i}}What does this mean for me?{{/i}} In most cases, another plugin will take over the execution of the technical code, and you have to create a corresponding content blocker."),{strong:(0,s.jsx)("strong",{children:c.map((e=>r[e])).join(", ")}),i:(0,s.jsx)("i",{})}),severity:"info"}]})})}})},ee=()=>{const{__:e}=(0,y.s)(),{renderCodeMirror:t}=(0,u.X)(),[n,i]=(0,l.useState)();return(0,s.jsxs)(a.A.Item,{label:e("Code executed on page load"),children:[(0,s.jsx)(a.A.Item,{name:"codeOnPageLoad",noStyle:!0,children:t()}),(0,s.jsx)(K,{name:"codeOnPageLoad"}),(0,s.jsx)("div",{ref:i,children:(0,s.jsx)(C.E,{style:{marginTop:10},form:"service",valueName:"codeOnPageLoad",widthOfRef:n,noBr:!0,splitView:!1})})]})};var te=n(24325);const ne=e=>{let{name:t}=e;const{__:n,_i:i}=(0,y.s)(),{isPro:r}=(0,te.J)(),{setCookiesViaManager:o,isGcm:c,template:d}=(0,u.X)(),[p,m]=(0,l.useState)(),{managerLabel:g,features:f}=(0,S.XR)(o,{isGcm:c,presetId:null==d?void 0:d.identifier}),v=i(n("Only run this script if the user has {{u}}not{{/u}} allowed to use %s",g),{u:(0,s.jsx)("u",{})});return r&&f.executeCodeWhenNoTagManagerConsentIsGiven?(0,s.jsxs)(a.A.Item,{required:!0,wrapperCol:{offset:h.sN.labelCol.span,span:h.sN.wrapperCol.span},children:[(0,s.jsx)(a.A.Item,{name:t,valuePropName:"checked",noStyle:!0,children:(0,s.jsx)(R.A,{})}),(0,s.jsxs)("span",{children:["  ",v]}),(0,s.jsx)("div",{style:{marginTop:10},ref:m,children:(0,s.jsx)(C.E,{form:"service",valueName:t,widthOfRef:p,renderDiff:e=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(R.A,{disabled:!0,checked:e}),(0,s.jsxs)("span",{children:["  ",v]})]}),noBr:!0})})]}):null},se=()=>{const{__:e}=(0,y.s)(),{renderCodeMirror:t,essentialGroupId:n}=(0,u.X)(),[i,r]=(0,l.useState)(),o=(0,l.useCallback)((e=>({opacity:e?void 0:0,height:e?void 0:0,margin:e?void 0:0})),[]);return(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.group!==t.group,children:l=>{let{getFieldValue:c}=l;const d=c("group")!==n;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(a.A.Item,{label:e("Code executed on opt-out"),style:o(d),children:[(0,s.jsx)(a.A.Item,{name:"codeOptOut",noStyle:!0,children:t()}),(0,s.jsx)(K,{name:"codeOptOut"}),(0,s.jsx)("div",{ref:r,children:(0,s.jsx)(C.E,{style:{marginTop:10},form:"service",valueName:"codeOptOut",widthOfRef:i,noBr:!0,splitView:!1})})]}),d&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(ne,{name:"executeCodeOptOutWhenNoTagManagerConsentIsGiven"})})]})}})},ie=()=>{const{__:e}=(0,y.s)(),{renderCodeMirror:t}=(0,u.X)(),[n,i]=(0,l.useState)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(a.A.Item,{label:e("Code executed on opt-in"),children:[(0,s.jsx)(a.A.Item,{name:"codeOptIn",noStyle:!0,children:t()}),(0,s.jsx)(K,{name:"codeOptIn"}),(0,s.jsx)("div",{ref:i,children:(0,s.jsx)(C.E,{style:{marginTop:10},form:"service",valueName:"codeOptIn",widthOfRef:n,noBr:!0,splitView:!1})})]}),(0,s.jsx)(ne,{name:"executeCodeOptInWhenNoTagManagerConsentIsGiven"})]})},re=/{{([A-Za-z0-9_]+)}}/gm,ae=["codeOptIn","codeOptOut","codeOnPageLoad"],oe=()=>{const{__:e}=(0,y.s)(),{template:t}=(0,u.X)();return(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>ae.map((n=>e[n]!==t[n])).filter(Boolean).length>0,children:n=>{let{getFieldValue:i}=n;const r=ae.map((e=>i(e))).join(""),o=Array.from(r.matchAll(re)).map((e=>{let[,t]=e;return t})),l=[];return o?o.map((n=>{var i;const r=["codeDynamics",n],{label:o,invalidMessage:c=e("Please fill in a value!"),example:d,expression:u,hint:h}=(null==t||null==(i=t.dynamicFields)?void 0:i.find((e=>e.name===n)))||{};return l.indexOf(n)>-1?null:(l.push(n),(0,s.jsxs)(a.A.Item,{label:o||(0,s.jsx)("code",{children:n}),required:!0,children:[(0,s.jsx)(a.A.Item,{noStyle:!0,name:r,rules:[{required:!0,pattern:u?new RegExp(u):void 0,message:c}],normalize:e=>e.trim(),children:(0,s.jsx)(B.A,{placeholder:d})}),(0,s.jsx)(x.q,{notices:[{message:h,severity:"info"}]})]},n))})):null}})};var le=n(25330),ce=n(19117),de=n(64715),ue=n(97745);const he=()=>{const{__:e,_i:t}=(0,y.s)(),{isGcm:n,setCookiesViaManager:i,template:r}=(0,u.X)(),[o,c]=(0,l.useState)(),{message:d}=ce.A.useApp(),{useManager:h,serviceIsManager:p}=(0,S.XR)(i,{isGcm:n,presetId:null==r?void 0:r.identifier}),m=(0,l.useMemo)((()=>({[N.um.AdStorage]:e("Storing and reading of data such as cookies (web) or device identifiers (apps) related to advertising."),[N.um.AdUserData]:e("Sending user data to Google for online advertising purposes."),[N.um.AdPersonalization]:e("Evaluation and display of personalized advertising."),[N.um.AnalyticsStorage]:e("Storing and reading of data such as cookies (web) or device identifiers (apps), related to analytics (e.g. visit duration)."),[N.um.FunctionalityStorage]:e("Storing and reading of data that supports the functionality of the website or app (e.g. language settings)."),[N.um.PersonalizationStorage]:e("Storing and reading of data related to personalization (e.g. video recommendations)."),[N.um.SecurityStorage]:e("Storing and reading of data related to security (e.g. authentication functionality, fraud prevention, and other user protection).")})),[]),g="googleTagManagerWithGcm"===h&&(0,s.jsx)(L.A,{span:24,children:(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>{let{uniqueName:n,name:s}=e,{uniqueName:i,name:r}=t;return n!==i||s!==r},children:t=>{let{getFieldValue:n}=t;const i=n("uniqueName")||(0,_.Y)(n("name"));return!!i&&(0,s.jsx)(de.A,{title:e('Accessible as "Additional Consent" in Google Tag Manager to fire tags only if consent for this service has been given.'),children:(0,s.jsxs)(f.A,{checked:!0,disabled:!0,children:[(0,s.jsx)("code",{onClick:()=>{(0,ue.l)(i),d.success(e("Successfully copied to the clipboard!"))},children:i}),": ",e("Unique identifier of this service")," ",(0,s.jsx)(le.A,{})]})})}})});return n&&!p&&(0,s.jsxs)(a.A.Item,{label:e("Requested consent types for Google Consent Mode"),children:[(0,s.jsxs)(V.A,{children:[g,(0,s.jsx)(a.A.Item,{noStyle:!0,name:"googleConsentModeConsentTypes",children:(0,s.jsx)(f.A.Group,{style:{marginTop:6},children:Object.values(N.um).map((e=>(0,s.jsx)(L.A,{span:24,children:(0,s.jsxs)(f.A,{value:e,children:[(0,s.jsx)("code",{children:e}),": ",m[e]]})},e)))})})]}),(0,s.jsxs)("p",{className:"description",style:{marginTop:10},ref:c,children:[(0,s.jsx)(C.E,{form:"service",valueName:"googleConsentModeConsentTypes",widthOfRef:o,difference:C.MA,renderDiff:e=>(0,s.jsxs)(V.A,{children:[g,(0,s.jsx)(f.A.Group,{style:{marginTop:6},value:e,disabled:!0,children:Object.values(N.um).map((e=>(0,s.jsx)(L.A,{span:24,children:(0,s.jsxs)(f.A,{value:e,children:[(0,s.jsx)("code",{children:e}),": ",m[e]]})},e)))})]})}),t(e("You must obtain consent according to the Google Consent Mode based on so-called consent types, {{strong}}if the service can respect these consent types{{/strong}} (check the documentation of the provider of this service to find out if an integration has been implemented). Especially for Google services, you should request all necessary consent types in order to be able to collect as much data as possible. Consent in consent types is only granted if the website visitor explicitly agrees to all services (with all features) or explicitly agrees to these consent types in the individual privacy settings, but not if they do not exercise their right to object if you use this service on the legal basis of a legitimate interest."),{strong:(0,s.jsx)("strong",{})})]})]})},pe=()=>{const{__:e}=(0,y.s)(),{setCookiesViaManager:t,createdTagManagers:n,isGcm:i,template:r}=(0,u.X)(),{managerLabel:o,features:l,expectedManagerPresetId:c}=(0,S.XR)(t,{isGcm:i,presetId:null==r?void 0:r.identifier});return l.events&&(0,s.jsxs)(a.A.Item,{label:e("Event names for %s",o),required:!0,children:[(0,s.jsx)(a.A.Item,{name:"tagManagerOptInEventName",noStyle:!0,children:(0,s.jsx)(B.A,{addonBefore:e("Opt-in"),style:{maxWidth:400,marginRight:10,marginBottom:5}})}),(0,s.jsx)(a.A.Item,{name:"tagManagerOptOutEventName",noStyle:!0,children:(0,s.jsx)(B.A,{addonBefore:e("Opt-out"),style:{maxWidth:400,marginRight:10,marginBottom:5}})}),(0,s.jsx)("p",{className:"description",children:(0,s.jsx)(J.Y,{url:e("https://devowl.io/knowledge-base/real-cookie-banner-with-google-tag-manager-or-matomo-tag-manager/")})}),(0,s.jsx)(a.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.tagManagerOptInEventName!==t.tagManagerOptInEventName||e.tagManagerOptOutEventName!==t.tagManagerOptOutEventName,children:t=>{let{getFieldValue:i}=t;const r=i("tagManagerOptInEventName"),a=i("tagManagerOptOutEventName");return(r.length>0||a.length>0)&&!!o&&!n[c].length&&(0,s.jsx)(x.q,{notices:[{message:e("You have not yet defined a %s service. To use event names, you must create a %s service.",o,o),severity:"warning"}]})}})]})},me=()=>{const{__:e,_i:t}=(0,y.s)(),{template:n,setCookiesViaManager:i,contentBlockerTemplates:r,defaultTemplateValues:o,isGcm:l}=(0,u.X)(),c=(null==o?void 0:o.name)||"",{managerLabel:d}=(0,S.XR)(i,{isGcm:l,presetId:null==n?void 0:n.identifier}),p=null==n?void 0:n.consumerData.scan,m=null==n?void 0:n.consumerData.technicalHandlingIntegration,g=[{message:null==n?void 0:n.technicalHandlingNotice,severity:"info"},{message:m&&n.codeOptIn?t(e("The {{strong}}%1$s{{/strong}} plugin is connected to this service. This means that the scripts mentioned below may not be run exactly as they are or may be changed. So, it's best not to make any further changes or delete the script. Instead, if you want to make changes related to {{strong}}%2$s{{/strong}}, please do it directly in the {{strong}}%1$s{{/strong}}.",m.name,n.name),{strong:(0,s.jsx)("strong",{})}):void 0,severity:"info"},!!p&&n.codeOptIn&&!m&&!/^(wordpress-comments|matomo-plugin.*)$/.test(n.identifier)&&{message:r.length>0?e('You are currently embedding "%1$s" outside of Real Cookie Banner. You can have it blocked via a content blocker before consenting. Alternatively, you can use the following opt-in script to load %1$s directly into Real Cookie Banner.<br /><br />If you want to embed "%1$s" via Real Cookie Banner, you have to deactivate the content blocker at the end of the form and remove "%1$s" outside Real Cookie Banner.<br /><br />If you prefer to keep the current integration, you only have to delete the opt-in script in this form.',c):e("If you have already used %s before creating this service, please deactivate the script/plugin with which the service was loaded so far. The opt-in script will now take care of the correct integration.",c),severity:"warning"}].filter(Boolean).filter((e=>{let{message:t}=e;return t}));return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(z.r,{offset:h.sN.labelCol.span,headlineExtra:(0,s.jsx)(Y.A,{title:e("Priority of script execution"),placement:"bottomRight",overlayStyle:{maxWidth:500},forceRender:!0,content:(0,s.jsx)(a.A.Item,{name:"executePriority",rules:[{type:"number",min:0,max:99,required:!0,message:e("Please provide a valid priority!")}],style:{marginBottom:0},extra:(0,s.jsx)("p",{className:"description",style:{margin:"10px 0 0 0"},children:t(e("Scripts executed on opt-in or on opt-out can have the property {{code}}unique-write-name{{/code}}. If multiple scripts have the same name, only one will be executed to avoid multiple executions. If scripts with the same name are executed in different services, the one with the lowest priority is executed (defines the execution order). Changing the priority is relevant, for example, if you create two services for tracking with and without cookies (on different legal bases) for the same analysis tool."),{code:(0,s.jsx)("code",{})})}),children:(0,s.jsx)(Z.A,{min:0,max:99,addonBefore:e("Priority")})}),children:(0,s.jsx)(Q.Ay,{type:"link",icon:(0,s.jsx)(X.A,{}),children:e("Priority")})}),description:d?e("Define the %1$s event that should be thrown in the data layer when a visitor decides to accept or reject this service. The event can be used as a trigger in %1$s. For users who do not allow to use %1$s a fallback can be defined. You can define HTML and JavaScript code that is executed when a visitor decides to accept or reject this service, or when a page is loaded. It is important to define the event names and executed code correctly, as this will ensure that scripts are executed and cookies are only set with the user's consent.",d):e("Define HTML and JavaScript code that is executed when a visitor decides to accept or reject this service or when a page is loaded. It is important to define the executed code correctly, as this ensures that scripts are executed and cookies are set only after the user's consent."),children:e("Technical handling")}),(0,s.jsx)(pe,{}),(0,s.jsx)(he,{}),(0,s.jsx)(oe,{}),g.length>0&&(0,s.jsx)(a.A.Item,{wrapperCol:{offset:h.sN.labelCol.span,span:h.sN.wrapperCol.span},style:{marginBottom:10},children:(0,s.jsx)(x.q,{notices:g})}),(0,s.jsx)(ie,{}),(0,s.jsx)(se,{}),(0,s.jsx)(ee,{})]})};var ge=n(13125);const fe=()=>{const{__:e,_x:t}=(0,y.s)(),{templateCheck:n,isTemplateUpdate:i}=(0,u.X)();return!(!n&&!i)&&(0,s.jsx)(a.A.Item,{name:"templateCheck",valuePropName:"checked",required:!0,rules:[{type:"boolean",required:!0,transform:e=>e||void 0,message:e("Please confirm that you have checked the contents of the service.")}],children:(0,s.jsxs)(f.A,{children:[t("I have checked the information in the service template myself for correctness and completeness and have added missing information or corrected information that does not fit my use case. I am aware that the manufacturer of Real Cookie Banner cannot take any liability in this respect.","legal-text")," ",(0,s.jsx)(J.Y,{url:e("https://devowl.io/knowledge-base/is-real-cookie-banner-legally-compliant/")})]})})};var ve=n(93842);const ye=()=>{const{__:e}=(0,y.s)(),t=(0,l.useRef)(),n=(0,l.useRef)(),{initiallyScrollToField:i,form:r}=(0,u.X)();return function(e,t){(0,l.useEffect)((()=>{setTimeout((()=>{e.scrollToField(t,{behavior:"smooth",block:"center"})}),0)}),[t])}(r,i),(0,s.jsxs)("div",{ref:t,children:[(0,s.jsx)(g.g,{containerRef:t,resetButton:n,resetButtonEvent:ve.U,form:"service"}),(0,s.jsx)(m.o,{form:"service"}),(0,s.jsx)(H,{}),(0,s.jsx)(ge.T,{}),(0,s.jsx)(me,{}),(0,s.jsx)(j,{}),(0,s.jsx)(p.h,{type:"service"}),(0,s.jsxs)(a.A.Item,{className:"rcb-antd-form-sticky-submit",colon:!1,labelAlign:"left",label:(0,s.jsx)(ve.T,{anchorRef:n}),children:[(0,s.jsx)(fe,{}),(0,s.jsx)("div",{style:{textAlign:"center",margin:"10px 0"},children:(0,s.jsx)("input",{type:"submit",className:"button button-primary",value:e("Save")})})]})]})};var xe=n(60971),je=n(76576),be=n(69609),Ce=n(27449),ke=n(71951),we=n(42090),Ae=n(32991),Ie=n(95122),Oe=n(40164);const Se=(0,o.PA)((e=>{let{template:t,overwriteAttributes:n,navigateAfterCreation:o=!0,scrollToTop:p=!0,onCreated:m}=e;var g,f,v;const{routeGroup:{group:y,link:x},cookie:j,id:b,queried:C,fetched:k}=(()=>{const e=(0,be.K)(),{group:t}=e,n=(0,c.g)(),s=isNaN(+n.cookie)?0:+n.cookie,i=!!n.cookie,r=t.cookies.entries.get(+n.cookie)||new Ce.G(t.cookies,{id:0});return{routeGroup:e,cookie:r,id:s,queried:i,fetched:0!==r.key}})(),w=(0,c.Zp)(),{initiallyScrollToField:A}=(0,je.f)(),{cookieStore:I,optionStore:{isTcf:O,isGcm:S,isGcmShowRecommandationsWithoutConsent:T,isBannerLessConsent:P,isDataProcessingInUnsafeCountries:N,isConsentForwarding:D,setCookiesViaManager:E,createdTagManagers:B,territorialLegalBasis:F,others:{useEncodedStringForScriptInputs:R,activePlugins:M,iso3166OneAlpha2:V,frontend:{predefinedDataProcessingInSafeCountriesLists:L}}}}=(0,ke.g)(),{essentialGroup:G}=I,$=(null==j?void 0:j.templateModel)||I.templatesServices.get(null==t?void 0:t.identifier),U=I.groups.sortedGroups.map((e=>{let{data:{id:t,name:n}}=e;return{id:t,name:n}})),q=!!o,{prompt:W,form:J,isBusy:z,defaultValues:H,onFinish:X,onFinishFailed:Y,onValuesChange:Z,contextValue:Q}=(0,d.b)({isEdit:k,territorialLegalBasis:F,entityTemplateVersion:null==j||null==(f=j.data)||null==(g=f.meta)?void 0:g.presetVersion,attributes:n,selectedGroup:y.key,trackFieldsDifferFromDefaultValues:["group"],setCookiesViaManager:E,isGcm:S,groups:U,template:null==$?void 0:$.use,allowContentBlockerCreation:q,shouldUncheckContentBlockerCheckbox:null==$||null==(v=$.use)?void 0:v.shouldUncheckContentBlockerCheckbox,handleSave:async e=>{try{const t=e=>R?`encodedScript:${function(e){return btoa(encodeURIComponent(e).replace(/%([0-9A-F]{2})/g,((e,t)=>String.fromCharCode(parseInt(t,16)))))}(e)}`:e,{name:n,status:s,providerContact:i,purpose:r,isEmbeddingOnlyExternalResources:a,dataProcessingInCountries:l,dataProcessingInCountriesSpecialTreatments:c,technicalDefinitions:d,group:u,codeDynamics:h,createContentBlocker:p,createContentBlockerId:g,uniqueName:f,googleConsentModeConsentTypes:v,codeOptIn:y,codeOptOut:b,codeOnPageLoad:k,succeessorDeletionCheck:A,...O}=e,S={...O,providerContactPhone:i.phone,providerContactEmail:i.email,providerContactLink:i.link,codeOptIn:t(y),codeOptOut:t(b),codeOnPageLoad:t(k),isEmbeddingOnlyExternalResources:a,dataProcessingInCountries:JSON.stringify(l),dataProcessingInCountriesSpecialTreatments:JSON.stringify(c),codeDynamics:JSON.stringify(h),technicalDefinitions:JSON.stringify(a?K.technicalDefinitions:d),uniqueName:f||(0,_.Y)(n),googleConsentModeConsentTypes:JSON.stringify(v),presetId:null==$?void 0:$.data.identifier,presetVersion:null==$?void 0:$.data.version};if(delete S.templateCheck,C)j.setName(n),j.setStatus(s),j.setPurpose(r),j.setMeta(S),j.setGroup(u),await j.patch();else{const e=I.groups.entries.get(u),t=new Ce.G(e.cookies,{title:{raw:n},content:{raw:r,protected:!1},status:s,meta:S});if(await t.persist(),A){const e=$.data.consumerData.successorOf.map((e=>{let{id:t}=e;return t})),t=I.groups.sortedGroups.map((e=>{let{cookies:t}=e;return[...t.entries.values()]})).flat(),n=e.map((e=>t.find((t=>{let{key:n}=t;return n===e}))||new Ce.G(I.essentialGroup.cookies,{id:e})));await Promise.allSettled(n.map((e=>e.delete())))}null==m||m(t)}const T=g||(null==$?void 0:$.data.identifier);return()=>o&&(p?w(`/blocker/new?force=${T}&comingFromServiceCreation=1${"string"==typeof o?`&navigateAfterCreation=${encodeURIComponent(o)}`:""}`):"string"==typeof o?window.location.href=o:w(`${x.slice(1)}/${u}`))}catch(e){throw e.responseJSON.message}}}),K=k?{name:j.data.title.raw,status:j.data.status,group:y.key||void 0,purpose:j.data.content.raw,provider:j.data.meta.provider,isProviderCurrentWebsite:j.data.meta.isProviderCurrentWebsite,providerContact:{phone:j.data.meta.providerContactPhone,email:j.data.meta.providerContactEmail,link:j.data.meta.providerContactLink},providerPrivacyPolicyUrl:j.data.meta.providerPrivacyPolicyUrl,providerLegalNoticeUrl:j.data.meta.providerLegalNoticeUrl,uniqueName:j.data.meta.uniqueName||j.data.slug,isEmbeddingOnlyExternalResources:j.data.meta.isEmbeddingOnlyExternalResources,dataProcessingInCountries:JSON.parse(JSON.stringify(j.dataProcessingInCountries)),dataProcessingInCountriesSpecialTreatments:JSON.parse(JSON.stringify(j.dataProcessingInCountriesSpecialTreatments)),legalBasis:j.data.meta.legalBasis,technicalDefinitions:JSON.parse(JSON.stringify(j.technicalDefinitions)),codeDynamics:JSON.parse(JSON.stringify(j.codeDynamics)),tagManagerOptInEventName:j.data.meta.tagManagerOptInEventName,tagManagerOptOutEventName:j.data.meta.tagManagerOptOutEventName,googleConsentModeConsentTypes:JSON.parse(JSON.stringify(j.googleConsentModeConsentTypes)),executePriority:j.data.meta.executePriority,codeOptIn:j.data.meta.codeOptIn,executeCodeOptInWhenNoTagManagerConsentIsGiven:j.data.meta.executeCodeOptInWhenNoTagManagerConsentIsGiven,codeOptOut:j.data.meta.codeOptOut,executeCodeOptOutWhenNoTagManagerConsentIsGiven:j.data.meta.executeCodeOptOutWhenNoTagManagerConsentIsGiven,codeOnPageLoad:j.data.meta.codeOnPageLoad,deleteTechnicalDefinitionsAfterOptOut:j.data.meta.deleteTechnicalDefinitionsAfterOptOut,createContentBlocker:!1,createContentBlockerId:void 0,templateCheck:void 0,succeessorDeletionCheck:void 0}:H;(0,l.useEffect)((()=>{if(C&&!k){const e=[...I.groups.entries.values()].filter((e=>{let{cookies:t}=e;return t.entries.get(b)}))[0]||y;e.cookies.getSingle({params:{id:b,context:"edit"}})}}),[C,k]),(0,l.useEffect)((()=>{!$||$.use||$.busy||$.fetchUse()}),[$]),(0,l.useEffect)((()=>{p&&(0,xe.V)(0)}),[]);const ee=(0,l.useCallback)((async e=>{try{return(await(0,we.E)({location:Ae.H,params:{slug:e}})).filter((e=>e.ID!==j.key)).length>0}catch(e){return!1}}),[j.key]),te=C&&!k||$&&!$.use,[ne,se]=(0,u.U)({...Q,isTcf:O,isGcmShowRecommandationsWithoutConsent:T,isBannerLessConsent:P,isDataProcessingInUnsafeCountries:N,isConsentForwarding:D,hasServiceByUniqueName:ee,createdTagManagers:B,iso3166OneAlpha2:V,predefinedDataProcessingInSafeCountriesLists:L,essentialGroupId:G.key,skipIfActiveComponents:M,renderCodeMirror:()=>(0,s.jsx)(Ie.r,{settings:window.cm_settings}),initiallyScrollToField:A},{},{deps:[te],observe:["initiallyScrollToField"]});return te?(0,s.jsx)(Oe.e,{maxWidth:"fixed",children:(0,s.jsx)(i.A,{active:!0,paragraph:{rows:8}})}):(0,s.jsx)(Oe.e,{maxWidth:"fixed",children:(0,s.jsx)(ne,{value:se,children:(0,s.jsxs)(r.A,{spinning:z||(null==$?void 0:$.busy)||!1,children:[W,(0,s.jsx)(a.A,{name:`cookie-${y.key}-${b}`,form:J,...h.sN,initialValues:K,onFinish:X,onFinishFailed:Y,onValuesChange:Z,scrollToFirstError:{behavior:"smooth",block:"center"},labelWrap:!0,children:(0,s.jsx)(ye,{})})]})})})}))},69609:(e,t,n)=>{n.d(t,{K:()=>o});var s=n(41594),i=n(27667),r=n(65216),a=n(71951);const o=()=>{const{cookieGroup:e}=(0,i.g)(),{cookieStore:t}=(0,a.g)(),n=isNaN(+e)?0:+e,o=!!e,l=t.groups.entries.get(n)||new r.r(t.groups,{id:0}),c=(0,s.useCallback)((e=>{let{key:t}=e;return`#/cookies/${l.key}/edit/${t}`}),[l]),d=`#/cookies/${l.key}/new`;return{group:l,id:n,queried:o,fetched:0!==l.key,link:"#/cookies",editLink:c,addCookieLink:d}}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.19/abed9edb791b183652c535235683e4e6/108.lite.js.map
