"use strict";(self.webpackChunkrealCookieBanner_=self.webpackChunkrealCookieBanner_||[]).push([[406],{36:(e,o,t)=>{t.r(o),t.d(o,{WebsiteBlocker:()=>U});var r=t(6425),n=t(7936),i=t(9522),l=t(998),a=t(8552),s=t(5914);const c=999999999,d=[Symbol("extendBlockerHeroStylesheet"),(e,o)=>{let{jsx:t,jsxControl:r,control:n,boolIf:i,boolNot:l,boolSwitch:a,boolOr:d,rule:p,varName:u,nestedQuery:h,className:b}=e,{unsetDialogStyles:g,isMobile:v,visualThumbnail:{width:f,height:x,forceRatioIsset:m,forceRatio:k,hide:y,titleType:C},blocker:{visualContentType:w,visualBlur:B,isVisualDarkMode:S,presetId:I},bodyDesign:{acceptAllBg:T}}=o;const N=w("is-audio-player"),Y=[w("is-video-player"),I("is-vimeo"),C("is-top")],[D]=t("div",{classNames:"hero-wrapper",aspectRatio:i({when:l(N),then:{when:m("is-set"),then:`1 / calc(${k()} / 100)`,or:`${f()} / ${x()}`}}),position:"relative",height:i(N,"100px"),background:i(S,"linear-gradient(315deg, #2f3237 1%, #5e5e5e 100%)","linear-gradient(hsla(0, 0%, 89%, 1) 1%, hsla(0, 0%, 97%, 1) 100%)")}),[$]=t("div",{classNames:"hero-bg-wrapper",width:i(N,"100px","100%"),overflow:i(N,"hidden"),position:"absolute",inset:"0px"}),[R]=t("img",{classNames:"hero-bg",objectFit:"cover",position:"absolute",inset:"0px",width:"100%",height:"100%",filter:`blur(${B()})`},{loading:"lazy"}),[O]=t("div",{display:i(y("has-title"),"none"),classNames:"hero-title",padding:"5px 10px",maxWidth:"calc(100% - 140px)",fontSize:"15px",overflow:"hidden",textDecoration:"none",textOverflow:"ellipsis",whiteSpace:"nowrap",position:i(C("is-top"),"absolute"),textAlign:i(C("is-center"),"center"),color:a([[N,i(S,"white","black")],[Y,"rgb(0, 173, 239)"]],"white"),textShadow:a([[N,i(S,"1px 1px 1px black","1px 1px 1px white")],[Y,"none"]],"1px 1px 1px black"),fontWeight:i(Y,"bold"),top:i(Y,"20px","10px"),background:a([[N,"none"],[C("is-center"),"#0000007d"],[Y,"#00000059"]],"none"),left:a([[N,"110px"],[Y,"20px"]],"10px"),marginBottom:i(C("is-center"),"15px")}),[A]=t("div",{classNames:"hero-container",cursor:"pointer",position:"absolute",top:"0px",left:"0px",width:"100%",height:"100%",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",background:i(d([y("has-overlay"),N]),"none","rgb(0 0 0 / 38%)"),boxShadow:i(d([y("has-title"),C("is-center"),N]),"none","inset 0 115px 60px -60px rgb(0 0 0 / 77%)")}),[,W]=p({classNames:"hero-button",flex:"none !important",boxShadow:"rgb(0 0 0 / 15%) 0px 0px 100px 30px, rgb(0 0 0 / 40%) 0px 2px 5px 1px",zIndex:9}),F=u(),[H]=t("div",{classNames:"hero-play-wrapper",cursor:"pointer",zIndex:9,borderRadius:i(N,"99px","5px"),position:i(N,"absolute"),left:i(N,"120px"),top:i(N,"49px"),padding:i(N,"10px 10px 10px 13px","15px 30px"),background:a([[N,i(S,"#111213",T())],[Y,"rgb(0 0 0 / 70%)"]],T()),[F]:i(N,"0.2","0.3"),pseudos:{":hover":{background:"rgb(26 183 234 / 100%)"},":after":{content:'""',display:"block",background:"transparent",boxSizing:"border-box",width:"0px",height:`calc(60px * var(${F}))`,borderColor:"transparent transparent transparent white",transition:"100ms all ease",cursor:"pointer",borderStyle:"solid",borderWidth:`calc(40px * var(${F})) 0 calc(40px * var(${F})) calc(60px * var(${F}))`}}}),L=u().substr(2);h(`@keyframes ${L}`,{from:{opacity:0},to:{opacity:1}});const[j]=t("div",{classNames:"hero-db-overlay",position:"fixed",inset:"0px",minHeight:"100vh",zIndex:c,background:"#000000cf",animation:`${L} 0.3s`}),z=b(),P=r("dialog",n({rectTop:0,rectLeft:0,rectWidth:0},{rectTop:s.dD,rectLeft:s.dD,rectWidth:s.dD},(e=>{let{rectLeft:o,rectTop:t,rectWidth:r}=e;const[,n]=p({classNames:["hero-db-container",g],position:"absolute",left:o(),top:t(),padding:i(v,"20px 5px","20px 30px"),boxSizing:"border-box",zIndex:c,animation:`${L} 0.3s`,display:"flex",width:r(),pseudos:{[`.${z}`]:{width:"550px",left:"50%",maxWidth:"calc(100vw - 40px)",transform:"translateX(-50%)"}}});return n})),{tabIndex:0,modifyProps:(e,o)=>{let{rectWidth:t}=o;e.className+=t<=550?` ${z}`:""}});return{HeroWrapper:D,HeroBackgroundWrapper:$,HeroBackground:R,HeroTitle:O,HeroContainer:A,heroButton:W,HeroPlayButton:H,HeroOverlay:j,HeroOverlayBlocker:P}}];var p=t(7114),u=t(1208);const h=[Symbol("extendBlockerStylesheet"),(e,o)=>{let{jsx:t,boolIf:r,computed:n}=e,{layout:{dialogBorderRadius:i,type:l},design:{textAlign:a,fontFamily:c,fontInheritFamily:d,fontColor:p,borderWidth:u,borderColor:h},headerDesign:{borderWidth:b,borderColor:g},footerDesign:{borderWidth:v,borderColor:f},blocker:x,isMobile:m}=o;const{isDialog:k,isBanner:y}=n([l],(e=>{let[o]=e;return{type:o,isDialog:"dialog"===o,isBanner:"banner"===o}})),C=x.visualType("is-wrapped"),w=n([u,h,b,g,v,f,p],(e=>{let[o,t,r,n,i,l,a]=e;return{borderWidth:(0,s.dD)(o||1),borderColor:0===o?r>0?n:i>0?l:a:t}})),[B]=t("div",{classNames:"inner",textAlign:a("val"),fontFamily:r(d,"inherit",c()),color:p("hex"),borderRadius:r(k,i()),border:`${w.borderWidth()} solid ${w.borderColor()}`,position:"relative",padding:r({when:C,then:{when:m,then:"20px 30px",or:"30px 50px"}}),overflow:r(C,"hidden")}),[S]=t("div",{classNames:"content",boxShadow:r(C,"#0000004d 0px 0px 100px 30px, 0px 2px 5px 2px #0000001c"),borderRadius:r({when:C,then:{when:k,then:i()}}),position:"relative"}),[I]=t("img",{classNames:"inner-bg",objectFit:"cover",position:"absolute",top:0,left:0,width:"100%",height:"100%",filter:`blur(${x.visualBlur()})`},{loading:"lazy"});return{isDialog:k,isBanner:y,Inner:B,Content:S,InnerBackground:I,blockerOverwrites:w}}];var b=t(8700);function g(e){const{blocker:o}=(0,u.K)(),t=e||(0,b.o)();return t.extend(...((e,o)=>{let{presetId:t,isVisualDarkMode:r,visualBlur:n,visualContentType:i,visualType:l,visualThumbnail:a}=o;return[e,e=>{let{className:o,vars:c,variable:d,meta:{id:p}}=e;const u={firstButton:o()},[h]=c({isVisualDarkMode:r,visualBlur:n,visualContentType:i,visualType:l,presetId:t},{visualType:(0,s.$S)(l,["wrapped"]),visualContentType:(0,s.$S)(i,["audio-player","video-player"]),visualBlur:s.dD,presetId:(0,s.$S)(t,["youtube","vimeo"])}),b=a||{},{forceRatio:g,hide:v,titleType:f,width:x,height:m}=b,[k]=c({hide:v||[],forceRatio:g||0,titleType:f||"top",width:x,height:m},{titleType:(0,s.$S)(null==a?void 0:a.titleType,["center","top"]),hide:(0,s.g9)(null==a?void 0:a.hide,["overlay","title"])});return{a11yIds:u,blocker:h,visualThumbnail:{...k,forceRatioIsset:d(g,s.tZ)},individualBlockerClassName:p}}]})(t.reactRootSymbol,o),!0).extend(...h)}var v=t(6545),f=t(3558);const x=[Symbol("extendBlockerBodyStylesheet"),(e,o)=>{let{jsx:t,rule:r,boolIf:n,className:i}=e,{isDialog:l,isMobile:a,blockerOverwrites:{borderWidth:s,borderColor:c},design:{bg:d,fontSize:p},layout:{dialogBorderRadius:u},bodyDesign:{padding:h,descriptionFontSize:b,descriptionInheritFontSize:g},boolLargeOrMobile:v}=o;const f=i(),[x]=t("div",{classNames:"body-container",background:d(),padding:v(h,n),lineHeight:"1.4",borderStyle:"solid",borderColor:c(),borderWidth:s(),borderTopWidth:"0px",borderRadius:n(l,`0 0 ${u()} ${u()}`),pseudos:{">div":{margin:"auto"},[`<.${f} `]:{borderBottom:"0px",borderRadius:"0px"},">div>div,>div>a:last-of-type":{marginBottom:n(a,"10px","15px")}}}),[m]=t("div",r({classNames:"cookie-scroll",fontSize:n(g,v(p,n),v(b,n)),textAlign:"left",marginBottom:"10px",maxHeight:"400px",overflowY:"scroll",paddingRight:"10px"}));return{Container:x,showFooter:f,CookieScroll:m}}];function m(){const{blocker:{services:e},consent:o,groups:t}=(0,u.K)();return(0,n.Kr)((()=>{const r=[],n=[];for(const e of Object.values(o))n.push(...e);for(const{items:o}of t)for(const t of o)e.indexOf(t.id)>-1&&-1===n.indexOf(t.id)&&r.push(t);return r}),[t,e,o])}var k=t(1477),y=t(7140),C=t(5750),w=t(6812);const B=(0,l.g)(Promise.all([t.e(261),t.e(452),t.e(671),t.e(4)]).then(t.bind(t,9694)).then((e=>{let{Cookie:o}=e;return o}))),S=[Symbol("extendBlockerContentStylesheet"),(e,o)=>{let{jsx:t,boolIf:r,boolSwitch:n}=e,{boolLargeOrMobile:i,blockerOverwrites:{borderColor:l,borderWidth:a},isDialog:s,design:{bg:c,textAlign:d},layout:{dialogBorderRadius:p},footerDesign:u,headerDesign:h}=o;const b=i(h.borderWidth,r),[g]=t("div",{classNames:"header-container",position:"relative",background:r(h.inheritBg,c(),h.bg()),borderRadius:r(s,`${p()} ${p()} 0 0`),padding:i(h.padding,r),paddingBottom:`calc(${b} + ${i(h.padding,r,2)})`,borderWidth:a(),borderStyle:"solid",borderColor:l(),borderBottom:"unset",pseudos:{":after":{content:"''",display:"block",position:"absolute",left:"0px",right:"0px",bottom:"0px",background:h.borderColor(),height:b},">div":{margin:"auto",display:"flex",alignItems:"center",position:"relative",textAlign:r(h.inheritTextAlign,d("val"),h.textAlign("val")),justifyContent:r(h.inheritTextAlign,n([[d("is-center"),"center"],[d("is-right"),"flex-end"]]),n([[h.textAlign("is-center"),"center"],[h.textAlign("is-right"),"flex-end"]]))}}}),v=i(u.borderWidth,r),[f]=t("div",{classNames:"footer-container",fontWeight:u.fontWeight(),color:u.fontColor(),position:"relative",padding:i(u.padding,r),paddingTop:`calc(${v} + ${i(u.padding,r,0)})`,background:r(u.inheritBg,c(),u.bg()),fontSize:i(u.fontSize,r),textAlign:r(u.inheritTextAlign,d("val"),u.textAlign()),borderRadius:r(s,`0 0 ${p()} ${p()}`),borderWidth:a(),borderStyle:"solid",borderColor:l(),borderTop:"unset",pseudos:{":after":{content:"''",display:"block",position:"absolute",left:"0px",right:"0px",top:"0px",background:u.borderColor(),height:v},">div":{margin:"auto",lineHeight:"1.8"}}});return{HeaderContainer:g,FooterContainer:f}}];var I=t(3963),T=t(6546);const N=12,Y=(0,l.g)(Promise.resolve((e=>{let{closeIcon:o}=e;const{hasCloseIcon:t,HeaderTitle:n,HeaderContainer:i}=g().extend(...T.h).extend(...S),l=(0,u.K)(),{blocker:{name:a},texts:{blockerHeadline:s},keepVariablesInTexts:c}=l;return(0,r.Y)(i,{children:(0,r.FD)("div",{children:[(0,r.Y)(n,{className:o?t:void 0,children:c?s:s.replace(/{{name}}/gi,a)}),o]})})}))),D=(0,l.g)(Promise.resolve((()=>{const{Container:e,CookieScroll:o,a11yIds:{firstButton:t}}=g().extend(...x),i=(0,u.K)(),l=(0,y.p)(),[a,s]=(0,n.J0)(!1),{texts:{blockerLoadButton:c,blockerLinkShowMissing:d,blockerAcceptInfo:p},onUnblock:h,productionNotice:b,isConsentRecord:v,i18n:{close:f}}=i,S=m(),{description:I,teachings:T}=(0,k.bM)({services:S,disableListServicesNotice:!0,disableTcfPurposes:!0});return(0,r.Y)(e,{children:(0,r.FD)("div",{children:[(0,r.Y)(C.X,{description:I,teachings:[...T,p],nl2br:!0}),(0,r.Y)(w.$,{id:t,type:"acceptIndividual",onClick:()=>s(!a),forceShow:!0,busyOnClick:!1,"aria-expanded":a,"aria-controls":l,children:a?f:d}),(0,r.Y)(o,{children:a&&S.map((e=>(0,r.Y)(B,{cookie:e,checked:!0,disabled:!0},e.id)))}),(0,r.Y)(w.$,{type:"acceptAll",onClick:e=>h(e),forceShow:!0,sticky:!0,busyOnClick:!v,children:c}),b]})})}))),$=(0,l.g)(Promise.resolve((()=>{const{FooterContainer:e}=g().extend(...S),{rows:o,render:t}=(0,I.D)({putPoweredByLinkInRow:1});return(0,r.Y)(e,{children:(0,r.Y)("div",{children:t(o)})})}))),R=e=>{let{closeIcon:o,...t}=e;const i=(0,u.K)(),{designVersion:l=N,blocker:{visualType:a,visualThumbnail:s,name:c},texts:{blockerHeadline:d},i18n:{skipToConsentChoices:h}}=i,{Inner:b,Content:v,InnerBackground:f,A11ySkipToLink:x,a11yIds:{firstButton:m}}=g().extend(...p.R),k=(0,n.li)();return(0,r.FD)(b,{ref:k,className:"wp-exclude-emoji",...t,children:[(0,r.Y)(x,{href:`#${m}`,"aria-label":`${d.replace(/{{name}}/gi,c)}, ${h}`,children:h}),"wrapped"===a&&(0,r.Y)(f,{src:s.url,alt:s.title,"aria-hidden":!0}),(0,r.FD)(v,{children:[(0,r.Y)(Y,{closeIcon:o}),(0,r.Y)(D,{}),1===l&&(0,r.Y)($,{})]})]})},O=e=>{let{blockerClassName:o,blockerOverlayId:t,heroContainerRef:l}=e;const s=g(),{HeroOverlay:c,HeroOverlayBlocker:h,A11ySkipToLink:b,screenReaderOnlyClass:x}=s.extend(...d).extend(...p.R),m=(0,u.K)(),{headerDesign:{fontSize:k,fontColor:y},i18n:{close:C},blockedNode:w}=m,B=(0,n.li)(),[S,I]=(0,n.J0)(!!(null==w?void 0:w.hasAttribute(i.q8))),T=(0,a.y)(l,{observe:S}),N=(0,n.li)(),Y=(0,n.hb)((e=>{var o;S&&[N.current,null==(o=N.current)?void 0:o.nextElementSibling].indexOf(e.target)>-1&&I(!1)}),[S,N.current]),D=e=>{var o;e.preventDefault(),I(!1),null==(o=B.current)||o.focus()};return(0,n.vJ)((()=>{if(l.current){const e=e=>{B.current=document.activeElement;const o=e.target.classList.contains(x);return l.current&&(l.current.consentDelegateClick={element:B.current}),I(!0),setTimeout((()=>{var e;o&&(null==(e=N.current)?void 0:e.nextElementSibling.querySelector('a[href^="#a"]')).focus()}),50),e.preventDefault(),e.stopImmediatePropagation(),!1};return l.current.addEventListener("click",e,!0),()=>{var o;return null==(o=l.current)?void 0:o.removeEventListener("click",e,!0)}}return()=>{}}),[l.current]),S&&T?(0,r.Y)(f.Z,{renderInContainer:document.body,children:(0,r.FD)(n.FK,{children:[(0,r.Y)(c,{ref:N,onClick:Y}),(0,r.FD)(h,{id:t,rectLeft:T.left+window.scrollX,rectTop:T.top+window.scrollY,rectWidth:T.width,className:o,onClick:Y,"interaction-player-uqid":S?"blocker":void 0,children:[(0,r.Y)(b,{href:"#",onFocus:D,children:" "}),(0,r.Y)(n.tY,{fallback:null,children:(0,r.Y)(R,{closeIcon:(0,r.Y)(v.U,{tooltipText:C,width:k,color:y,thickness:2,onClick:()=>I(!1)})})}),(0,r.Y)(b,{href:"#",onFocus:D,children:" "})]})]})}):null},A=e=>{let{text:o}=e;const{blocker:{isVisualDarkMode:t},bodyDesign:{acceptAllBg:i}}=(0,u.K)(),l=(0,n.Kr)((()=>{let e=[];o.split("").forEach((o=>o.charCodeAt(0).toString().split("").map(Number).forEach((o=>e.push(o))))),e=e.map((e=>0===e?1:e)),e=e.map(((o,t)=>t>0?e[t-1]!==o&&o:o)).filter(Boolean),e=e.map(((o,t)=>(0===t||t===e.length-1||!(Math.abs(e[t-1]-o)>=4&&Math.abs(e[t+1]-o)))&&o)).filter(Boolean);for(let o=0;o<100&&!(e.length>500);o++)e=[...e,...e];return e}),[o]);return(0,r.Y)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",position:"absolute",left:170,top:44,right:20,height:46,overflow:"hidden"},children:l.map(((e,o)=>(0,r.Y)("div",{style:{height:10*e+"%",background:t?"white":i,paddingLeft:2,marginLeft:0===o?0:2}},o)))})},W=e=>{let{svgProps:o={},fill:t}=e;return(0,r.FD)("svg",{"aria-hidden":!0,width:68,height:48,version:"1.1",enableBackground:"new 0 0 595.3 420.2",viewBox:"0 0 595.3 420.2",...o,style:{cursor:"pointer",zIndex:9},children:[(0,r.Y)("path",{d:"m582.3 67.8c-6.8-25.6-17.8-47.4-43.4-54.2-46.5-12.5-241.3-13.6-241.3-13.6s-194.7 1.1-241.2 13.6c-25.6 6.8-36.5 28.5-43.4 54.2-12.5 46.4-13 142.3-13 142.3s.5 95.9 13 142.3c6.8 25.6 17.8 47.4 43.4 54.2 46.5 12.4 241.2 13.6 241.2 13.6s194.8-1.1 241.2-13.6c25.6-6.8 36.6-28.5 43.4-54.2 12.4-46.5 13-142.3 13-142.3s-.4-95.9-12.9-142.3z",fill:t}),(0,r.Y)("path",{d:"m401.8 210.1-173.3-96.3v192.6",fill:"#fff"})]})};var F=t(8639);const H=(0,l.g)(Promise.resolve(R));(0,l.g)(Promise.resolve((e=>{let{blockerClassName:o,blockerOverlayId:t,...i}=e;const l=g(),{HeroWrapper:a,HeroBackgroundWrapper:s,HeroBackground:c,HeroTitle:h,HeroContainer:b,heroButton:v,HeroPlayButton:f,screenReaderOnlyClass:x}=l.extend(...d).extend(...p.R),m=(0,u.K)(),{texts:{blockerHeadline:k},blocker:{presetId:y,name:C,visualHeroButtonText:B,visualContentType:S,visualThumbnail:I}}=m,T=(0,n.li)(),N=(0,n.li)(),[Y,D]=(0,n.J0)(!1),{url:$}=I,R=I.title||C,F=k.replace(/{{name}}/gi,C),H=`${I.title||F}ThiS iS jUst ANother TEXT TO reACh minIMum length`,L=["video-player","audio-player"].indexOf(S)>-1;return(0,r.FD)(n.FK,{children:[(0,r.FD)(a,{ref:T,...i,children:[(0,r.Y)(s,{"aria-hidden":!0,children:(0,r.Y)(c,{src:$,alt:R})}),(0,r.FD)(b,{role:"presentation",onMouseEnter:()=>D(!0),onMouseLeave:()=>D(!1),ref:N,children:[(0,r.Y)(h,{"aria-hidden":!0,children:R}),(0,r.Y)("a",{href:"#",className:x,"aria-label":`${F}, ${R}`,children:R}),"audio-player"===S&&(0,r.Y)(A,{text:H}),B&&-1===["audio-player","video-player"].indexOf(S)?(0,r.FD)(w.$,{type:"acceptAll",forceShow:!0,busyOnClick:!1,className:v,children:["  ",B,"  "]}):L&&("youtube"===y&&"video-player"===S?(0,r.Y)(W,{fill:Y?"#ff0808":"rgb(0 0 0 / 70%)"}):(0,r.Y)(f,{}))]})]}),(0,r.Y)(O,{blockerClassName:o,blockerOverlayId:t,heroContainerRef:N})]})})));var L=t(2655),j=t(9408),z=t(7177),P=t(5780),M=t(2767),E=t(3114),K=t(7513),V=t(9521);const J=(0,l.g)(Promise.resolve((e=>{let{skipInitialConsent:o}=e;const t=(0,F.N)(),{description:l}=t.reactRootSymbol,{recorder:a,isGcm:s,blocker:{visualType:c,visualThumbnail:d},blockedNode:p,createBefore:h,updateGcmConsentTypeChecked:v}=(0,u.K)(),[f,k]=(0,b.d)(t),C=(e=>{const{individualBlockerClassName:o,showFooter:t}=g(e).extend(...x),{designVersion:r=N}=(0,u.K)();return[1===r?t:void 0,e.className,o].filter(Boolean).join(" ")})(t),w=(0,y.p)(t);m(),(0,n.vJ)((()=>{}),[s,o]),(0,n.vJ)((()=>a?(a.restart(),()=>{a.toggle(!1)}):()=>{}),[]);const B={id:w},S={blockerClassName:C,blockerOverlayId:`${l}-o`};return(0,r.Y)(f,{value:k,children:(null==p?void 0:p.hasAttribute(i.Wu))?(0,r.Y)(O,{...S,heroContainerRef:{current:h||p}}):(0,r.Y)("div",{className:C,id:l,children:(0,r.Y)(H,{...B})})})}))),U=e=>{let{container:o,blockedNode:t,createBefore:i,poweredLink:l,blocker:a,paintMode:s,setVisualAsLastClickedVisual:c}=e;const{frontend:d,customizeValuesBanner:p,iso3166OneAlpha2:h,bannerDesignVersion:b,bannerI18n:g,isPro:v,isLicensed:f,isDevLicense:x,affiliate:m,isCurrentlyInTranslationEditorPreview:k}=(0,V.j)(),y=(0,E.C)(),C=y.getUserDecision(!0),w=(0,n.Kr)((()=>new P.v(o)),[]),B=(0,L.u)(),{onUnblock:S}=(I=y.getOptions(),{onUnblock:(e,o)=>{let{onPersistConsent:t,onApplyConsent:r,groups:n,blocker:i,recorder:l,isGcm:a,gcmConsent:s}=e;const{decisionCookieName:c,tcfCookieName:d,gcmCookieName:p}=I,{services:u,visualThumbnail:h,id:b}=i,g=(0,j.y)(c),v=n.find((e=>{let{isEssential:o}=e;return o})),f=!1===g?{[v.id]:v.items.map((e=>{let{id:o}=e;return o}))}:g.consent;for(const{id:e,items:o}of n)for(const{id:t}of o)if(u.indexOf(t)>-1){var x;if((null==(x=f[e])?void 0:x.indexOf(t))>-1)continue;f[e]=f[e]||[],f[e].push(t)}t({consent:f,gcmConsent:void 0,buttonClicked:"unblock",blocker:b,blockerThumbnail:(null==h?void 0:h.embedId)?`${h.embedId}-${h.fileMd5}`:void 0,tcfString:()=>z.A.get(d),recorderJsonString:l?JSON.stringify(l.createReplay()):void 0,uiView:"initial",event:o}).then((()=>r()))}});var I;const[T,N]=(0,u.t)({recorder:w,container:o,blockedNode:t,createBefore:i,...p,...d,productionNotice:(0,r.Y)(M.A,{isPro:v,isLicensed:f,isDevLicense:x,i18n:g}),paintMode:s,poweredLink:l,iso3166OneAlpha2:h,gcmConsent:[],blocker:a,designVersion:b,i18n:g,keepVariablesInTexts:k,affiliate:m,consent:{...!1===C?{}:C.consent},onPersistConsent:K.x,onApplyConsent:()=>(0,E.C)().applyCookies({type:"consent"})},{...B,onUnblock:(e,o)=>{S(e,o),c(o)}});return(0,r.Y)(T,{value:N,children:(0,r.Y)(n.tY,{fallback:null,children:(0,r.Y)(J,{})})})}},8489:(e,o,t)=>{t.d(o,{A:()=>r});const r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"minus",theme:"outlined"}},1503:(e,o,t)=>{t.d(o,{Q:()=>i});var r=function(){return r=Object.assign||function(e){for(var o,t=1,r=arguments.length;t<r;t++)for(var n in o=arguments[t])Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n]);return e},r.apply(this,arguments)},n={primaryColor:"#333",secondaryColor:"#E6E6E6"};function i(e,o){if(void 0===o&&(o={}),"function"==typeof e.icon){var t=o.placeholders||n;return l(e.icon(t.primaryColor,t.secondaryColor),o)}return l(e.icon,o)}function l(e,o){var t="svg"===e.tag?r(r({},e.attrs),o.extraSVGAttrs||{}):e.attrs,n=Object.keys(t).reduce((function(e,o){var r=o,n=t[r],i="".concat(r,'="').concat(n,'"');return e.push(i),e}),[]),i=n.length?" "+n.join(" "):"",a=(e.children||[]).map((function(e){return l(e,o)})).join("");return a&&a.length?"<".concat(e.tag).concat(i,">").concat(a,"</").concat(e.tag,">"):"<".concat(e.tag).concat(i," />")}},8552:(e,o,t)=>{t.d(o,{y:()=>u});var r,n=t(7936),i=["bottom","height","left","right","top","width"],l=new Map,a=function e(){var o=[];l.forEach((function(e,t){var r,n,l=t.getBoundingClientRect();r=l,n=e.rect,void 0===r&&(r={}),void 0===n&&(n={}),i.some((function(e){return r[e]!==n[e]}))&&(e.rect=l,o.push(e))})),o.forEach((function(e){e.callbacks.forEach((function(o){return o(e.rect)}))})),r=window.requestAnimationFrame(e)};const s=function(e,o){return{observe:function(){var t=0===l.size;l.has(e)?l.get(e).callbacks.push(o):l.set(e,{rect:void 0,hasRectChanged:!1,callbacks:[o]}),t&&a()},unobserve:function(){var t=l.get(e);if(t){var n=t.callbacks.indexOf(o);n>=0&&t.callbacks.splice(n,1),t.callbacks.length||l.delete(e),l.size||cancelAnimationFrame(r)}}}};function c(e){return"boolean"==typeof e}function d(e){return!(!e||"[object Function]"!={}.toString.call(e))}var p="undefined"!=typeof window&&window.document&&window.document.createElement?n.Nf:n.vJ;function u(e,o,t){let r,i;c(o)?r=o:(r=o?.observe??!0,i=o?.onChange),d(t)&&(i=t),n.vJ((()=>{c(o)&&console.warn("Passing `observe` as the second argument to `useRect` is deprecated and will be removed in a future version of Reach UI. Instead, you can pass an object of options with an `observe` property as the second argument (`useRect(ref, { observe })`).\nSee https://reach.tech/rect#userect-observe")}),[o]),n.vJ((()=>{d(t)&&console.warn("Passing `onChange` as the third argument to `useRect` is deprecated and will be removed in a future version of Reach UI. Instead, you can pass an object of options with an `onChange` property as the second argument (`useRect(ref, { onChange })`).\nSee https://reach.tech/rect#userect-onchange")}),[t]);let[l,a]=n.J0(e.current),u=n.li(!1),h=n.li(!1),[b,g]=n.J0(null),v=n.li(i);return p((()=>{v.current=i,e.current!==l&&a(e.current)})),p((()=>{l&&!u.current&&(u.current=!0,g(l.getBoundingClientRect()))}),[l]),p((()=>{if(!r)return;let o=l;if(h.current||(h.current=!0,o=e.current),!o)return void console.warn("You need to place the ref");let t=s(o,(e=>{v.current?.(e),g(e)}));return t.observe(),()=>{t.unobserve()}}),[r,l,e]),b}}}]);
//# sourceMappingURL=https://sourcemap.devowl.io/real-cookie-banner/5.1.9/25eae03216840b6461ef92c4c907194e/banner-lite-blocker-ui.lite.js.map
